Request headers
Some APIs require you to include specific headers in your requests to provide metadata about the request. You can configure request headers in <PERSON>pidog, while <PERSON><PERSON><PERSON> also automatically adds headers to your requests as needed.

Configuring request headers
You can set up headers in the Headers tab of your request. Enter any key-value pairs you need, and <PERSON><PERSON><PERSON> will send them along with your request. Apidog provides prompts for common options you can use to autocomplete your setup, such as Content-Type.

image.png
Autogenerated headers
Apidog will automatically add certain headers to your requests based on your request selections and settings. Click the Hidden option at the top of your request's Headers tab to see information about what <PERSON><PERSON><PERSON> will send with your request.

image.png
Hover over a header to view details about it. Apidog will indicate why the header has been added and how to deactivate or override the header value if needed.

You can make changes to a header in the Authorization tab, the request Body, Cookies for the request domain, the Settings, and in some cases in the Headers tab itself. If you need to navigate to a different part of the app, Apidog will display a link on the right-hand side.

To deactivate an autogenerated header in the Headers tab, clear its checkbox. To override an autogenerated header value, clear the checkbox next to the autogenerated entry and add a separate entry for the header, with its name in the Key field and your desired value in the Value field.

If you have more than one entry for the same header, <PERSON><PERSON><PERSON> indicates which one will be overridden. Apidog prioritizes headers you have either explicitly added in Headers or indirectly with selections you made in the other parts of your request such as Authorization. Overridden headers will be displayed like ~~Authorization~~.

For Content-Length and Content-Type headers, Apidog will automatically calculate the values when you send your request, based on the data in the Body tab. However, you can override both values.

