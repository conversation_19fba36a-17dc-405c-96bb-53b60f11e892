![Screenshot 2025-06-02 at 1.02.32 PM](https://us1.discourse-cdn.com/cursor1/optimized/3X/1/c/1c78fc496854916f249af902b4ad08fdc8f05551_2_563x500.png)

(The following is direct from Claud LLM)

** YOU MUST HAVE A CURRENT PAID CURSOR ACCOUNT IN ORDER TO USE ANY EXTERNAL LLM

For heavy development, **using your own Anthropic API key will definitely be cheaper**.

Here’s why:

**Cursor’s Pricing:**

- API pricing + 20% markup when you use their system
- Even with Cursor Pro, you’ll likely exceed the 500 “fast” requests quickly with heavy development

**Direct Anthropic API:**

- No markup - you pay Anthropic’s rates directly
- Claude Sonnet 4 pricing: you pay only for tokens used
- Much more cost-effective for high volume

**The Math:** If you’re doing heavy development and making hundreds of requests daily, that 20% markup from Cursor adds up fast. Plus, once you exceed Cursor’s included usage, you’re paying the markup on every additional request.

**My Recommendation for Heavy Development:**

1. Get an Anthropic API account directly
2. Set up billing with Anthropic
3. Use your API key in Cursor
4. You can still keep a basic Cursor subscription for other features, but use your own API key for Claude

You’ll save significantly on costs while getting the same Claude Sonnet 4 performance. The setup is straightforward - just add your Anthropic API key in Cursor’s settings.

# IDE integrations

> Integrate Claude Code with your favorite development environments

Claude Code seamlessly integrates with popular Integrated Development
Environments (IDEs) to enhance your coding workflow. This integration allows you
to leverage Claude's capabilities directly within your preferred development
environment.

## Supported IDEs

Claude Code currently supports two major IDE families:

* **Visual Studio Code** (including popular forks like Cursor and Windsurf)
* **JetBrains IDEs** (including PyCharm, WebStorm, IntelliJ, and GoLand)

## Features

* **Quick launch**: Use `Cmd+Esc` (Mac) or `Ctrl+Esc` (Windows/Linux) to open
  Claude Code directly from your editor, or click the Claude Code button in the
  UI
* **Diff viewing**: Code changes can be displayed directly in the IDE diff
  viewer instead of the terminal. You can configure this in `/config`
* **Selection context**: The current selection/tab in the IDE is automatically
  shared with Claude Code
* **File reference shortcuts**: Use `Cmd+Option+K` (Mac) or `Alt+Ctrl+K`
  (Linux/Windows) to insert file references (e.g., @File#L1-99)
* **Diagnostic sharing**: Diagnostic errors (lint, syntax, etc.) from the IDE
  are automatically shared with Claude as you work

## Installation

### VS Code

1. Open VSCode
2. Open the integrated terminal
3. Run `claude` - the extension will auto-install

Going forward you can also use the `/ide` command in any external terminal to
connect to the IDE.

<Note>
  These installation instructions also apply to VS Code forks like Cursor and
  Windsurf.
</Note>

### JetBrains IDEs

Install the
[Claude Code plugin](https://docs.anthropic.com/s/claude-code-jetbrains) from
the marketplace and restart your IDE.

<Note>
  The plugin may also be auto-installed when you run `claude` in the integrated
  terminal. The IDE must be restarted completely to take effect.
</Note>

<Warning>
  **Remote Development Limitations**: When using JetBrains Remote Development,
  you must install the plugin in the remote host via `Settings > Plugin (Host)`.
</Warning>

## Configuration

Both integrations work with Claude Code's configuration system. To enable
IDE-specific features:

1. Connect Claude Code to your IDE by running `claude` in the built-in terminal
2. Run the `/config` command
3. Set the diff tool to `auto` for automatic IDE detection
4. Claude Code will automatically use the appropriate viewer based on your IDE

If you're using an external terminal (not the IDE's built-in terminal), you can
still connect to your IDE by using the `/ide` command after launching Claude
Code. This allows you to benefit from IDE integration features even when running
Claude from a separate terminal application. This works for both VS Code and
JetBrains IDEs.

<Note>
  When using an external terminal, to ensure Claude has default access to the
  same files as your IDE, start Claude from the same directory as your IDE
  project root.
</Note>

## Troubleshooting

### VS Code extension not installing

* Ensure you're running Claude Code from VS Code's integrated terminal
* Ensure that the CLI corresponding to your IDE is installed:
  * For VS Code: `code` command should be available
  * For Cursor: `cursor` command should be available
  * For Windsurf: `windsurf` command should be available
  * If not installed, use `Cmd+Shift+P` (Mac) or `Ctrl+Shift+P` (Windows/Linux)
    and search for "Shell Command: Install 'code' command in PATH" (or the
    equivalent for your IDE)
* Check that VS Code has permission to install extensions

### JetBrains plugin not working

* Ensure you're running Claude Code from the project root directory
* Check that the JetBrains plugin is enabled in the IDE settings
* Completely restart the IDE. You may need to do this multiple times
* For JetBrains Remote Development, ensure that the Claude Code plugin is
  installed in the remote host and not locally on the client

## Step 3: Start Using It

- Use Cursor’s chat feature or inline suggestions
- Sonnet 4 should now be available in the model dropdown
- You’ll be billed directly by Anthropic based on usage

**Quick Tip:** Set up billing alerts in your Anthropic console so you can monitor your usage, especially during heavy development.

The exact menu locations might vary slightly depending on your Cursor version, but look for “Models,” “AI,” or “API Keys” in the settings. Most users find it pretty intuitive once they’re in the settings.



## Claude Sonnet 4 LLM Pricing Overview

**API Pricing**

- Input tokens: $3 per million tokens
- Output tokens: $15 per million tokens

These rates apply whether you use Claude Sonnet 4 via the Anthropic API, Amazon Bedrock, or Google Cloud Vertex AI

.

**Cost Reduction Options**

- Batch processing: Up to 50% cost savings
- Prompt caching: Up to 90% cost savings (for repeated prompts)

**Platform Access**

- Claude Sonnet 4 is available for direct chat (web, iOS, Android) and via API for developers

- .

**Subscription Plans (Web & App Access)**

- Free: Limited usage, access to Claude Sonnet 4
- Pro: $20/month (or $17/month with annual billing) for increased usage and features
- Max: From $100/month for much higher usage limits and advanced features
- Team: $25–$30/person/month for collaborative features (minimum 5 users)
- Enterprise: Custom pricing for large organizations

**Example API Cost Calculation**

| Usage Type    | Cost per 1,000 tokens | Cost per 1,000,000 tokens |
| ------------- | --------------------- | ------------------------- |
| Input tokens  | $0.003                | $3                        |
| Output tokens | $0.015                | $15                       |

**Summary**

- For API use, expect to pay $3 per million input tokens and $15 per million output tokens

- Substantial discounts are available for batch processing and prompt caching

- For chat access, subscription plans start at $0 (free tier) and go up based on usage and features

These prices are current as of June 2025 and may vary slightly by platform or region
