NTLM
NTLM (NT LAN Manager) is an authentication protocol developed by Microsoft based on HTTP requests. It uses the NTLMv1 and NTLMv2 algorithms to generate signatures, ensuring the integrity and authenticity of requests, and preventing them from being tampered with or forged. To learn more about NTLM Authentication, please visit the official documentation.



Basic Settings
The basic authentication parameters for NTLM Authentication are as follows:

Username

Used to identify the username for the current request.

Password

Used to identify the password for the current request.

Advanced Settings
You can click the "Advanced" option to add more signature parameters. If left blank, they will be automatically generated.

Domain

Used to identify the domain for the current request.

Workstation

Used to identify the workstation for the current request.



