## Preparation

Before configuring in the Okta admin panel, please open the SAML Single Sign-On page in Apidog organization settings, enable the "Require SAML Authentication" switch, and stay on this page.

![image.png](https://docs.apifox.com/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/485615/image-preview?onlineShareType=apidoc&locale=zh-CN)

## Configure Okta

To configure your SAML application, follow these steps:

- Open the Okta admin panel in your browser.
- Go to **Applications**, click **Create App Integration**.
- Select **SAML** as the sign-in method.
- Enter an App name on the General Settings page, such as Apidog.
- Copy the **Assertion Consumer Service URL** from Apidog and paste it into the **Single sign-on URL** field on the Okta Configure SAML page.
- Copy the **Identifier** from Apidog and paste it into the **Audience URI (SP Entity ID)** field on the Okta Configure SAML page.
- Leave the **Default RelayState** field in the Okta Configure SAML page empty.
  - Select **Persistent** for the **Name ID format** in the Okta Configure SAML page.
  - Select **Okta username** for the **Application username** in the Okta Configure SAML page.
- Keep the remaining configuration items as default and save the Okta configuration.
- On the Sign on page of the Okta application, under the SAML 2.0 option, click **More details**, copy the **Sign on URL**, and paste it into the **Login URL** field in Apidog.
- Copy the **Issuer** from Okta and paste it into the **Issuer** field in Apidog.
- In Okta, click **Download** next to **Signing Certificate**, open it with a code editor like Visual Studio Code, copy the text from the file, and paste it into the **Certificate** field in Apidog.
- Save the Apidog configuration.

## Test Your SAML Configuration

Now you can return to the Apidog main window, click on the organization name in the sidebar, and then click on the single sign-on entry on the right. Please test if you can log in normally.