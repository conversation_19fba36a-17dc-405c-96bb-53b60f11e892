Typically in API design, while the successful `200 OK` responses often differ across various endpoints due to distinct output data needs, the error responses such as `400 Bad Request` and `404 Not Found` tend to be consistent across different endpoints. 

Apidog smartly addresses this commonality with its `Response Component` feature, which allows for the reuse of predefined error responses, making the API documentation process more efficient and the API behavior more consistent.

:::highlight purple
The Response Component feature in <PERSON>pidog is compatible with [the Components in the OAS](https://swagger.io/docs/specification/describing-responses/).
:::

## Add a response component

In the left directory tree of the `APIs` module, you can navigate to the `Components` section, then click on `New Response` under `Responses` to create a new response component.

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341081/image-preview" style="width: 440px" />
</p>

Creating a Response Component is similar to specifying the response section when defining an endpoint in terms of including `HTTP status code`, `Content type`, `Schema`, and `Examples`. For detailed guidance, you can refer to the Response section in [Specify an endpoint](doc-533932).

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341089/image-preview" style="width: 640px" />
</p>

Unique feature of Response Component:
- **Added in new endpoints by default**: When selected as "Yes", this component will be automatically included in all **NEW** endpoints added to the project by default. 

:::highlight purple
Existing endpoints are not affected by this setting.
:::

## Referencing response components
In the `Response` section of an endpoint, you can reference a pre-defined Response Component.

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341094/image-preview" style="width: 640px" />
</p>

- Referenced response components cannot be modified within the endpoint. You must make changes to the original Response Component. Any modifications made will impact all endpoints referencing this component.
- If you wish to modify a Response Component that has been referenced in an endpoint, you can `Dereference` it. Dereferencing will turn the Response into a regular editable Response, and changes to the Response Component will no longer affect it.

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341108/image-preview" style="width: 640px" />
</p>

- In an endpoint, a component can only be referenced once; multiple instances of the same component cannot coexist within the same endpoint.

### Batch operations

You can bulk `Add` the existing Response Components to selected endpoints, or `Remove` this Component in bulk from the selected endpoints.

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341091/image-preview" style="width: 640px" />
</p>

- If the selected endpoint already includes this Response Component, it will not be added again.
- If the selected endpoint does not contain this Response Component, the remove action will not take effect.

## Default response template

Many companies have a standardized structure for their responses. In such cases, you can leverage the Default Response Template to maintain the company's fixed structure as the default response template.

In the left directory tree under the `Components` section, you can access and utilize the `Default Response Template` feature.

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341106/image-preview" style="width: 240px" />
</p>

When a new endpoint is created, the content of this template is used as the initial response.

- Changes made to the default response template will impact only new endpoints, and existing ones will remain unaffected.
- There exists a single default response template, which cannot be added or removed.

The initial default response template is a 200 Success Response with a Content type of JSON and a data structure of an empty Object node.

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341107/image-preview" style="width: 640px" />
</p>

## FAQ

**Q: Can I use a response component as the default response?**

A: No, response components are intended for generic error responses like 400, 404, and similar status codes. If you need to use a fixed default response, please use the default response template.