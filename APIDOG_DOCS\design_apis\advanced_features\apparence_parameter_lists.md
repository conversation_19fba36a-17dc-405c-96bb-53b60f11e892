Apidog supports two different appearances for parameter lists:

- **Modern**: This style presents the properties of each parameter compactly grouped together.

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341152/image-preview" style="width: 640px" />
</p>

- **Classic**: In this style, the properties of each parameter are displayed in a table format.

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341153/image-preview" style="width: 640px" />
</p>

You can configure which appearance to use for parameter lists in Apidog by navigating to `Settings` > `General Settings` > `Feature Settings` > `Endpoint Feature Settings`.

Once set, this preference will be reflected in the APIs module within the Apidog app and in the shared documentation that is distributed externally. 


:::tip[]
If the screen width is insufficient, it will automatically transition to a "modern" style.
:::