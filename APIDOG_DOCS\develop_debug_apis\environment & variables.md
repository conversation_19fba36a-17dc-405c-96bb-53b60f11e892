In Apidog, a variable serves as a dynamic placeholder for values that can be utilized across multiple API requests and scripts. When executing a request or script, <PERSON><PERSON><PERSON> references the current value of the variable. By grouping variables into environments, you can easily adapt your testing setup to different work scenarios.

## Getting Started with Variables
Here's a quick guide to creating and using variables in Apidog:

<Steps>
  <Step>
    Open the default PetStore project, which is pre-installed in your Team space for every user.
  </Step>
  <Step>
    Locate and click the environment icon `≡` at the top right in your interface.
  </Step>
  <Step>
    Find the `Global Variables` section. Create a new variable called `my_variable` with "123" as its initial value.
      
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342742/image-preview)
  </Step>
  <Step>
    Click the `Save` button.
  </Step>
  <Step>
    Open the "Find pet by ID" endpoint, and switch to the `Run` tab.
  </Step>
  <Step>
    locate the path parameter "PetId", and add `{{my_variable}}` as the value.
  </Step>
  <Step>
    Move your cursor over the variable name to view its current value and scope.
      
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342743/image-preview)
  </Step>
  <Step>
    Click the Environments dropdown besides the `≡` icon, and switch to `Local Mock` environment.
  </Step>
  <Step>
    Click `Send` to execute the request.
  </Step>
  <Step>
    You will find the response displayed in the lower half of the interface. By switching to the "Actual Request" tab, you can view the request that was actually sent, with variables replaced by their respective actual values.
      <p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/342744/image-preview" style="width: 640px" />
</p>

  </Step>
</Steps>

:::highlight purple
For a deeper understanding of variables in Apidog, go to [Using variables](apidog://link/pages/577908).
:::

## Create and switch environments

The term "environment" is commonly used in development teams, distinguishing between "development environment," "testing environment," "production environment," and so on. Each environment encompasses one or a set of servers. When the client is in a particular environment, all requests are sent to the servers in that environment. Upon switching environments, requests are directed to a different set of servers.

Learn more about [environments and services](apidog://link/pages/584758) .
In Apidog, variables allow you to save and reuse values easily. Storing a value as a variable enables you to access it across various environments, requests, scripts, and test scenarios. Utilizing variables increases productivity and promotes teamwork among colleagues.

## What are variables

A variable is a symbolic representation of data that allows you to retrieve a value without manually inputting it every time it's needed. This can be beneficial for reusing the same values across different locations. By using variables, your requests become more adaptable and easier to understand by hiding the specific details.

For instance, when you have the same token in multiple requests and anticipate changes to it in the future, you can save the token as a variable named `{{my_token}}` and utilize it in your requests by putting `{{my_token}}` in the request param value. Each time the token changes, the variable value will update it throughout your requests, wherever the variable is used.

<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342746/image-preview)
</Background>

This concept can be applied to any repeated data in your requests. The stored value in a variable will be included wherever the variable is referenced when your requests are executed. Varables could take effect in param values, in bodies, in URLs or in headers.

In Apidog's `Tests` module, variables can be used to [pass data between requests](apidog://link/pages/601617).

## Variable scopes

Apidog offers support for variables in various scopes, allowing you to adjust your processes to different development, testing, and collaboration requirements. The scopes in Apidog correspond to the contexts in which your requests operate, and each scope serves a unique purpose.

Arranged from the widest to the narrowest scope, these include: global, environment, data, and local variables.

- `Global variables`allow data to be shared across requests, scripts, and environments. Their wide scope makes them perfect for testing and initial experimentation. There are two types of global variables:
  - `Global Variables Shared within Project`: These variables are accessible across an entire project and are perfect for sharing data between different endpoints. For example, you can extract a token from a login endpoint and store it as a global variable, which can then be used for authentication in other endpoint requests.
<Background>
![global-variables-shared-within-projects.jpg](https://api.apidog.com/api/v1/projects/544525/resources/349440/image-preview)
</Background>
  - `Global Variables Shared within Team`: These variables can be shared throughout the entire team and are perfect for sharing data between different endpoints across different projects. They are useful for situations where variables like a token must be shared between separate projects, such as using a token from a login project for authentication in a finance project — a typical scenario in microservices architectures. 

<Background>
![global-variables-shared-within-teams.jpg](https://api.apidog.com/api/v1/projects/544525/resources/349420/image-preview)
</Background>

:::note
1. On the environment management page, you can only view and update the current values of existing team global variables. Adding, deleting, or renaming variables, as well as changing their initial values, is not allowed here.
2. When running endpoints, you can [use pre/post processors](https://docs.apidog.com/postman-scripts-reference-593586m0#pmglobals) to extract or set variables as team global variables. However, you can only modify their current values — you cannot change their names or initial values.
3. To add, delete, or manage team variables, you need team admin permissions. These actions can only be performed in the [Team Resources > Variables](apidog://link/pages/760131).
:::

- `Environment variables` allow you to target your work to specific environments, such as local development, testing, or production. When switching environments, the values of the environment variables for the current environment take effect. Only one environment can be active at a time. 

- `Data variables` are sourced from external CSV or JSON files to define datasets in test scenarios or in the Apidog CLI. These variables have transient values that do not persist after a request or collection run.

- `Local variables` are temporary variables confined to a single request or test scenario run and disappear once the run is completed. Local variables are useful for temporarily overriding other variable scopes without maintaining the value after execution.

When a variable with the same name is defined in different scopes, the value from the narrower scope will take precedence. In other words, the priority of variable effectiveness is global< environment < data < local.

For instance, if there is a global variable named "id" and a local variable also named "id", the value of the local variable will be utilized when the request is executed.


:::tip[]
Variables in Apidog are stored as strings. When saving objects or arrays, make sure to use `JSON.stringify()` to convert them to strings before storing, and use `JSON.parse()` when retrieving them.
:::

## Initial and current values

Each variable has an Initial value and Current value.

- **Initial value** represents the value set within the element (environment or globals) where the variable is established. This value is synchronized with Apidog's servers and is shared with your team when you share that specific element. Setting an initial value can be beneficial for collaborating with teammates. However, please be aware that the data within an initial value will be shared with others and potentially be accessible to the public.

- **Current value** is utilized when sending a request. These are local values that are not synchronized with Apidog's servers. Changes made to a current value are not permanently saved in the original shared API documentations, environment, or globals.

<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342761/image-preview)
</Background>

Current value is only saved locally, making it suitable for storing sensitive data such as passwords, tokens, etc. This data is not synchronized with other team members, allowing each individual to utilize their own current value seperately and securely.

If you do not need to use a personal Current Value, you can leave the Current Value empty. In this case, the Current value will follow the Initial value. However, if you input a value in the Current value field, the variable will no longer use the Initial value.

You can also click on the link icon `🔗` next to the Current value to rebind the Current value to the Initial Value.

:::tip[]
- Since the current values are only stored locally, using cleaning software to clean the Apidog file cache will cause the current values to be deleted. So please be cautious.
- When changing devices, the current value does not automatically migrate with your account. You can achieve migration by using the export-import environment feature. 
- If you are using Apidog Web, the current value is stored in your browser data.
:::

### Using variables in Apidog CLI

After setting up your Test Scenario in the Apidog client, you can run this scenario using the Apidog CLI on any machine.

It's important to note that when running in the client, the current value of variables is used, while in the CLI, the initial value of variables is used. If the results of running in the client and the CLI differ, this is often the reason.


:::highlight purple
Learn more about the [Apidog CLI](apidog://link/pages/605134).
:::

## Defining variables

There are several ways to define variables, depending on where your variable values come from. You can preset variable values in the `Environment Management` popup, `extract variables` or perform `database operations` in the pre/post-processors, or set variable values using `custom scripts`.

### Preset variable values in environment management

You can preset values for global variables and environment variables in the "Environment Management" popup by following these steps:

<Steps>
  <Step>
    Click on the "Environment Management" button `≡` in the top right corner of the interface.
  </Step>
  <Step>
    Switch to "Global Variables" or a specific environment.
  </Step>
  <Step>
    Add the variable name,initial value and current value.
  </Step>
  <Step>
    Click `Save`.
  </Step>
</Steps>

:::tip[]
Variables' **Initial Value** can only be set in the environment popup. When using processors and scripts, you can only set the Current value of variables but not the Initial value.
:::

### Add an "Extract Variable" operation

Apidog supports visually extracting values from the API response and saving them as variables. Follow these steps:

<Steps>
  <Step>
    In the Run tab (DESIGN Mode) or Request tab (DEBUG Mode), navigate to Post Processors.
  </Step>
  <Step>
    Hover over "Add PostProcessor" and select "Extract Variable".
  ![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342755/image-preview)
  </Step>
  <Step>
   Enter the Variable Name and choose the Variable Scope.
Select the extraction source, such as Response JSON, Response XML, or Response Text, etc.
  </Step>
  <Step>
If the response is in JSON/XML format, you can utilize JSONPath/XPath syntax to parse a specific part of the Response JSON/XML and save it as the value of the variable.
      
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342756/image-preview)
  </Step>
  <Step>
After clicking "Send" to send the request, the variable extraction will be executed, and you can view the logs in the Console.

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342758/image-preview)
  </Step>
</Steps>

:::highlight purple 
Learn more about Extract Variable<Icon icon="material-outline-build"/>
:::

### Setting variables in scripts

You can easily set variables in your pre or post operation scripts using the`set`method. Below is an example:

```
pm.environment.set('variable_key', 'variable_value');
```

**Syntax for`set`:**

1. Environment Variables

```
// Set an environment variable  
pm.environment.set('variable_key', 'variable_value');  

// Unset an environment variable  
pm.environment.unset('variable_key');
```
Environment variables can hold different types of data, such as arrays, objects, and strings. For non-string data like objects or arrays, you need to convert them to a string using`JSON.stringify` before storing them:
```
var array = [1, 2, 3, 4];  
pm.environment.set('array', JSON.stringify(array));  

var obj = { a: [1, 2, 3, 4], b: { c: 'val' } };  
pm.environment.set('obj', JSON.stringify(obj));  
```
To read the stored data, use`JSON.parse`to convert it back:

```
try {  
  var array = JSON.parse(pm.environment.get('array'));  
  var obj = JSON.parse(pm.environment.get('obj'));  
} catch (e) {  
  // Handle exception  
}
```

2. Global Variables

- Global variables shared within the project:

```
// Set a global variable  
pm.globals.set('variable_key', 'variable_value');  

// Unset a global variable  
pm.globals.unset('variable_key');  
```

- Global variables shared within the team:

```
// Set a team-shared global variable  
pm.globals.set('variable_key', 'variable_value', 'TEAM');  

// Unset a team-shared global variable  
pm.globals.unset('variable_key', 'TEAM');  
```

3. Local variables

```
// Set a local variable  
pm.variables.set('variable_key', 'variable_value');  

// Unset a local variable  
pm.variables.unset('variable_key');  
```

[Learn more about scrip syntax.](apidog://link/pages/593586)

### Fetching variable values from database

Apidog offers a unique feature: connecting to a database to retrieve data, setting it as a variable, and using it in requests. Here are the steps:

<Steps>
  <Step>
    In the **Run** tab (DESIGN Mode) or **Request** tab (DEBUG Mode), navigate to Post Processors.
  </Step>
  <Step>
    Hover over "Add PostProcessor" and select "Database operation".
      
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342780/image-preview)
  </Step>
  <Step>
   Name the database operation and set up the database connection. Learn more about Database connections.

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342782/image-preview)
  </Step>
  <Step>
Enter the SQL command. The command supports using `{{variables}}` within it.

  </Step>
  <Step>
Set "Extract Result To Variable". JSONPath is supported.
  </Step>
  <Step>
Click Send to execute the request. You can view the database operation's results in the Console.

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342783/image-preview)
  </Step>
</Steps>


:::highlight purple
Learn more about [Database operations](apidog://link/pages/588469).
:::

## Using variables

In Apidog, you can use double curly braces to reference variables across your project. For example, to refer to a variable named "username" in your request authorization settings, enclose the variable name within double curly braces like: `{{my_variable}}`.

When you run an endpoint, Apidog will resolve the variable and replace it with its current value.

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342785/image-preview)

For instance, in a request URL where a variable is referenced, such as: 

```json
http://127.0.0.1/pet/findByStatus?status={{CurrentStatus}}
```

Apidog will use the stored value of the `CurrentStatus` variable when running the request. If the value of `CurrentStatus` is "available", the request will be sent to the URL including the query parameter: 
```json
http://127.0.0.1/pet/findByStatus?status=available
```

You can view the assembled request in the "Actual request" tab.

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/342786/image-preview" style="width: 640px" />
</p>

When referencing a variable within a request body, enclose the reference in double quotes, like: 

```json
{ 
    "status" : "{{CurrentStatus}}"
    "quantity" : {{TotalPet}}
}
```

:::tip[]
- Double quotes are desired when using variables for string type in json format. Do not add double quotes for any other types, as shown in the example above.

- Double curly braces may sometimes trigger incorrect JSON format warnings. You can ignore these warnings.
:::

Variables can be used in request URLs, parameters, headers, authorization settings, request bodies, and header presets.

Sometimes, you may encounter an "Unresolved variable" notification, indicating that the variable is not defined in the environment or global variables. However, this may not necessarily be a problem. If you are extracting variables in a post-processor or using a script to set variables, the values may not be obtained before running the request. If they are local variables, they might expire after execution, which would also result in the variable value being unavailable. Therefore, you can first send the request to verify if there is an issue.

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/342787/image-preview" style="width: 340px" />
</p>

### Accessing subelement values of variables

If the value of the variable is in the form of an object or an array, the value of the attribute in the variable can be read by `{{variableName.attributeName}}` or `{{variableName[0].attributeName}}`. For example:

1. The object variable user is as follows:

```js
{
  "id": 1,
  "name": "jack"
}
```

- You can reference the name property of the user object as `{{user.name}}` in the API parameter.
- You can reference the name property in the user object as `pm.variables.get("user.name")` in customized scripts.

2. The array variable user is as follows:

```js
[
  {
    id: 1,
    name: "jack",
  },
];
```

- You can reference the name property of the first element in the user array as `{{user[0].name}}` in the API parameter.
- You can reference the name property in the first element of the user array as `pm.variables.get("user[0].name")` in customized scripts.

Reading the value of a property in a variable (object or array) as `{{user.name}}` follows the JSONPath syntax specification. You can replace the `\$` symbol in the JSONPath with the variable name.

View details of [JSONPath](apidog://link/pages/645606).

### Using variables in scripts

When using variables in scripts, you cannot directly use the syntax `{{variable}}`. Instead, you need to first assign the variable value to a variable using the `get` method. Here is an example of how to do this:

```js
var name = pm.environment.get("variable");
```

Here are more syntax about `get`:

1. Environment Variables

```
// Fetch an environment variables
var variable_key = pm.environment.get('variable_key');

```

Environment variables can store data in various formats, such as arrays, objects, and strings. For non-string data like objects or arrays, you need to use`JSON.stringify`to store them properly:

```
// Store an array  
var array = [1, 2, 3, 4];  
pm.environment.set('array', JSON.stringify(array));  

// Store an object  
var obj = { a: [1, 2, 3, 4], b: { c: 'val' } };  
pm.environment.set('obj', JSON.stringify(obj));  
```
When fetching such data, you must use`JSON.parse`to convert it back to its original format:

```
try {  
  var array = JSON.parse(pm.environment.get('array'));  
  var obj = JSON.parse(pm.environment.get('obj'));  
} catch (e) {  
  // Handle exceptions  
}
```
2. Global Variables

- Global variables shared within the project:

```
// fethch global variables shared within project
var variable_key = pm.globals.get('variable_key');
```

- Global variables shared within the team:

```
// fethch global variables shared within team
var variable_key = pm.globals.get('variable_key', 'TEAM');
```

3. Local Variables

```
// fethch local variables
var variable_key = pm.variables.get('variable_key');
```

### Logging variables

You can log variable values to the Apidog Console during the execution of your requests.

To log the value of a variable in your script, utilize the following syntax:

```js
console.log(pm.variables.get("variable_key"));
```

To view the logged results, click on the Console tab in Response section.

### Using data variables

In Apidog, if you have multiple sets of data that need to be sent as request parameters, you can utilize the data variable feature. In the Test Scenario, you can import data in CSV or JSON format. Subsequently, you can reference these data variables using `{{variable_name}}`, where the variable name corresponds to the column name in the CSV.

When selecting this set of test data in the Test Scenario, the values of `{{variables}}` will be replaced with the actual data during runtime. Each row of data corresponds to one execution, and in the test report, you can view the requests and responses for each run.

:::highlight purple 
For further information, refer to the guide on [data-driven testing](apidog://link/pages/602987).
:::

## FAQ

**Q: Can I reference variables in mocks? In what scenarios can I use variables?**

A: Variables only become real values when the request is sent. This means that variables can be used in some scenarios, but not in others.

Scenarios where you can use variables:

- **Requests**: You can directly use `{{variable}}` in places like request parameters, body, path, and authentication.
- **Pre- and post-scripts**: In these scripts, you can use `pm.environment.get("variablename")` or similar statements to reference variables, but cannot use `{{variable}}` directly.
- **Test scenarios**: Requests in test scenarios would be sent so they are the same with requests and scripts.

Scenarios where you cannot use variables:

- **API specification**: Default values for requests, default values and mocks for responses, cannot use variables.
- **Mocks**: Advanced mocks and mock scripts do not support the use of variables.

Important note:

- Do not set a variable's value to be another variable. That can lead to issues with properly resolving the variable values.

The key point is that variables are only resolved when the actual request is made. They cannot be used in static parts of the API definition or in mocking scenarios, as those do not involve the request being sent.

When making API requests, it is often necessary to switch between development, testing, and production environments. Apidog makes it convenient to send requests to different environments - you simply need to click and select the desired environment at the top right corner of the interface.

An Apidog environment consists of two core elements. The first is the `service (base URL)`, used to maintain the target of request delivery. The second element comprises a group of one or more `variables` that can be referenced in requests or scripts. When switching between environments, both the service (base URL) and environment variables will use the values defined in the current environment.

## Create an environment

<Steps>
  <Step>
    Click on the "Environment Management" button `≡` in the top right corner of the interface.
  </Step>
  <Step>
    Click on the last item in the left-side list labeled `New Environment`.
  </Step>
  <Step>
    Enter a name for your new environment. Add Service base URL and variables.

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342798/image-preview)
  </Step>
  <Step>
    Click `Save`.
  </Step>
  <Step>
To use the new environment, select it from the environment selector at the top right of the interface. This makes it the active environment and sets all variables to the values specified in the environment.
  </Step>
</Steps>

:::tip[]
Environments in Apidog are distinct from those in Postman, where there is a tendency to associate each Base URL with a separate environment. This blurs the lines between environments and base URLs. 

In contrast, Apidog's environments directly reflect real environments like development, testing, and production, rather than treating each base URL as one single environment.

For example, within a testing environment, there may be various services such as user service and order service all contained within the same testing environment. It is important that these services are not divided into separate user and order environments.
:::



## Services (base URLs)

The Service (base URL) is the primary feature in an Apidog environment. In Apidog, an endpoint starts with a forward slash (/) and does not include the base URL; when sending an endpoint request, it is necessary to specify the destination base URL. This underlines the importance of the Service feature in the environment—selecting a particular environment is a prerequisite for sending an endpoint request.

A standardized base URL format begins with the protocol and **without** trailing slash (/), like

```
https://127.0.0.1
```
or
```
http://abc.com/v1
```

Every environment contains a **default server**; when an endpoint does not specify a specific service, the request is directed to the default server.

For instance, if the **default server** base URL for your production environment is `http://abc.com/v1` and your endpoint path is `/pet`, then when you send a request in the production environment, the actual request sent would be

```
http://abc.com/v1/pet
```


:::tip[]
If the endpoint path begins with http:// or https://, the base URL will not be appended to the request. Nonetheless, this practice is generally discouraged.
:::

:::tip[BASE_URL variable]

In Apidog, there is a special environment variable called BASE_URL, which stores the base URL for the "Default Server" of the current environment. 

It is generally discouraged to use this variable. For custom scripts, it is recommended to use `pm.request.getBaseUrl()` to fetch the base URL for the present endpoint. Avoid using `pm.environment.get('BASE_URL')`, as it may not correctly capture the base URL for the current endpoint if the endpoint does not employ the "Default Server."

If the user manually creates an environment variable labeled BASE_URL, it will supersede the system's predefined BASE_URL. 

Scripts are unable to modify the base URL. The command `pm.environment.set('BASE_URL', 'My_url')` within a script will establish a genuine environment variable called BASE_URL instead of altering the base URL itself.
:::

### Utilize multiple services

If your project involves multiple distinct services — for example, directing user-related requests to https://user.abc.com and order-related requests to https://order.abc.com - then you need to utilize multiple services. Here is the specific process:

<Steps>
  <Step>
    Add multiple services and their respective base URLs to the environment.
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342793/image-preview)
  </Step>
  <Step>
Assign the endpoint to the designated service.
In DESIGN mode, select the service from the Service dropdown in the `Edit` tab.
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342794/image-preview)
In DEBUG mode, select the service from the Service dropdown in the `Info` tab.

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342795/image-preview)
  </Step>
  <Step>
Services can also be added by folder. Click on the folder name, and in the Folder settings, select the service from the Service dropdown. Then all endpoints under this folder will be directed to this service.
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/342796/image-preview" style="width: 540px" />
</p>
    </Step>
  <Step>
By selecting the environment at the top right corner of the interface and clicking send, the endpoint bound to the service will be directed to the corresponding base URL.
  </Step>
  <Step>
When you switch to different environments in the environment management section, you may notice that the User service and Order service are also added to other environments but with empty base URLs. 

      ![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342797/image-preview)
      
This is because in most teams, both test and develop environments have corresponding services. By setting the respective base URLs for each environment, when you switch environments at the top right corner, all endpoint requests will be directed to the correct services. In practice, this is the most efficient design approach.

  </Step>
</Steps>

## Add environment variables

When you add a variable to an environment, you can specify the Initial value (shared) and the Current value (local) for the variable. 

:::highlight purple
Learn more about [Using variables](doc-577908).
:::

## Switch between environments

Apidog shows the current environment in the environment selector at the top right of the workbench. Whenever you make a request or execute a script, Apidog will use the current values for all variables in the selected environment. 

To switch to a different environment, simply choose it from the environment selector.

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/342800/image-preview" style="width: 340px" />
</p>

:::tip[]
In Apidog, endpoints and requests are two distinct concepts. An endpoint represents the API specification and does not include the base URL, whereas a request is the concrete API request, which includes the base URL. Therefore, the services defined in an Apidog environment only apply to endpoints, not to requests.

When using the requests functionality, you can leverage the environment variables in a similar way to Postman, by using the {{Base_url}} syntax.
:::

## Environment Migration

In Apidog, the Initial value of variables is synchronized within the team, while the Current value is only stored locally. This means that when you use Apidog on another computer, you won't have access to the previously used Current values.

Therefore, Apidog provides the functionality to migrate environments. You can export the services and variables in environments as a JSON file and then import it on another computer. Here are the steps:
<Steps>
  <Step>
    In the environment management, hover over the `...` next to the Environments list, click Export to obtain a JSON file.
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/342803/image-preview" style="width: 540px" />
</p>
  </Step>
  <Step>
On another computer, open environment management, hover over the `...` next to the Environments list, and click Import. Select the JSON file to import.
  </Step>
  <Step>
You can also import environments exported from Postman.
    </Step>
</Steps>

## Visibility Scope of Environments

Sometimes, you may need to create variables for personal use only and prefer they are not visible to others. In such cases, you can create a **Private Environment**.

In the top right corner of the environment, you can set the visibility scope of the environment. By default, it is shared, while you can make environments you create visible only to yourself. However, you cannot change the visibility of environments created by others.

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/342804/image-preview" style="width: 340px" />
</p>

:::tip[]
Private Environments share the same Service list as other environments. Adding or removing services in a Private Environment will affect all environments simultaneously.
:::

