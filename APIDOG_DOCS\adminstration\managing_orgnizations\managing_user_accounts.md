Managing user accounts
SSO is available on Apidog Enterprise plan.

Joining organizations via SSO
When a new user successfully logs in to Apidog via SSO, if there are available seats within the organization, the new user will automatically join the corresponding organization.

There are two ways to log in via SSO:

Access the organization's SAML authentication page directly through a browser. The format of this URL is https://apidog.com/orgs/{org-name}/sso, please replace "{org-name}" with your real organization's name.

If the organization's owner has configured the allowed email domains, users can click "Sign in with <PERSON><PERSON>" on the login page and enter their work email to access to the SAML authentication page.



When users join an organization automatically through SSO login, they are granted organization member permissions by default but are not assigned with any teams within the organization. The organization owner needs to manually assign these members to the corresponding teams to grant them access to projects.

Adding existing user accounts
If someone already has an Apidog account, the organization owner can invite them to join a team within the organization. Once they accept the invitation, they become members of the organization and are assigned to the corresponding team.



Managing organization members
Organization owners can view the profiles of organization members, including their linked SSO identities, and can also assign organization members to teams. Organization members can only access projects within teams they are assigned to.



