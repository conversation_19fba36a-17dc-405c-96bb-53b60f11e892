Create and use AI tools in Postman
Postman supports AI-driven development with AI requests in collections and AI Request blocks in Postman Flows. You can use AI requests and AI Request blocks to explore model behavior and add AI-assisted logic to flows.

With the Postman AI Tool Builder, you can also generate MCP servers that expose AI tools from public APIs in the Postman API Network. Each MCP server exposes API requests as tools that language models and other MCP-compatible clients can call. You can customize the server code, run it locally, and iterate on tool behavior from within your Postman workflow.

Postman AI Tool Builder
Create AI requests and add them to your collections
You can create AI requests to interact with AI models. You can then prompt the model, review the response details, and manage your conversations with them. In addition, you can edit your AI request settings to change how you interact with the model.

To learn more, see Add your AI requests to your collections.

Create AI Request blocks and add them to your flows
With the Postman AI Tool Builder and Postman Flows, you can use a visual, low-code editor to build API-first AI agents on an infinite, drag-and-drop canvas.

To learn more, see Add your AI request blocks to your flows.

Add your AI requests to your collections
You create AI requests to interact with an AI model of your choice, such as one from OpenAI, Google, or Anthropic. You can then manage your conversations with them, and edit the settings that influence how you interact with the model.

AI requests can help you evaluate different AI models, add AI to your workflows, prototype agents, and much more.

Create your AI requests
Create AI requests to interact with AI models. You can then add them to your collections and organize them in your workspace for your project.

To learn more, see Create an AI request for your collection.

Use your AI requests to interact with AI models
Use prompts to interact with AI models. As you interact with the model, it returns a response. You can then manage your conversations and create new ones.

To learn more, see Use your AI request to interact with an AI model.

Create an AI request for your collection
You use AI requests to interact with AI models, which can help you evaluate different models, add AI to your workflows, prototype AI agents, and much more.

Create a new AI request
To create your new AI request, do the following:

Choose an existing workspace or create a new one.
Select New > Magic icon AI. Postman opens a new AI request in a new tab.
Select the request URL box. Postman lists the available models.
Select the Authorization tab to view the Auth Type that Postman has selected for you.
(Optional) Select a different Auth Type.
Edit your authorization details. To learn more, see Add API authorization details to requests in Postman.
Select the Prompt tab and enter your user prompt.
(Optional) Enter your system prompt.
Select Send.
If your request succeeds, Postman adds your Message (user prompt) and the Response to the open conversation. You can use the steps in the following section to save your new AI request to a collection.

Save your new AI request
If you don't have any multi-protocol collections in your workspace, Postman will open a save request dialog that asks you for a request and collection name. Otherwise, Postman displays more options, such as the option to add your new AI request to an existing collection.

To save your new AI request, do the following:

Select Save icon Save. Postman opens a save request dialog.
Name your new AI request.
If you don't have any multi-protocol collections in your workspace, name your collection and skip to the last step.
(Optional) Select Add description and describe your new AI request.
To save your new request to an existing collection, select a collection. To save your request to an existing folder, select a folder. If you want to search for a collection or folder, enter your search term in the search box.
If you want to create a new collection, select New Collection, name your collection, and select Create. If you want to create a new folder, select New Folder, name your folder, and select Create.
Select Save.
Your new AI request is ready to use to interact with an AI model. You can also change your request settings.

Use your AI request to interact with an AI model
You interact with an AI model with two kinds of prompts: a user prompt and a system prompt.

A user prompt is an individual request made within the context of an ongoing conversation, such as a question or instruction. For example, "When did Postman's founders found the company" or "List Postman's best collaboration features".

A system prompt sets the parameters and overall context for the entire conversation. For example, "Respond like you're Cooper the dog—Postman's official mascot. You're friendly and helpful."

As you interact with the AI model, it returns a response. Postman adds your prompt and its response to your conversation.

Interact with the AI model
To interact with the AI model, do the following:

Choose an existing AI request or create a new one.
Enter your user prompt.
(Optional) Enter or edit your system prompt.
Select Send.
Repeat these steps to add to your conversation.
Postman adds your Message (user prompt) and the Response to the open conversation. In the conversation, you can view the user prompt and response details for each interaction.

Each response includes a response code, response time (in milliseconds), response payload size (in kilobytes), token count, and timestamp.

To view the user prompt details for an individual interaction, select Down Large icon next to your user prompt. Depending on the conversation and interaction, you may see the System Prompt, Context (such as past interactions), and Settings (which you can edit for future interactions).

Manage your conversations
You can rename and delete your conversations, and create new ones.

Important: Conversations aren't saved and won't be available after you close an AI request. If you want to preserve your conversations, keep the AI request open.

Rename your conversation
To rename your conversation, do the following:

If you have more than one conversation, and the conversation you want to rename isn't open, select History icon History next to your conversation's name and select the conversation you want to rename.
Select your conversation's name and rename your conversation.
Select the Return or Enter key.
Delete your conversation
To delete your conversation, do the following:

Select History icon History next to your conversation's name.
Hover over your conversation's name.
Select Options icon and then select Delete.
Create a new conversation
To create a new conversation, do the following.

Select History icon History next to your conversation's name.
Select Add icon.
(Optional) Rename your conversation.

Manage your AI request settings
You can edit your AI request authorization details and adjust the settings that influence how you interact with the model.

Edit your request authorization details
To edit your request authorization details, do the following:

Choose an existing AI request or create a new one.
Select the Authorization tab.
Select an Auth Type.
Edit your authorization details.
To learn more, see Add API authorization details to requests in Postman.

Adjust your request settings
Your AI request settings are specific to the AI model and the AI model provider, though some settings (such as temperature) are common across many models.

To better understand each setting, read the setting's description or visit the AI model provider's website for more information.

To adjust your request settings, do the following:

Choose an existing AI request or create a new one.
Select the Settings tab.
Adjust your request settings.

Add your AI request blocks to your flows
You create AI request blocks to interact with an AI model of your choice, such as one from OpenAI, Google, or Anthropic. You can then manage how you interact with the model.

AI request blocks can help you evaluate different AI models, add AI to your workflows, prototype agents, and much more.

Create your AI request blocks
Create AI request blocks to interact with AI models. You can then add them to your flows and organize them in your workspace for your project.

To learn more, see Create an AI request block for your flow.

Use your AI request blocks to interact with AI models
Use prompts to interact with AI models. As you interact with the model, it returns a response. You can then manage how you interact with the model.

To learn more, see Use your AI request block to interact with an AI model.

Create an AI request block for your flow
You use AI Request blocks to interact with AI models, which can help you evaluate different models, add AI to your workflows, prototype AI agents, and much more. You can add AI Request blocks to flow modules and actions.

Create a new flow module
You can use an existing flow module or create a new one. If you choose to use an existing flow module, skip to Add a new AI request block.

To create a new flow module, do the following:

Choose an existing workspace or create a new one.
From the Postman sidebar, select Flow icon Flows.
Select Add icon Create a new folder or flow > Add-On Lego icon Create flow module.
In the sidebar, hover over your new flow module and select Options icon View more actions > Rename.
Rename your new flow module.
Select the Return or Enter key.
Add a new AI request block
To add a new AI request block, do the following:

From the canvas toolbar, select Add icon Block.
Select Magic icon AI Request. You can also search for the block by entering "AI Request" in the search box.
Decide where on the canvas you want to place your new block and select that location.
Set up your new AI request block
To set up your new AI request block, do the following:

Select Find or create new request.
Select an AI request. If you want to search for the request, enter your search term in the search box.
If you want to create a new AI request, select Add icon Create a new request and create a new AI request.
Run the flow module
From the canvas toolbar, select Run icon Run.

Use your AI request block to interact with an AI model
When you create an AI request block, you select an AI request for it. You can use the AI request to control how your AI request block interacts with the model in your flow.

Interact with the AI model
To interact with the AI model, do the following:

Choose an existing AI request block or create a new one.
Select Open web icon next to your AI request's name. Postman opens the AI request in a new tab.
Enter your user prompt.
(Optional) Enter or edit your system prompt.
Select Save.
Your AI request block uses your last saved user and system prompts to interact with the model. To learn more, see Use your AI request to interact with an AI model.

Manage your AI request settings
To manage your AI request settings, do the following:

Choose an existing AI request block or create a new one.
Select Open web icon next to your AI request's name. Postman opens the AI request in a new tab.
To edit your request authorization details, select the Authorization tab and edit your request authorization details.
To adjust your request settings, select the Settings tab and adjust your request settings.


The AI Request block
AI Request block
The AI Request block runs an AI request that you select from a collection and sends the result from its output port.

To learn more about AI requests and AI request blocks, see Build AI agents with the Postman AI Agent Builder.

Input
Send - If you connect another block to this input port, that block triggers the AI Request block to run. This connection is optional. The AI Request block will run when the flow runs, even if this port isn't connected to another block.

Outputs
Success - Sends the response of a successful API request. A successful request has a 2xx HTTP status code.

Fail - Sends the response of an unsuccessful API request. An unsuccessful request has an HTTP status code other than 2xx.

Setup
Select a request - Select this dropdown list to see all the collections in your workspace. Select a collection, then select an AI Request.

Variables - If the selected request has one or more variables, they'll be listed here. You can assign a value to a variable by connecting another block's output port to the variable's input port, or by inserting a data block with Add icon Add data blocks. If your request uses environment variables, you can select the environment from the Add environment dropdown list.

Specify how to parse the response
The AI Request block can parse its response as JSON, XML, HTML, or text.

To choose how to parse the response, do the following:

Select the AI Request block, then select Additional Settings Additional Settings.

Select Parse Response Parse Response.

Select a format from the dropdown list. You can also select Auto to let the AI Request block decide how to parse the response. The default setting is Auto.

Related blocks
The Select and Evaluate blocks are often connected to the AI Request block. The Select block is useful for extracting specific information from a response. The Evaluate block is useful for transforming response data and creating conditions to route data in your flow based on a response.

Select
Evaluate


The HTTP Request block
HTTP Request block
The HTTP Request block runs an HTTP request that you select from a collection and sends the result from its output port.

Input
Send - If you connect another block to this input port, that block triggers the HTTP Request block to run. This connection is optional. The HTTP Request block will run when the flow runs, even if this port isn't connected to another block.

Outputs
Success - Sends the response of a successful API request. To succeed, a request must return a 2xx HTTP status code and pass all of its tests.

Fail - Sends the response of an unsuccessful API request. A request will fail if it returns an HTTP status code other than 2xx or if any of its tests fail. If your request is returning a 2xx HTTP status code but failing a test, you might want to disregard the test temporarily. In this case, you can connect a block to the Fail output and that block will receive the data returned by the request.

Setup
Find or create new request - Select this dropdown list to see all the collections in your workspace. Select a collection, then select a request or Create a new request. If there are no collections in the workspace, some introductory requests will appear that you can choose from to get started.

Variables - If the selected request has one or more variables, they'll be listed here. You can assign a value to a variable by connecting another block's output port to the variable's input port. You can also assign a value to a variable by inserting a data block with Add icon Add data blocks. If your request uses environment variables, you can select the environment from the Add environment dropdown list.

Create a new request
To create new and edit existing requests, you can use the Postman Flows request editor that's built into the HTTP Request block. To create a request in the HTTP Request block, do the following:

Select Find or create new request. The collections in your workspace appear in a dropdown list.

Select a collection and select Create a new request. If you don't have any collections, select Create a new request. The Postman Flows request editor opens.

Postman Flows request editor
In the request editor, select a method from the dropdown list and enter a URL. Alternatively, you can copy a cURL command from your terminal and paste it here. Besides the URL, this add the parameters, headers, and body specified in your cURL command.

(Optional) Select and edit the request's name.

(Optional) Select and edit the request's parameters, headers, and body.

Select Save.

Specify how to parse the response
The HTTP Request block can parse its response as JSON, XML, HTML, or text.

To choose how to parse the response, do the following:

Select the HTTP Request block, then select Additional Settings Additional Settings.

Select Parse Response Parse Response.

Select a format from the dropdown list. You can also select Auto to let the HTTP Request block decide how to parse the response. The default setting is Auto.