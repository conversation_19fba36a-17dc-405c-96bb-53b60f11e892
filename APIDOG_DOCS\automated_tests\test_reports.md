After the automated testing run is completed, a test report will be output, where you can see which requests have not passed the test.

Click on the "Test Report" tab to view the historical test reports for the current test scenario. To help testers quickly distinguish, the functional test report is marked with a branch icon <Icon icon="ph-bold-git-branch"/> on the left, and the performance test report is marked with a dashboard icon <Icon icon="undefined-dashboard-3-line"/>. You can also click the filter dropdown to quickly categorize.

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/343210/image-preview)

## View functional test report details

Click "More" to view the execution result of each request.


![image.png](https://api.apidog.com/api/v1/projects/544525/resources/343218/image-preview)

### Debug it

In the details of each request, you can see the request and response of this request. You can also click "Debug it" to resend this request.

Note: When using "Debug it", if you used variable values generated by the prerequisite steps, they cannot be retrieved. You can only use the current value of the variable.

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/343215/image-preview)

## View performance test report

The performance test report records various key performance test indicators during the test process, including the total number of requests, the number of requests per second, the average response time, the maximum/minimum response time, and the request failure rate.

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/343220/image-preview)

Click on the "Error" to check the failed requests and analyze the possible error causes. 

<p style="text-align:center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/343221/image-preview" style="width:640px" />
</p>

You can also filter the requests in the filter bar.

<p style="text-align:center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/343222/image-preview" style="width:640px" />
</p>

In performance testing, a large number of requests are issued, so only error requests are classified and displayed, and no detailed error information or individual request details are recorded. If you find unexpected errors, please first run the "Functional Test" and solve all the problems before running the "Performance Test".

## Export and share reports

The test report supports export in HTML format. After the test task is completed, click the "Export Report" button.

:::tip[]
Performance test report cannot be exported.
:::

<p style="text-align:center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/343223/image-preview" style="width:340px" />
</p>

The test report supports sharing. After entering a test report, click the "Share" button on the right to generate a link and send it to others.

If you can access the request/response details of this report, you can check the "Also share request/response details" option to share all the request/return details in this test report. Other users can directly see the details of the test report after opening the link, which improves the efficiency of team collaboration.

After clicking to share the test report, the local test report data will be uploaded to the cloud.

<p style="text-align:center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/343224/image-preview" style="width:640px" />
</p>

Other team members can view all the test reports that have been shared in the "Shared" page in "Test Report".

If the "Also share request/response details" button is grayed out, it means that the details of this test report cannot be shared with other project members.

The reason why the details cannot be shared may be:

- The details were not saved before running and generating the test report.
- The current test report was generated by other project members.
