

### 📚 **Knowledge Base / Documentation Platforms**

> Tools primarily used to create, manage, and share internal/external knowledge bases.

- **Confluence**

- **Document360**

- **BookStack**

- **GitBook**

- **Helpjuice**

- **HelpDocs**

- **Heroic Knowledge Base**

- **KnowAll**

- **Notion**

- **Nuclino**

- **Papyrs**

- **ProProfs**

- **ReadMe**

- **Tettra**

- **Whatfix**

- **Zendesk**

- **Wiki.js**

- **ClickHelp**

- **Atera**

- **Freshservice**

- **IT Glue**

- **N‑able Passportal**

- **SuperOps**

---

### ⚙️ **Static Site Generators / Docs Site Builders**

> Tools for developers to generate fast, static documentation websites from markdown or other content.

- **Docusaurus**

- **MkDocs**

- **MkDocs Material**

- **Sphinx**

- **Read the Docs**

- **Docsify**

- **GitHub Wiki**

- **Jekyll**

- **Hugo**

- **Slate**

- **AsciiDoc / Asciidoctor**

- **Nuxt Content**

---

### 🧪 **API Documentation & Developer Portals**

> Platforms tailored for documenting APIs and developer interfaces.

- **Apidog**

- **Apiary**

- **GitHub**

- **Read the Docs**

- **GitBook**

- **Doxygen**

- **Swagger / OpenAPI-compatible tools (used by many above)**

---

### ✍️ **Markdown Editors / Writing Tools**

> Lightweight tools for writing content in markdown or plain text.

- **iA Writer**

- **Typora**

- **MarkdownPad**

- **SimpleMDE**

---

### 🧰 **Interactive Guide / Process Documentation Tools**

> Tools to create step-by-step guides, onboarding walkthroughs, or tutorials from recorded workflows.

- **Tango**

- **Scribe**

- **iorad**

- **FlowShare**

- **Stepsy**

- **Screensteps**

- **Snagit**

- **Greenshot**

- **FastStone Capture**

- **Folge**

- **Whale**

---

### 🛠️ **Advanced Documentation / Publishing Tools**

> More robust desktop-based tools for creating detailed, print-ready, or enterprise-grade help content.

- **Adobe RoboHelp**

- **MadCap Flare**

- **Adobe FrameMaker**
