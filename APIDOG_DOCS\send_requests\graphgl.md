GraphQL
GraphQL is a query language for endpoints and a server-side runtime for executing queries using a type system you define for your data. GraphQL is not tied to any specific database or storage engine and works with your existing code and data.

Creating a New GraphQL Request
To create a new GraphQL request in a project, click on "Body" → "GraphQL" in sequence.


Requesting GraphQL
Enter your query in the Query box on the "Run" tab. You can also click the manual Fetch Schema button in the input box to enable the "code completion" feature for Query expressions, assisting in entering Query statements.


Query statements support using GraphQL variables for requesting. For specific usage, please refer to the GraphQL syntax.


