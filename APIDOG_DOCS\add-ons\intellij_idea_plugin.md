Apidog Fast Request is a plugin launched by Apidog for the IntelliJ IDEA.

It automatically detects and analyzes endpoints in Java/Kotlin projects, allows you to send requests with just one click, generates OpenAPI specs without needing Swagger annotations, and publishes Stripe-like API documentation—all within IntelliJ IDEA. Simplify your API development process today!

<Video src="https://www.youtube.com/watch?v=jcrYqkPjJQs&t=2s"></Video>

## Installation

Search for "Apidog Fast Request" in the plugin Marketplace of IntelliJ IDEA and install it.

<Background>
![SCR-20250116-nmqr.png](https://api.apidog.com/api/v1/projects/544525/resources/349578/image-preview)
</Background>

:::tip[]
The plugin supports IntelliJ IDEA 2019.3 and higher versions.
:::

<Embed src="https://plugins.jetbrains.com/embeddable/install/25925" width="245px" height="48px"/>

## Features

- [Send requests](https://docs.fastrequest.apidog.com/send-requests-808605m0)
- [Generate OpenAPI Specs](https://docs.fastrequest.apidog.com/generate-openapi-specs-808606m0)
- [Export OpenAPI(Swagger) files](https://docs.fastrequest.apidog.com/export-openapiswagger-files-808608m0)
- [Publish API documentations](https://docs.fastrequest.apidog.com/publish-api-documentations-808613m0)

## Privacy and Security Statement

The Apidog Fast Request plugin only runs the detection of Java and Kotlin source code on your local machine. At Apidog, we prioritize the security of your data assets, ensuring that your Java and Kotlin source code will never be uploaded to the Apidog server.

## Contact Us

If you encounter any issues or have feedback while using the Apidog Fast Request plugin, please reach us at [<EMAIL>](mailto:<EMAIL>), join our [Discord community](https://discord.gg/fGCT7f5ndd) or [Slack community](https://join.slack.com/t/apidogfastrequest/shared_invite/zt-2wx3sf6qq-oQHC3A3~oK~kVfluhkXqWA).
