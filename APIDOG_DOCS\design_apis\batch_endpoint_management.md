
The batch endpoint management feature offers a streamlined way to manage multiple endpoints simultaneously, saving time and enhancing efficiency.

On the `APIs` module, you can access all endpoints within a particular directory and execute batch operations, such as `bulk deletion` and `bulk movement`.

## Browse endpoints

To view all endpoints within the current project, click the `Endpoints` tab located under the `Root Directory`. To explore endpoints under a specific directory, select the respective directory.

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/341162/image-preview)

You can filter and sort the data table to suit your needs. It’s possible to apply multiple sorting criteria.

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/341164/image-preview)

The Endpoints tab features a search function that allows for locating endpoints by name or path.

![CleanShot 2024-05-29 at <EMAIL>](https://api.apidog.com/api/v1/projects/544525/resources/341165/image-preview)

To delve into the details of the endpoint fields, click on the endpoint name or path.

![CleanShot 2024-05-29 at <EMAIL>](https://api.apidog.com/api/v1/projects/544525/resources/341166/image-preview)
   
You can customize the `Table Preview Settings` to include custom fields.

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/341163/image-preview)

## Bulk endpoints operations

Start by selecting the endpoints you wish to manage.

![CleanShot 2024-05-29 at <EMAIL>](https://api.apidog.com/api/v1/projects/544525/resources/341168/image-preview)

You can then proceed with batch operations to suit your needs, including `Bulk deletion`, `bulk modification` of endpoint statuses, `bulk addition and deletion` of tags, `bulk modification` of responsible personnel, `bulk export`, and `bulk movement` of directories.

## FAQ

**Q: How can I bulk delete folders?**

A: Currently, there is no option for bulk deleting folders. Each directory must be deleted individually.

**Q: How can I bulk add/remove prefixes to the path of endpoints?**

A: It is not possible to process path changes through bulk operations. You can export the file in Apidog format from settings, perform text-based batch find-and-replace, and then re-import the file back into Apidog.