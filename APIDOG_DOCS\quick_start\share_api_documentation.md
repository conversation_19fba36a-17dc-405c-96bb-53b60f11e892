Share your API documentation
When the API development is complete and debugging is successful, you can very conveniently publish it as API documentation.

1
Switch to the Share Docs module.
2
Click New to create a new quick share.
3
Give it a name, select all environments, and click Save.

4
You can now see this Share in the list. Click Open to open it in a web page.
5
You can switch between endpoints in the left directory tree. For each endpoint, you can click Try it out to send requests, and you can also generate request code and data model code.
image.png
Apidog also supports customizing the documentation layout, custom domain names, custom URLs, and more. Learn more about Publish API documentation.

