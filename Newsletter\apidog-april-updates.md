Apidog's April New Updates:

- 🔥[New Feature] `Security Schemes` is now live, compatible with OpenAPI (Swagger) specification.

- 🔥[New Feature] The security schemes now support sprint branches, versioning and change history.

- 🔥[New Feature] When commenting on endpoints or test scenarios, you can use `@` to mention team members.
- 🔥[New Feature] For published/shared API documentation, each page is now available in `.md` format — just append `.md` to the URL, and supports one-click copy, making it easier for large language models (LLMs) to access and use the content. In addition, every doc site is now baked with `llms.txt`, `sitemap.xml`, and `robots.txt` files.
- 🔥[New Feature] You can now configure an IP Allowlist for teams, supporting both single IP addresses and IP ranges.

- ⚡[Optimization] When importing an OpenAPI file, if the body defines multiple media types, the first one will be imported by default.
- ⚡[Optimization] When configuring an email allowlist for published online documentation, you can now customize the login page description and the toast message after entering an email.
- ⚡[Optimization] When debugging endpoints related to `AI with LLMs` provided by LiteLLM, the message content now supports automatically merging.
- ⚡[Optimization] When importing Postman collections, descriptions of collections and folders are now included.

- 🐞[Bug Fix] Fixed the issue where the Mock service took too long to respond when an enpoint was defined with a `No-Content` response.


