# What is Postman?

Postman is an all-in-one [API platform](https://www.postman.com/api-platform/) for building and working with APIs. It takes the pain out of every stage of the API lifecycle—from designing and testing to delivery and monitoring. Built for teams, Postman makes it easy to collaborate, stay organized, and build secure, reliable APIs faster

## API repository

Easily store, catalog, and collaborate around all your API artifacts on one central platform. Postman can store and manage API specifications, documentation, workflow recipes, test cases and results, metrics, and everything else related to APIs.

## Tools

The Postman platform includes a comprehensive set of tools that help accelerate the API lifecycle—from design, testing, documentation, and mocking to the sharing and discoverability of your APIs.

## Governance

Postman's full-lifecycle approach to governance helps teams build high-quality, consistent APIs from the start. By shifting governance left, organizations can align development and design practices, reduce risk, and foster stronger collaboration across teams.

## Workspaces

Postman workspaces help you organize your API work and collaborate across your organization or across the world. Choose from internal, public, or partner workspaces.

## Integrations

Postman integrates with the most important tools in your software development pipeline to enable API-first practices. The Postman platform is also extensible through the [Postman API](https://learning.postman.com/docs/developer/intro-api/) and through [open source technologies](https://blog.postman.com/announcing-postman-open-technologies/).
