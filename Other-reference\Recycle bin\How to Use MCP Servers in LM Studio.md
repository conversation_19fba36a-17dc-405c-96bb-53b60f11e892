# How to Use MCP Servers in LM Studio

The world of local Large Language Models (LLMs) represents a frontier of privacy, control, and customization. For years, developers and enthusiasts have run powerful models on their own hardware, free from the constraints and costs of cloud-based services.However, this freedom often came with a significant limitation: isolation. Local models could reason, but they could not act. With the release of version 0.3.17, LM Studio shatters this barrier by introducing support for the Model Context Protocol (MCP), a transformative feature that allows your local LLMs to connect with external tools and resources.

![](https://assets.apidog.com/blog-next/2025/06/image-495.png)

This guide provides a comprehensive, deep-dive into configuring and using this powerful feature. We will move from foundational concepts to advanced, practical examples, giving you a complete picture of how to turn your local LLM into an interactive and effective agent.

## What is a MCP Server?

Before you can configure a server, it is crucial to understand the architecture you are working with. The Model Context Protocol (MCP) is an open-source specification, originally introduced by Anthropic, designed to create a universal language between LLMs and external tools. Think of it as a standardized "API for APIs," specifically for AI consumption.

The MCP system involves two main components:

- **MCP Host**: This is the application where the LLM resides. The Host is responsible for managing tool discovery, presenting the available tools to the model during an inference pass, and handling the entire lifecycle of a tool call. In this guide, **LM Studio is the MCP Host.**
- **MCP Server**: This is a program that exposes a collection of tools (functions) through an HTTP endpoint. A server can be a simple script running on your machine or a robust, enterprise-grade service on the web. For example, a company like Stripe could offer an MCP server for payment processing, or a developer could write a personal server to interact with their smart home devices.

The beauty of this protocol is its simplicity and standardization. Any developer can build a server that exposes tools, and any application that acts as a host can connect to it, creating a vendor-agnostic ecosystem.

## Step-by-Step Guide: Adding a Remote MCP Server

The primary method to add and manage MCP servers in LM Studio is by editing a central configuration file named `mcp.json`.

### Finding and Editing `mcp.json`

You can access this file directly from the LM Studio interface, which is the recommended approach.

1. Launch LM Studio and look at the right-hand sidebar.
2. Click on the **Program tab**, which is represented by a terminal prompt icon (`>_`).
3. Under the "Install" section, click the **Edit mcp.json** button.

![](https://assets.apidog.com/blog-next/2025/06/image-495.png)

This action opens the configuration file directly within LM Studio's in-app text editor. The application automatically watches this file for changes, so any server you add or modify will be reloaded the moment you save.

![](https://assets.apidog.com/blog-next/2025/06/image-495.png)

### Example Configuration: The Hugging Face Server

To illustrate the process, we will connect to the official Hugging Face MCP server. This tool provides your LLM with the ability to search the Hugging Face Hub for models and datasets—a perfect first step into tool use.

First, you need an access token from Hugging Face.

1. Navigate to your Hugging Face account's [Access Tokens settings](https://huggingface.co/settings/tokens).
2. Create a new token. Give it a descriptive name (e.g., `lm-studio-mcp`) and assign it the `read` role, which is sufficient for searching.
3. Copy the generated token (`hf_...`).

Next, add the following structure to your `mcp.json` file.

```json
{
  "mcpServers": {
    "hf-mcp-server": {
      "url": "<https://huggingface.co/mcp>",
      "headers": {
        "Authorization": "Bearer <YOUR_HF_TOKEN>"
      }
    }
  }
}
```

Now, replace the placeholder `<YOUR_HF_TOKEN>` with the actual token you copied from Hugging Face. Save the file (Ctrl+S or Cmd+S).

That's it. Your LM Studio instance is now connected.

### Verification and Testing

To confirm the connection is active, you must use a model that is proficient at function calling. Many modern models, such as variants of Llama 3, Mixtral, and Qwen, have this capability. Load a suitable model and start a new chat.

Issue a prompt that would necessitate the tool, for example:

> "Can you find some popular LLM models on Hugging Face that are under 7 billion parameters?"

If everything is configured correctly, the model will recognize the need for a tool. Instead of answering directly, it will trigger a tool call, which LM Studio will intercept and present to you for confirmation.

### Tool Call Confirmations in LMStudio

The power to connect your LLM to external tools comes with significant responsibility. An MCP server can, by design, access your local files, make network requests, and execute code. LM Studio mitigates this risk with a critical safety feature: **tool call confirmations**.

When a model wants to use a tool, a dialog box appears in the chat interface. This box gives you a complete, human-readable overview of the pending action:

- The name of the tool being called.
- The specific arguments the model wants to send to it.

You have full control. You can inspect the arguments for anything suspicious and then choose to **Allow** the call once, **Deny** it, or, for tools you trust implicitly, **Always allow** that specific tool.

**Warning: Never install or grant permissions to an MCP server from a source you do not fully trust.** Always scrutinize the first tool call from any new server. You can manage and revoke your "Always allow" permissions at any time in **App Settings > Tools & Integrations**.

### Connect LMStudio to a Local MCP Server

While connecting to remote servers is useful, the true power of MCP for many users is the ability to run servers on their local machine. This grants an LLM access to local files, scripts, and programs, all while keeping data entirely offline.

**LM Studio supports both local and remote MCP servers**. To configure a local server, you would add an entry to your `mcp.json` file that points to a local URL.

For example, if you were running a server on your machine on port 8000, your configuration might look like this:

```json
{
  "mcpServers": {
    "my-local-server": {
      "url": "http://localhost:8000"
    }
  }
}
```

## The Future is Local and Connected

The integration of MCP into LM Studio is more than an incremental update; it's a foundational shift. It bridges the gap between the raw intelligence of LLMs and the practical utility of software applications. This creates a future where your AI is not just a conversationalist but a personalized assistant that operates securely on your own hardware.

Imagine a writer with a local MCP server that provides custom tools for summarization, fact-checking against a private library of documents, and style analysis—all without sending a single word to the cloud. Or a developer whose LLM can interact with a local server to run tests, read compiler output, and search internal codebases.

To facilitate this vision, the LM Studio team has also made it easy for developers to share their creations. The "Add to LM Studio" button, which uses a custom `lmstudio://` deeplink, allows for one-click installation of new MCP servers. This lowers the barrier to entry and paves the way for a vibrant, community-driven ecosystem of tools.

By embracing an open standard and prioritizing user control, LM Studio has provided a powerful framework for the next generation of personal AI.
