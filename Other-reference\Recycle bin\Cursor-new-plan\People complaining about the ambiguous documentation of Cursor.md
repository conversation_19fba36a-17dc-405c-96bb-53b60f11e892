# # People complaining about the ambiguous documentation of Cursor

Burst rate limits can be dipped into at any time for particularly bursty sessions but are slow to refill. Local rate limits refill fully every few hours.“

Rigghhttt really clarifying 😂

I don’t really agree that this is more info. It still feels unnecessarily vague. It reads a lot like Claude Code subscription pricing. If that’s the case does that also mean we only get ~40 requests every 5 hours on the Pro plan? Trying to explain rate limits with compute time doesn’t help us if we don’t have any insight into what compute time per request looks like. Also associating that compute time with a “session” isn’t helpful if we don’t know what a session is. Again is a session here the same as the 5 hour window from first message you get with Claude Code and if so can we get an “average” amount of requests one might expect to fit into a session?

Sounds like there’s a rate limit per 1 minute, and maybe every 4 hours (guessing) or so.

What I don’t understand is why they can’t be transparent about the exact number of requests that is for each of those.

Absolute shit doc, please add more clarity, add numbers. You are breaking the way most subscriptions work. We need clarity.

in how many hours does the rate limit resets , and how many prompt for example with sonnet 4 hit that limit

Still quite unclear.

1. Is deepseek 3.1/gemini flash still free with the new pricing model or does it consume the hidden quota?

2. What's the timespan for the burst quota refill?

It almost reads like every provider might have a different rate limit and possibly even session thresholds. I would assume everything is “free,” and everything is also rate-limited.

From the sound of that you'll be reasonably able to use opus. If that's the case I'll give this a shot but more clarity is definitely needed.

his documentation is beyond useless, as is. I think it is time to leave `Cursor` and not look back.

[u/mntruell](https://www.reddit.com/user/mntruell/) You really need to fix this. This isn't transparency. This is a waste of time.
