Create and send cookies
Apidog's cookie manager allows you to see and modify cookies linked to various domains. These cookies are stored within Apidog and can be used when making requests in Apidog.

About cookies
A cookie is a package of data sent from a website and stored on the user's computer by the user's web browser while the user is browsing. Cookies are designed to be a reliable mechanism for websites to remember stateful information (such as items added to the shopping cart in an online store) or to record the user's browsing activity. They typically contain two pieces of information: a site name and a unique user ID.

When you return to a site, it can read the cookie to remember you and your preferences and adapt its content accordingly. Without cookies, you would need to log in anew after leaving a site or recreate your shopping cart if you exit a page.

Use the cookie manager
In the bottom right corner of the Apidog interface, you can see a " Cookies" button. Click it to open the Cookies pop-up window.

Cookies link
In the Cookies window, you can see a list of domains and their related cookies.

Create a cookie
To create a new cookie for a domain, click "+ New".

Apidog supports these attributes:

Domain - The domain to which <PERSON><PERSON><PERSON> will send the cookie. Do not include the protocal.

Name - The name of the cookie.

Value - The value of the cookie.

Path - The URL path to which the cookie is limited. If the path is /, the cookie is sent to all requests within that domain.

Expires - The date and time after which the cookie will no longer be sent by Apidog.

MaxAge - Specify cookie's expiration time. It defines the number of seconds until the cookie expires.

HttpOnly - Indicates that the cookie cannot be accessed by client-side scripts (e.g., via document.cookie in JavaScript). This cookie will only be included in requests' cookie headers. This attribute does not influence Apidog's functionality.

Secure - The cookie is only sent over secure connections, specifically when the URL starts with https://.

Choose Save to store the cookie under the appropriate domain in the Apidog cookie manager.

Edit or delete cookies
To modify an existing cookie, choose the cookie, make the desired changes, and then select Save.

To remove a cookie, click the Delete next to the cookie.

To clear all cookies and domains from the Apidog cookie manager, select Clear All.

Sending cookies with a request
Whenever you make a request to a domain to which you've added a cookie, that cookie will appear automatically in the Headers tab of your request. If the cookie is not visible, choose hidden to display autogenerated headers.

You can't directly override cookie headers in the Headers tab. Modify the cookie using the cookie manager or delete the cookie and set the request headers manually. Cookies added in the cookie manager and the Headers tab are merged by Apidog before a request is sent.

