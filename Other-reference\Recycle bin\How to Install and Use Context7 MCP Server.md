## How to Install and Use Context7 MCP Server

Working with up-to-date libraries and accurate code is essential in development, but many AI tools and code assistants still rely on outdated or generalized information. This often results in frustrating bugs and inefficiencies. Anyone who's received a code example with deprecated APIs or methods that don't exist knows how disruptive it can be to the development process, wasting time and causing unnecessary debugging efforts

That’s where **Context7** comes in — a powerful MCP (Model Context Protocol) server that brings version-aware documentation directly into your development workflow. In this guide, we’ll walk through what Context7 is, how it works, how to install it on various platforms, and how to use it effectively to supercharge your code generation with precision and speed.

Before diving into details , check out **[Apidog](https://apidog.com/)**—a powerful tool for designing, testing, and documenting APIs. Apidog allows you to seamlessly integrate your APIs, enhancing your workflow with structured models and easy collaboration. If you're looking to boost your automation and API management.

![](https://assets.apidog.com/blog-next/2025/04/image-663.png)

You can check the Medusa API Docs as example: https://medusa.apidog.io/

## Why Traditional Code Suggestions Often Fail

Language models are typically trained on static data. This means the suggestions they provide might be:

- **Outdated**: Using older versions of APIs or methods that have since been replaced or removed.
- **Incorrect**: Hallucinating functions that don’t exist or combining methods improperly.
- **Generic**: Offering one-size-fits-all code that doesn’t reflect the specific library version you’re working with.

These issues slow down development, introduce bugs, and increase context-switching as developers scramble to cross-check information on forums and documentation sites.

## What is Context7?

**Context7** is an MCP server that solves these problems by dynamically injecting **up-to-date, version-specific documentation** into your prompts. Whenever you write a prompt that includes `use context7`, the server fetches current official documentation and code examples and integrates them directly into the context window for your AI assistant.

It’s like having the latest docs *right inside your code editor*, interpreted and summarized for you in real time.

## Key Benefits

- **Real-Time Documentation Access**No more flipping between tabs — get the most recent official docs delivered directly into your prompt.
- **Version-Specific Code Examples**Receive examples that are accurate for the exact version of the library you’re using.
- **Boosted Productivity**Eliminate the need for repetitive manual searches and reduce time spent debugging outdated code.
- **Universal Compatibility**Works with major MCP-compatible clients including Claude Desktop, Cursor, Windsurf, and more.

## How Context7 MCP Server Works

When you type a prompt like:

```
Create a CRUD API in FastAPI with authentication. use context7

```



The Context7 server:

- **Identifies the library** being referenced (e.g., FastAPI).
- **Looks up the latest version** of the official documentation.
- **Parses and injects** the relevant content into the AI’s prompt context.
- **Returns a response** with updated, version-accurate code examples.

This all happens behind the scenes, within milliseconds.

## How to Install Context7 MCP Server

### Prerequisites

Before you begin, make sure you have:

**Node.js** version 18 or higher

An MCP-compatible client like Claude, Cursor, or Windsurf

### Installation via Smithery CLI (Recommended for Claude Users)

If you’re using Claude Desktop, install via the Smithery CLI with a single command:

```
npx -y @smithery/cli install @upstash/context7-mcp --client claude

```



This will install and configure Context7 for immediate use.

### Manual Installation by Client

#### **For Cursor**

![](https://assets.apidog.com/blog-next/2025/04/image-662.png)

Go to: `Settings` → `Cursor Settings` → `MCP` → `Add new global MCP server`

Or manually edit the file: `~/.cursor/mcp.json`

```
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    }
  }
}

```

#### **For Windsurf**

![](https://assets.apidog.com/blog-next/2025/04/image-661.png)

Update your configuration file:

```
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    }
  }
}

```



#### **For VS Code / VS Code Insiders**

![](https://assets.apidog.com/blog-next/2025/04/image-660.png)

Update your VS Code MCP config:

```
{
  "servers": {
    "Context7": {
      "type": "stdio",
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    }
  }
}

```



#### **For Claude Code**

```
claude mcp add context7 -- npx -y @upstash/context7-mcp@latest

```

#### 

#### **For Claude Desktop**

![](https://assets.apidog.com/blog-next/2025/04/image-659.png)

Edit your config file (e.g., `claude_desktop_config.json`):

```
{
  "mcpServers": {
    "Context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    }
  }
}

```



### Alternative Runtimes

Prefer using **Bun** or **Deno** instead of Node? No problem.

#### Bun:

```
{
  "mcpServers": {
    "context7": {
      "command": "bunx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    }
  }
}

```



#### Deno:

```
{
  "mcpServers": {
    "context7": {
      "command": "deno",
      "args": ["run", "--allow-net", "npm:@upstash/context7-mcp"]
    }
  }
}

```



## Using Context7 in Your Workflow

Once installed, using Context7 is simple. Just include `use context7` in any prompt where you want the model to reference updated docs.

### Example Prompts:

`Create a Next.js 14 project with routing and server components. use context7`

`Write a MongoDB aggregation pipeline to group and sort documents. use context7`

`Show how to use TanStack Router in a React project. use context7`

These prompts will trigger Context7 to fetch the latest docs and ensure the model’s response is accurate.

## Advanced Features

Context7 offers tools that go beyond just prompt enhancement. You can also programmatically query docs using the following endpoints:

### `resolve-library-id`

Converts a general library name into a Context7-compatible identifier.

**Parameter**: `libraryName` (required)

### `get-library-docs`

Fetches documentation for a given library.

**Parameters**:

`context7CompatibleLibraryID` (required)

`topic` (optional): e.g., `"hooks"`, `"routing"`

`tokens` (optional): Defaults to `5000`, limits the size of the response

This makes it easy to build custom tools or integrate Context7 into your internal developer workflows.

## Local Development and Customization for Context7 MCP Server

Want to contribute or extend Context7? You can run it locally with the following steps:

### 1. Clone the Repository

```
git clone https://github.com/upstash/context7-mcp.git
cd context7-mcp
```

### 2. Install Dependencies

```
bun i
```



### 3. Build the Project

```
bun run build
```



### 4. Update Your MCP Config

Point your MCP config to the local build:

```
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["tsx", "/path/to/context7-mcp/src/index.ts"]
    }
  }
}

```

### 5. Test with MCP Inspector

Validate your setup using the MCP Inspector:

```
npx -y @modelcontextprotocol/inspector npx @upstash/context7-mcp@latest

```

This tool helps simulate prompt responses and verify that everything’s working correctly before pushing changes or deploying.

**Additionally, you can enhance Your AI Automation with Apidog MCP Server Integration**Take your AI-driven workflows even further by integrating with the **[Apidog MCP Server](https://docs.apidog.com/apidog-mcp-server)**.

![](https://assets.apidog.com/blog-next/2025/04/image-664.png)

This powerful connection allows your AI assistant to **interact directly with API specifications from Apidog projects**, enabling seamless API exploration, code generation, and structured model creation.

## Troubleshooting Common Issues

Even with a smooth setup process, occasional hiccups can occur depending on your environment or client configuration. Here are some common problems and how to resolve them quickly:

`ERR_MODULE_NOT_FOUND`

This error typically appears when using `npx` to launch the MCP server and Node.js fails to properly resolve the package.

**Solution**: Use `bunx` instead of `npx`.

Update your MCP configuration like so:

**Why it works**: In some environments, `npx` may not install dependencies correctly or handle scoped packages as expected. `bunx` offers a more reliable alternative, especially in projects already using Bun as a runtime.

### MCP Client Errors

Some MCP clients may throw errors or fail to connect due to specific argument formatting or package resolution issues.

**Here are a few quick fixes to try:**



**Remove `@latest` from the package name**Some clients or runtimes have trouble with version tags. Try simplifying the package call:

```
"args": ["@upstash/context7-mcp"]

```

**Use `bunx` instead of `npx`** As mentioned above, Bun is often more consistent for executing external packages.

**Try Deno as a fallback**If you’re in a Deno-first environment or want additional sandboxing, this works well:

```
{
  "mcpServers": {
    "context7": {
      "command": "deno",
      "args": ["run", "--allow-net", "npm:@upstash/context7-mcp"]
    }
  }
}

```



### Still Stuck?

If none of these fixes work, try:

- Restarting your MCP client/editor
- Ensuring you’re on a supported Node.js version (v18+ recommended)
- Opening an issue on the [GitHub repo](https://github.com/upstash/context7-mcp/issues) with your error message and system info

## Final Thoughts

In an age where libraries evolve rapidly, using AI tools without up-to-date context is risky. **Context7 MCP Server** bridges this gap by injecting real-time, accurate documentation into your coding experience.

Whether you're building apps with cutting-edge frameworks or maintaining legacy systems, Context7 keeps your code suggestions grounded in reality ,saving you time, reducing bugs, and increasing confidence.
