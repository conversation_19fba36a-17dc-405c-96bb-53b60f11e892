CI/CD in Apidog
Continuous integration and continuous delivery (CI/CD) are essential components of an API development workflow. Apidog provides integration with widely-used CI tools, enabling you to monitor API builds directly within the same platform where you design and test your APIs.

You can execute API test scenarios created in Apidog as part of your CI pipeline using the Apidog CLI.

Getting Started

1
Orchestrate test scenarios and debug them until they pass.
2
Switch to the CI/CD tab, set up environment parameters, test data, and other necessary configurations.
Learn more about configurations of Apidog CLI.


3
Choose your CI/CD platform, and copy the corresponding commands to configure in your CI/CD platform.
image.png

4
Run the pipeline and get the result in your CI/CD platform.
CI/CD Platform supported
Apidog supports various CI/CD platforms, including:

GitLab CI/CD

Jenkins

Azure Pipelines

Bitbucket Pipelines

CircleCI

GitHub Actions

Travis CI

If your CI/CD platform is not listed above, you can still configure your CI/CD using the code provided in the "Command Line" option.

