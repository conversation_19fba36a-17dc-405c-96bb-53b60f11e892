Apidog provides an efficient mock engine that can generate reasonable responses based on API specs without the need for specific configuration, making it highly efficient.

API mocking can be used in the following scenarios:

1. During parallel frontend and backend development, when the API spec is available but the API hasn't been developed yet. In this case, the frontend can use mock data to assist in development.

2. When the API contains production data that's inconvenient to call directly, the frontend can use mock data to aid in development.

3. During testing, when external data requires a test dataset, mock data can be used as the data source.

## Getting started

<Steps>
  <Step>
    Specify an endpoint or import an API spec; this endpoint needs to have a specified response.
  </Step>
  <Step>
   Go to "Mock" tab, and click the mock URL to copy. 
<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/352100/image-preview)
</Background>
  </Step>
  <Step>
    Paste this URL in your browser to obtain mock data. Refreshing will update the data.
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/343580/image-preview" style="width: 440px" />
</p>
  </Step>
</Steps>

## What can Apidog mocking do?

Apidog's Mock functionality can fulfill the following requirements:

1. [Return automatically generated data based on the API Spec](apidog://link/pages/618190).
2. [Return the response example specified in the API Spec](apidog://link/pages/618208).
3. [Return a specified response](apidog://link/pages/618204).
4. [Return different custom responses based on different request parameters](apidog://link/pages/618204).
5. [Return a response whose parameters are related to the request parameters](apidog://link/pages/618209).

## Mock server

Apidog provides three mock methods: Local mock, Cloud mock, and Runner mock.

### Local mock
Local mock is a server installed on your computer along with the Apidog client. It opens when the Apidog client is opened and only works when your Apidog client is open. 
- Local mock is suitable for local frontend debugging scenarios. 
- You can see the URL of this Local mock server in the Local mock environment in the environment management popup. 
- The local mock server cannot be closed or removed from the environment.
- Local mock is only available in Apidog Client but not Apidog Web.

### Cloud mock
Cloud mock has the same functionality as local mock but is deployed on Apidog servers. Unlike local mock, it doesn't depend on whether your local machine is open, and you can access cloud mock data from any machine at any time. 
- Cloud mock is suitable as a sandbox environment for public APIs. 
- Cloud mock supports encrypted access.
- It is off by default but can be turned on or off.

:::highlight purple
Learn more about [Cloud mock](apidog://link/pages/621066).
:::

### Runner mock
The Runner mock server is located on the team's self-deployed runner. The team needs to deploy the runner on a server first, then all team members can use this runner to mock data. It also doesn't depend on whether your local machine is open. 
- Runner mock is suitable for use as a data source for large-scale automated testing or as a sandbox environment for non-public APIs.


:::highlight purple
Learn more about [Self-hosted runner mock](apidog://link/pages/621086).
:::

## Accessing the mock server

There are two main ways to access the mock server: URL access and Apidog sending.

### URL access
In every HTTP endpoint in Apidog, you'll see a "mock" module. It's in the "API" tab in `DESIGN` mode and in the "Mock" tab in `DEBUG` mode.

Here, you can copy the mock URL and use it anywhere to request mock data.
If the endpoint specifies multiple responses or multiple mock expectations, each response/expectation will have its own mock URL.

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/343588/image-preview)

You can click "Request" to make these request in Apidog directly.

Note: If you use "Click to copy", it copies only the URL here, not including the method and body. You'll need to add these necessary parts when making the request. 

### Accessing mock data within Apidog

In each Apidog project, you can find Local mock and Cloud mock in the environment switcher in the top right corner.

When you switch the environment to Local mock/Cloud mock, requests in Apidog will be sent to the corresponding mock environment.

Note: Only endpoints in Apidog with paths starting with '/' will be sent to the mock environment. Endpoints with their own prefixed URL that don't start with '/' won't be sent to the mock environment.