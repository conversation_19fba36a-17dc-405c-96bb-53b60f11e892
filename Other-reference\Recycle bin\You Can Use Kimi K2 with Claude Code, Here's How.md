# You Can Use Kimi K2 with <PERSON>, Here's How:

Something crazy just happened for the AI Coding Game.

I was scrolling through my feed when I stumbled upon a thread discussing a wild new setup: developers are running Moonshot AI's brand-new Kimi K2 model directly within <PERSON>'s coding environment. At first, it sounded like a weird fan-fiction crossover, but the more I read, the more I realized it was not only possible but game-changing. I managed to get my hands on a test key, and after a few hours of tinkering, I can confirm: this combination is absolutely, unequivocally insane.

![](https://assets.apidog.com/blog-next/2025/07/image-229.png)

What we're witnessing is a perfect storm of cutting-edge AI. On one side, you have Kimi K2, a behemoth of a model with a revolutionary architecture that excels at thinking, reasoning, and, most importantly, writing and fixing code like a seasoned developer. On the other, you have Claude Code, a refined, developer-centric environment built for seamless AI interaction.

This article is your comprehensive guide to replicating this powerhouse setup. We'll start by diving deep into what makes Kimi K2 a titan in the world of AI, focusing on its benchmark-crushing performance in agentic coding and its open-source philosophy. Then, I'll walk you through the surprisingly simple steps to get Kimi K2 running natively inside your Claude Code environment. Prepare to have your mind blown.

### Is Kimi K2 another Deepseek Moment for Open Source LLMs?



Before we get to the "how," we need to understand the "why." What makes Kimi K2 so special? Released by Moonshot AI, Kimi K2 isn't just another incremental update to an existing model. It's a fundamental leap forward, built on a Mixture-of-Experts (MoE) architecture.

![](https://assets.apidog.com/blog-next/2025/07/image-230.png)

In simple terms, an MoE model is like having a team of specialized experts instead of a single generalist. Kimi K2 has a staggering 1 trillion total parameters (the "experts" in the team) but only activates 32 billion of them for any given task. This allows it to achieve the performance of a massive model while being incredibly efficient. It was trained on a colossal 15.5 trillion tokens of data, giving it an unparalleled understanding of knowledge, reasoning, and code.

But the real story lies in the benchmarks. While Kimi K2 excels across the board, its performance in agentic coding and tool use is where it truly embarrasses the competition.

### Kimi K2 is Really Good at in Agentic Coding

"Agentic coding" is the ability of an AI to act like an autonomous software engineer. It doesn't just write a snippet of code; it understands a problem, devises a plan, uses tools (like a file editor or a terminal), and attempts to solve complex software engineering tasks from start to finish. This is the holy grail of AI-assisted development, and Kimi K2 is the new king.

![](https://assets.apidog.com/blog-next/2025/07/image-235.png)

Let's look at the numbers from the official Hugging Face release:

- **SWE-bench Verified (Agentic Coding):** This benchmark tests an AI's ability to resolve real-world GitHub issues. With a single attempt, Kimi K2 achieves an accuracy of **65.8%**. To put that in perspective, GPT-4.1 scores 54.6%, and Claude 4 Opus hits 72.5% (with extended thinking). Kimi K2 is not just competing; it's setting a new standard for open-source models and even rivaling the top closed-source giants. When allowed multiple attempts, its score jumps to a phenomenal **71.6%**.
- **SWE-bench Multilingual (Agentic Coding):** The challenge is the same, but with code in multiple programming languages. Kimi K2 scores **47.3%**, trouncing competitors like DeepSeek-V3 (25.8%) and Qwen3 (20.9%). This demonstrates its deep, language-agnostic understanding of programming logic.
- **TerminalBench & Aider-Polyglot:** These benchmarks test the model's ability to operate within a terminal and its proficiency across multiple languages in a real-world development setting. In both, Kimi K2 shows state-of-the-art performance, proving its practical, hands-on capabilities.

### The Power of Open Source

![](https://assets.apidog.com/blog-next/2025/07/image-231.png)

Perhaps the most revolutionary aspect of Kimi K2 is its commitment to the open-source community. Both the model weights and the underlying code are released under a Modified MIT License. This is a massive win for developers and the AI community for several key reasons:

1. **Transparency and Trust:** We can see how the model is built, fostering trust and enabling researchers to build upon its architecture.
2. **Customization:** Businesses and individuals can fine-tune Kimi K2 on their own private data, creating highly specialized versions for specific tasks without sending sensitive information to a third party.
3. **Democratization:** It breaks down the barriers to entry, allowing anyone with the computational resources to run a state-of-the-art model. It prevents vendor lock-in and encourages a more competitive and innovative ecosystem.

This combination of raw agentic power and an open-source philosophy makes Kimi K2 not just a powerful tool but a foundational piece of technology for the future of software development.

### The Tutorial: Connecting Kimi K2 to Claude Code

Now for the magic. The secret to this "insane combo" lies in a clever bit of API compatibility. The team at Moonshot AI has provided an Anthropic-compatible API endpoint for Kimi K2. This means we can trick Claude Code, which is designed to talk to Anthropic's models, into talking to Kimi K2 instead—without changing a single line of Claude's own code.

Here are the step-by-step instructions.

### Step 1: Register on the Moonshot AI Platform

First, you need to get access to the Kimi K2 API.

- **Navigate to the Moonshot AI Platform:** Open your web browser and go to `https://platform.moonshot.ai/`.
- **Sign Up:** Create a new account. You'll likely need to provide an email address and set up a password.

This platform will be your central hub for managing your API keys, tracking your usage, and accessing documentation.

### Step 2: Get Your API Key

Once you're registered and logged in, you need to grab your personal API key. This key is your secret password that authenticates your requests to the Kimi K2 model.

- **Find the API Keys Section:** Look for a section in your account dashboard labeled "API Keys" or something similar.
- **Create a New Key:** Generate a new secret key. The platform will display a string of characters that likely starts with `sk-`.
- **Important:** Copy this key immediately and save it somewhere safe, like a password manager. For security reasons, you will likely not be able to see this key again after you close the window. **Treat this key like a password; never share it publicly or commit it to a public GitHub repository.**

### Step 3: Configure Your Environment

This is the most crucial step. We are going to use environment variables to tell Claude Code where to send its requests and how to authenticate them. Open your terminal or command prompt.

**Set the Authentication Token:** Type the following command, replacing `sk-YOURKEY` with the actual API key you copied in Step 2.

```bash
export ANTHROPIC_AUTH_TOKEN=sk-YOURKEY
```

This command tells any application that uses the Anthropic SDK (like Claude Code) to use your Moonshot AI key for authentication.

**Set the Base URL (The Secret Sauce):** Now, type this second command exactly as written.

```bash
export ANTHROPIC_BASE_URL=https://api.moonshot.ai/anthropic
```

This is the command that performs the magic. It tells Claude Code to redirect all API calls that would normally go to `api.anthropic.com` to Moonshot AI's compatible endpoint instead. Claude Code will think it's talking to Claude, but the requests will be intercepted and handled by the powerful Kimi K2 model.

(Note: If you're on Windows, you might need to use the `set` command instead of `export`, e.g., `set ANTHROPIC_AUTH_TOKEN=sk-YOURKEY`)

### Step 4: Start Claude Code and Unleash the Beast!

That's it. You're ready.

Launch your Claude Code application as you normally would. You won't see any special pop-ups or notifications. The change is completely transparent. But the moment you start interacting with it, you'll feel the difference.

You are now running Kimi K2 inside Claude Code.

![](https://assets.apidog.com/blog-next/2025/07/image-233.png)

![](https://assets.apidog.com/blog-next/2025/07/image-234.png)

Every prompt, every question, every request to "fix this bug" or "refactor this class" will be piped directly to Kimi K2's brain. You get the raw, benchmark-crushing agentic coding power of Kimi, all while working within the polished, user-friendly interface that you're already familiar with.

### Conclusion: A Glimpse into the Future

This isn't just a cool hack; it's a profound statement about the future of AI in development. It proves that the future isn't about a single monolithic AI that rules them all. It's about flexibility, compatibility, and the power of open ecosystems. By providing a compatible API, Moonshot AI has enabled a level of interoperability that empowers developers to choose the best tool for the job without being locked into a single provider.

The combination of Kimi K2's open-source agentic power and Claude Code's refined environment is more than just an "insane combo"—it's a paradigm shift. It represents a future where developers can mix and match best-in-class components to create their ultimate AI-powered workflow. Today, we've shown how to connect a breakthrough model to a beloved interface. Tomorrow, who knows what incredible combinations the community will discover? Go ahead, follow the steps, and get ready to experience the next evolution of coding.
