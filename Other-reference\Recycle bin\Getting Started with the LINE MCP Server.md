> **Pro Tip:** Want to build, test, and document APIs faster? Apidog is the all-in-one platform trusted by developers for seamless API workflows. Try it for free!

# Quickstart Guide: Connecting AI to LINE with the LINE MCP Server

Ready to make your AI chat with millions on LINE? The **LINE MCP server** is your bridge—connecting AI agents like <PERSON> or <PERSON> to LINE’s Messaging API. This guide will show you what the LINE MCP server is, why it’s a game-changer, and how to get it running in minutes. Let’s get started!

## What is LINE and the Messaging API?

LINE is a massively popular messaging app, especially in Asia. Its Messaging API lets you build bots and services that interact with users through LINE Official Accounts. The **LINE MCP server** takes this further, letting your AI control a LINE bot using natural language—no more manual coding for every interaction.

![Official liine website](https://assets.apidog.com/blog-next/2025/07/Screenshot-2025-07-08-164345.png)

## Introducing the LINE MCP Server

The **LINE MCP server** is an open-source connector that lets AI agents talk to the LINE Messaging API using the Model Context Protocol (MCP)—think of it as a universal plug for AI tools. With it, your AI can:
- Send and receive messages
- Create rich Flex Messages
- Broadcast to multiple users
- Fetch user profiles

It’s still in trial mode, but it’s already powerful for building smart, AI-driven chatbots.

## Why Use the LINE MCP Server?
- **AI-Powered Conversations:** Let your AI handle chats naturally
- **No More API Headaches:** Skip complex integrations
- **Feature-Rich:** Dynamic messages, broadcasts, user data
- **Open Source:** Fork and extend on [GitHub](https://github.com/line/line-bot-mcp-server)

## How to Install the LINE MCP Server for Claude Desktop

### Prerequisites
- **Node.js 20+** ([Download](https://nodejs.org/en/download))
- **Docker** ([Get Started](https://www.docker.com/get-started/))
- **LINE Official Account** ([LINE Developers Console](https://developers.line.biz/console/))
- **Channel Access Token** (from your Messaging API channel)
- **Destination User ID** (optional, for targeted messages)
- **Claude Desktop** ([Download](https://claude.ai/download))
- **GitHub account** (for Docker method)

### Option 1: Install with npx

**Step 1: Create a LINE Official Account**
1. Go to the [LINE Developers Console](https://developers.line.biz/console/)
2. Create a Provider and a Messaging API channel
3. Get your Channel Access Token
4. Enable the webhook

![user id](https://assets.apidog.com/blog-next/2025/07/image_1-1.png)
![channel access token](https://assets.apidog.com/blog-next/2025/07/image_2-2.png)

**Step 2: Install Claude Desktop**
- Download and install from [claude.ai/download](https://claude.ai/download)
- Open, click **Get Started**, and log in

![Install claude desktop](https://assets.apidog.com/blog-next/2025/07/download_cluade-1.png)

**Step 3: Configure the LINE MCP Server**
1. Open Claude Desktop > **Settings** (gear icon)
2. Go to **Developer** > **Edit Config**
3. Edit or create `claude_desktop_config.json` and add:
```json
{
  "mcpServers": {
    "line-bot": {
      "command": "npx",
      "args": ["@line/line-bot-mcp-server"],
      "env": {
        "CHANNEL_ACCESS_TOKEN": "FILL_HERE",
        "DESTINATION_USER_ID": "FILL_HERE"
      }
    }
  }
}
```
4. Fill in your Channel Access Token and (optionally) Destination User ID
5. Save and restart Claude Desktop

![available mcp servers in claude](https://assets.apidog.com/blog-next/2025/07/claude_available_tools-1.png)

### Option 2: Install with Docker

**Step 1: Create a LINE Official Account** (same as above)

**Step 2: Build the Docker Image**
```bash
<NAME_EMAIL>:line/line-bot-mcp-server.git
cd line-bot-mcp-server
docker build -t line/line-bot-mcp-server .
```

![docker desktop](https://assets.apidog.com/blog-next/2025/07/image-157.png)

**Step 3: Configure for Claude Desktop**
1. Open Claude Desktop > **Settings** > **Developer** > **Edit Config**
2. Edit or create `claude_desktop_config.json` and add:
```json
{
  "mcpServers": {
    "line-bot": {
      "command": "docker",
      "args": [
        "run",
        "-i",
        "--rm",
        "-e",
        "CHANNEL_ACCESS_TOKEN",
        "-e",
        "DESTINATION_USER_ID",
        "line/line-bot-mcp-server"
      ],
      "env": {
        "CHANNEL_ACCESS_TOKEN": "FILL_HERE",
        "DESTINATION_USER_ID": "FILL_HERE"
      }
    }
  }
}
```
3. Fill in your tokens, save, and restart Claude Desktop

![claude available tools](https://assets.apidog.com/blog-next/2025/07/view_tools.png)

## Try It Out: Prompt Examples

**1. Send a Text Message**
> Send LINE message to user say "I love you MCP"

![sample prompt 1](https://assets.apidog.com/blog-next/2025/07/prompt-1.webp)

**2. Get User Profile and Send Flex Message**
> Get user profile and create beautiful Flex message and send to user

![sample prompt 2](https://assets.apidog.com/blog-next/2025/07/image-2.webp)

Claude will fetch the user’s profile and send a personalized Flex Message.

## Troubleshooting
- **Webhook not working?** Check your ngrok URL and webhook settings
- **Server won’t start?** Verify Node.js or Docker and environment variables
- **Claude not connecting?** Double-check your config and restart
- **npx/Docker errors?** Make sure they’re installed and in your PATH

## How the LINE MCP Server Supercharges AI Bots
- **Seamless AI Integration:** Let your AI send messages, create Flex content, and fetch user data
- **Natural Prompts:** Control the bot with simple language
- **Scalable:** Test locally or deploy to the cloud
- **Growing Feature Set:** Messaging, profiles, broadcasting, and more coming soon

Imagine building a bot that fetches a user’s profile or broadcasts a promo to all followers—all with a single prompt. The possibilities are endless!

## Wrap-Up
The **LINE MCP server** is your shortcut to building smart, AI-powered LINE chatbots. By connecting your AI to LINE’s Messaging API, you unlock a world of interactive, engaging experiences for your users. Whether you’re a pro or just starting out, this tool makes AI integration easy and fun.
