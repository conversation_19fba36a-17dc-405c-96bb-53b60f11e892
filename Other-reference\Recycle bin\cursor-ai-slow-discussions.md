Cursor.ai Responding very slow on pro paid version why?
I have purchased pro version today after free trail but suddenly it got slow down response,is it worth it to get paid version for now it think it is not
Hey! Could you send a video of the slow responses?
Check it yesterday was working fine but today again it happening,kindly fix this issue it is huge hurdle for my work

I have the same problem today with the paid subscription. Also very slow, may take minutes to generate any answer, most often over a minute. And seems like terminal output is not visible to me, when generating answer. What is happening?

It really slow during peak times, waiting until failed :persevering_face:

https://us1.discourse-cdn.com/flex020/uploads/cursor1/original/3X/f/a/fa5d2941fdad2dfed72baf450c02edac6f4f0d07.png

I found sth. If you open an MCP Server and wanna call it, Cursor’ll wait forever. When you close all the MCP servers, it works well.

gab<PERSON>-filin<PERSON><PERSON>ky


TL;DR
Noticed a high error rate and significant slowdown (50x slower than before) in Cursor’s ‘code apply process’. Observed high GPU usage (90% under Copy), minimal VRAM and CUDA involvement, and loud fan noise. Restarting the app and system had no effect. Suspect inefficient use of local resources by the small local model.

Issue
Recently, I’ve been facing significant performance issues with Cursor that I believe warrant further clarification and potential solutions.

Cursor seems to use a small local language model to execute this feature, parsing the output from an LLM to the file being edited. However, I am not entirely sure if this model runs locally or if there is another underlying process involved.

At first, I began to notice a higher-than-usual error rate from the small local model that Cursor uses for interpreting and parsing LLM output. This model plays a crucial role by processing the output from the primary LLM and applying code changes. Following this initial issue, the code application process itself started to slow down dramatically, running nearly ten times slower than what I’ve previously experienced, which has greatly affected my workflow.

Soon after, I observed that my computer’s fan began to spin loudly whenever the ‘apply code’ phase was running, suggesting a considerable increase in resource consumption. To understand the cause, I checked the Task Manager and saw that GPU usage spiked significantly, reaching up to 88% under Copy processing. Surprisingly, despite this high usage, VRAM and CUDA processing remained minimal, which pointed to an inefficient utilization of the available GPU resources. This pattern raised questions about the underlying implementation of the local model and how it manages hardware resources during these operations.

Troubleshooting Attempts
Application Restart: I restarted the Cursor application, but this did not resolve the problem.
System Reboot: Restarting the computer also had no impact on the issue.
Seeking Clarification
Expected Behavior: Is it normal for Cursor’s local language model to exhibit such high GPU usage with minimal VRAM and CUDA involvement?
Potential Issues: Could this be a bug or an unintended effect introduced in recent updates?
Recommendations: Are there any known optimizations or solutions to prevent this high GPU load and improve overall performance?
Given that Cursor is a paid product, users should not need to deal with these kinds of development challenges. Ensuring an efficient and reliable user experience is crucial. Any feedback or acknowledgment from the team would be appreciated.

Screenshot for Reference
Screenshot 2024-11-10 163027
Screenshot 2024-11-10 163027
3072×1182 343 KB
I’ve attached a screenshot showing the GPU usage spike and the specifics of Copy processing load during code application for further context.


Maybe an extension is using resources. Open the command palette and search for Developer: Open Process Explorer to see if you can identify it. Also, try starting in disable-extensions mode to check if the issue persists. [image]


Hi there! I wanted to see if you have any ideas or suggestions for tackling this issue.

Update:
It started to work correctly again without me doing anything. Not sure what happened.

I’m glad the problem is resolved.

it started to behave like that again. Any insights?

Did you get update 0.42.5? Also, check your logs:

Thank you for your reply. Yes, I got the latest update.

Version: 0.42.5
VSCode Version: 1.93.1
Commit: 001668006cc714afd397f4ef0d52862f5a095530
Date: 2024-11-14T00:33:36.512Z
Electron: 30.4.0
Chromium: 124.0.6367.243
Node.js: 20.15.1
V8: 12.4.254.20-electron.0
OS: Windows_NT x64 10.0.22631
I will go through the logs to see if I can find any clues. I don’t understand why I have this massive GPU load when I experience the issue.

 I found something that might be related.

This is only a small excerpt. It seems to be a memory leak.

2024-11-16 12:44:45.104 [error] [5573] potential listener LEAK detected, having 2817 listeners already. MOST frequent listener (2814):: Error
    at m.create (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:433:58394)
    at J.q [as onWillDispose] (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:436:1100)
    at K.a (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:2559:50844)
    at K.h (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:498:21630)
    at K.g (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:498:21614)
2024-11-16 12:44:59.340 [error] [5573] potential listener LEAK detected, having 2905 listeners already. MOST frequent listener (2902):: Error
    at m.create (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:433:58394)
    at J.q [as onWillDispose] (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:436:1100)
    at K.a (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:2559:50844)
    at K.h (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:498:21630)
    at K.g (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:498:21614)
2024-11-16 12:45:11.622 [error] [5573] potential listener LEAK detected, having 2993 listeners already. MOST frequent listener (2990):: Error
    at m.create (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:433:58394)
    at J.q [as onWillDispose] (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:436:1100)
    at K.a (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:2559:50844)
    at K.h (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:498:21630)
    at K.g (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:498:21614)
2024-11-16 12:45:25.149 [error] [5573] potential listener LEAK detected, having 3081 listeners already. MOST frequent listener (3078):: Error
    at m.create (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:433:58394)
    at J.q [as onWillDispose] (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:436:1100)
    at K.a (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:2559:50844)
    at K.h (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:498:21630)
    at K.g (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:498:21614)


Maybe an extension is using resources. Open the command palette and search for Developer: Open Process Explorer to see if you can identify it. Also, try starting in disable-extensions mode to check if the issue persists.

Here are my version infos:

Version: 0.42.5
VSCode Version: 1.93.1
Commit: 001668006cc714afd397f4ef0d52862f5a095530
Date: 2024-11-14T00:33:36.512Z
Electron: 30.4.0
Chromium: 124.0.6367.243
Node.js: 20.15.1
V8: 12.4.254.20-electron.0
OS: Darwin arm64 23.6.0
As the others, I experienced this with a growing codebase. I don’t know if there is anything to be done here but with more files in my workspace, also the wait always increased (as well as the errors/failures to apply changes).

Try updating by downloading the latest version from this page.

changelog.cursor.com
Cursor - The IDE designed to pair-program with AI.
Cursor is an IDE designed to pair-program with AI. You can chat, edit and use agents specifically designed to help you code with codebase-wide understanding.

Sorry for the delay in providing a follow-up. After disabling all extensions (I had only the very basic ones) the issue went away. I have also updated Cursor since, so I can’t pinpoint the root cause accurately.

Thank you for your continued support.


Is Cursor extremely slow for you since the 0.49 update?

:white_check_mark: I’ve checked the forum and haven’t seen this issue reported yet.

:lady_beetle: Since updating to Cursor 0.49 (currently running version 0.49.4), the app has become extremely slow. Tool invocation now takes unusually long — sometimes over a minute — before anything happens. This lag occurs consistently across sessions.

:counterclockwise_arrows_button: Steps to reproduce:
1. Launch Cursor (version 0.49.4)
2. Attempt to use any AI tool (e.g., Sonnet 3.7 or ChatGPT-4.1)
3. Observe delay before tool becomes responsive (often > 60 seconds)

:laptop: System Info:
• Device: Mac mini (2024)
• Chip: Apple M4
• Memory: 16 GB
• OS: macOS Sequoia 15.3.2
• Cursor version: 0.49.4
• Tools used: Sonnet 3.7, ChatGPT-4.1

:prohibited: Yes, this issue significantly disrupts my workflow and makes Cursor nearly unusable.

I previously reported that Cursor became extremely slow after updating to version 0.49.4, especially when invoking AI tools (sometimes delays of over 60 seconds).

The solution in my case:

I disabled referencing past chats and deactivated all MCP servers I was using (see screenshots).

After doing this, Cursor returned to normal speed and tools became responsive again.

•	supabase (Tool: query)
•	mem0
•	browser-tools (Tools like getConsoleLogs, getNetworkErrors, runSEOAudit, etc.)

Same issue. For me, most often it is not even responding after 60 seconds - just “Generating…”.

Cursor has become unusable after the update - but was working very well before.


It’s the same for me. I use it on windows 10 though, but it is extremely slow, and crashes a lot. the last 2 updates has caused this.

Same here! Its painstakingly slow after the update. Worst is - it makes my whole system unresponsive . Extremely extremely annoying. Almost unusable.

Its gets stuck forever with this light green bar that keeps looping left to right, at the top left corner (below the menu) of the IDE.

I am on ubuntu 24.04.2

Cursor:
Version: 0.49.6
VSCode Version: 1.96.2
Commit: 0781e811de386a0c5bcb07ceb259df8ff8246a50
Date: 2025-04-25T04:44:33.500Z
Electron: 34.3.4
Chromium: 132.0.6834.210
Node.js: 20.18.3
V8: 13.2.152.41-electron.0
OS: Linux x64 6.8.0-58-generic


It is slow for me too. Not the query part, rather the entire UI is slow. I notice that light green bar that keeps looping left to right too. I been using 0.48.9, and that’s been great. However, 0.49.6 is still causing the issue for me. The UI is very laggy.

Hello,

I’m on the $20/month paid plan. The cursor program started out fast but now just typing my instructions into the composer is painfully slow. There is a 5-10 second delay from when I type to when my own text appears on the screen. I’m not even talking about AI processing. It seems the program has grinded to a halt. Anyone else experience this?

Hey, try starting in safe mode with cursor --disable-extensions. If that doesn’t work, try clearing the cache or reinstalling Cursor.

Extremely slow
I find cursor composer is really slow for me right now. I only have 1 python file with about 350 lines of code. It just says applying and it does not stop. My laptop is not the best so that might be an issue, but it is a bigger problem now than it was before.

Anyone else feel like Cursor AI's code generation is painfully slow lately?
Not sure if it's just me, but Cursor AI has been crazy slow at generating code recently. I’m on the Pro plan, so I expected things to be a bit snappier, but nah—it still takes like 20-30 seconds just to generate a simple snippet. Sometimes it even times out or gives me that "try again" message.

I’m working on a personal project—a little Django + React app—and even basic stuff like generating a serializer or fixing imports feels sluggish. Yesterday I asked it to clean up a function and I swear it took longer than if I had just rewritten it myself.

I love the context awareness and the way it integrates with my code, but man, the lag is starting to get frustrating.

Anyone else seeing this?

Cursor ai is too slow

Cursor AI runs extremely slow and hangs after a certain amount of usage. There is no problem when starting a project from scratch, but as it progresses, it uses a lot of memory. Sometimes it restarts the IDE.
Does anyone have a suggested solution for this?
win 10 16gb i7 9th gen

Same issue. I think this has something to do with chat history. Regardless Cursor should be able to handle it. This is kind of frustrating especially for large codebases. My only solution for now is to clone my codebase to a new folder directory. Everything seems smooth now. I hope they’ll come up …

When agent works cursor ıde getting slow

Anyone else finding Cursor ridiculously slow and error-prone lately?
I’ve been relying on Cursor for a while now, but over the past few days, it’s been incredibly sluggish—like, painfully slow. Tasks that should be instantaneous are taking ages, and sometimes it freezes up to the point where I can’t even get my work done. On top of that, I’ve run into a bunch of weird errors that make it feel less reliable than it should be. It just doesn’t seem to be adapting or getting smarter anymore, which was kind of the whole point, right?

I’m curious: is anyone else experiencing this? Are these issues widespread, or am I just unlucky? If you’ve noticed the same drop in performance and reliability, please share your experiences. Maybe if enough of us speak up, the team behind Cursor will take notice and push out some fixes or improvements.

Let’s make some noise and hope they listen!

The app I'm working on is getting quite big, so maybe that's why the chat window is pausing every 10 seconds or so (pausing for about 20 seconds each time) but i wonder if it's because the past chat is so long. I don't know how to clear it and resume with new chat. Or maybe because there are so many files loded into the context window. I can't try much when it's pausing all the time. Not sure how big the app is compared to other apps because i'm new at this stuff. Without the thousands of node files it would be like 100 files?


A solution is, or what worked for me, is to open a new chat and not use the reference of the old chat in the new, hope that helps
same here, new chat, no more crashing or lagging

Try turning off extensions, sometimes linters can slow down Cursor. Disable them or their checks. If that doesn’t help, try launching Cursor with cursor --disable-extensions.

Just to clear up some confusion - you can’t actually run out of gpt-4o-mini credits, it’s unlimited for all users. The sluggishness is likely coming from either extensions or system resources

Try running cursor --disable-extensions from the command line like @deanrie suggested - this should help identify if an extension is causing the slowdown

If that doesn’t help, check out our performance troubleshooting guide for other things to try

Cursor AI Agents Slow & Crashing
ssue Summary
Agents hang mid-task or take 10+ minutes for simple actions.
Frequent crashes, requiring a restart.
Small tasks (e.g., updating CSS) are unreasonably slow.
Expected Behavior
Agents should respond quickly and not freeze or crash

I think this issue is related to a large amount of chat history in your project. You can try renaming your project and opening it. If that helps, then the cause is clear. If your chat history is important to you, you can try exporting it using this extension: https://marketplace.visualstudio.com/items?itemName=SpecStory.specstory-vscode

Cursor ai chat and composer super slow
I send a question and answer never comes…

It’s just stuck there forever. Only change I did recently is downloading some extensions for reading Jupyter notebooks.

Hey, try booting in safe mode with: cursor --disable-extensions, and see if it resolves the issue. If it does, then the problem is caused by one of the extensions.

Hi everyone!
I’m new to cursor AI I’ve been using for three/four days and I liked it, and was planning to buy the pro version but for the last couple days it started be very slow.
I’m running it on a Mac OS, I won’t say it’s strong but it’s not slow or weak either. When running cursor, the laptop’s performance is pretty good and all other apps work just fine except for cursor itself, it’s hard to write, copy, click… even to quit the app!

if i dont want to rename the project? is there no “clear chat history or clear cache” ?

Unfortunately, at the moment, there is no such option. To clear the history, you need to completely reinstall Cursor. If you don’t want to rename your project, just move it to another location on your disk.


Cursor AI slow after upgrading to Pro Version

Hey, which version of Cursor are you using? Also, let me know which model you’re using. Could you also share any errors from the DevTools panel?

Hi, I am using the following version
Version: 0.49.6 (user setup)
VSCode Version: 1.96.2
Commit: 0781e811de386a0c5bcb07ceb259df8ff8246a50
Date: 2025-04-25T04:49:20.797Z
Electron: 34.3.4
Chromium: 132.0.6834.210
Node.js: 20.18.3
V8: 13.2.152.41-electron.0
OS: Windows_NT x64 10.0.26100

image
image
378×297 9.04 KB
I am not using any specific model, its set to auto select

image
image
299×248 7.83 KB
how do I access errors from DevTools panel

What happens if you try to select a specific model, like Claude? You can open the DevTools panel from the command palette.