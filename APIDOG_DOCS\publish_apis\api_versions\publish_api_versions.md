After creating an API version within a project, you can publish it by navigating to "Share Docs -> Publish". Once the API versions are set for the public, its content will be accessible via the API documentation link.

<Background>
![Publishing API Versions](https://assets.apidog.com/help/assets/images/publish-api-version-01-a9bed301be9784c18245df8eea794ee8.png)
</Background>
    
To add an API version for publishing, click the `Add` button next to the `Release Version`.

- **Source:** Select from the existing API versions in the project. The published document will display the content of the selected API versions.
- **Display Version Number:** The version number displayed to users on the published document.
- **Environments:** The environment in which readers can initiate debugging within the docs.
- **Slug:** The unique identifier appended to the link of the online API document. e.g., `https://example.apidog.io/2-0-0`.

<Background>
![Publishing API Versions](https://assets.apidog.com/help/assets/images/publish-api-version-02-7226bca59231d095f3ad28e230bc83b4.png)
</Background>
    
**Important:** Once you’ve added the versions for publication, they will appear in a list where you can arrange their order. This order determines how versions are presented to users on the online document. The version listed first is the default version. If users access the online document without specifying a slug, they will be directed to this default version.

<Background>
![Publishing API Versions](https://assets.apidog.com/help/assets/images/publish-api-version-03-083bc562515154cc7a625bdad6b71d32.png)
</Background>
    
When the release versions have been successfully set up, click on `Publish` next to the `Publish Status` to publish the document. This way, users can access the published document via the online link, where they can view all released versions and click on different version numbers to access the corresponding content.

<Background>
![Publishing API Versions](https://assets.apidog.com/help/assets/images/publish-api-version-04-5a8c4a80dabd1b6049ac3e4500e711d8.png)
</Background>