When you log into <PERSON>pidog via the web app, navigate to a project, and attempt to send a request to an endpoint, you might face Cross-Origin Resource Sharing (CORS) restrictions. To help users overcome this challenge, Apidog offers several types of request proxy agents:

<table border="1">
    <tr>
        <th>Request Proxy</th>
        <th>Description</th>
    </tr>
    <tr>
        <td>**Cloud Agent**</td>
        <td>
            - No installation required<br>
            - Supports sending HTTPS requests<br>
            - Requires connection to the Apidog cloud<br>
            - Cannot access private or local network environments
        </td>
    </tr>
    <tr>
        <td>**Browser Extension**</td>
        <td>
            - Installed locally<br>
            - Supports sending HTTPS requests<br>
            - Requires downloading the Apidog [Browser Extension](apidog://link/pages/821769)
        </td>
    </tr>
    <tr>
        <td>**Self-hosted Request Proxy Agent**</td>
        <td>
            - Deployed on a self-hosted server<br>
            - Supports sending HTTPS requests<br>
            - Requires server deployment<br>
            - Restricted by the network environment of the deployment server, possibly unable to access local network environments
        </td>
    </tr>
</table>

To choose which request proxy agent to use, go to the`Request Proxy`section located at the bottom right of the project page. By default, the system selects`Auto`mode. In this mode, if the browser extension is installed, it will be used to send requests. If not, the cloud agent will be employed for sending requests.

<Background>
![request-proxy-apidog.jpg](https://api.apidog.com/api/v1/projects/544525/resources/350181/image-preview)
</Background>

When sharing an API documentation or making it public, you can choose a request proxy for the online documentations to avoid cross-origin (CORS) issues. The options are:


<table>
  <thead>
    <tr>
      <th>Request Proxy</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><strong>Cloud Agent</strong></td>
      <td>Uses Apidog's cloud agent to handle requests. Note that this Agent cannot access endpoints from the intranet.</td>
    </tr>
    <tr>
      <td><strong>Browser Extension</strong></td>
      <td>Uses the [browser extension](apidog://link/pages/821769) installed by the user in their own browser as the Agent to send requests. If the user has not installed the extension, they will be prompted to install it to initiate requests.</td>
    </tr>
    <tr>
      <td><strong>No Proxy</strong></td>
      <td>Does not use a cross-origin proxy, meaning requests are sent directly from the user's browser to the endpoint. Ensure that the endpoint server is properly configured to avoid CORS (Cross-Origin Resource Sharing) issues.</td>
    </tr>
    <tr>
      <td><strong>Self-hosted request proxy Agent</strong></td>
      <td>Uses a self-hosted [request proxy agent](apidog://link/pages/780303) deployed by your team to handle requests.</td>
    </tr>
  </tbody>
</table>

<Background>
![request-proxy-online-documentation.png](https://api.apidog.com/api/v1/projects/544525/resources/350187/image-preview)
</Background>

In Apidog client, cross-origin restrictions can be completely avoided. However, we still provide the options to select a request proxy for situations where local network environments cannot access certain endpoints. There are two types of request proxies available:

<table border="1">
    <tr>
        <th>Request Proxy</th>
        <th>Description</th>
    </tr>
    <tr>
        <td>**Use System Proxy**</td>
        <td>
            Endpoint requests from Apidog will use the proxy configured in the [network proxy configuration](apidog://link/pages/640838). Note that only proxies specified in the`Proxy configurations for sending requests`are applied during requests. If the setting is configured to`Not Using Proxy`, requests will be sent directly from the client to the endpoint. 
        </td>
    </tr>
    <tr>
        <td>**Self-hosted Request Proxy**</td>
        <td>
            Endpoint requests from Apidog will use a specified [self-hosted request proxy](apidog://link/pages/780303) for handling requests.
        </td>
    </tr>
</table>

<Background>
![configure-request-proxy.jpg](https://api.apidog.com/api/v1/projects/544525/resources/350192/image-preview)
</Background>

:::note
When a [self-hosted request proxy](https://docs.apidog.com/use-request-proxy-agents-for-debugging-806444m0#set-different-request-proxy-agents-for-services-in-different-environments) is set up for a specific service (via its base URL) within a project environment, all endpoint requests to that service made through the Apidog web or client will automatically use this assigned proxy. This setting overrides any personal proxy configurations made in `Request Proxy`.
:::