# BlueJ - A free Java Development Environment designed for beginners, used by millions worldwide.

****BlueJ**** is a Windows-based platform for the ****Java Development Kit (JDK)****. It is a free Java environment started in 1999 by <PERSON> and <PERSON> at Monash University, Australia, as a successor to Blue. It requires installing JDK version 1.3 or higher before installing BlueJ. It can be freely downloaded from its official website of BlueJ. It was developed to support the learning and teaching of ****OOP (object-oriented programming)****. The objects can be interactively created and tested. It is a simple user interface. BlueJ has a simpler interface than most professional IDEs. It offers many tools that are specific to its educational goals. There are also standard development tools available, such as an editor, compiler, and runtime environment.

****Features of BlueJ:**** Some of the features of BlueJ are:

- The tasks can be performed more conveniently.
- It shows you a sample program to give a brief idea of programming.
- Debugging is easy, as it indicates errors at the bottom of the screen.
- It is a simpler interface than any other IDE.
- It has some new features that are not seen in any other IDE.
- BlueJ can run on Windows, macOS, [Linux](https://www.geeksforgeeks.org/linux-unix/linux-tutorial/), or any platform that runs Java.
- It allows you to interact with objects.

## How to install BlueJ?

Make sure you have JDK installed first before downloading and installing BlueJ; otherwise, it will not work.

1. Go to official page of BlueJ, click on link based on your preference whether it is Windows, Mac Os, or Linux.
2. BueJ setup box will appear, click on Next.
3. On the next page, click on Next.
4. Then click Install and Finish.

### How to start BlueJ?

1. Double-click the BlueJ icon available on the desktop to start Java.
2. The BlueJ window will appear on your screen.
3. The screen shows ‘Project’ in the top left corner. It is the creation of a folder that contains various BlueJ classes.
4. Click on it and select ‘New Project’ from the drop-down menu.
5. Enter file name (for example, Sample) and finally click on the create button.
6. The project folder created with the Sample name is created and is shown on your screen.
7. Now, click ‘New Class’ suppose named ‘welcome’, and enter the class name in the text box.
8. Finally, click OK to create a class icon. (Class name is basically program name where your program is saved on hard disk).
9. On double clicking icon, ‘welcome’, sample program is displayed
10. Then, to write your program, delete sample program available on window.
11. Then, type your program.
12. After completion of your program, click on ‘compile’ button.
13. If your program is error free then message will appear on screen at bottom pane as ‘Class Compiled- no syntax errors’. Now, you are ready to execute program.

****Execution of a program****

1. After successful compilation of program, select class icon and click right-handed mouse button. A drop-down menu appears. Select and click option void main(String args[]).
2. A “Method Call” window appears on screen, click OK.
3. Then, desired output will be shown on next screen.

****Supported Languages****

Java and Stride both languages are supported by BlueJ. [Java](https://www.geeksforgeeks.org/java/java/) support has been provided in BlueJ since its inception, while Stride support was added in 2017. BlueJ now uses Java 11+, which requires 64-bit [operating system](https://www.geeksforgeeks.org/operating-systems/what-is-an-operating-system/) which 95+% users will have. For 32-bit operating system download BlueJ 4.1.4. instead.

## User Interface Walkthrough

BlueJ has a clean and simple interface designed to help beginners learn Java programming. Here's a quick look at the main parts of the BlueJ window:

### 1. Class Diagram Window

- This is the large area you see when you open or create a project.
- It shows all your classes as boxes.
- You can right-click these boxes to compile, run, or edit your code.
- It also helps you see relationships between classes.

### 2. Code Editor

- Double-click on a class box to open the code editor.
- This is where you write and edit your Java programs.
- You can compile your code by clicking the ****"Compile"**** button at the top.
- Errors are shown at the bottom so you can fix them easily.

### 3. Terminal Window

- When your program prints something using `System.out.println()`, it appears here.
- It's like a black console where your program can show messages or ask for input.

### 4. Object Bench

- Found at the bottom of the main screen.
- You can create objects of your classes and test them directly.
- Right-click a class, choose a constructor, and the object appears here.
- Then, right-click the object to call methods without writing a full program.

> Must Read
> 
> - [10 Best Java IDE For Developers](https://www.geeksforgeeks.org/java/best-java-ide-for-developers/)
> - [Difference between BlueJ and JDK 1.3](https://www.geeksforgeeks.org/java/difference-between-bluej-and-jdk-1-3/)
> - [Introduction to Java Swing](https://www.geeksforgeeks.org/java/introduction-to-java-swing/)

## Conclusion

BlueJ is a beginner-friendly Java development environment designed to make learning [object-oriented programming](https://www.geeksforgeeks.org/dsa/introduction-of-object-oriented-programming/) simple and interactive. With its clean interface, visual class diagrams, and easy-to-use tools like the code editor and object bench, it helps students understand programming concepts without being overwhelmed. Whether you're writing, compiling, or running code, BlueJ makes the process smooth and intuitive—perfect for those just starting their journey in Java programming.

## Download and Install

#### Version 5.5.0, released 3 June 2025 (Many feature improvements, [see more](https://bluej.org/versions.html))

[

#### Windows

![](https://bluej.org/windows-logo.png)](https://github.com/k-pet-group/BlueJ-Greenfoot/releases/download/BLUEJ-RELEASE-5.5.0/BlueJ-windows-5.5.0.msi)

Requires 64-bit Windows, Windows 8 or later. Also available: [Standalone zip](https://github.com/k-pet-group/BlueJ-Greenfoot/releases/download/BLUEJ-RELEASE-5.5.0/BlueJ-windows-5.5.0.zip) suitable for USB drives.

[

#### macOS

![](https://bluej.org/apple-logo.png)](https://github.com/k-pet-group/BlueJ-Greenfoot/releases/download/BLUEJ-RELEASE-5.5.0/BlueJ-mac-aarch64-5.5.0.dmg)

Requires macOS 11 or later. Also available: [A version for Macs with Intel processors (2021 and earlier)](https://github.com/k-pet-group/BlueJ-Greenfoot/releases/download/BLUEJ-RELEASE-5.5.0/BlueJ-mac-x64-5.5.0.dmg) - see [this link](https://support.apple.com/en-gb/116943) for how to tell which processor you have.

[

#### Ubuntu/Debian

![](https://bluej.org/ubuntu-logo.png)](https://github.com/k-pet-group/BlueJ-Greenfoot/releases/download/BLUEJ-RELEASE-5.5.0/BlueJ-linux-x64-5.5.0.deb)

Requires 64-bit Intel processor running Debian 11 or Ubuntu 20.04 or later. Also available: [A version for ARM64 processors (e.g. Raspberry Pi).](https://github.com/k-pet-group/BlueJ-Greenfoot/releases/download/BLUEJ-RELEASE-5.5.0/BlueJ-linux-arm64-5.5.0.deb)

[

#### Other

![](https://bluej.org/java-logo.png)](https://github.com/k-pet-group/BlueJ-Greenfoot/releases/download/BLUEJ-RELEASE-5.5.0/BlueJ-generic-5.5.0.jar)

Please read the [Installation instructions.](https://bluej.org/generic-installation-instructions.html) (Works on most platforms with Java/JavaFX 21 support).

**Note:** BlueJ requires a 64-bit operating system, which 95+% of users will have.  
For 32-bit operating systems, [download BlueJ 4.1.4](https://bluej.org/versions.html) instead.



BlueJ Tutorial: https://bluej.org/tutorial/tutorial-v4.pdf
