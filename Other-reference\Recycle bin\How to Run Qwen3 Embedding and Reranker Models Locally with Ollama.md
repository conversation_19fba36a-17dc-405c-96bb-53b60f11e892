**Pro Tip:**
*Before you get lost in the world of embeddings and rerankers, remember: your API workflow deserves some love too! Apidog is the all-in-one platform for designing, testing, and documenting APIs—trusted by teams who want to build, automate, and launch faster. Give it a whirl and see how much smoother your dev life can be!*

# Run Qwen3 Embedding & Reranker Models Locally with Ollama: The DIY AI Powerhouse

Ever wish you could run state-of-the-art AI models on your own machine—no cloud, no privacy worries, no surprise bills? Thanks to open-source heroes and tools like Ollama, you can! And with Alibaba Cloud's Qwen3 models (especially the embedding and reranker flavors), you're set to build next-level search engines, RAG systems, or just flex your AI muscles—all from your laptop.

This guide is your ticket to local AI glory. We'll break down what embedding and reranker models actually do, walk you through Ollama setup, and show you how to wire up a full RAG pipeline in Python. Ready? Let's roll.

---

## Embedding vs. Reranker: The Dynamic Duo

Let's demystify the magic. In a RAG (Retrieval-Augmented Generation) system, you need both speed and smarts. Enter:

**1. The Embedding Model: Your Genius Librarian**

Imagine a library with a million books and no catalog. Nightmare, right? The embedding model is your super-librarian: it reads everything and files each document in a multi-dimensional "meaning space."

In practice, it turns text into a dense vector of numbers. Similar meanings? Vectors are close together. When you ask a question, the model finds the closest matches—fast.

**2. The Reranker: The Nitpicky Expert**

The embedding model's first pass is quick, but not always perfect. The reranker is your subject-matter expert: it takes the top picks and does a deep, pairwise analysis, scoring each for true relevance. The result? The best answers bubble to the top.

---

## Meet Qwen3: Open-Source AI, Supercharged

Alibaba Cloud's Qwen3 models aren't just another set of LLMs—they're leaderboard-topping, multilingual, and come in sizes for every machine. Why are they awesome?

- **Top of the charts:** Qwen3-Embedding-8B is a star on the MTEB benchmark.
- **Multilingual magic:** 100+ languages supported.
- **Flexible:** Choose from 0.6B, 4B, or 8B parameters, and pick your quantization for the perfect speed/accuracy tradeoff.
- **Instruction-aware:** Tweak them for your domain or task.

---

## Step 1: Set Up Ollama

Ollama makes running LLMs locally a breeze. Here's how to get started:

**Install Ollama:**
- On macOS/Linux:
```bash
curl -fsSL https://ollama.com/install.sh | sh
```
- On Windows: [Download the installer](https://ollama.com/download).

Check it's working:
```bash
ollama --version
```

**Download the Qwen3 Models:**
```bash
# Embedding model (8B)
ollama pull dengcao/Qwen3-Embedding-8B:Q5_K_M

# Reranker model (4B)
ollama pull dengcao/Qwen3-Reranker-4B:Q5_K_M
```

List your models:
```bash
ollama list
```

---

## Step 2: Generate Embeddings Like a Pro

Let's turn text into vectors! You'll need the `ollama` Python library:
```bash
pip install ollama
```

Here's a quick script:
```python
import ollama

EMBEDDING_MODEL = 'dengcao/Qwen3-Embedding-8B:Q5_K_M'

def get_embedding(text: str):
    try:
        response = ollama.embeddings(
            model=EMBEDDING_MODEL,
            prompt=text
        )
        return response['embedding']
    except Exception as e:
        print(f"An error occurred: {e}")
        return None

sentence = "Ollama makes it easy to run LLMs locally."
embedding = get_embedding(sentence)

if embedding:
    print(f"Embedding for: '{sentence}'")
    print(f"First 5 dimensions: {embedding[:5]}")
    print(f"Total dimensions: {len(embedding)}")
```

---

## Step 3: Rerank Like a Boss

The reranker uses the `chat` endpoint. We'll prompt it to act as a relevance judge:
```python
import ollama

RERANKER_MODEL = 'dengcao/Qwen3-Reranker-4B:Q5_K_M'

def rerank_document(query: str, document: str) -> float:
    prompt = f"""
    You are an expert relevance grader. Your task is to evaluate if the
    following document is relevant to the user's query.
    Please answer with a simple 'Yes' or 'No'.
    Query: {query}
    Document: {document}
    """
    try:
        response = ollama.chat(
            model=RERANKER_MODEL,
            messages=[{'role': 'user', 'content': prompt}],
            options={'temperature': 0.0}
        )
        answer = response['message']['content'].strip().lower()
        if 'yes' in answer:
            return 1.0
        return 0.0
    except Exception as e:
        print(f"An error occurred during reranking: {e}")
        return 0.0

user_query = "How do I run models locally?"
doc1 = "Ollama is a tool for running large language models on your own computer."
doc2 = "The capital of France is Paris."

score1 = rerank_document(user_query, doc1)
score2 = rerank_document(user_query, doc2)

print(f"Relevance of Doc 1: {'Relevant' if score1 > 0.5 else 'Not Relevant'} (Score: {score1})")
print(f"Relevance of Doc 2: {'Relevant' if score2 > 0.5 else 'Not Relevant'} (Score: {score2})")
```

---

## Step 4: Build a Mini RAG Pipeline

Let's put it all together! We'll use `numpy` for similarity search:
```bash
pip install numpy
```

```python
import ollama
import numpy as np

EMBEDDING_MODEL = 'dengcao/Qwen3-Embedding-8B:Q5_K_M'
RERANKER_MODEL = 'dengcao/Qwen3-Reranker-4B:Q5_K_M'

documents = [
    "The Qwen3 series of models was developed by Alibaba Cloud.",
    "Ollama provides a simple command-line interface for running LLMs.",
    "A reranker model refines search results by calculating a precise relevance score.",
    "To install Ollama on Linux, you can use a curl command.",
    "Embedding models convert text into numerical vectors for semantic search.",
]

corpus_embeddings = []
print("Generating embeddings for the document corpus...")
for doc in documents:
    response = ollama.embeddings(model=EMBEDDING_MODEL, prompt=doc)
    corpus_embeddings.append(response['embedding'])
print("Embeddings generated.")

def cosine_similarity(v1, v2):
    return np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))

user_query = "How do I install Ollama?"
query_embedding = ollama.embeddings(model=EMBEDDING_MODEL, prompt=user_query)['embedding']

retrieval_scores = [cosine_similarity(query_embedding, emb) for emb in corpus_embeddings]
top_k_indices = np.argsort(retrieval_scores)[::-1][:3]

print("\n--- Initial Retrieval Results (before reranking) ---")
for i in top_k_indices:
    print(f"Score: {retrieval_scores[i]:.4f} - Document: {documents[i]}")

retrieved_docs = [documents[i] for i in top_k_indices]

print("\n--- Reranking the top results ---")
reranked_scores = [rerank_document(user_query, doc) for doc in retrieved_docs]

reranked_results = sorted(zip(retrieved_docs, reranked_scores), key=lambda x: x[1], reverse=True)

print("\n--- Final Results (after reranking) ---")
for doc, score in reranked_results:
    print(f"Relevance Score: {score:.2f} - Document: {doc}")
```

---

## That's a Wrap!

You've just built a local, open-source RAG pipeline with Qwen3 and Ollama—no cloud required. Tweak the models, try different quantization levels, and plug this into your own apps. The future of private, powerful AI is in your hands.

And don't forget: when you're ready to design, test, and document your APIs, Apidog is the sidekick you need. Happy hacking!
