# Meta Title:
Cursor Pro Plan Rate Limits Explained: How They Work & What You Need to Know

# Meta Description:
Learn how Cursor Pro Plan's rate limits work, what burst and local limits mean, and how to manage your usage for a smooth coding experience.

# Excerpt:
Discover how Cursor Pro Plan's rate limits operate, including burst and local limits, reset times, and your options if you hit a limit. Stay productive with this clear guide.

---

# Cursor Pro Plan Rate Limits: Everything You Need to Know

In the rapidly evolving world of AI-powered coding, understanding how rate limits work is key to getting the most out of your Cursor Pro Plan. Cursor meters rate limits based on underlying compute usage, and these limits reset every few hours. Here's a clear breakdown of what that means for you.

## What Are Cursor Rate Limits?

Cursor applies rate limits to all plans on Agent. These limits are designed to balance fair usage and system performance. There are two main types of rate limits:

- **Burst Rate Limits:**
  - Allow for short, high-activity sessions.
  - Can be used at any time for particularly bursty work.
  - Slow to refill after use.
- **Local Rate Limits:**
  - Refill fully every few hours.
  - Designed for steady, ongoing usage.

Both types of limits are based on the total compute you use during a session. This includes:
- The model you select
- The length of your messages
- The size of files you attach
- The length of your current conversation

## How Do Rate Limits Work?

- **All plans are subject to rate limits on Agent.**
- **Limits reset every few hours,** so you can resume work after a short break.
- **Compute usage varies:** Heavier models, longer messages, and larger files will use up your limits faster.

## What Happens If You Hit a Limit?

If you use up both your local and burst limits, Cursor will notify you and present three options:

1. **Switch to models with higher rate limits** (e.g., Sonnet has higher limits than Opus).
2. **Upgrade to a higher tier** (such as the Ultra plan).
3. **Enable usage-based pricing** to pay for requests that exceed your rate limits.

## Can I Stick with the Old Pro Plan?

Yes! If you prefer a simple, lump-sum request system, you can keep the legacy Pro Plan. Just go to your [Dashboard](https://cursor.com/dashboard) > Settings > Advanced to control this setting. For most users, the new Pro plan with rate limits will be preferable.

## Quick Reference Table

| Limit Type        | Description                                 | Reset Time         |
|-------------------|---------------------------------------------|--------------------|
| Burst Rate Limit  | For short, high-activity sessions           | Slow to refill     |
| Local Rate Limit  | For steady, ongoing usage                   | Every few hours    |

---

# Conclusion: Mastering Cursor Pro Plan Rate Limits

Cursor Pro Plan's rate limits are designed to give you flexibility and control. By understanding how burst and local limits work, you can plan your sessions, manage your compute usage, and avoid interruptions. If you ever hit a limit, you have clear options to keep your workflow moving. And if you prefer the old system, you can always opt out. Stay productive and make the most of your Cursor Pro Plan with this knowledge at your fingertips.
