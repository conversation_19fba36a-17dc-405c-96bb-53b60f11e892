For test steps imported from endpoints/endpoint cases, their request data can be synchronized with the associated endpoint spec/cases.

![Sync Data](https://assets.apidog.com/uploads/help/2024/04/02/d06fdbe8d87ee69d71bdf791fa07f9d1.png)

When importing endpoints/endpoint cases into test steps, you can choose **Manual** or **Automated** mode.

The **Manual** mode is suitable for scenarios where multiple people collaborate, meaning that in a project, the endpoint specification, development, and testing processes all have a clear division of labor and each process has a dedicated person in charge. These processes require data independence to avoid unintended changes affecting each other. After ensuring the stability of the endpoint spec/case data, the tester can manually synchronize it with the test steps.

The **Automated** mode is suitable for small teams or independent developers, meaning that in a project, the endpoint specification, development, and testing processes are all handled by a single role, improving the efficiency of endpoint maintenance and testing. When the endpoints/endpoint cases in "Endpoint Management" change, the endpoint/endpoint case data in the test steps will also be adjusted accordingly, ensuring data consistency between "Endpoint Management" and "Automated Testing".

![Sync Mode](https://assets.apidog.com/uploads/help/2024/04/02/c67a270fd4d56302cadf7a74faa9954c.png)

## Sync relationship

| Test step import method                | Associated data      | Test step sync mode                                          | Sync content                                                               |
|--------------------------------------------|----------------------|--------------------------------------------------------------|----------------------------------------------------------------------------|
| Imported from endpoint - Manual mode       | Endpoint Spec        | **Manual mode** (click "Update" button in test steps)        | ✅ Changes in "parameter name", "Path", "Method", "pre/post-actions", etc.<br><br>❌ "Parameter values". |
| Imported from endpoint - Automated mode    | Endpoint Spec        | **Automated mode** (when endpoint spec is updated and saved) | ✅ Changes in "parameter name", "Path", "Method", "pre/post-actions", etc.<br><br>❌ "Parameter values". |
| Imported from endpoint case - Copy         | Endpoint Spec        | **Manual mode** (click "Update" button in test steps)        | ✅ Changes in "parameter name", "Path", "Method", "pre/post-actions", etc.<br><br>❌ "Parameter values". |
| Imported from endpoint case - Reference    | Endpoint Case        | **Automated mode** (when endpoint case is updated and saved) | ✅ Reference endpoint case data, any changes in the endpoint case will affect the content of this test step. |

## Manual sync mode

When the Manual mode is selected, the associated button will appear in "blue". When there are any changes in the endpoint spec in the "Endpoint Management", the data in the test steps will not be updated in real-time. Testers can manually click the "Update" button in the "Test Steps" details to synchronize the changes from the endpoints/endpoint cases to the test steps.

This mode is suitable for testing scenarios where testers have a large number of requests for fine-tuning the request parameters, avoiding unintended influences on these test data and ensuring isolation between test data and data from the endpoints being developed.

![Manual Sync](https://assets.apidog.com/uploads/help/2024/04/02/b15481b3349d697909fd1f3c06eb51f0.png)
![Sync Data](https://assets.apidog.com/help/assets/images/sync-data-39c91169de5760902173561e52979eca.png)

In **Manual** mode, the dropdown menu of the associated button also supports manually filtering the scope of data synchronization.

![Filter Sync](https://assets.apidog.com/uploads/help/2024/04/02/1672b5067e6a13f65e7305c3bb8883ea.png)

### Sync endpoint specs

For test steps generated by importing `Endpoint → Manual update` or importing from an endpoint case → Copy, the associated content is the endpoint spec. These test steps can be synchronized with the endpoint spec content by clicking the "Sync Now" button on the page.

:::tip[]
After triggering a manual sync, the test step will synchronize data from the endpoint spec, including "parameter name", "pre/post-scripts", etc. Note that the sync object is the endpoint spec, and if the request parameters in the endpoint spec do not have sample values set, the synchronized content will not include "parameter values".
:::

Saving and updating the data in "Endpoint Spec" will not affect associated test steps.

![Sync Endpoint spec](https://assets.apidog.com/help/assets/images/sync-api-63f236630138b541338c4f235b7e0277.png)

Clicking the "Update" button in the test step will trigger the data synchronization mechanism, and the button will indicate "Synced". After confirming that the synchronized data is correct, you need to click the "Save" button in the upper-right corner to save the test step.

![Save Sync](https://assets.apidog.com/uploads/help/2024/04/02/84f9e3ffc8bcd5d0a53e4712188866a6.png)

### Batch sync data

If most of the data in the current test steps are using the Manual mode, and the tester has confirmed that the data in these endpoint specs/cases is correct and wants to quickly synchronize the data to the test steps, they can click the "Update" button on the test step page to perform a batch sync operation.

![Batch Sync](https://assets.apidog.com/uploads/help/2024/04/02/d222a28754f932172c72e1d598ba79ec.png)

## Automated sync mode

The associated button will appear in orange. As mentioned before, this mode helps solo developers with controlling the flow and keeping track of it by themselves.

![Automated Sync](https://assets.apidog.com/uploads/help/2024/04/02/2b34f7b27a9d978e096934c092bcdabe.png)

### Sync endpoint specs

For test steps associated with endpoint spec, when the sync mode is set to Auto Sync, if there are changes in the endpoint spec and it is saved, the data will be updated in real-time in the test step. The synchronized content is consistent with the Manual sync mode described before.

![Auto Sync Endpoints](https://assets.apidog.com/uploads/help/2024/04/02/a0414fe32a0ee53e01169e338c073084.png)

In Automated mode, any inconsistencies between the data in the test step and the endpoint spec will be highlighted. Testers can choose to "Revert" to maintain complete consistency with the endpoint spec, or they can choose to "Save to Document", which will overwrite the data in the endpoint spec with the data from the test step to maintain consistency.

![Highlight Discrepancies](https://assets.apidog.com/uploads/help/2024/04/02/e897e5d274f04bdd1b88f6fd63e26532.png)

### Sync endpoint cases

To ensure consistency and uniqueness in managing data for endpoint cases that are referenced in test steps, adjustments should be made to the referenced endpoint case in the "Endpoint Management" system so that both the endpoint cases and test steps utilize the same request data.

After clicking the "Edit" button in the instruction on the test step page, you will be taken to the associated endpoint case page to make adjustments. The test step will automatically synchronize the data after the changes to the endpoint case, including "parameter name", "parameter value", "pre/post-scripts", and all other request content.

![Sync Endpoint Cases](https://assets.apidog.com/uploads/help/2024/04/02/3c62b2526a9b8cb7a1c84bc349f4c6a7.png)