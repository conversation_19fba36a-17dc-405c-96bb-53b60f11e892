Request basics
The Apidog client supports sending API requests with HTTP, GraphQL, SOAP, gRPC, or other popular protocols. You can create requests in Apidog, send them to observe the responses, and save them for team collaboration.

Create a new request
To create a request in Apidog, you only need to click "+" - "New Request"



You can fill in the necessary parts of the request according to the API spec into the Apidog interface, such as the request method, URL, request parameters, authorization, and so on.

Using this method, you can create HTTP, GraphQL, and SOAP requests. Learn more about creating WebSocket and gRPC requests.

Request URL
To make a request in Apidog, you need to specify the URL representing the API endpoint you're connecting. Each operation you can perform using an API is typically associated with an endpoint, which has a particular URL.

If you're building an API, the URL will usually consist of the base location plus a path. For instance, in the request URL https://api.example.com/user/12, https://api.example.com is the base URL, and /user/12 is the endpoint path.

If you're using a public API, your API provider will provide the URLs you need in their developer documentation.

Learn more about how to read an API documentation.


You can enter query parameters in the URL field, or you can enter them in the Params tab. If your request uses path parameters, you can enter them in the URL field. Learn more about query parameters/path parameters.

Apidog will automatically add http:// to the beginning of your URL if you don't specify a protocol.

Request methods
In Apidog, you can select which request method to use on the left side of the request URL. The default method is GET. Here are the most common request methods:

GET: Retrieve data from a specified resource. It should not have any effect on the data.

POST: Submit data to be processed to a specified resource. Often used to create new resources.

PUT: Update existing resources. It replaces the entire resource with the data sent in the request.

PATCH: Apply partial modifications to a resource.

DELETE: Request the removal of a resource.

In API design, the same URL can be requested with different methods, representing different operations on the same resource. For example, GET /user/{id} typically retrieves the user information for this id, while PUT /user/{id} updates the user information for this id.

Send the request
After you specify the request protocol, method, and URL, add any other details required by the API you're sending the request to:

Provide any parameters, body data, or request headers that need to be sent with the request.

Set up any required authentication and authorization.

You also have the option to use cookies with your requests by selecting Cookies.

Once you've entered all the request details, select Send to transmit the request to the API server. You can view the response from the server in the response pane. Learn more about API response structure in Apidog.

You can view requests you've sent in History in the sidebar and send them again.

