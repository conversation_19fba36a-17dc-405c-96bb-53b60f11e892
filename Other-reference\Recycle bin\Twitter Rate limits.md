# Rate limits

Every day, many thousands of developers make requests to the X API. To help manage the sheer volume of these requests, limits are placed on the number of requests that can be made. These limits help provide the reliable and scalable API that our developer community relies on.

The maximum number of requests allowed is based on a time interval, typically over a specified period or window of time. The most common interval is fifteen minutes. For example, an endpoint with a limit of 900 requests per 15 minutes allows up to 900 requests in any 15-minute interval.

Rate limits depend on the authentication method. For instance, if using [OAuth 1.0a User Context](https://docs.x.com/resources/fundamentals/authentication), each set of users’ [Access Tokens](https://docs.x.com/resources/fundamentals/authentication#obtaining-access-tokens-using-3-legged-oauth-flow) has its own rate limit per period. Alternatively, if using [OAuth 2.0 Bearer Token](https://docs.x.com/resources/fundamentals/authentication#oauth-2-0), your app will have its own separate limit per time period. When these limits are exceeded, an error is returned.

## 

[​

](https://docs.x.com/x-api/fundamentals/rate-limits#table-of-contents)

Table of contents

- [X API v2 rate limits](https://docs.x.com/x-api/fundamentals/rate-limits#v2-limits)
- [X API Enterprise rate limits](https://docs.x.com/x-api/fundamentals/rate-limits#v2-limits-enterprise)
- [Rate limits and authentication method](https://docs.x.com/x-api/fundamentals/rate-limits#auth)
- [HTTP headers and response codes](https://docs.x.com/x-api/fundamentals/rate-limits#headers-and-codes)
- [Recovering from rate limits](https://docs.x.com/x-api/fundamentals/rate-limits#recovering)
- [Tips to avoid being rate limited](https://docs.x.com/x-api/fundamentals/rate-limits#tips)

## 

[​

](https://docs.x.com/x-api/fundamentals/rate-limits#x-api-v2-rate-limits)

X API v2 rate limits

The following table lists the rate limits of each X API paid plan. These limits are also available in the [developer portal](https://docs.x.com/resources/fundamentals/developer-portal)’s products section.

| Endpoint                                                                                                                                  | Pro Limit                                                                           | Basic Limit                                                                        | Free Limit                                                                      |
| ----------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------- | ------------------------------------------------------------------------------- |
| **Tweets**                                                                                                                                |                                                                                     |                                                                                    |                                                                                 |
| [DELETE /2/tweets/:id](https://docs.x.com/x-api/posts/post-delete-by-post-id)                                                             | 50 requests / 15 mins<br>**PER USER**                                               | 5 requests / 15 mins<br>**PER USER**                                               | 17 requests / 24 hours<br>**PER USER**<br>17 requests / 24 hours<br>**PER APP** |
| [DELETE /2/users/:id/likes/:tweet_id](https://docs.x.com/x-api/posts/causes-the-user-in-the-path-to-unlike-the-specified-post)            | 50 requests / 15 mins<br>**PER USER**                                               | 100 requests / 24 hours<br>**PER USER**                                            | 1 requests / 15 mins<br>**PER USER**                                            |
| [DELETE /2/users/:id/retweets/:tweet_id](https://docs.x.com/x-api/posts/causes-the-user-in-the-path-to-unretweet-the-specified-post)      | 50 requests / 15 mins<br>**PER USER**                                               | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| [GET /2/tweets](https://docs.x.com/x-api/posts/post-lookup-by-post-ids)                                                                   | 900 requests / 15 mins<br>**PER USER**<br>450 requests / 15 mins<br>**PER APP**     | 15 requests / 15 mins<br>**PER USER**<br>15 requests / 15 mins<br>**PER APP**      | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [GET /2/tweets/:id](https://docs.x.com/x-api/posts/post-lookup-by-post-id)                                                                | 900 requests / 15 mins<br>**PER USER**<br>450 requests / 15 mins<br>**PER APP**     | 15 requests / 15 mins<br>**PER USER**<br>15 requests / 15 mins<br>**PER APP**      | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [GET /2/tweets/:id/liking_users](https://docs.x.com/x-api/users/returns-user-objects-that-have-liked-the-provided-post-id)                | 75 requests / 15 mins<br>**PER USER**<br>75 requests / 15 mins<br>**PER APP**       | 5 requests / 15 mins<br>**PER USER**<br>25 requests / 15 mins<br>**PER APP**       | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [GET /2/tweets/:id/quote_tweets](https://docs.x.com/x-api/posts/retrieve-posts-that-quote-a-post)                                         | 75 requests / 15 mins<br>**PER USER**<br>75 requests / 15 mins<br>**PER APP**       | 5 requests / 15 mins<br>**PER USER**<br>5 requests / 15 mins<br>**PER APP**        | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [GET /2/tweets/:id/retweeted_by](https://docs.x.com/x-api/users/returns-user-objects-that-have-retweeted-the-provided-post-id)            | 75 requests / 15 mins<br>**PER USER**<br>75 requests / 15 mins<br>**PER APP**       | 5 requests / 15 mins<br>**PER USER**<br>5 requests / 15 mins<br>**PER APP**        | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [GET /2/tweets/counts/all](https://docs.x.com/x-api/posts/full-archive-search-counts)                                                     | 300 requests / 15 mins<br>**PER APP**                                               |                                                                                    |                                                                                 |
| [GET /2/tweets/counts/recent](https://docs.x.com/x-api/posts/recent-search-counts)                                                        | 300 requests / 15 mins<br>**PER APP**                                               | 5 requests / 15 mins<br>**PER APP**                                                | 1 requests / 15 mins<br>**PER APP**                                             |
| [GET /2/tweets/search/all](https://docs.x.com/x-api/posts/full-archive-search-counts)                                                     | 1 requests / second<br>**PER USER**<br>1 requests / second<br>**PER APP**           |                                                                                    |                                                                                 |
| [GET /2/tweets/search/recent](https://docs.x.com/x-api/posts/recent-search-counts)                                                        | 300 requests / 15 mins<br>**PER USER**<br>450 requests / 15 mins<br>**PER APP**     | 60 requests / 15 mins<br>**PER USER**<br>60 requests / 15 mins<br>**PER APP**      | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [GET /2/tweets/search/stream](https://docs.x.com/x-api/posts/filtered-stream)                                                             | 50 requests / 15 mins<br>**PER APP**                                                |                                                                                    |                                                                                 |
| [GET /2/tweets/search/stream/rules](https://docs.x.com/x-api/posts/rules-lookup)                                                          | 450 requests / 15 mins<br>**PER APP**                                               |                                                                                    |                                                                                 |
| [GET /2/users/:id/liked_tweets](https://docs.x.com/x-api/posts/returns-post-objects-liked-by-the-provided-user-id)                        | 75 requests / 15 mins<br>**PER USER**<br>75 requests / 15 mins<br>**PER APP**       | 5 requests / 15 mins<br>**PER USER**<br>5 requests / 15 mins<br>**PER APP**        | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [GET /2/users/:id/mentions](https://docs.x.com/x-api/posts/user-mention-timeline-by-user-id)                                              | 300 requests / 15 mins<br>**PER USER**<br>450 requests / 15 mins<br>**PER APP**     | 10 requests / 15 mins<br>**PER USER**<br>15 requests / 15 mins<br>**PER APP**      | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [GET /2/users/:id/timelines/reverse_chronological](https://docs.x.com/x-api/posts/user-home-timeline-by-user-id)                          | 180 requests / 15 mins<br>**PER USER**                                              | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| [GET /2/users/:id/tweets](https://docs.x.com/x-api/posts/user-posts-timeline-by-user-id)                                                  | 900 requests / 15 mins<br>**PER USER**<br>1500 requests / 15 mins<br>**PER APP**    | 5 requests / 15 mins<br>**PER USER**<br>10 requests / 15 mins<br>**PER APP**       | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [GET /2/users/reposts_of_me](https://docs.x.com/x-api/users/returns-repost-of-user)                                                       | 75 requests / 15 mins<br>**PER USER**                                               | 75 requests / 15 mins<br>**PER USER**                                              | 1 requests / 15 mins<br>**PER USER**                                            |
| [POST /2/tweets](https://docs.x.com/x-api/posts/creation-of-a-post)                                                                       | 100 requests / 15 mins<br>**PER USER**<br>10000 requests / 24 hours<br>**PER APP**  | 100 requests / 24 hours<br>**PER USER**<br>1667 requests / 24 hours<br>**PER APP** | 17 requests / 24 hours<br>**PER USER**<br>17 requests / 24 hours<br>**PER APP** |
| [POST /2/tweets/search/stream/rules](https://docs.x.com/x-api/posts/adddelete-rules)                                                      | 100 requests / 15 mins<br>**PER APP**                                               |                                                                                    |                                                                                 |
| [POST /2/users/:id/likes](https://docs.x.com/x-api/posts/causes-the-user-in-the-path-to-like-the-specified-post)                          | 1000 requests / 24 hours<br>**PER USER**                                            | 200 requests / 24 hours<br>**PER USER**                                            | 1 requests / 15 mins<br>**PER USER**                                            |
| [POST /2/users/:id/retweets](https://docs.x.com/x-api/posts/causes-the-user-in-the-path-to-repost-the-specified-post)                     | 50 requests / 15 mins<br>**PER USER**                                               | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| [PUT /2/tweets/:tweet_id/hidden](https://docs.x.com/x-api/posts/hide-replies)                                                             | 50 requests / 15 mins<br>**PER USER**                                               | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| **Users**                                                                                                                                 |                                                                                     |                                                                                    |                                                                                 |
| [DELETE /2/users/:source_user_id/following/:target_user_id](https://docs.x.com/x-api/users/unfollow-user)                                 | 50 requests / 15 mins<br>**PER USER**                                               | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| [DELETE /2/users/:source_user_id/muting/:target_user_id](https://docs.x.com/x-api/users/unmute-user-by-user-id)                           | 50 requests / 15 mins<br>**PER USER**                                               | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| [GET /2/users](https://docs.x.com/x-api/users/user-lookup-by-ids)                                                                         | 900 requests / 15 mins<br>**PER USER**<br>300 requests / 15 mins<br>**PER APP**     | 100 requests / 24 hours<br>**PER USER**<br>500 requests / 24 hours<br>**PER APP**  | 1 requests / 24 hours<br>**PER USER**<br>1 requests / 24 hours<br>**PER APP**   |
| [GET /2/users/:id](https://docs.x.com/x-api/users/user-lookup-by-id)                                                                      | 900 requests / 15 mins<br>**PER USER**<br>300 requests / 15 mins<br>**PER APP**     | 100 requests / 24 hours<br>**PER USER**<br>500 requests / 24 hours<br>**PER APP**  | 1 requests / 24 hours<br>**PER USER**<br>1 requests / 24 hours<br>**PER APP**   |
| [GET /2/users/:id/blocking](https://docs.x.com/x-api/users/returns-user-objects-that-are-blocked-by-provided-user-id)                     | 15 requests / 15 mins<br>**PER USER**                                               | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| [GET /2/users/:id/muting](https://docs.x.com/x-api/users/returns-user-objects-that-are-muted-by-the-provided-user-id)                     | 15 requests / 15 mins<br>**PER USER**                                               | 100 requests / 24 hours<br>**PER USER**                                            | 1 requests / 24 hours<br>**PER USER**                                           |
| [GET /2/users/by](https://docs.x.com/x-api/users/user-lookup-by-usernames)                                                                | 900 requests / 15 mins<br>**PER USER**<br>300 requests / 15 mins<br>**PER APP**     | 100 requests / 24 hours<br>**PER USER**<br>500 requests / 24 hours<br>**PER APP**  | 1 requests / 24 hours<br>**PER USER**<br>1 requests / 24 hours<br>**PER APP**   |
| [GET /2/users/by/username/:username](https://docs.x.com/x-api/users/user-lookup-by-username)                                              | 900 requests / 15 mins<br>**PER USER**<br>300 requests / 15 mins<br>**PER APP**     | 100 requests / 24 hours<br>**PER USER**<br>500 requests / 24 hours<br>**PER APP**  | 3 requests / 15 mins<br>**PER USER**<br>3 requests / 15 mins<br>**PER APP**     |
| [GET /2/users/me](https://docs.x.com/x-api/users/user-lookup-me)                                                                          | 75 requests / 15 mins<br>**PER USER**                                               | 250 requests / 24 hours<br>**PER USER**                                            | 25 requests / 24 hours<br>**PER USER**                                          |
| [GET /2/users/search](https://docs.x.com/x-api/users/user-search)                                                                         | 900 requests / 15 mins<br>**PER USER**<br>300 requests / 15 mins<br>**PER APP**     |                                                                                    |                                                                                 |
| [POST /2/users/:id/following](https://docs.x.com/x-api/users/follow-user)                                                                 | 50 requests / 15 mins<br>**PER USER**                                               | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| [POST /2/users/:id/muting](https://docs.x.com/x-api/users/mute-user-by-user-id)                                                           | 50 requests / 15 mins<br>**PER USER**                                               | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| **Spaces**                                                                                                                                |                                                                                     |                                                                                    |                                                                                 |
| [GET /2/spaces](https://docs.x.com/x-api/spaces/space-lookup-up-space-ids)                                                                | 300 requests / 15 mins<br>**PER USER**<br>300 requests / 15 mins<br>**PER APP**     | 5 requests / 15 mins<br>**PER USER**<br>25 requests / 15 mins<br>**PER APP**       | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [GET /2/spaces/:id](https://docs.x.com/x-api/spaces/space-lookup-by-space-id)                                                             | 300 requests / 15 mins<br>**PER USER**<br>300 requests / 15 mins<br>**PER APP**     | 5 requests / 15 mins<br>**PER USER**<br>25 requests / 15 mins<br>**PER APP**       | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [GET /2/spaces/:id/buyers](https://docs.x.com/x-api/spaces/retrieve-the-list-of-users-who-purchased-a-ticket-to-the-given-space)          | 300 requests / 15 mins<br>**PER USER**<br>300 requests / 15 mins<br>**PER APP**     | 5 requests / 15 mins<br>**PER USER**<br>25 requests / 15 mins<br>**PER APP**       | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [GET /2/spaces/:id/tweets](https://docs.x.com/x-api/spaces/retrieve-posts-from-a-space)                                                   | 300 requests / 15 mins<br>**PER USER**<br>300 requests / 15 mins<br>**PER APP**     | 5 requests / 15 mins<br>**PER USER**<br>25 requests / 15 mins<br>**PER APP**       | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [GET /2/spaces/by/creator_ids](https://docs.x.com/x-api/spaces/space-lookup-by-their-creators)                                            | 300 requests / 15 mins<br>**PER USER**<br>1 requests / second<br>**PER APP**        | 5 requests / 15 mins<br>**PER USER**<br>25 requests / second<br>**PER APP**        | 1 requests / second<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**      |
| [GET /2/spaces/search](https://docs.x.com/x-api/spaces/search-for-spaces)                                                                 | 300 requests / 15 mins<br>**PER USER**<br>300 requests / 15 mins<br>**PER APP**     | 5 requests / 15 mins<br>**PER USER**<br>25 requests / 15 mins<br>**PER APP**       | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| **Direct Messages**                                                                                                                       |                                                                                     |                                                                                    |                                                                                 |
| [DELETE /2/dm_events/:id](https://docs.x.com/x-api/direct-messages/delete-dm)                                                             | 1500 requests / 24 hours<br>**PER USER**<br>4000 requests / 24 hours<br>**PER APP** | 200 requests / 15 mins<br>**PER USER**<br>2500 requests / 24 hours<br>**PER APP**  |                                                                                 |
| [GET /2/dm_conversations/:dm_conversation_id/dm_events](https://docs.x.com/x-api/direct-messages/get-dm-events-for-a-dm-conversation)     | 15 requests / 15 mins<br>**PER USER**                                               | 1 requests / 24 hours<br>**PER USER**                                              |                                                                                 |
| [GET /2/dm_conversations/with/:participant_id/dm_events](https://docs.x.com/x-api/direct-messages/get-dm-events-for-a-dm-conversation)    | 15 requests / 15 mins<br>**PER USER**                                               | 1 requests / 24 hours<br>**PER USER**                                              |                                                                                 |
| [GET /2/dm_events](https://docs.x.com/x-api/direct-messages/get-recent-dm-events)                                                         | 15 requests / 15 mins<br>**PER USER**                                               | 1 requests / 24 hours<br>**PER USER**                                              |                                                                                 |
| [GET /2/dm_events/:id](https://docs.x.com/x-api/direct-messages/get-dm-events-by-id)                                                      | 15 requests / 15 mins<br>**PER USER**                                               | 5 requests / 24 hours<br>**PER USER**                                              |                                                                                 |
| [POST /2/dm_conversations](https://docs.x.com/x-api/direct-messages/create-a-new-dm-conversation)                                         | 15 requests / 15 mins<br>**PER USER**<br>1440 requests / 24 hours<br>**PER APP**    | 1 requests / 24 hours<br>**PER USER**<br>1 requests / 24 hours<br>**PER APP**      |                                                                                 |
| [POST /2/dm_conversations/:dm_conversation_id/messages](https://docs.x.com/x-api/direct-messages/send-a-new-message-to-a-dm-conversation) | 15 requests / 15 mins<br>**PER USER**<br>1440 requests / 24 hours<br>**PER APP**    | 1 requests / 24 hours<br>**PER USER**<br>1 requests / 24 hours<br>**PER APP**      |                                                                                 |
| [POST /2/dm_conversations/with/:participant_id/messages](https://docs.x.com/x-api/direct-messages/send-a-new-message-to-a-user)           | 1440 requests / 24 hours<br>**PER USER**<br>1440 requests / 24 hours<br>**PER APP** | 1 requests / 24 hours<br>**PER USER**<br>1 requests / 24 hours<br>**PER APP**      |                                                                                 |
| **Lists**                                                                                                                                 |                                                                                     |                                                                                    |                                                                                 |
| [DELETE /2/lists/:id](https://docs.x.com/x-api/lists/delete-list)                                                                         | 300 requests / 15 mins<br>**PER USER**                                              | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| [DELETE /2/lists/:id/members/:user_id](https://docs.x.com/x-api/lists/remove-a-list-member)                                               | 300 requests / 15 mins<br>**PER USER**                                              | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| [DELETE /2/users/:id/followed_lists/:list_id](https://docs.x.com/x-api/lists/unfollow-a-list)                                             | 50 requests / 15 mins<br>**PER USER**                                               | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| [DELETE /2/users/:id/pinned_lists/:list_id](https://docs.x.com/x-api/lists/unpin-a-list)                                                  | 50 requests / 15 mins<br>**PER USER**                                               | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| [GET /2/lists/:id](https://docs.x.com/x-api/lists/list-lookup-by-list-id)                                                                 | 75 requests / 15 mins<br>**PER USER**<br>75 requests / 15 mins<br>**PER APP**       | 5 requests / 15 mins<br>**PER USER**<br>5 requests / 15 mins<br>**PER APP**        | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [GET /2/lists/:id/members](https://docs.x.com/x-api/users/returns-user-objects-that-are-members-of-a-list-by-the-provided-list-id)        | 900 requests / 15 mins<br>**PER USER**<br>900 requests / 15 mins<br>**PER APP**     | 5 requests / 15 mins<br>**PER USER**<br>25 requests / 15 mins<br>**PER APP**       | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [GET /2/lists/:id/tweets](https://docs.x.com/x-api/posts/list-posts-timeline-by-list-id)                                                  | 900 requests / 15 mins<br>**PER USER**<br>900 requests / 15 mins<br>**PER APP**     | 5 requests / 15 mins<br>**PER USER**<br>25 requests / 15 mins<br>**PER APP**       |                                                                                 |
| [GET /2/users/:id/list_memberships](https://docs.x.com/x-api/lists/get-a-users-list-memberships)                                          | 75 requests / 15 mins<br>**PER USER**<br>75 requests / 15 mins<br>**PER APP**       | 5 requests / 15 mins<br>**PER USER**<br>25 requests / 15 mins<br>**PER APP**       | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [GET /2/users/:id/owned_lists](https://docs.x.com/x-api/lists/get-a-users-owned-lists)                                                    | 15 requests / 15 mins<br>**PER USER**<br>15 requests / 15 mins<br>**PER APP**       | 100 requests / 24 hours<br>**PER USER**<br>500 requests / 24 hours<br>**PER APP**  | 1 requests / 24 hours<br>**PER USER**<br>1 requests / 24 hours<br>**PER APP**   |
| [GET /2/users/:id/pinned_lists](https://docs.x.com/x-api/lists/get-a-users-pinned-lists)                                                  | 15 requests / 15 mins<br>**PER USER**<br>15 requests / 15 mins<br>**PER APP**       | 100 requests / 24 hours<br>**PER USER**<br>500 requests / 24 hours<br>**PER APP**  | 1 requests / 24 hours<br>**PER USER**<br>1 requests / 24 hours<br>**PER APP**   |
| [POST /2/lists](https://docs.x.com/x-api/lists/create-list)                                                                               | 300 requests / 15 mins<br>**PER USER**                                              | 100 requests / 24 hours<br>**PER USER**                                            | 1 requests / 24 hours<br>**PER USER**                                           |
| [POST /2/lists/:id/members](https://docs.x.com/x-api/lists/add-a-list-member)                                                             | 300 requests / 15 mins<br>**PER USER**                                              | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| [POST /2/users/:id/followed_lists](https://docs.x.com/x-api/lists/follow-a-list)                                                          | 50 requests / 15 mins<br>**PER USER**                                               | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| [POST /2/users/:id/pinned_lists](https://docs.x.com/x-api/lists/pin-a-list)                                                               | 50 requests / 15 mins<br>**PER USER**                                               | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| [PUT /2/lists/:id](https://docs.x.com/x-api/lists/update-list)                                                                            | 300 requests / 15 mins<br>**PER USER**                                              | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| **Bookmarks**                                                                                                                             |                                                                                     |                                                                                    |                                                                                 |
| [DELETE /2/users/:id/bookmarks/:tweet_id](https://docs.x.com/x-api/bookmarks/remove-a-bookmarked-post)                                    | 50 requests / 15 mins<br>**PER USER**                                               | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| [GET /2/users/:id/bookmarks](https://docs.x.com/x-api/bookmarks/bookmarks-by-user)                                                        | 180 requests / 15 mins<br>**PER USER**                                              | 10 requests / 15 mins<br>**PER USER**                                              | 1 requests / 15 mins<br>**PER USER**                                            |
| [GET /2/users/:id/bookmarks/folders](https://docs.x.com/x-api/posts/bookmarks/introduction)                                               | 50 requests / 15 mins<br>**PER USER**<br>50 requests / 15 mins<br>**PER APP**       | 5 requests / 15 mins<br>**PER USER**<br>5 requests / 15 mins<br>**PER APP**        | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [GET /2/users/:id/bookmarks/folders/:folder_id](https://docs.x.com/x-api/posts/bookmarks/introduction)                                    | 50 requests / 15 mins<br>**PER USER**<br>50 requests / 15 mins<br>**PER APP**       | 5 requests / 15 mins<br>**PER USER**<br>5 requests / 15 mins<br>**PER APP**        | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [POST /2/users/:id/bookmarks](https://docs.x.com/x-api/bookmarks/add-post-to-bookmarks)                                                   | 50 requests / 15 mins<br>**PER USER**                                               | 5 requests / 15 mins<br>**PER USER**                                               | 1 requests / 15 mins<br>**PER USER**                                            |
| **Compliance**                                                                                                                            |                                                                                     |                                                                                    |                                                                                 |
| [GET /2/compliance/jobs](https://docs.x.com/x-api/compliance/list-compliance-jobs)                                                        | 150 requests / 15 mins<br>**PER APP**                                               | 5 requests / 15 mins<br>**PER APP**                                                | 1 requests / 15 mins<br>**PER APP**                                             |
| [GET /2/compliance/jobs/:job_id](https://docs.x.com/x-api/compliance/get-compliance-job)                                                  | 150 requests / 15 mins<br>**PER APP**                                               | 5 requests / 15 mins<br>**PER APP**                                                | 1 requests / 15 mins<br>**PER APP**                                             |
| [POST /2/compliance/jobs](https://docs.x.com/x-api/compliance/create-compliance-job)                                                      | 150 requests / 15 mins<br>**PER APP**                                               | 15 requests / 15 mins<br>**PER APP**                                               | 1 requests / 15 mins<br>**PER APP**                                             |
| **Usage**                                                                                                                                 |                                                                                     |                                                                                    |                                                                                 |
| [GET /2/usage/tweets](https://docs.x.com/x-api/usage/post-usage)                                                                          | 50 requests / 15 mins<br>**PER APP**                                                | 50 requests / 15 mins<br>**PER APP**                                               | 1 requests / 15 mins<br>**PER APP**                                             |
| **Trends**                                                                                                                                |                                                                                     |                                                                                    |                                                                                 |
| [GET /2/trends/by/woeid/:id](https://docs.x.com/x-api/trends/trends)                                                                      | 75 requests / 15 mins<br>**PER APP**                                                | 15 requests / 15 mins<br>**PER APP**                                               |                                                                                 |
| [GET /2/users/personalized_trends](https://docs.x.com/x-api/trends/introduction)                                                          | 10 requests / 15 mins<br>**PER USER**<br>200 requests / 15 mins<br>**PER APP**      | 1 requests / 15 mins<br>**PER USER**<br>20 requests / 15 mins<br>**PER APP**       | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 24 hours<br>**PER APP**    |
| **Communities**                                                                                                                           |                                                                                     |                                                                                    |                                                                                 |
| [GET /2/communities/:id](https://docs.x.com/x-api/communities/lookup/introduction)                                                        | 300 requests / 15 mins<br>**PER USER**<br>300 requests / 15 mins<br>**PER APP**     | 1 requests / 15 mins<br>**PER USER**<br>25 requests / 15 mins<br>**PER APP**       | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |
| [GET /2/communities/search](https://docs.x.com/x-api/communities/search/introduction)                                                     | 300 requests / 15 mins<br>**PER USER**<br>300 requests / 15 mins<br>**PER APP**     | 1 requests / 15 mins<br>**PER USER**<br>25 requests / 15 mins<br>**PER APP**       | 1 requests / 15 mins<br>**PER USER**<br>1 requests / 15 mins<br>**PER APP**     |

## 

[​

](https://docs.x.com/x-api/fundamentals/rate-limits#rate-limits-and-authentication-method)

Rate limits and authentication method

Rate limits are set at both the developer App and user access token levels:

- **[OAuth 2.0 Bearer Token](https://docs.x.com/resources/fundamentals/authentication/oauth-2-0/application-only): App rate limit** This method allows you to make a certain number of requests on behalf of your developer App. When using this authentication method, limits are determined by the requests made with a Bearer Token.
  
  - Example: With a limit of 450 requests per 15-minute interval, you can make 450 requests on behalf of your App within that interval.

- **[OAuth 1.0a User Context](https://docs.x.com/resources/fundamentals/authentication/oauth-1-0a/obtaining-user-access-tokens): User rate limit** This method allows requests to be made on behalf of a X user identified by the user Access Token. For example, if retrieving private metrics from Posts, authenticate with user Access Tokens for that user, generated using the [3-legged OAuth flow](https://docs.x.com/resources/fundamentals/authentication#obtaining-access-tokens-using-3-legged-oauth-flow).
  
  - Example: With a limit of 900 requests per 15 minutes per user, you can make up to 900 requests per user in that time frame.

…

## 

[​

](https://docs.x.com/x-api/fundamentals/rate-limits#http-headers-and-response-codes)

HTTP headers and response codes

Use HTTP headers to understand where your application stands within a given rate limit, based on the most recent request made.

- `x-rate-limit-limit`: rate limit ceiling for the endpoint
- `x-rate-limit-remaining`: remaining requests for the 15-minute window
- `x-rate-limit-reset`: remaining time before the rate limit resets (in UTC epoch seconds)

### 

[​

](https://docs.x.com/x-api/fundamentals/rate-limits#error-responses)

Error Responses

If an application exceeds the rate limit for an endpoint, the API will return a [HTTP 429 “Too Many Requests”](http://tools.ietf.org/html/rfc6585) response with the following error message in the response body:

Copy

```json
{ "errors": [ { "code": 88, "message": "Rate limit exceeded" } ] }
```

## 

[​

](https://docs.x.com/x-api/fundamentals/rate-limits#recovering-from-a-rate-limit)

Recovering from a rate limit

When these rate limits are exceeded, a 429 ‘Too many requests’ error is returned from the endpoint. As discussed below, when rate limit errors occur, a best practice is to examine HTTP headers that indicate when the limit resets and pause requests until then.  
When a “too many requests” or rate-limiting error occurs, the frequency of making requests needs to be slowed down. When a rate limit error is hit, the `x-rate-limit-reset:` HTTP header can be checked to learn when the rate-limiting will reset  

. Another common pattern is based on exponential backoff, where the time between requests starts off small (for example, a few seconds), then doubled before each retry. This is continued until a request is successful, or some reasonable maximum time between requests is reached (for example, a few minutes).  

Ideally, the client-side is self-aware of existing rate limits and can pause requests until the currently exceeded window expires. If you exceed a 15-minute limit, then waiting a minute or two before retrying makes sense.  

Note that beyond these limits on the number of requests, the Standard Basic level of access provides up to 500,000 Posts per month from the recent search and filtered stream endpoints. If you have exceeded the monthly limit on the number of Posts, then it makes more sense for your app to raise a notification and know its enrollment day of the month and hold off requests until that day.

### 

[​

](https://docs.x.com/x-api/fundamentals/rate-limits#tips-to-avoid-being-rate-limited)

Tips to avoid being rate limited

The tips below are there to help you code defensively and reduce the possibility of being rate limited. Some application features that you may want to provide are simply impossible in light of rate-limiting, especially around the freshness of results. If real-time information is an aim of your application, look into the filtered and sampled stream endpoints.

#### 

[​

](https://docs.x.com/x-api/fundamentals/rate-limits#caching)

Caching

Store API responses if expecting frequent usage. Instead of calling the API on every page load, cache the response locally.

#### 

[​

](https://docs.x.com/x-api/fundamentals/rate-limits#prioritize-active-users)

Prioritize active users

If your site keeps track of many X users (for example, fetching their current status or statistics about their X usage), consider only requesting data for users who have recently signed into your site.

#### 

[​

](https://docs.x.com/x-api/fundamentals/rate-limits#adapt-to-the-search-results)

Adapt to the search results

If your application monitors a high volume of search terms, query less often for searches that have no results than for those that do. By using a back-off you can keep up to date on queries that are popular but not waste cycles requesting queries that very rarely change. Alternatively, consider using the filtered stream endpoint and filter with your search queries.

#### 

[​

](https://docs.x.com/x-api/fundamentals/rate-limits#denylist)

Denylist

If an application abuses the rate limits, it will be denied. Denied apps are unable to get a response from the X API. If you or your application has been denied and you think there has been a mistake, you can use our [Platform Support forms](https://support.x.com/forms/platform) to request assistance. Please include the following information:

1. Explain why you think your application was denied.
2. If you are no longer being rate limited, describe in detail how you fixed the problem.
