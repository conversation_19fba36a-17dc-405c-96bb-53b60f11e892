Dear Apidog Users, 
We hope this message finds you all well!  As May draws to a close, we want to thank you for being a valued part of the Apidog community. This month, our team has been hard at work delivering new features and thoughtful improvements to help you build, debug, and collaborate more efficiently. Let’s dive into what’s new!

🌱 Schema Reference Insights: On the Schema page, you can now see which endpoints, schemas, and Markdown documents reference the current schema — streamlining the management of dependencies and documentation.
[Image]
https://assets.apidog.com/uploads/help/2025/04/30/304c312ba6612b40f3d40c9d4422856d.gif
📋 Markdown Preview for LLM SSE Debugging: Working with AI and LLMs? Debugging those SSE streaming responses just got a major upgrade — Preview merged message content in a clean Markdown format for an incredibly readable experience. 
[Image]
https://canny-assets.io/images/5c1a3d838c1aeb646c0c9117cf8b0c08.gif
🧫 Experimental Feature to Reduce Memory Usage: Try out new experimental features to send API requests using independent processes and optimize local service process memory, reducing resource usage.
[Image]
🔥 Instant gRPC Endpoint Import via Server Reflection: Say goodbye to manual uploads! Now you can import endpoints directly into your gRPC projects via Server Reflection. Get to testing faster and more seamlessly.
🌿 API Version Selection for Import/Export: When importing/exporting project data, you can now select specific API versions, giving you more control over your project’s evolution.
🔒 PKCE Authorization for HashiCorp Vault OIDC: PKCE authorization mode is now supported when integrating with HashiCorp Vault using OIDC, enhancing security and compatibility.
📂 gRPC Endpoint Grouping: gRPC projects now support grouping endpoints by .proto file or package name, making large projects easier to navigate and manage.
🧭 Redis Database Connection Modes: You can now choose between Standalone or Cluster mode when configuring Redis database connections to fit your deployment needs. 
⚙️ Smarter JSON Parameter Parsing: Pasting JSON into the request parameter name field? It’s now automatically parsed into parameter names and values for faster setup.
🔔 Notifications for Batch-running Test Scenarios: When batch-running test scenarios, you can now configure whether to receive notifications upon completion.
🔑 Custom Security Scheme Combinations: Create and apply custom security scheme combinations to handle complex authentication needs, making API authentication more flexible and secure.
⚡️ IPv4-First Domain Resolution: During endpoint debugging, domain name resolution will now prioritize IPv4 over IPV6, improving compatibility and reliability.
🛰️ Supports for HTTPS Endpoints of  TLS v1.0 & v1.1: Endpoint debugging now supports HTTPS endpoints using TLS v1.0 and v1.1, ensuring broader compatibility with legacy systems.

🌟 Looking Ahead
We’re committed to continuous improvement and innovation. Your feedback and ideas are what drive us forward—so keep them coming! If you have suggestions or want to connect with other API developers, join our vibrant Discord or Slack communities.

P.S. Explore the full details of all these updates in the Apidog Changelog! 🚀

Happy API Building!
Regards,
The Apidog Team