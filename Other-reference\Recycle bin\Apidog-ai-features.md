| 中文                                                   | 英文                                                                                                                                                     |
| ---------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------ |
| AI 功能                                                | AI Features                                                                                                                                            |
| 通过使用你的模型供应商 API Key 来在 {appName} 中使用 AI 能力。          | Use your model provider API Key to enable AI capabilities in {appName}.                                                                                |
| 开启 AI 功能                                             | Enable AI Features                                                                                                                                     |
| 如果关闭，团队中的全部项目都会隐藏 AI 功能入口，所有成员都不可使用 AI 功能            | Once turned off, AI functionality will be removed from every project, and team members will lose access to AI tools.                                   |
| 模型供应商                                                | Model Providers                                                                                                                                        |
| 请先添加模型供应商，添加完模型供应商后才能使用 AI 功能<br><br><br>            | Please add the model provider first. After adding the model provider, the AI feature can be used.                                                      |
| 编辑                                                   | Edit                                                                                                                                                   |
| 添加供应商                                                | Add Provider                                                                                                                                           |
| 默认模型                                                 | Default Model                                                                                                                                          |
| 当成员没有手动指定模型时，所有 AI 功能默认使用的模型。                        | The model that all AI features default to when a member has not manually specified a model.                                                            |
| 自动选择                                                 | Auto Select                                                                                                                                            |
| 系统自动根据使用场景选择效果最好的模型                                  | The system automatically selects the best-performing model based on the scenario.                                                                      |
| 功能 & 提示词                                             | Functions & Prompt                                                                                                                                     |
| 生成接口用例                                               | Generate Endpoint Cases                                                                                                                                |
| 针对指定的接口，让 AI 读取完整接口定义后生成合适的接口用例                      | Let AI read the complete endpoint definition and generate suitable endpoint cases for the specified endpoint.                                          |
| AI 辅助参数修改                                            | AI-assisted parameter modification                                                                                                                     |
| 针对指定位置的接口参数，让 AI 进行字段说明、Mock 规则填写等操作                 | For the specified endpoint parameters, let the AI perform field description/mock rule filling, etc.                                                    |
| 生成测试场景                                               | Generate Test Scenarios                                                                                                                                |
| 根据输入的测试需求，AI 自行从全部项目接口中选择出合适的进行测试场景编排并生成<br><br><br> | Based on the test requirements provided, the AI independently selects suitable project endpoints from all available ones and generates test scenarios. |
| 编辑供应商                                                | Edit Provider                                                                                                                                          |
| API Key                                              | API Key                                                                                                                                                |
| 输入 API Key                                           | Enter API Key                                                                                                                                          |
| 测试                                                   | Test                                                                                                                                                   |
| 连接成功                                                 | Connect successful.                                                                                                                                    |
| 连接失败                                                 | Connect failed.                                                                                                                                        |
| API 前置 URL                                           | API Base URL                                                                                                                                           |
| 输入 API 前置 URL，例如：https://api.openai.com              | Enter API base URL, e.g. https://api.openai.com                                                                                                        |
| 模型列表                                                 | Model List                                                                                                                                             |
| 已启用                                                  | Enabled                                                                                                                                                |
| 已停用                                                  | Disabled                                                                                                                                               |
| 模型名称                                                 | Model Name                                                                                                                                             |
| 输入模型的名称，例如：DeepSeek Reasoner 或 DeepSeek Chat         | Enter the name of the model, e.g. DeepSeek Reasoner, DeepSeek Chat                                                                                     |
| 模型 ID                                                | Model ID                                                                                                                                               |
| 输入模型 ID，例如：deepseek-reasoner 或 deepseek-chat         | Enter the ID of the model, e.g. deepseek-reasoner, deepseek-chat                                                                                       |
| 添加模型                                                 | Add Model                                                                                                                                              |
| 这个页面有未保存的改动，确定关闭吗？                                   | This page has unsaved changes, Are you sure to close?                                                                                                  |
| 确定移除此供应商？                                            | Are you sure to remove this provider?                                                                                                                  |
| AI 模型                                                | AI Model                                                                                                                                               |
| 提示词                                                  | Prompt                                                                                                                                                 |
| 使用预设                                                 | Use Preset                                                                                                                                             |
| 自定义                                                  | Customize                                                                                                                                              |
| 插入变量                                                 | Insert Variables                                                                                                                                       |
| 系统提示词                                                | System Instruction                                                                                                                                     |
| 快捷提示词                                                | Quick Prompts                                                                                                                                          |
| 添加                                                   | Add                                                                                                                                                    |
| 标题                                                   | Title                                                                                                                                                  |
| OpenAI                                               | OpenAI                                                                                                                                                 |
| Azure OpenAI                                         | Azure OpenAI                                                                                                                                           |
| Anthropic                                            | Anthropic                                                                                                                                              |
| Gemini                                               | Gemini                                                                                                                                                 |
| 火山引擎                                                 | Volcengine                                                                                                                                             |
| 阿里云百炼                                                | Alibaba Cloud                                                                                                                                          |
| 腾讯混元                                                 | Tencent Hunyuan                                                                                                                                        |
| 硅基流动                                                 | Silicon Flow                                                                                                                                           |
| 深度求索                                                 | DeepSeek                                                                                                                                               |
| 自定义 API 配置                                           | Custom API Configuration                                                                                                                               |
| 支持所有兼容 OpenAI API 规范的模型提供商                           | Support all model providers compatible with OpenAI API specifications                                                                                  |
