## Web-Based API Client

- [Apidog](https://apidog.com/) - All-in-one API debugging and testing tools.  web version, desktop version, offline version, and self-host version available.
- [Restfox](https://restfox.dev/) ([repo](https://github.com/flawiddsouza/Restfox)) - Offline-first web HTTP client
- [Hoppscotch](https://hoppscotch.io/) ([repo](https://github.com/hoppscotch/hoppscotch)) - Open source API development ecosystem
- [Firecamp](https://firecamp.io/) ([repo](https://github.com/firecamp-dev/firecamp)) - Open Source Postman Alternative inspired by VS Code DX
- [gRPC UI](https://github.com/fullstorydev/grpcui) - An interactive web UI for gRPC, along the lines of postman
- [Yaade](https://docs.yaade.io/) ([repo](https://github.com/EsperoTech/yaade)) - Yaade is an open-source, self-hosted, collaborative API development environment
- [Prestige](https://prestige.dev/) ([repo](https://github.com/sharat87/prestige)) - A text-based in-browser HTTP client, an interface-less Postman alternative
- [Requestly](https://requestly.com/) ([repo](https://github.com/requestly/requestly)) - A Browser extension with API Client, API Mocking & API Interception and Modification capabilities.


