# Best 10 Cursor MCP Servers for Vibe Coders in 2025

Let’s face it—modern development moves fast. And with so many tools out there, staying efficient without bouncing between apps is a game-changer. That’s where MCP (Model Context Protocol) servers come in. They work behind the scenes to connect powerful features like web scraping, design previews, browser automation, or even document conversion right into your Cursor editor.

Instead of switching tabs or writing boilerplate code, you get smart, seamless tools that just work inside your flow. In this guide, we’re highlighting 10 of the best open-source MCP servers you can start using today to boost your productivity in Cursor.

#### 1. Apidog MCP Server

[Apidog MCP Server](https://docs.apidog.com/apidog-mcp-server) is built specifically for developers working with APIs. It connects AI assistants like Cursor directly to your API documentation, so you can build smarter and faster without switching tabs.

Unlike generic context plugins, Apidog focuses on making your OpenAPI specs, endpoint data, and schema details instantly accessible to the AI. Want to generate a TypeScript interface or build a Python client based on your real endpoints? Just ask Cursor—it already knows your API’s structure.

**Top Features:**

- Syncs with Apidog projects, public docs, or local OpenAPI files
- Enables natural language queries like “What’s the response structure for /users?”
- Caches specs locally for fast, offline-ready development
- Keeps AI suggestions accurate and project-aware

![](https://assets.apidog.com/blog-next/2025/05/image-452.png)

### Configuring MCP in Cursor

Edit MCP Config FileOpen Cursor editor, click the settings icon (top-right), select "MCP" from the left menu, then click "+ Add new global MCP server".

![](https://assets.apidog.com/blog-next/2025/05/image-458.png)

Add ConfigurationPaste the following configuration in the opened `mcp.json`. Remember to replace `<access-token>` and `<project-id>` with your own:

```
{
  "mcpServers": {
    "API specification": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "apidog-mcp-server@latest",
        "--project=<project-id>"
      ],
      "env": {
        "APIDOG_ACCESS_TOKEN": "<access-token>"
      }
    }
  }
}
```

Verify ConfigurationTest the connection by asking the AI (in Agent mode):

```
Please fetch API specification via MCP and tell me how many endpoints exist in the project
```

Successful connection is confirmed when AI returns your Apidog project's API information.

![](https://assets.apidog.com/blog-next/2025/05/image-459.png)

Apidog MCP Server removes one of the biggest productivity killers in API dev: jumping back and forth between docs and code. It turns API specs into an intelligent, queryable knowledge base.

#### 2. **Browserbase MCP Server**

![](https://assets.apidog.com/blog-next/2025/05/image-454.png)

**Overview:** Browserbase MCP Server empowers AI assistants to interact with web pages in a cloud browser environment, handling dynamic content and complex user interactions.

**Key Features:**

- Cloud-based browser sessions with session management
- Full-page and element-specific screenshots
- JavaScript execution and DOM manipulation
- Parallel browser instances for multitasking

![](https://assets.apidog.com/blog-next/2025/05/image-455.png)

This server is particularly useful for testing web applications and automating repetitive website interactions.

To add Browserbase to Cursor, first obtain API credentials from [browserbase.io](https://browserbase.io/), then add the following configuration to your `.cursor/mcp.json` file:

```
{
  "mcpServers": {
    "browserbase": {
      "command": "node",
      "args": ["path/to/mcp-server-browserbase/browserbase/dist/index.js"],
      "env": {
        "BROWSERBASE_API_KEY": "your_api_key_here",
        "BROWSERBASE_PROJECT_ID": "your_project_id_here"
      }
    }
  }
}
```

#### 3. **Magic MCP Server**

![](https://assets.apidog.com/blog-next/2025/05/image-456.png)

[Magic MCP Server](https://github.com/21st-dev/magic-mcp) integrates generative AI capabilities directly into your development workflow. It allows for:

- Generation of placeholder images during front-end development
- Transformation of text into various formats or styles
- Summarization of content for documentation
- Creation of code samples from natural language descriptions

By leveraging OpenAI's API, Magic MCP brings powerful AI-driven features to assist in various development tasks.

```
{
  "mcpServers": {
    "@21st-dev/magic": {
      "command": "npx",
      "args": ["-y", "@21st-dev/magic@latest", "API_KEY=\"your-api-key\""]
    }
  }
}
```

Config file locations:

- Cursor: `~/.cursor/mcp.json`
- Windsurf: `~/.codeium/windsurf/mcp_config.json`
- Cline: `~/.cline/mcp_config.json`
- Claude: `~/.claude/mcp_config.json`

#### 4. **Opik MCP Server**

![](https://assets.apidog.com/blog-next/2025/05/image-457.png)

Opik MCP Server connects your AI assistant to real-time web search and knowledge exploration. Instead of relying on outdated context, Opik allows developers to fetch the latest insights, facts, or references directly from the internet, making the AI more informed and useful for research-heavy tasks.

**Key Features:**

- Real-time web search integration
- Summarization of external content
- Source-aware responses for citations
- Useful for documentation, fact-checking, and content generation

This server simplifies the management and visualization of machine learning experiments.

### Cursor Integration

To integrate with Cursor IDE, create a `.cursor/mcp.json` file in your project directory with the following configuration:

```
{
  "mcpServers": {
    "opik": {
      "command": "/path/to/node",
      "args": [
        "/path/to/opik-mcp/build/index.js",
        "--apiUrl",
        "https://www.comet.com/opik/api",
        "--apiKey",
        "YOUR_API_KEY",
        "--workspace",
        "default",
        "--debug",
        "true"
      ],
      "env": {
        "OPIK_API_BASE_URL": "https://www.comet.com/opik/api",
        "OPIK_API_KEY": "YOUR_API_KEY",
        "OPIK_WORKSPACE_NAME": "default",
      }
    }
  }
}
```

Replace `/path/to/node` with the path to your Node.js executable and `/path/to/opik-mcp` with the path to your opik-mcp installation. Also replace `YOUR_API_KEY` with your actual Opik API key.

#### 5. **Figma Context MCP Server**

![](https://assets.apidog.com/blog-next/2025/05/image-460.png)

Figma Context MCP Server bridges design and code by feeding Figma data directly to your coding assistant. It allows developers to query designs, understand layouts, and generate UI code based on what’s already designed—no more back-and-forth with the design team.

**Key Features:**

- Access to Figma frames, layers, and text
- Convert designs to component code (e.g., React, HTML/CSS)
- Analyze spacing, colors, and typography
- Identify visual inconsistencies or missing elements
- Translate Figma structure into development-ready insights

This integration ensures design consistency and streamlines the development process.

```
{
  "mcpServers": {
    "figma": {
      "command": "npx",
      "args": ["-y", "figma-context-mcp"],
      "env": {
        "FIGMA_ACCESS_TOKEN": "your_figma_token_here"
      }
    }
  }
}
```

#### 6. **Pandoc MCP Server**

![](https://assets.apidog.com/blog-next/2025/05/image-461.png)

Pandoc MCP Server brings powerful document conversion capabilities to your development environment. It allows for the transformation of documents between various formats, such as:

- Markdown to PDF, HTML, DOCX, and more
- Processing academic papers or research notes
- Generating reports from different source materials
- Building publishing workflows

This server is invaluable for developers working with diverse documentation formats.

To add the Excel MCP server to your Cursor configuration, add this to your `.cursor/mcp.json` file:

```json
{
  "mcpServers": {
    "excel": {
      "command": "npx",
      "args": ["-y", "excel-mcp-server"]
    }
  }
}
```

#### 7. **Excel MCP Server**

![](https://assets.apidog.com/blog-next/2025/05/image-466.png)

[Excel MCP Server](https://github.com/negokaz/excel-mcp-server) turns your spreadsheets into accessible data sources for AI. It enables your AI assistant to read, query, and even write to Excel files in real time, helping you automate report generation, data transformation, and logic implementation directly in code.

**Key Features:**

- Read and parse Excel sheets, rows, and formulas
- Generate visualizations or summaries from spreadsheet data
- Modify cells and formulas programmatically
- Export processed data back to Excel
- Great for automating repetitive reporting tasks

This server is particularly beneficial for processing data stored in Excel spreadsheets and generating reports.

To add the Excel MCP server to your Cursor configuration, add this to your `.cursor/mcp.json` file:

```json
{
  "mcpServers": {
    "excel": {
      "command": "npx",
      "args": ["-y", "excel-mcp-server"]
    }
  }
}
```

#### 8. **Mindmap MCP Server**

![](https://assets.apidog.com/blog-next/2025/05/image-464.png)

[Mindmap MCP Server](https://github.com/YuChenSSR/mindmap-mcp-server) gives your AI assistant access to structured thinking maps. This is especially useful when brainstorming features, planning architectures, or outlining documents. The assistant can read, interpret, and even suggest changes to mindmaps, turning raw ideas into actionable steps.

**Key Features:**

- Import and interpret structured mindmaps
- Transform visual nodes into structured outlines or tasks
- Maintain logical hierarchies for planning and documentation
- Collaborate on evolving concepts with AI support
- Use as a tool for creative planning or product scoping

This tool helps developers organize ideas and plan features effectively.

Add the following to your `.cursor/mcp.json` file:

```
{
  "mcpServers": {
    "mindmap": {
      "command": "npx",
      "args": ["-y", "mindmap-mcp-server"]
    }
  }
}
```

#### 9. **Markdownify MCP Server**

![](https://assets.apidog.com/blog-next/2025/05/image-463.png)

[Markdownify MCP Server](https://github.com/zcaceres/markdownify-mcp) enables the conversion of various content formats into clean, structured markdown. It is useful for:

- Converting HTML content to markdown
- Cleaning up documentation
- Standardizing content formats
- Preparing content for markdown-based platforms

This server streamlines the process of preparing content for platforms like GitHub or technical blogs.

To integrate this server with a desktop app, add the following to your app's server configuration:

```
{
  "mcpServers": {
    "markdownify": {
      "command": "node",
      "args": [
        "{ABSOLUTE PATH TO FILE HERE}/dist/index.js"
      ],
      "env": {
        // By default, the server will use the default install location of `uv`
        "UV_PATH": "/path/to/uv"
      }
    }
  }
}
```

#### 10. Tavily MCP Server

![](https://assets.apidog.com/blog-next/2025/05/image-467.png)

[Tavily MCP Server](https://github.com/Tomatio13/mcp-server-tavily) focuses on delivering high-quality, curated knowledge for your AI assistant. It combines multiple knowledge sources and uses intelligent filtering to give your assistant more relevant information—making it ideal for deep research, code references, or technical writing.

**Key Features:**

- Context-rich knowledge retrieval
- AI-friendly summaries of complex topics
- Considers source authority and reliability
- Multi-source aggregation (docs, blogs, articles)
- Powerful for research-heavy dev tasks

Edit the configuration file as follows:

```
"mcpServers": {
  "tavily-search": {
    "command": "uv",
    "args": [
      "--directory",
      "C:\\your_path\\mcp-server-tavily",
      "run",
      "tavily-search"
    ],
    "env": {
      "TAVILY_API_KEY": "YOUR_TAVILY_API_KEY",
      "PYTHONIOENCODING": "utf-8"
    }
  }
}
```

## How to Choose the Right MCP Server for Cursor

With so many MCP servers available, choosing the right one for your development workflow can feel overwhelming. Here are a few factors to consider to make the best choice:

### 1. **Your Use Case**

Different MCP servers excel at different tasks:

- **Apidog** is best if you're working with APIs and OpenAPI documentation.
- **Firecrawl** shines in web scraping, browser automation, and multi-modal agents.
- **Figma** is perfect for design-to-code workflows.

Match the server to your most common coding needs.

### 2. **Open Source vs Hosted**

If privacy is a concern or you need more control:

- Go for **open-source** MCP servers like Firecrawl, Apidog, and AnyMCP.
- Hosted options are convenient but may not offer full transparency or local caching.

### Conclusion: Streamline Your Development Workflow

Integrating these MCP servers into your Cursor environment can significantly enhance your development workflow. By reducing context switching and bringing powerful tools directly into your IDE, you can focus more on coding and less on managing disparate tools.

Starting with Firecrawl MCP Server is a pragmatic choice due to its robust web scraping capabilities and strong community support. As you become more comfortable, exploring other servers like Browserbase for browser automation or Magic MCP for generative AI utilities can further augment your development experience.
