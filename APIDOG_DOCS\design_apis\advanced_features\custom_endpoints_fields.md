
When collaborating on APIs, there may be a need for some custom additional fields to describe endpoints. For example, you could add fields such as "Authorization Required," "Response Format," or "Rate Limiting Policy" to provide more context to users consuming the API.

In Apidog, you can leverage Customized Fields to implement these additional fields seamlessly. 

## Configure custome fields

You can set up endpoint fields in Apidog by navigating to `Settings` > `General Settings` > `Feature Settings` > `Endpoint Feature Settings`. The customized fields that you configure here will apply project-wide in Apidog.

<p style="text-align:center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341136/image-preview" style="width:640px" />
</p>

Apidog provides three default built-in fields that you can manually enable or disable. If enabled, the field will appear in the endpoint metadata.

<p style="text-align:center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341135/image-preview" style="width:640px" />
</p>

### Add new field

You also have the flexibility to add your custom fields as needed.

<p style="text-align:center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341134/image-preview" style="width:440px" />
</p>

The field type in Apidog supports the following types: `Text`, `Number`, `Single select`, `Multiple select`, `Date`, `Team member`, `Link`, `Email`, `Single label`, and `Multiple label`.

### Show fields in API documentation

In Quick Share, you have the option to choose whether to display these custom fields. 


<Steps>
  <Step>
    When creating a Quick Share in Apidog, you can select the option "Show API Fields" to include the API fields you defined.
      <p style="text-align:center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341142/image-preview" style="width:540px" />
</p>
  </Step>
  <Step>
    Select fields to be displayed.
<p style="text-align:center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341143/image-preview" style="width:540px" />
</p>     
  </Step>
  <Step>
    They will be displayed in the documentation.
  </Step>
</Steps>

When publishing documentation in Apidog, you can also choose which fields to display. In the `Share Docs` section, under `Publish Docs` > `Basic Settings` > `Content Display`, you have the option to select fields.

<p style="text-align:center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341145/image-preview" style="width:540px" />
</p>