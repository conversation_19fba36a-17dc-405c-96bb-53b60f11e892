## 2. Apidog MCP Server: Connect AI to API Specifications

[The Apidog MCP server](https://docs.apidog.com/apidog-mcp-server) bridges the gap between your API specifications and AI-powered development workflows. This essential MCP server allows <PERSON> and other AI assistants to directly access and work with your API documentation, making code generation more accurate and contextually aware.

### Key Capabilities

- **API Specification Integration:** Connects AI to your [Apidog project](https://docs.apidog.com/connect-api-specification-within-apidog-project-to-ai-via-apidog-mcp-server-901476m0), [online API documentation published by Apidog](https://docs.apidog.com/connect-online-api-documentation-published-by-apidog-to-ai-via-apidog-mcp-server-901468m0), or [local OpenAPI/Swagger files](https://docs.apidog.com/connect-openapi-files-to-ai-via-apidog-mcp-server-901477m0)
- **Intelligent Code Generation:** Generate DTOs, controllers, and client code based on your actual API contracts
- **Real-time Updates:** AI can refresh cached API data to work with the latest specification changes

### Why Developers Love It

The Apidog MCP Server eliminates the guesswork from API-driven development. Instead of manually translating API specs into code, developers can simply ask Claude to "generate Java records for the Product schema" or "add the new fields to the User DTO based on the API specification." The AI pulls directly from your authoritative API documentation, ensuring generated code matches your exact contracts.

### Setup Process

Let's say, now we want to use local OpenAPI/Swagger files as the data source for Claude AI.

1. Open Claude Code’s settings and navigate to the MCP tab.
2. Add the Apidog MCP server configuration to `mcp.json`:

```json
//for macOS/Linux:
{
  "mcpServers": {
    "API specification": {
      "command": "npx",
      "args": [
        "-y",
        "apidog-mcp-server@latest",
        "--oas=<oas-url-or-path>"
      ]
    }
  }
}


//for Windows:
{
  "mcpServers": {
    "API specification": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "apidog-mcp-server@latest",
        "--oas=<oas-url-or-path>"
      ]
    }
  }
}
```

3. Test the connection by asking Claude Code to fetch one of the endpoint specifications.

4. If the AI successfully returns information that meets the expectations, the connection is established!
