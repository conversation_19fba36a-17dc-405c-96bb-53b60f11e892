> Discover how to integrate Apidog with Algolia to enhance search functionality for your API documentation.

By default, the published API documentation includes a built-in search feature that lets users search for endpoints or documentation by name or path. 

![search-bar-api-documentation.png](https://api.apidog.com/api/v1/projects/544525/resources/347620/image-preview)

Additionally, Apidog offers integration with Algolia to enhance search capabilities. To take advantage of this feature, you'll need to enable it and complete the required configurations. Note that your Apidog version must be 2.6.31 or later.

![algolia-search-integration.png](https://api.apidog.com/api/v1/projects/544525/resources/347621/image-preview)

![integrating-algolia-search-apidog.png](https://api.apidog.com/api/v1/projects/544525/resources/347622/image-preview)

:::tip
Algolia is free to use, but it comes with certain limits. To increase these limits, you can either upgrade to Algolia's paid version or [apply for the free Algolia DocSearch plan](https://docsearch.algolia.com/apply).
:::

## Integrating Algolia with Apidog

Integrating Apidog with Algolia enhances the search functionality of your API documentation. Follow these steps to set it up:

### Step 1: Create an Algolia Account
1. Visit the [Algolia website](https://www.algolia.com/) and sign up for an account.
2. After registering, log in to your Algolia account.

![algoria-website.png](https://api.apidog.com/api/v1/projects/544525/resources/347623/image-preview)

### Step 2: Create an Algolia Application
1. Once logged in, create a new application in the Algolia dashboard.
2. Click on`Upload a File`to upload data.

![uploading-files-algolia.png](https://api.apidog.com/api/v1/projects/544525/resources/347624/image-preview)

:::tip
For starters to set up easier, you can use the provided example code to generate a JSON file. Simply drag and drop the JSON file into Algolia. The JSON file's name will be your `Index Name`, which you’ll need later in Apidog’s documentation search configuration.
:::

![upload-records-algolia.png](https://api.apidog.com/api/v1/projects/544525/resources/347625/image-preview)

3. Design your search display.

![designing-search-display-algolia.png](https://api.apidog.com/api/v1/projects/544525/resources/347626/image-preview)

4. Proceed by clicking`Next`until you reach the final step.

![order-results-setting-algolia.png](https://api.apidog.com/api/v1/projects/544525/resources/347627/image-preview)

5. In the final step, you will be asked how you want to build the search front-end. You can skip this option.

![finish-algolia-settings.png](https://api.apidog.com/api/v1/projects/544525/resources/347628/image-preview)

### Step 3: Configure Apidog with Algolia Settings
1. In the Algolia dashboard, locate the configuration details for your application.

![finding-algolia-api-keys.png](https://api.apidog.com/api/v1/projects/544525/resources/347629/image-preview)

The`Index Name`will be displayed here:

![finding-algolia-index-name.png](https://api.apidog.com/api/v1/projects/544525/resources/347630/image-preview)

2. Copy and fill these configuration details into Apidog’s documentation search settings.

### Step 4: Save and Enable the Feature

After filling in the required configuration details in Apidog, save the settings to enable the enhanced search functionality.

![integrating-algolia-with-apidog.png](https://api.apidog.com/api/v1/projects/544525/resources/347631/image-preview)

:::tip
While Algolia is free to use, there are limits on its usage. You can either upgrade to a paid plan for higher limits or [apply for the free Algolia DocSearch plan](https://docsearch.algolia.com/apply).

If you hit the free plan’s limits, you will see a notification in Apidog:

![algolia-limits.png](https://api.apidog.com/api/v1/projects/544525/resources/347632/image-preview)

:::

## Free Algolia DocSearch Plan

Algolia offers a free DocSearch plan for specific purposes (like documentation search). You can [apply for the free Algolia DocSearch plan here](https://docsearch.algolia.com/apply/). For the`Documentation URL`field in the application form, you can enter your public API documentation URL from Apidog.

Once the application is successful, Algolia will send you an confirmation email, and you will receive a message in Algolia dashboard inviting you to join the application. Click to confirm to join the application Algolia offers.

![applying-algolia-free-plan.png](https://api.apidog.com/api/v1/projects/544525/resources/347633/image-preview)

After confirmation, switch to the application at the top left corner of the dashboard, copy the`Application ID`and`Search API Key`and fill in the corresponding fields in Apidog. Please note that the`Write API Key`needs to be found in a different location.


![image.png](https://api.apidog.com/api/v1/projects/544525/resources/348498/image-preview)

To find the`Write API Key`, follow the steps below:

![get-algolia-api-key-step-1.png](https://api.apidog.com/api/v1/projects/544525/resources/347910/image-preview)

![get-algolia-api-key-step-2.png](https://api.apidog.com/api/v1/projects/544525/resources/347912/image-preview)

The `Algolia API Key` here needs to be configured into the `Write API Key` in the Apidog configuration item.

![get-algolia-api-key-step-3.png](https://api.apidog.com/api/v1/projects/544525/resources/347911/image-preview)

Similarly, the value of`Index Name`can be found here:

![finding-algolia-free-plan-index-name.png](https://api.apidog.com/api/v1/projects/544525/resources/347635/image-preview)