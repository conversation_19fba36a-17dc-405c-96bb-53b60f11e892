# How can "NotebookLM" be used in business? We considered scenarios for using it within the company.

## Introduction

This article is a participation article for Qiita Tech Festa 2025.

This may be a sudden question, but do you use NotebookLM on a regular basis?

About a year ago, I switched from a sales position to become a back-end engineer at the company, and when I come into contact with AI tools, I often find myself thinking, "Could this help improve the efficiency of not only our development work, but also other tasks on the sales side?"

In this article, we will introduce a specific scenario of how to use Google's **NotebookLM** , one of the many AI tools, to contribute to the improvement of business efficiency not only in your own organization but also in other organizations within the company, such as sales and back office. We hope you will find this useful as a case study when planning and promoting the introduction of AI tools to business departments!

## First of all, what is "NotebookLM"?

NotebookLM is an AI-equipped notebook tool provided by Google. Its biggest feature is that the AI **​​uses the documents (sources) uploaded by the user as a knowledge source** to conduct dialogue and analysis.

### What's great about NotebookLM! Main features

- **🗂️ Various files can be imported as sources.**  
  You can import PDFs, text, Google documents, website URLs, **audio files, and even YouTube videos** . Even if your team's knowledge is scattered in various places, it is powerful to be able to consolidate it here.

- **✅ Reliability that "AI lies" are unlikely to occur**  
  AI answers are always generated based on the uploaded materials. This greatly reduces the risk of "hallucination," where AI tells plausible lies. Answers always include citations, making fact-checking easy.

- **🤖 Easy-to-understand UI and operability**  
  No specialized knowledge required! Just upload the file and chat. The intuitive UI makes it easy to deploy to business members.

- **🤝 Quickly share team-specific AI**  
  The notebook you create can be shared with team members with a single URL. The idea is that you can easily create "AI assistants" specialized for specific tasks for each department.

### Why NotebookLM and not ChatGPT or other generative AI?

The big difference between this and general-purpose chat AI is that it can **safely handle closed information** .

"I want accurate answers about product specifications and company rules!"

In times like these, NotebookLM really shines. It doesn't refer to information on the web, and only uses the documents at hand as the "single correct answer." This allows you to get highly accurate answers specific to your business while maintaining security.  
*There is also a function to search for the source on the web .

Google has stated that it will not use any personal data uploaded by users, such as sources, queries, or responses from models, to train NotebookLM. This is true for both personal and Workspace accounts, but it may differ depending on your plan, so be sure to check your settings.

## Three scenarios for using NotebookLM

From here, we will introduce three scenarios in which NotebookLM can be used, using common business tasks as concrete examples.

### Scenario 1: Consolidate scattered knowledge! Promote self-solving with "internal FAQs"

#### ▶ Do you have any of these issues?

Product specifications, operational rules, past inquiry history... Information is scattered all over the place, such as in internal communication tools and various files in Google Drive, so it is difficult to find the information you are looking for right away. As a result, simple confirmation tasks take a lot of time.

#### 💡 Here's the solution with NotebookLM!

Related documents are consolidated into one notebook and **a dedicated AI-powered FAQ** is built. Members simply ask questions in natural language about what they want to know. AI will search across multiple documents and return answers with citations. This should dramatically reduce the time spent searching for information.

##### ▼ Prompt example

| the purpose             | Example prompt                                             | Example of source to be loaded                                                    |
| ----------------------- | ---------------------------------------------------------- | --------------------------------------------------------------------------------- |
| **Specification check** | `「〇〇機能のCSVダウンロード仕様について、詳細を教えて。」`<br>`「先月のリリースでの変更点を要約して。」` | ・Product specifications<br>・Release notes<br>・Past update materials               |
| **troubleshooting**     | `「顧客から『パスワード再設定メールが届かない』と報告あり。考えられる原因と対処法を教えて。」`           | ・Past inquiry response history (copy and paste from Slack, etc.)<br>・Fault report |
| **Check company rules** | `「アカウントを無償提供する場合の申請フローを教えて。発注エビデンスの体裁って何だっけ？」`             | ・Business flow diagrams<br>・Company regulations documents                         |

---

### Scenario 2: AI analyzes meeting audio! Automate the creation of meeting minutes and task management

#### ▶ Do you have any of these issues?

After regular meetings with clients or internal meetings, it is a hassle to create meeting minutes by listening to the recordings. It is also a struggle to organize important next actions, and I worry about missing things.

#### 💡 Here's the solution with NotebookLM!

**The speech recognition function** of NotebookLM is useful. If you upload the recording data of the meeting (mp3, etc.), the AI ​​will automatically transcribe it. You can then request a summary or task extraction for the content.

**Usage example:**

1. Upload audio files recorded during web conferences to NotebookLM
2. Just ask the AI ​​via chat!
   - `「この会議の要点を3行でまとめて。」`
   - `「決定事項と担当者をリストアップして。」`
   - `「発生したToDoを、担当者と期限がわかるように表形式で整理して。」`
3. By copying and pasting the generated text, you can instantly draft meeting minutes or enter activity history into CRM.

---

### Scenario 3: "AI Onboarding Mentor" to help new members get started right away

#### ▶ Do you have any of these issues?

It is important to educate new members, but OJT tends to be personalized and the burden on the person in charge of training is heavy. In addition, new members may feel reluctant to ask about such basic things over and over again.

#### 💡 Here's the solution with NotebookLM!

**With NotebookLM, you can easily create an AI onboarding mentor** packed with training materials and manuals ! New members can ask the AI ​​questions 24 hours a day without hesitation and proceed with their self-study. This is expected to standardize and streamline the content of training.

**Usage example:**

- **The new member is...**
  
  - `「お客様へのキックオフMTGの手順を、ステップバイステップで教えてください。」`
  - `「クレーム対応で、一番最初にやるべきことは何ですか？」`

- **Educational personnel…**
  
  - `「これらの資料から、製品知識の理解度を確認するクイズを10問作って。」`
* It will be even more effective if you use the mind map function and audio summary in NotebookLM.

## (Extra Edition - Personal Use) Practice in 3 steps! How to use it for self-study

Here, I will introduce some of the personal ways to use NotebookLM that I personally find useful recently!  
If you are interested, please give it a try!

### Step 1: Collect your study notes in a Google Doc

First, compile your daily study notes into a Google document. If you usually take notes using Notion or similar, you can just copy and paste the contents.

**[TIPS]**  
If your document is long, splitting it into separate files by month or theme will make it easier to manage later.

※Example of study notes

[![Gs49N-abEAE3uiT.jpeg](https://qiita-user-contents.imgix.net/https%3A%2F%2Fqiita-image-store.s3.ap-northeast-1.amazonaws.com%2F0%2F2957850%2F5978a25d-e2ec-491f-8b29-d960f6363910.jpeg?ixlib=rb-4.0.0&auto=format&gif-q=60&q=75&s=f0be7f32c9137838fe14d0d08a335b7a)](https://qiita-user-contents.imgix.net/https%3A%2F%2Fqiita-image-store.s3.ap-northeast-1.amazonaws.com%2F0%2F2957850%2F5978a25d-e2ec-491f-8b29-d960f6363910.jpeg?ixlib=rb-4.0.0&auto=format&gif-q=60&q=75&s=f0be7f32c9137838fe14d0d08a335b7a)

### Step 2: Load Google Docs into NotebookLM's source

Next, open NotebookLM and specify the Google Doc you just created as the source. Since you can select the file directly from Google Drive, the integration is super smooth!

**[TIPS]**  
When you update the contents of a document, you can **resynchronize it with one click** on NotebookLM . (I hope to see automatic synchronization in the future!)

[![Gs49gaabAAEi95K.jpeg](https://qiita-user-contents.imgix.net/https%3A%2F%2Fqiita-image-store.s3.ap-northeast-1.amazonaws.com%2F0%2F2957850%2F49e1733b-cca4-4b8a-9306-1a34f6bff773.jpeg?ixlib=rb-4.0.0&auto=format&gif-q=60&q=75&s=ddecfbfd28cff7e3048ba94d77066344)](https://qiita-user-contents.imgix.net/https%3A%2F%2Fqiita-image-store.s3.ap-northeast-1.amazonaws.com%2F0%2F2957850%2F49e1733b-cca4-4b8a-9306-1a34f6bff773.jpeg?ixlib=rb-4.0.0&auto=format&gif-q=60&q=75&s=ddecfbfd28cff7e3048ba94d77066344)

### Step 3: Generate review output in chat

Once the source is loaded, all you have to do is ask the AI ​​via chat.  
It will create questions in various formats based on the contents of your study notes.

[![Gs4-CI2a4AA9x_J.jpeg](https://qiita-user-contents.imgix.net/https%3A%2F%2Fqiita-image-store.s3.ap-northeast-1.amazonaws.com%2F0%2F2957850%2F681ea2a8-72b1-4ee8-8063-f4608f99c32b.jpeg?ixlib=rb-4.0.0&auto=format&gif-q=60&q=75&s=fd266e2470cd6bb419a498f27acbee32)](https://qiita-user-contents.imgix.net/https%3A%2F%2Fqiita-image-store.s3.ap-northeast-1.amazonaws.com%2F0%2F2957850%2F681ea2a8-72b1-4ee8-8063-f4608f99c32b.jpeg?ixlib=rb-4.0.0&auto=format&gif-q=60&q=75&s=fd266e2470cd6bb419a498f27acbee32)

I'll also introduce my own custom prompt for NotebookLM!

```
あなたは私の学習アシスタントです。提供されたソース（私の学習メモ）のみを基に、
以下の形式でテストまたはクイズを作成してください。

**目的:** 私の学習内容の理解度を確認し、記憶の定着を促すこと。

**形式:**
短答問題（簡潔な説明を求める）

**指示（ルール）:**
* 問題を出す際には冒頭から問題文のみ記載してください。「わかりました！それでは〜」のような会話形式の文章は必要ありません。
* 問題文は1問ずつ箇条書きにして出題してください。（例：問題1、問題2...）
* 特に指示がない場合、最大でも10問まで出題してください。
* 答えはすべての出題した問題文の後にまとめて箇条書きとして記載してください。
* ただし、ただ答え単体を書くのではなく「具体的な活用方法やタイミング」なども合わせて併記できるものは記載してください。（例：解答1、解答2...）
* 必要に応じて、追加で学習すべき点や補足説明を提案してください。
* 専門用語には適切な説明を加えてください。
```

### Advantages of this method

- 🧠 **Output efficiency** : AI generates optimal questions based on the notes you write
- ✏️Knowledge **consolidation** : By repeatedly actively recalling information (output), it becomes easier to remember it.
- 🗣️Flexible **customization** : Create questions in any format you like, such as question and answer, fill-in-the-blank, or summary, by simply changing the prompts.

## Precautions when using (security and accuracy)

- **🔐 Data Privacy and SecurityNotebookLM**  
  states that uploaded data will not be used to train AI models. However, if you are handling confidential customer information, it is important to check your company's information security policy and establish a safe operating system.

- **🎯 Accuracy of information (thorough fact-checking)**  
  NotebookLM's answers are all based on the source document. If the original material is outdated or incorrect, the AI's answers will naturally be the same. Don't just accept the AI's answers, but make it a habit for your team to always **click on the source to check the primary information .**

## summary

This time, we introduced three scenarios for improving work efficiency in sales organizations using NotebookLM.

1. **Accelerating knowledge searches with in-house FAQs**
2. **Automating meeting minutes and task management using voice analysis**
3. **Onboarding support by AI mentor**

I believe that the examples I have given here are applicable not only to sales organizations, but also to engineering organizations and back office staff.

In the future, we would like to actually try out these scenarios within the company and find out whether they really improved efficiency, and what unexpected tips and points of caution we discovered from using them, in order to improve work efficiency throughout the organization.

*As an aside, if you suddenly share information with another organization one-sidedly, it often doesn't penetrate well and ends up being a self-satisfied sharing, so it may be a good idea to take a "bouncing ideas off the wall" approach and accompany each organization from the construction of operations and concrete design that are suited to their circumstances...! (I say this as a warning to myself)

I hope this article will inspire you to promote the use of AI in your organization.  
Thank you for reading to the end!
