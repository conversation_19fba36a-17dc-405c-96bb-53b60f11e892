Import WSDL
WSDL is a file format for describing WebService APIs.While you can debug WebServices using Apidog,its also possible import WSDL format files into Apidog.

Importing Data
Open the "Settings" panel in Apidog, click "Import Data", select the WSDL or XML format file and upload.

importing-data-from-WSDL.png
The import preview page will display the expected APIs to be imported. You can check the base URL in the WSDL file on the "Environments" tab page. After confirming it is correct, click the "Confirm" button.

import-preview.png
Debugging APIs
Click the environment in the upper right corner to switch to the imported environment when sending request to API. The base URL will be automatically filled in the actual request address.

debugging-apis.png
