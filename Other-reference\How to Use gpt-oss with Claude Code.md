# How to Use gpt-oss with Claude Code

> Pro tip: Building and testing APIs while you experiment with GPT-OSS? Use Apidog—the all-in-one API development platform—to design, mock, test, and publish docs in one place. It pairs nicely with Claude Code so you can iterate on prompts and verify endpoints without switching tools.

Want to run OpenAI‑compatible, open‑weight models in the familiar **Claude Code** CLI? **GPT-OSS** (20B and 120B) delivers strong coding and reasoning performance, and you can route Claude Code to it through OpenAI‑style endpoints. This guide shows three reliable ways to connect Claude Code to GPT-OSS using Hugging Face Inference Endpoints, OpenRouter, or a LiteLLM proxy. Pick the path that fits your workflow and budget.

## Why pair GPT-OSS with Claude Code?

**GPT-OSS** is Open AI’s open‑weight model family with a 128K context window and an Apache 2.0 license—ideal for private deployments and customization. **Claude Code** (v0.5.3+) provides a fast, conversational CLI for coding. By pointing Claude Code at an OpenAI‑compatible endpoint for GPT-OSS, you keep the UX you like while controlling costs and deployment.

![open ai's open weight models](https://assets.apidog.com/blog-next/2025/08/gpt-oss-120b-20b-open-weight-ai.webp)

## Prerequisites

Make sure you have:

- **<PERSON> Code ≥ 0.5.3**: Check `claude --version`. Install with `pip install claude-code` or update with `pip install --upgrade claude-code`.
- **Hugging Face account**: Create a read/write token at [huggingface.co](https://huggingface.co/).
- **OpenRouter API key** (for the OpenRouter path): Get one at [openrouter.ai](https://openrouter.ai/).
- **Python 3.10+ and Docker**: Needed for local runs or LiteLLM.
- **Command‑line familiarity**: You’ll set environment variables and run basic commands.

![Claude code](https://assets.apidog.com/blog-next/2025/08/claude_code_home-1.png)

## Option A — Self‑host GPT‑OSS on Hugging Face

Use Hugging Face Inference Endpoints to deploy a private, scalable TGI server with OpenAI compatibility.

### 1) Choose a model

1. Open the GPT‑OSS repo on Hugging Face: [openai/gpt-oss-20b](https://huggingface.co/openai/gpt-oss-20b) or [openai/gpt-oss-120b](https://huggingface.co/openai/gpt-oss-120b).
2. Accept the Apache 2.0 license.
3. Optional alternative: **Qwen3-Coder-480B-A35B-Instruct** ([Qwen/Qwen3-Coder-480B-A35B-Instruct](https://huggingface.co/Qwen/Qwen3-Coder-480B-A35B-Instruct)); choose a GGUF build for lighter hardware.

![hugging face gpt-oss model](https://assets.apidog.com/blog-next/2025/08/image-110.png)

### 2) Deploy a Text Generation Inference endpoint

1. On the model page, select **Deploy** > **Inference Endpoint**.
2. Pick the **Text Generation Inference (TGI)** template (≥ v1.4.0).
3. Enable OpenAI compatibility (check **Enable OpenAI compatibility** or add `--enable-openai`).
4. Choose hardware: A10G/CPU for 20B; A100 for 120B. Create the endpoint.

### 3) Gather credentials

1. When the endpoint is **Running**, note:
   - **ENDPOINT_URL** like `https://<your-endpoint>.us-east-1.aws.endpoints.huggingface.cloud`.
   - **HF_API_TOKEN** from your Hugging Face account.
2. Capture the model ID (e.g., `gpt-oss-20b` or `gpt-oss-120b`).

### 4) Point Claude Code at your endpoint

1. Export environment variables:

```bash
export ANTHROPIC_BASE_URL="https://<your-endpoint>.us-east-1.aws.endpoints.huggingface.cloud"
export ANTHROPIC_AUTH_TOKEN="hf_xxxxxxxxxxxxxxxxx"
export ANTHROPIC_MODEL="gpt-oss-20b"  # or gpt-oss-120b
```

2. Verify the connection:

```bash
claude --model gpt-oss-20b
```

Claude Code will route to your TGI endpoint via `/v1/chat/completions` using the OpenAI‑compatible schema.

### 5) Cost and scaling

- Hugging Face Inference Endpoints auto‑scale; monitor usage. A10G is about ~$0.60/hour; A100 is about ~$3/hour.
- Prefer local? Run TGI via Docker and point Claude Code at it:

```bash
docker run --name tgi -p 8080:80 -e HF_TOKEN=hf_xxxxxxxxxxxxxxxxx ghcr.io/huggingface/text-generation-inference:latest --model-id openai/gpt-oss-20b --enable-openai
```

Then set `ANTHROPIC_BASE_URL="http://localhost:8080"`.

## Option B — Use OpenRouter as a managed proxy

Skip infrastructure and access GPT‑OSS through OpenRouter’s unified API.

### 1) Create an account and pick a model

1. Sign up at [openrouter.ai](https://openrouter.ai/) and copy your API key from **Keys**.
2. Choose a model slug:
   - `openai/gpt-oss-20b`
   - `openai/gpt-oss-120b`
   - `qwen/qwen3-coder-480b` (Qwen’s coder model)

![gpt-oss model on openrouter](https://assets.apidog.com/blog-next/2025/08/Screenshot-2025-08-07-004048.png)

### 2) Configure Claude Code

1. Export variables:

```bash
export ANTHROPIC_BASE_URL="https://openrouter.ai/api/v1"
export ANTHROPIC_AUTH_TOKEN="or_xxxxxxxxx"
export ANTHROPIC_MODEL="openai/gpt-oss-20b"
```

2. Test the connection:

```bash
claude --model openai/gpt-oss-20b
```

Claude Code will stream responses via OpenRouter, with built‑in fallback support.

### 3) Pricing notes

- Approx. costs: ~$0.50/M input tokens and ~$2.00/M output tokens for GPT‑OSS‑120B; far below many proprietary options.
- OpenRouter bills per usage; no infrastructure to manage.

## Option C — Route via LiteLLM for multi‑model workflows

Use LiteLLM as a local proxy if you want to hot‑swap between GPT‑OSS, Qwen, or Anthropic models.

### 1) Install and configure LiteLLM

1. Install [LiteLLM](https://docs.litellm.ai/):

```bash
pip install litellm
```

2. Create `litellm.yaml`:

```yaml
model_list:
  - model_name: gpt-oss-20b
    litellm_params:
      model: openai/gpt-oss-20b
      api_key: or_xxxxxxxxx  # OpenRouter key
      api_base: https://openrouter.ai/api/v1
  - model_name: qwen3-coder
    litellm_params:
      model: openrouter/qwen/qwen3-coder
      api_key: or_xxxxxxxxx
      api_base: https://openrouter.ai/api/v1
```

3. Start the proxy:

```bash
litellm --config litellm.yaml
```

### 2) Point Claude Code to LiteLLM

1. Export variables:

```bash
export ANTHROPIC_BASE_URL="http://localhost:4000"
export ANTHROPIC_AUTH_TOKEN="litellm_master"
export ANTHROPIC_MODEL="gpt-oss-20b"
```

2. Confirm it works:

```bash
claude --model gpt-oss-20b
```

LiteLLM forwards to GPT‑OSS via OpenRouter and supports cost logging. Prefer simple‑shuffle routing to avoid incompatibilities with Anthropic providers.

### 3) Notes

- Use simple‑shuffle over latency‑based routing for stability with Anthropic models.
- Review LiteLLM logs to track usage and costs.

## Validate your setup

Open Claude Code and try these checks:

- **Code generation**

```bash
claude --model gpt-oss-20b "Write a Python REST API with Flask"
```

Expected response (example):

```python
from flask import Flask, jsonify
app = Flask(__name__)
@app.route('/api', methods=['GET'])
def get_data():
    return jsonify({"message": "Hello from GPT-OSS!"})
if __name__ == '__main__':
    app.run(debug=True)
```

- **Codebase analysis**

```bash
claude --model gpt-oss-20b "Summarize src/server.js"
```

- **Debugging**

```bash
claude --model gpt-oss-20b "Debug this buggy Python code: [paste code]"
```

## Troubleshooting

- **404 on `/v1/chat/completions`**: Ensure `--enable-openai` is enabled in TGI (Option A) or verify model availability on OpenRouter (Option B).
- **Empty responses**: Confirm `ANTHROPIC_MODEL` exactly matches the model slug (e.g., `gpt-oss-20b`).
- **400 after swapping models**: Set LiteLLM to simple‑shuffle routing (Option C).
- **Slow first token**: Warm up Hugging Face endpoints with a small prompt after scale‑to‑zero.
- **Claude Code crashes**: Upgrade to ≥ 0.5.3 and recheck environment variables.

## Benefits of this setup

- **Lower cost**: OpenRouter’s pricing is competitive; local TGI can be very cost‑effective once hardware is in place.
- **Open and flexible**: GPT‑OSS’s Apache 2.0 license supports private customization and deployment.
- **Great ergonomics**: Keep Claude Code’s productive CLI while tapping GPT‑OSS’s capability.
- **Model agility**: Switch between GPT‑OSS, Qwen, and Anthropic providers with LiteLLM or OpenRouter.

## Wrap‑up

You now have three proven ways to use **GPT‑OSS** with **Claude Code**—self‑host on Hugging Face, go fully managed with OpenRouter, or proxy locally with LiteLLM. Use the validation steps to confirm everything is wired correctly, then iterate on prompts, analyze codebases, and debug faster with an open‑weight model behind the scenes.
