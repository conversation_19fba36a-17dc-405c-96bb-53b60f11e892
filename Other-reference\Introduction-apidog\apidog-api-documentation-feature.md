How to Generate API Documentation Using Apidog

API documentation is essential in modern software development. It helps developers understand how to effectively use an API and promotes collaboration, adoption, and smooth integration.

In this guide, we'll walk you through how to create API documentation with Apidog, a powerful tool that simplifies the API documentation process.

Automatically Generate Standardized API Documentation Using Apidog
Step 1: Sign up to Apidog
To get started with Apidog, you'll need to create an account. Once you're logged in, you'll be welcomed by Apidog's intuitive and user-friendly interface.

Sign up to Apidog
Step 2: Create a New API Endpoint
Overview - Apidog Docs
Overview - Apidog Docs

Apidog

Each API documentation project consists of various endpoints, each representing a specific route or functionality of your API. To add a new endpoint, simply click the "+" button or select "New Endpoint" within your project.

creating new endpoint at Apidog
Step 3: Define API Endpoint Specifications
Now, it's time to provide details about your API. You'll need to specify:

The endpoint URL
A brief description
Request and response information
This is where <PERSON>pidog makes things easy. For each endpoint, you can:

Specify the HTTP method (GET, POST, PUT, DELETE, etc.).
Define request parameters, including their names, types, and descriptions.
defining API endpoint specification at <PERSON>pidog
Describe the expected response, including status codes, response formats (JSON, XML, etc.), and example responses.
Add API response example in API documentation
API documentation doesn't have to be complicated. With Apidog, you can complete this task with just a few clicks. Its visual interface makes it far easier than generating documentation manually from code.

Step 4. Generate Your API Documentation
Once you've filled in all the essential API information, simply click on "Save as Endpoint", and Apidog will automatically generate a well-structured API documentation for you.


By following these four simple steps, you'll have a fully standardized API documentation ready to go. This process not only ensures consistency and clarity but also saves you valuable time.

(Optional) Step 5: Test Your API
Apidog also offers an interactive testing environment for your API endpoints. You can send requests, view responses, and verify that your API behaves as expected—all from within the platform. This feature is perfect for both initial testing and ongoing validation.

Why Use Apidog for API Documentation?
Apidog stands out from other tools like Swagger and Postman. It offers an all-in-one solution for generating, managing, and sharing API documentation with ease. Here’s why Apidog is a great choice:

Apidog
Apidog's API Documentation Features
Before diving into the details of how to generate API documentation with Apidog, let's take a moment to appreciate the key features that make Apidog a standout choice for API documentation:

API Documentation: Apidog provides a user-friendly interface for creating and maintaining API documentation. It supports various API types, including HTTP APIs, and allows you to define API paths, request methods, parameters, response data, and more.
Data Schema: Apidog offers robust data schema capabilities, allowing you to define the structure of data for API responses and request bodies. This ensures that your API documentation accurately represents the expected data format.
Real-Time Updates: The change history feature tracks and manages modifications made to your API documentation over time. It provides version comparison and rollback options, facilitating collaboration among team members. Any changes made to the API documentation are promptly reflected in the shared online version, ensuring that everyone has access to the latest information.
Share Online: You can publish and share your API documentation online with specific team members or stakeholders. It supports customization of access, language, sharing scope, and online debugging.
Batch API Management: When dealing with multiple APIs, Batch API Management simplifies tasks like bulk deletion, status modification, and tag management. It enhances API management efficiency within your project.
Online Debugging: Apidog's online documentation includes a debugging environment, allowing team members to test and validate APIs directly within the documentation.
Bonus Tips about API Documentation in Apidog
1. Sharing Your Documentation
Once you've created and customized your API documentation, it's time to share it with your intended audience. Apidog provides options for sharing your documentation with team members, collaborators, or the public. You can generate shareable links or embed documentation directly into your website or application.

2. Real-time Updates
APIs evolve over time, and so should their documentation. Apidog ensures that any changes made to your API are instantly reflected in the online documentation. This real-time update feature keeps your documentation accurate and up to date, enhancing collaboration and reducing the risk of outdated information.

3. Import/Export Functionality
Apidog prioritizes seamless integration within the API ecosystem. It facilitates smooth data exchange by supporting exports in various formats, such as OpenAPI, Markdown, and HTML. Additionally, it simplifies project migration and collaboration by enabling data import from sources like OpenAPI and Postman.

What is Good API Documentation?
A standard API documentation possesses several essential characteristics. It should be clear, correct, and comprehensive, offering a detailed explanation of the API's functionality, including all endpoints, HTTP methods, request parameters, and response formats. The documentation should be easy for developers to understand, avoiding unnecessary jargon and complex terminology.

Here are the key attributes of good API documentation:

Clarity and Readability: Good API documentation is written in a clear and easily understandable manner. It uses plain language and avoids unnecessary technical jargon, making it accessible to a wide range of developers, from novices to experts.
Consistency: The documentation maintains a consistent structure and format throughout. A well-organized layout, clear headings, and standardized terminology make it easy for developers to navigate and find the information they need.
Interactive Elements: Some API documentation may incorporate interactive elements, such as the ability to test API endpoints directly from the documentation, view live response examples, and experiment with different parameters. These features enhance the user experience.
Authentication and Authorization: It explains the authentication and authorization mechanisms required to access the API. This includes details about API keys, tokens, or any other security measures necessary for proper usage.
Error Handling: Comprehensive API documentation includes information about error responses, including status codes, error messages, and guidance on how to handle and troubleshoot common errors.
Versioning: In cases where the API has multiple versions, the documentation should clearly indicate versioning strategies, allowing developers to access the correct API version.
Conclusion
API documentation is an indispensable component of modern software development, and Apidog is your all-in-one solution for generating, managing, and sharing it effectively. With Apidog, you can streamline your API development process, collaborate seamlessly with your team, and ensure that your APIs are accessible and well-documented for developers worldwide.

> Apidog Version 2.7.15 or later is required.

Apidog provides a powerful SEO feature to help you optimize how your API documentation appears in search engines, attracting more traffic. You can configure SEO settings at the page level or site-wide level based on your actual needs:

**Page-Level SEO Settings**: Configure SEO for individual pages like endpoint documentations or Markdown files.
**Site-wide Level SEO Settings**: Configure global SEO metadata, robots.txt, sitemap.xml, redirect rules, and URL settings for the entire docs site.


## Page-Level SEO Settings

You can access page-level SEO settings by clicking the`SEO Settings`icon at the right side of the endpoint documentation or Markdown pages. 

<Background>
![page-level seo settings.png](https://api.apidog.com/api/v1/projects/544525/resources/356625/image-preview)
</Background>

In the pop-out panel, you can configure:

- **URL Slug:** Define the path for the current page. For example, set it to `find-by-id`, then the final page URL becomes: `https://{your-domain.com}/find-by-id`.
- **Meta Title:** The title of the webpage that will appear in search engine results.
- **Meta Description:** A brief description of the webpage shown in search engine results.
- **Keywords:** Keywords to help improve search engine understanding.
- **Custom Metadata:** Add extra `<meta>` tags in JSON format. Example:

  ```json
  [
    {"name": "robots", "content": "noindex"},
    {"name": "twitter:card", "content": "summary_large_image"}
  ]
  ```
  
  These will render in HTML as:
  
    ```html
    <meta name="robots" content="noindex" />
    <meta name="twitter:card" content="summary_large_image" />
    ```

## Site-wide Level SEO Settings

When publishing online docs sites, go to "**SEO Settings**" to apply site-wide SEO configurations.

<Background>
![side-wide-seo-settings.png](https://api.apidog.com/api/v1/projects/544525/resources/356630/image-preview)
</Background>

### Global Metadata

This sets default `<meta>` tags for every page in the docs site. You can use built-in variables to generate dynamic content. All pages inherit this configuration unless overridden by page-level seo settings.

<Background>
![global-metadata.png](https://api.apidog.com/api/v1/projects/544525/resources/356631/image-preview)
</Background>


**Configuration Format:**

Please use a valid JSON array, e.g.:

```json
[
  {"property": "og:title", "content": "{{PAGE_TITLE}} - {{SITE_NAME}}"},
  {"property": "og:description", "content": "{{DESCRIPTION}}"},
  {"property": "og:image", "content": "{{SITE_ICON}}"},
  {"name": "twitter:card", "content": "summary_large_image"},
  {"name": "description", "content": "global description information"},
  {"name": "keywords", "content": "global keywords"}
]
```

These will render in HTML as:

```html
<meta property="og:title" content="{{PAGE_TITLE}} - {{SITE_NAME}}" />
<meta property="og:description" content="{{DESCRIPTION}}" />
<meta property="og:image" content="{{SITE_ICON}}" />
<meta name="twitter:card" content="summary_large_image" />
<meta name="description" content="global description information" />
<meta name="keywords" content="global keywords" />

```

**Supported Built-in Variables:**

The following built-in variables are supported and can be used as placeholders in the content field when filled in:

| Variable  | Meaning|
| ----------------- | --------- |
| `{{PAGE_TITLE}}`  | Title of the current page|
| `{{PAGE_URL}}`    | Page's full URL|
| `{{SITE_NAME}}`   | Your documentation site name|
| `{{SITE_ICON}}`   | URL of your site icon   |
| `{{DESCRIPTION}}` | Default page description|
| `{{KEYWORDS}}`    | Default page keywords   |


**Priority:**

:::info[]
`Page-Level SEO Settings(endpoint or Markdown page)` **>** `Global Metadata` **>** `System defaults`
:::


### Robots File(robots.txt)

This file controls crawler behavior and is accessible at `https://{your-domain.com}/robots.txt`.


**Default content:**

```xml
User-Agent: *
Allow: /

Sitemap: {{SITEMAP_URL}}
```

`{{SITEMAP_URL}}` is automatically replaced with the actual `sitemap.xml`URL, provided the sitemap feature is enabled. 

If disabled, this line is removed automatically.


**How to prevent pages from being indexed:**

<AccordionGroup>
  <Accordion title="Block the entire site from indexing" defaultOpen>
   Add the following to **Global Metadata**:
    ```json
    {"name": "robots", "content": "noindex"}
    ```
  </Accordion>
  <Accordion title="Block a single page from indexing" defaultOpen={true}>
    Add the following to the **Custom Metadata** of the specific page:    
    ```json
    {"name": "robots", "content": "noindex"}
    ```
  </Accordion>
</AccordionGroup>


### Sitemap File(sitemap.xml)

When enabled, the system automatically generates a `sitemap.xml` listing all pages on the site to help search engines crawl more effectively. You can access the sitemap via `https://{your-domain.com}/sitemap.xml`.

- Default status: Enabled.

- If disabled, `sitemap.xml` will no longer be available and `Sitemap: {{SITEMAP_URL}}` will also be removed from `robots.txt`.

### Docs Redirect Rules

If you modify published documentation URLs, you can set redirect rules to avoid 404 errors when users visit old URLs.

<Background>
![docs redirect rules.png](https://api.apidog.com/api/v1/projects/544525/resources/356632/image-preview)
</Background>

You can manually add multiple redirect rules to automatically forward users from old URLs to new ones:

<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/356633/image-preview)
</Background>

### URL & Slug Rules

Click the`project settings`to view the default URL rules for your site pages.

<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/356634/image-preview)
</Background>

There are two types of URL naming rules, depending on whether a custom slug (URL) is set on the page.

<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/356635/image-preview)
</Background>

<AccordionGroup>
  <Accordion title="When the URL Slug is set" defaultOpen>
    The system uses the provided slug as the access path:

    ```js
    https://your-domain.com/{slug}
    ```

   For example, if the slug in a Markdown file is `api-overview`, the address will be:

    ```js
    https://your-domain.com/api-overview
    ```
  </Accordion>
  <Accordion title="When the URL Slug is blank">
    
The system auto-generates the URL using one of the following rules:

**Rule 1(default and recommended):**

```js
https://your-domain.com/{slugify(resourceName)}-{resourceKey}
```

- `resourceName`: The resource title (such as the endpoint name or documentation title)

- `slugify(...)`: Converts the title into a URL-friendly format (e.g., spaces become hyphens)

- `resourceKey`: Unique ID

**Example:**

```js
https://docs.apidog.com/SEO-settings-5702007m0
```

**Rule 2(more concise):**


```js
https://your-domain.com/{resourceKey}
```

This keeps only the unique ID—shorter but lacks semantic context.

**Example:**

```js
https://docs.apidog.com/5702007m0
```
  </Accordion>
</AccordionGroup>


Choose the rule that best fits your team’s SEO preferences and documentation workflow.

## Common Metadata Reference


```json
[
  {
    "name": "robots",
    "content": "noindex"
  },
  {
    "name": "charset",
    "content": "UTF-8"
  },
  {
    "name": "viewport",
    "content": "width=device-width, initial-scale=1.0"
  },
  {
    "name": "description",
    "content": "Page description"
  },
  {
    "name": "keywords",
    "content": "keyword1, keyword2, keyword3"
  },
  {
    "name": "author",
    "content": "Author Name"
  },
  {
    "name": "googlebot",
    "content": "index, follow"
  },
  {
    "name": "google",
    "content": "notranslate"
  },
  {
    "name": "google-site-verification",
    "content": "verification_token"
  },
  {
    "name": "generator",
    "content": "Mintlify"
  },
  {
    "name": "theme-color",
    "content": "#000000"
  },
  {
    "name": "color-scheme",
    "content": "light dark"
  },
  {
    "name": "format-detection",
    "content": "telephone=no"
  },
  {
    "name": "referrer",
    "content": "origin"
  },
  {
    "name": "refresh",
    "content": "30"
  },
  {
    "name": "rating",
    "content": "general"
  },
  {
    "name": "revisit-after",
    "content": "7 days"
  },
  {
    "name": "language",
    "content": "en"
  },
  {
    "name": "copyright",
    "content": "Copyright 2024"
  },
  {
    "name": "reply-to",
    "content": "<EMAIL>"
  },
  {
    "name": "distribution",
    "content": "global"
  },
  {
    "name": "coverage",
    "content": "Worldwide"
  },
  {
    "name": "category",
    "content": "Technology"
  },
  {
    "name": "target",
    "content": "all"
  },
  {
    "name": "HandheldFriendly",
    "content": "True"
  },
  {
    "name": "MobileOptimized",
    "content": "320"
  },
  {
    "name": "apple-mobile-web-app-capable",
    "content": "yes"
  },
  {
    "name": "apple-mobile-web-app-status-bar-style",
    "content": "black"
  },
  {
    "name": "apple-mobile-web-app-title",
    "content": "App Title"
  },
  {
    "name": "application-name",
    "content": "App Name"
  },
  {
    "name": "msapplication-TileColor",
    "content": "#000000"
  },
  {
    "name": "msapplication-TileImage",
    "content": "path/to/tile.png"
  },
  {
    "name": "msapplication-config",
    "content": "path/to/browserconfig.xml"
  },
  {
    "name": "og:title",
    "content": "Open Graph Title"
  },
  {
    "name": "og:type",
    "content": "website"
  },
  {
    "name": "og:url",
    "content": "https://example.com"
  },
  {
    "name": "og:image",
    "content": "https://example.com/image.jpg"
  },
  {
    "name": "og:description",
    "content": "Open Graph Description"
  },
  {
    "name": "og:site_name",
    "content": "Site Name"
  },
  {
    "name": "og:locale",
    "content": "en_US"
  },
  {
    "name": "og:video",
    "content": "https://example.com/video.mp4"
  },
  {
    "name": "og:audio",
    "content": "https://example.com/audio.mp3"
  },
  {
    "name": "twitter:card",
    "content": "summary"
  },
  {
    "name": "twitter:site",
    "content": "@username"
  },
  {
    "name": "twitter:creator",
    "content": "@username"
  },
  {
    "name": "twitter:title",
    "content": "Twitter Title"
  },
  {
    "name": "twitter:description",
    "content": "Twitter Description"
  },
  {
    "name": "twitter:image",
    "content": "https://example.com/image.jpg"
  },
  {
    "name": "twitter:image:alt",
    "content": "Image Description"
  },
  {
    "name": "twitter:player",
    "content": "https://example.com/player"
  },
  {
    "name": "twitter:player:width",
    "content": "480"
  },
  {
    "name": "twitter:player:height",
    "content": "480"
  },
  {
    "name": "twitter:app:name:iphone",
    "content": "App Name"
  },
  {
    "name": "twitter:app:id:iphone",
    "content": "12345"
  },
  {
    "name": "twitter:app:url:iphone",
    "content": "app://"
  },
  {
    "name": "article:published_time",
    "content": "2024-01-01T00:00:00+00:00"
  },
  {
    "name": "article:modified_time",
    "content": "2024-01-02T00:00:00+00:00"
  },
  {
    "name": "article:expiration_time",
    "content": "2024-12-31T00:00:00+00:00"
  },
  {
    "name": "article:author",
    "content": "Author Name"
  },
  {
    "name": "article:section",
    "content": "Technology"
  },
  {
    "name": "article:tag",
    "content": "tag1, tag2, tag3"
  },
  {
    "name": "book:author",
    "content": "Author Name"
  },
  {
    "name": "book:isbn",
    "content": "1234567890"
  },
  {
    "name": "book:release_date",
    "content": "2024-01-01"
  },
  {
    "name": "book:tag",
    "content": "tag1, tag2, tag3"
  },
  {
    "name": "profile:first_name",
    "content": "John"
  },
  {
    "name": "profile:last_name",
    "content": "Doe"
  },
  {
    "name": "profile:username",
    "content": "johndoe"
  },
  {
    "name": "profile:gender",
    "content": "male"
  },
  {
    "name": "music:duration",
    "content": "205"
  },
  {
    "name": "music:album",
    "content": "Album Name"
  },
  {
    "name": "music:album:disc",
    "content": "1"
  },
  {
    "name": "music:album:track",
    "content": "1"
  },
  {
    "name": "music:musician",
    "content": "Artist Name"
  },
  {
    "name": "music:song",
    "content": "Song Name"
  },
  {
    "name": "music:song:disc",
    "content": "1"
  },
  {
    "name": "music:song:track",
    "content": "1"
  },
  {
    "name": "video:actor",
    "content": "Actor Name"
  },
  {
    "name": "video:actor:role",
    "content": "Role Name"
  },
  {
    "name": "video:director",
    "content": "Director Name"
  },
  {
    "name": "video:writer",
    "content": "Writer Name"
  },
  {
    "name": "video:duration",
    "content": "120"
  },
  {
    "name": "video:release_date",
    "content": "2024-01-01"
  },
  {
    "name": "video:tag",
    "content": "tag1, tag2, tag3"
  },
  {
    "name": "video:series",
    "content": "Series Name"
  }
]
```

> Apidog Version Should Be 2.7.2 or Later.

Navigate to: `Share Docs` -> `Publish Docs Sites` -> `LLM-friendly Features` to enable the following options.

<Background>
![llm-friendly-features.png](https://api.apidog.com/api/v1/projects/544525/resources/356565/image-preview)
</Background>

## Enable "MCP"

If enabled, a "MCP" button will be displayed in the documentation, guiding end users how to use the current API documentation in MCP-enabled IDEs, such as Cursor, Cline, etc., to assist the Agentic AI in writing code. For more details, please read: [Conntect Online API Documentation Published by Apidog to AI via Apidog MCP Server](apidog://link/pages/901468).

![Apidog MCP Server](https://assets.apidog.com/uploads/help/2025/03/26/1562f2ed8710ec754897595552c1b84c.gif)

## Enable "Copy Page"

If enabled, a "Copy page" button will be displayed in the documentation. Users can copy web page as Markdown for LLMs.

<Background>
![copy-page.png](https://api.apidog.com/api/v1/projects/544525/resources/356566/image-preview)
</Background>

## Enable LLMs.txt

If enabled, a `llms.txt` Markdown file will be generated in the root directory of your documentation site. This file contains links to every Markdown page on your site, along with concise descriptions. For example:

<Background>
![llms-txt.png](https://api.apidog.com/api/v1/projects/544525/resources/356567/image-preview)
</Background>

### How can AI assistants use LLMs.txt?

There are two common methods for using LLMs.txt and the related Markdown files:

**1. Share Markdown links with AI assistants that can access URLs**

Each online documentation page published via Apidog has a Markdown version. You can:

- Add ".md" to any doc URL (e.g., https://example.apidog.io/page.md)
- Or click "View as Markdown" in the online documentation

<Background>
![Add-.md-to-any-doc-URL.png](https://api.apidog.com/api/v1/projects/544525/resources/356568/image-preview)
</Background>

AI assistants with Web Browsing capabilities can use these ".md" URLs to retrieve concise documentation.

For example, in Cursor, you can ask "Understand this info: @https://zojphlasi1.apidog.io/find-pet-by-id-12888653e0.md and help me generate a TypeScript client".

<Background>
![usage example using Cursor.png](https://api.apidog.com/api/v1/projects/544525/resources/356570/image-preview)
</Background>

:::tip[]
The prompt format must follow the specific rules of the AI tool being used. For instance, in Cursor, URLs must begin with `@` to be recognized as context.
:::

**2. Copy Markdown content for AI assistants that can't access URLs**

If the AI assistant cannot access content via URL, you need to copy and paste the Markdown content manually.

<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/356571/image-preview)
</Background>

Click the "Copy Page" button in the online documentation to get the current page content in Markdown format, then paste it into your conversation with the AI assistant.

Example prompt:
"Based on this endpoint definition, please generate a TypeScript client: (paste the copied content here)."

<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/356578/image-preview)
</Background>

### FAQs

<Accordion title="Does enabling LLMs.txt affect documentation security?" defaultOpen>
**No.** LLMs.txt only includes content that has already been publicly published. It simply converts HTML to Markdown and does not expose private or unpublished documentations. If access control (password, IP allowlist, email allowlist, etc.) is set up, users must still pass authentication to access LLMs.txt and the Markdown files. 
</Accordion>

<Accordion title="Can I use LLMs.txt if my docs are protected by password, IP, or email allowlist?？" defaultOpen={false}>
Yes, you can. However, since accessing LLMs.txt and Markdown files requires authentication, AI assistants may not be able to access them directly via URL. In that case, use the "**Copy Page**" feature and paste the content manually. 
</Accordion>

<Accordion title="Why don't I see the button `Copy Page` in Apidog App?" defaultOpen={false}>
These features are available in the **published online documentation**. After publishing the docs, open them in your **web browser** to see the buttons. 
</Accordion>

<Accordion title="I’ve enabled 'Web Search' feature for my AI assistant. Why can’t it read the web page content via URL?" defaultOpen={false}>
"Web Search" and "Web Browsing" are different features. 
- **Web Search** allows the AI to query search engines and summarize results. 
- **Web Browsing** allows the AI to directly access and read a specific URL’s content. 
</Accordion>

<Accordion title="What should I do if the AI assistant fails to access the Markdown file via URL?" defaultOpen={false}>
Use the "**Copy Page**" button in the online documentation and paste the content directly into the AI conversation. 
</Accordion>

<Accordion title="Do I need to do anything else after enabling LLMs.txt?" defaultOpen={false}>
No. Once enabled, the system will automatically generate `llms.txt` and Markdown files for each documentation page. You just need to maintain the original documentation.
</Accordion>

<Accordion title="How can I verify that LLMs.txt is working properly?" defaultOpen={false}>
Visit the `/llms.txt` path at the root of your published documentation site. If you see a structured list of page links, the feature is enabled and working.
</Accordion>


By default, your documentation are accessible on a `[subdomain].apidog.io` domain. However, you can customize this by setting a custom domain, meaning your audience will be able to access your documentation on a domain that fits your organization.

Custom domains can be set by users with admin permissons. Please follow these steps in order to set a custom domain.

## Initiating the custom domain setup

You can access the options for setting a custom domain for a project in the project's Share module. Simply click on the **Share Docs** menu in the sidebar, and then navigate to the **Publish** settings page in the secondary menu.

You will see a section titled **Custom Domain**. Click on the **Edit** button to initiate the custom domain setup.

<Background>

![CleanShot 2025-05-21 at <EMAIL>](https://api.apidog.com/api/v1/projects/544525/resources/355221/image-preview)
</Background>


There is two types of options for setting a custom domain:

1. **CNAME**: This is the recommended option. It is the easiest to setup and maintain. It is also the most flexible option, as it allows you to set a custom domain for a subdomain or a root domain.
2. **Reverse Proxy**: This option is more advanced and requires you to use Content Delivery Network (CDN) or setup a reverse proxy on your own server. It is recommended for users who are familiar with these technologies.
<Background>
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/344109/image-preview" style="width: 440px" />
</p>
</Background>
## Configuring CNAME

:::tip[]
This is only applicable if you have selected the **CNAME** option in the previous step.
:::

Configuring DNS happens _outside_ of Apidog, at the DNS provider you are using for your domain.

There are two parts to this step:

1. Configure a CNAME record
2. Wait for the changes to take effect

### Configure a CNAME record

The names of the fields and what to actually enter to configure the record may differ between DNS control panels, but we’ve covered the most common options here.If you're uncertain, verify with your DNS provider.

* The **type** is the kind of DNS record that you want to create. Here, you need to choose **CNAME**.
* The **name** or **DNS entry** is where you enter your subdomain. You might need to enter it in full (e.g. **docs.example.com**) or you might just need to enter the part before your apex domain (e.g. **docs**). If you’re not sure which to use, check with your DNS provider.
* The **target** or **value** or **destination** is where the subdomain should be pointed. You should see the value for this in the Publish settings in Apidog when you choose the DNS CNAME option. It will look something like `{docsSiteId}.apidog.io`. You should enter this value in full (e.g. `12345678.apidog.io`).
* You might also see a field named **TTL**, which stands for Time To Live. It’s the number of seconds that the DNS record can be cached for. If you’re not sure what to set, we suggest select `Auto` or remain default value.

Here’s an example of how a correct configuration looks in Cloudflare’s control panel:
<Background>
![](https://assets.apidog.com/help/assets/images/custom-domain-with-dns-cname-e2aa8ebcf17f637e3224c99a1a784e03.png)
</Background>



:::caution[]
**Note:** CNAME record cannot co-exist with another record for the same name. If you already have an A record, AAAA record, TXT record, or any other type of record for your chosen subdomain, you would need to remove those first, _before_ adding the CNAME record.
:::


:::caution[Are you using Cloudflare?]
If you are configuring DNS in Cloudflare’s control panel, please ensure that Cloudflare’s proxying (the orange cloud, also called "Proxy status" in your domain settings) is **disabled**. This is for two reasons:

* This option obfuscates the DNS target for your domain to the public, preventing Apidog from properly running routine checks on your custom domain.
* Your custom domain will already benefit from CDN.

Again, please **turn off Cloudflare proxying** to ensure that your documentation is served without issues.
:::





### Wait for the changes to take effect


The short answer: you might need to wait `10 minutes ~ 48 hours` for the DNS changes to take effect before moving onto the next step.

Remember the TTL (Time To Live) field we mentioned earlier? DNS records are cached for a period of time — which is usually a very good thing for performance reasons, because they typically don’t change very often. When they _do_ change, there is a period of time (the TTL value) where DNS cache servers need their cache to expire before they will check for any changes and behave accordingly.

In most cases, it’s best to allow at least 10 minutes before moving onto the next and final step. Sometimes it could all update a bit more quickly, or it could take longer. It’s rare for this to take longer than 48 hours.

Want to check how this process, known as _propagation_, is progressing? You could use a DNS lookup tool, such as [WhatsMyDNS](https://www.whatsmydns.net/). Enter your full subdomain, select CNAME from the dropdown list, and press the Search button. DNS cache servers around the world will respond to let you know what their cached result is. You’ll want to periodically check these results until the vast majority respond with your assigned CNAME value.

## Configuring CDN or Your Own Reverse Proxy Server

:::tip[]
This is only applicable if you have selected the **Reverse Proxy** option in the previous step.
:::

### Configure AWS CloudFront as reverse proxy

You can utilize the CDN service provided by cloud vendors like AWS CloudFront, Cloudflare Enterprise to set it up as your own reverse proxy server.

In the following example, we will configure AWS CloudFront as Reverse Proxy.

1. Log in to AWS, and navigate to [CloudFront](https://console.aws.amazon.com/cloudfront). Click Create Distribution.
2. Configure your distribution settings. Here are the values you'll need to change.

| Settings                        | Value                                                        |
| :------------------------------ | :----------------------------------------------------------- |
| Origin Domain Name              | Set to `{docsSiteId}.apidog.io`                               |
| Name                            | A description for the origin. This value lets you distinguish between multiple origins in the same distribution and therefore must be unique. |
| Origin Protocol Policy          | Set to **HTTP** Only                                         |
| Alternate Domain Names (CNAMEs) | Set to your custom domain name (the same one your configured in the Publish settings during the custom domain setup) |
| SSL Certificate                 | Set to the SSL Certificate for your custom domain stored in AWS Certificate Manager (ACM). |

3. Provide information on the Origin Custom Headers (the Header Name and Value fields appear only after you've provided an Origin Domain Name)

| Header Name         | Value                |
| :------------------ | :------------------- |
| X-Apidog-Docs-Site-ID | Set to `{docsSiteId}` |

:::tip[]
`{docsSiteId}` is your Docs Site ID, which can be found in the custom domain panel. Please make sure to enter the correct ID.

<Background>
<img src="https://api.apidog.com/api/v1/projects/544525/resources/355223/image-preview" style="width: 460px"/>
</Background>
:::

4. Configure the Default Cache Behavior Settings. Here are the values you'll need to change.

| Setting                           | Value                                                        |
| --------------------------------- | ------------------------------------------------------------ |
| Viewer Protocol Policy            | Select **Redirect HTTP to HTTPS**                            |
| Allowed HTTP Methods              | Select **GET, HEAD, OPTIONS, PUT, POST, PATCH, DELETE**.     |
| Cache and origin request settings | Select **Use legacy cache settings**. Select **All** for Headers, Query strings and Cookies |
<Background>
![](https://assets.apidog.com/help/assets/images/custom-domain-with-cdn-1-cae8a72ee17ee23e9bd3f3438109d2bd.png)
</Background>
5. Do not enable AWS **Web Application Firewall (WAF)**.
6. Click **Create distribution** at bottom of the page. You'll see your newly-created distribution in your CloudFront Distributions list. Note that the Status will reflect In progress until the distribution is Deployed.
7. Add a new CNAME record to your DNS for your custom domain pointing to the CloudFront Domain Name for your Distribution. This can be found by clicking on your Distribution ID, under the General tab, Distribution domain name (for example, fd1fbc7cac6197.cloudfront.net).

### Configuring your own reverse proxy server

You can configure your own reverse proxy server for your API documentation. In the following example, we will use `Nginx` as the reverse proxy server.

1. Add the following content to the `Nginx` configuration file for simple configuration.

```nginx
server {
    ...
    location / {
        proxy_pass  http://{docsSiteId}.apidog.io;
        proxy_set_header X-Apidog-Docs-Site-ID {docsSiteId};
        # Set your custom domain name to the Host value (Eg. docs.example.com).
        proxy_set_header Host docs.example.com;
        ...
    }
    ...
}
```

Caddy configuration example:

```caddy
:8080 {
    handle_path /* {
        reverse_proxy http://{docsSiteId}.apidog.io {
            header_up X-Apidog-Docs-Site-ID {docsSiteId}
            header_up Host "docs.example.com"
        }
    }
}

```

:::tip[]
`{docsSiteId}` is your Docs Site ID, which can be found in the custom domain panel. Please make sure to enter the correct ID.

<Background>
<img src="https://api.apidog.com/api/v1/projects/544525/resources/355223/image-preview" style="width: 460px"/>
</Background>
:::

2. Configure DNS record for your custom domain name to point to your reverse proxy server.

## Deploying API Documents to a Subdirectory of a Custom Domain

Apidog's `Reverse Proxy`allows API documents to be deployed to a subdirectory of a custom domain. For instance, you can deploy the documentation to the `/api-docs` path on a domain like https://example.com. When users visit https://example.com/api-docs, they will be accessing the online API documentation hosted by Apidog.

### Configuration Steps:

1. On Apidog's `Custom Domain` setting page, enter your custom domain.
2. Select `Reverse Proxy` and enable `Use Subdirectory`, then enter the subdirectory path.
<Background>
![](https://assets.apidog.com/uploads/help/2024/04/29/8ad1f025cf9ca959466013c2d80939b2.png)
</Background>
3. Next, you'll need to modify the configuration file of your web server. Assuming you're using Nginx to proxy your service, you can refer to the following configuration:

- `proxy_pass`: Forward client requests to another server (such as Apidog’s API documentation server).
- `proxy_set_header`: Set request headers sent by the proxy server to the upstream server, ensuring the request is properly handled. 

```nginx
server {
    ...
    location /api-docs/ {
        proxy_pass  http://{docsSiteId}.apidog.io/;

        proxy_set_header X-Apidog-Docs-Site-ID {docsSiteId};

        # Set your custom domain name to the Host value (Eg. docs.example.com).
        proxy_set_header Host docs.example.com;
        ...
    }
    ...
}
```

:::tip[]

- `/api-docs/` is the subdirectory of the custom domain, and it must end with a `/` in the Nginx configuration.
- `http://{docsSiteId}.apidog.io/` must also end with a `/`.
- Replace `{docsSiteId}` with your Apidog doc site ID.
    <Background>
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/355223/image-preview" style="width: 460px"/>
    </Background>

- `docs.example.com` is a sample custom domain. Replace it with your actual custom domain.
- After configuration, you need to restart Nginx on your server.

:::

## Enable HTTPS

Apidog's online documentation supports the HTTPS protocol, which has several advantages over HTTP:

- **Secure data transmission**: HTTPS uses SSL/TLS encryption to ensure the security of data transmission, preventing third parties from intercepting information.

- **SEO optimization**: Search engine crawlers prefer to use HTTPS because it offers better security and privacy protection. Therefore, HTTPS websites may have higher authority in search engine rankings than HTTP websites.

### Steps to Enable HTTPS:
1. Go to the `Publish` page and open the `Custom Domain` tab.
2. Switch on `HTTPS` to enable HTTPS, and optionally, you can enable `Always Use HTTPS` to prevent communication from being hijacked or man-in-the-middle attacks.
<Background>
![](https://assets.apidog.com/uploads/help/2024/04/29/db039aa84db048de49574a5bec49015d.png)
</Background>
## SSL Certificate Management

Once HTTPS is enabled, you can choose how to manage your SSL certificate:

- **Generated by Apidog**: Apidog will automatically generate an SSL certificate.
- **Use Your Own Certificate**: You can upload an SSL certificate and private key issued by a certificate authority(e.g., [Let's Encrypt](https://letsencrypt.org/)).

## Troubleshooting[](#troubleshooting)

If you are having issues setting up your custom domain, please contact us via [Discord](https://discord.gg/ZBxrzyXfbJ).

### Are you using Apidog Europe?

If you are using Apidog Europe, please ensure that you are using the correct domain for your custom domain setup.

The correct domain for Apidog Europe in previous setup is `{docsSiteId}.eu.apidog.com`.

