> **Pro Tip:** For all your API development needs, streamline your workflow with [Apidog](https://apidog.com) – the all-in-one API development platform trusted by professionals!

# Discover OpenCode: The Terminal-Based Open Source Alternative to Claude Code

If you're a developer who thrives in the terminal, you know the power and focus that comes from working directly in your shell. While many AI coding assistants are designed for graphical interfaces, they can disrupt your command-line flow. What if your AI coding partner could work right alongside you, in the environment you love most?

Meet **OpenCode**—a robust, open-source AI coding agent purpose-built for the terminal. After a comprehensive overhaul, OpenCode has emerged as a feature-rich, reliable, and highly adaptable tool for developers who value deep workflow integration and flexibility. Unlike simple wrappers around ChatGPT, OpenCode is a thoughtfully engineered solution that brings a host of advanced capabilities to your terminal.

Picture a sleek, customizable terminal UI that feels native to your workflow. OpenCode integrates seamlessly with Anthropic, letting you leverage your Claude Pro or Max subscription for top-tier, cost-effective AI responses. Plus, it supports over 75 LLM providers—including local models—so you're never tied to a single ecosystem. With automatic code context awareness via the Language Server Protocol (LSP), and instant session sharing, OpenCode is designed for serious productivity.

This guide will walk you through everything you need to know about OpenCode: from installation and setup to advanced automation, customization, and collaborative features.

### Quick Start: Installing OpenCode

Getting [OpenCode](https://github.com/sst/opencode) up and running is simple, with support for multiple package managers and installation methods across macOS, Linux, and Windows (via WSL).

**How to Install**

![](https://assets.apidog.com/blog-next/2025/06/image-469.png)

OpenCode is distributed as a Node.js package, making it easy to install globally with your favorite package manager:

- **npm**:

```bash
npm install -g opencode-ai
```

- **Bun**:

```bash
bun install -g opencode-ai
```

- **pnpm**:

```bash
pnpm install -g opencode-ai
```

- **Yarn**:

```bash
yarn global add opencode-ai
```

Prefer not to use Node.js package managers? There are direct install scripts for macOS and Linux, and Homebrew support for Mac users.

**Authenticating with Your AI Provider**

![](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-20-at-1.47.18-PM.png)

After installation, connect OpenCode to your preferred LLM provider. The authentication process is streamlined and user-friendly. Just run:

```bash
opencode auth login
```

You'll be guided through an interactive prompt to select a provider (Anthropic, OpenAI, Google, and more) and enter your API key, which is stored securely on your machine. OpenCode can also auto-detect API keys from environment variables (like `OPENAI_API_KEY`) or a `.env` file in your project root, making setup even easier.

To manage your credentials, use:

```bash
opencode auth list
```

Or the shorthand:

```bash
opencode auth ls
```

To remove a provider, run `opencode auth logout` and follow the interactive steps.

### Exploring the OpenCode Terminal Experience

Once authenticated, you're ready to launch your first session. Simply navigate to your project directory and start OpenCode:

```bash
opencode
```

You can also specify a different directory:

```bash
opencode /path/to/another/project
```

**Deep Code Intelligence with LSP**

OpenCode stands apart from basic AI tools by leveraging the Language Server Protocol (LSP). On launch, it scans your project, detects languages and frameworks, and starts the appropriate LSP server in the background. This is the same technology powering features like autocompletion and go-to-definition in editors like VS Code.

With LSP, OpenCode provides the LLM with a rich, structural understanding of your codebase. When you ask for a refactor or explanation, the agent can analyze dependencies, function signatures, and usage—leading to more accurate and context-aware results. No more manual copy-pasting or context wrangling; OpenCode handles it all automatically, with zero configuration.

**A Productive Terminal UI**

The TUI is designed for efficiency, featuring a main chat window, prompt input, and a status bar with session info. It's fully themeable to match your terminal's look, and offers extensive keybindings for a seamless, mouse-free workflow.

### Advanced Workflows & Customization

OpenCode isn't just interactive—it's scriptable and highly configurable for power users.

**Automate with Non-Interactive Mode**

Use the `opencode run` command for one-off, non-interactive tasks—perfect for scripts, aliases, or CI/CD integration. For example:

```bash
opencode run "Explain the most common uses of the 'awk' command with examples"
```

You can continue previous sessions, specify models, or generate shareable links—all from the command line.

**Effortless Session Sharing**

Collaboration is easy: after a session, generate a unique URL to share a read-only snapshot of your conversation. This is invaluable for code reviews, debugging, or onboarding new team members.

**Ultimate Model Flexibility**

OpenCode's provider-agnostic design means you can use over 75 LLMs, including OpenAI, Google, and local models via Ollama. Add multiple credentials and switch between them as needed, directly from the CLI.

**Personalize Your Setup**

Customize the TUI with themes, set your default model, tweak keybindings, and more via the configuration file—tailor OpenCode to your exact preferences.

![](https://assets.apidog.com/blog-next/2025/06/image-470.png)

"Tokyo Night" theme in OpenCode

### Final Thoughts: OpenCode for Terminal-First Developers

OpenCode redefines what's possible for AI-powered coding in the terminal. With deep LSP integration, multi-provider support, session sharing, and powerful automation, it's a must-have for developers who want an AI agent that fits their workflow—not the other way around. As an open-source project, OpenCode puts you in control, letting you code smarter, faster, and on your own terms.
