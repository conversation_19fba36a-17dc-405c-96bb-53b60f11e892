# 用 Apifox Markdown 打造高颜值产品文档的 10 个技巧

在撰写产品或者技术文档的时候，让内容的排布具有清晰的结构，以及让内容具有直观的视觉呈现是相当重要的。这样就能让看这份文档的读者快速 get 到内容中想要表达的意图，减少阅读理解的成本。

[Apifox](https://apifox.com/) 作为一款 API 管理工具，不仅能高效管理 API 文档，还能通过灵活的 [Markdown](https://markdown.apifox.cn/) 语法，帮助团队打造专业级产品文档。现在你看到的 [Apifox 帮助文档](https://docs.apifox.com/)，就是用 Apifox 自身的 Markdown 能力打造而成的。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_gif/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2hFicvqOoA5w3mVhgpMfGOgxGnB7XbiaibFI3qVsRb44H4tGR9vVsRSIzg/640?wx_fmt=gif&from=appmsg&tp=webp&wxfrom=10005&wx_lazy=1)

以下是在 Apifox 中使用 Markdown 撰写产品文档的 10 个实用技巧。文中涉及的 Markdown 示例，均已整理至 Apifox 的《[**Markdown 文档最佳实践**](https://markdown.apifox.cn/6629517m0)》模块中，可前往查看示例效果。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2yfUbmViaqnJKC53kneV8N0iczhknCxPDshGia9cl42LHyl6ib3rG0LiccJw/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHRrwUuhB2ek6GeMibpmVd9glwEFjYQRW5EiapWKwBtSM4bG9icn4KYickgw/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=10005&wx_lazy=1)

**目录树与标题独立配置**

在 Apifox 中新建 Markdown 文档并将其命名时，“文档标题”会默认作为目录树中的显示名称，如下图所示：

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2VmyXV2ByA0C66OHFNCepib6cAJz70EsN42G0oHOyblBe1szfWQlJVIg/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

如果你希望目录树名称和文档标题不一样，也可以分别自定义它们。

比如，你可以用“概述”作为目录名称，而将文档标题命名为更具体的“设计 API”。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2hiaYnL0vb2w3hlA7680u0Py66fEyvoDooJicVLKlhdajtKeEI9tQ6z1Q/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

**操作方法**

在 Markdown 文档内，点击“目录树中显示的名称”即可进行修改。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2SYC9iaQqKDZnVqCo8U1pqXPJcDjfVC7x5dPbGGZqjTBYhfNoacNz0ag/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

将目录名称和文档标题分开命名，可以让目录保持简洁，而文档标题则更具体。第三方平台解析文档链接时，会以“文档标题”作为预览显示，具体的文档标题会让显示效果更加友好。例如，这是在企业微信中的解析预览：

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2VoJQXC8tcQodhp5oqwmJVG9TM5xV8Pl39MzTFUUJDxTeZmKE5jiaiayg/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHibEdw9kqjoE8AfibpXFqffXRamPhoL4o4PDWsaTb9n4IOxnltNOrXzzA/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=10005&wx_lazy=1)

**引用项目资源与数据模型**

Apifox 支持在 Markdown 文档中直接插入当前项目的接口、文档和数据模型资源。

点击 Markdown 文档内的「插入项目内接口/文档」按钮，然后选择目标资源*（接口或文档）*即可。被嵌入的内容会自动以链接形式指向项目中的目标资源，在渲染界面点击后可直接跳转到相应的详情页。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2FIfoqgLzobwNg6tJ2YQGnrzoDIZiavyMDU6JialAB179reNsmp37IGUQ/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

依次选择「插入 -> 数据结构」即可将“数据模型”嵌入到 Markdown 文档中。当项目成员修改接口数据模型的字段时，文档中已嵌入的数据模型会自动同步更新，无需手动维护，文档始终反映最新的 API 结构，这可以避免因文档滞后导致的沟通障碍。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2RF8pic6IvgEofu7K2CN5FVRPvwzTZ1pwiblzbfnuAmIfIJXicFicRl2Xqg/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

示例代码： 

`<DataSchema id="*********" />   `

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2gjIia77ufqz5jyxJwovrFpMCrc8WatCHMbjhX4iaFgMaUbyic7E3eIhfA/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHkm6icBUaAzYXTFN8TyIwvX9NL6aTGS8wXFrHvqUF0xRW82WCVdffDkA/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=10005&wx_lazy=1)

**链接跳转行为与打开方式**

Apifox 的 Markdown 支持多种链接格式。在“在线文档”中点击这些链接时，链接的打开方式取决于链接的类型：有的会在浏览器的新标签页中打开，有的会在当前页跳转，还有的可以通过锚点直接跳转到文档中的指定位置，选择不同的跳转方式可以帮助用户更高效地浏览与定位内容。

下文将详细说明各类链接在“在线文档”中的打开行为及配置方式。

**在当前页打开的链接**

通过 Markdown 编辑器右上角的「插入项目内接口/文档」按钮插入的链接资源，会生成以`apifox://link/pages/...`开头的地址，这类链接点击后会在浏览器当前页中打开。例如：

`[Apifox Markdown 概述](apifox://link/pages/4275322)`

![Image](https://mmecoa.qpic.cn/sz_mmecoa_gif/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2xa91kN6vV0ejqYicatAnF90SZ5FNg2bNBwRnxHsQJte7xBG2WNh9A1Q/640?wx_fmt=gif&from=appmsg&tp=webp&wxfrom=10005&wx_lazy=1)

链接插入方式：点击「插入项目内接口/文档」按钮，选择接口或文档即可。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh28EMe8CbSiaYVoiadpjQmic2Gs6LOOO9egOzgjjRql2q5RlX4O6FCckwcg/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

形如`apifox://link/pages/...`这样的链接，在 Apifox 的 Markdown 中可按普通链接使用。文档发布或分享后，它会被自动转换为实际的在线访问地址（如 `https://xxx.apifox.cn/...`），并支持跳转。例如在「卡片」组件中的用法：

`<Card title="点击我" href="apifox://link/pages/4275322">     这是一个卡片，点击后跳转到目标页面   </Card>`

![Image](https://mmecoa.qpic.cn/sz_mmecoa_gif/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh204DEzhNwa41WlPsrTXcBzkrLDKuyHI6ov1StAqkoRN1YYeJVo6s4MQ/640?wx_fmt=gif&from=appmsg&tp=webp&wxfrom=10005&wx_lazy=1)

**在新标签页打开的链接**

只要不是通过「插入项目内接口/文档」方式添加的链接，点击后都会默认在浏览器的新标签页中打开。例如：

`[Apifox Markdown 概述](https://markdown.apifox.cn/4275322m0)`

![Image](https://mmecoa.qpic.cn/sz_mmecoa_gif/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2cSle0knwPh1lzXpbzc83bjAt1ZXWicQicyCfYcFCa29wtKHJ00w7wC7w/640?wx_fmt=gif&from=appmsg&tp=webp&wxfrom=10005&wx_lazy=1)

**锚点链接（新标签页打开）**

如果希望点击链接后跳转到某个目录下的具体位置，可以在链接后加锚点`#xxx`。例如：

`[Apifox Markdown 概述](https://markdown.apifox.cn/4275322m0#如何使用-apifox-markdown)   `

![Image](https://mmecoa.qpic.cn/sz_mmecoa_gif/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2jqIp2qzo9khRApoibibbJqEu6ltaCh027Ho9HH0wV9VWQ8naoaECZhXg/640?wx_fmt=gif&from=appmsg&tp=webp&wxfrom=10005&wx_lazy=1)

**锚点链接（当前页打开）**

你也可以在项目内的链接中使用锚点，实现在当前页里跳转到文档中的某个位置。例如：

`[Apifox Markdown 概述](apifox://link/pages/4275322#如何使用-apifox-markdown)`

![Image](https://mmecoa.qpic.cn/sz_mmecoa_gif/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2z95vR5vVQeHQzwIMic3usmova3e11wRx81D0MQvELr6VUQEIfl7T8icw/640?wx_fmt=gif&from=appmsg&tp=webp&wxfrom=10005&wx_lazy=1)

类似`apifox://link/pages/4275322#如何使用-apifox-markdown`这样的锚点链接需要将文档分享或者发布出去后，在浏览器中点击才能生效。在 Apifox 端内点击时会提示“引用资源未找到”，因为链接还未渲染为实际可访问的路径。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2fvgibBnrgwk7ONdYuS8vIz0ATSic5oj9dSnJu2W0Ahfe2vEIr6LsfdAw/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

上文的链接除了是 Markdown 格式的，还可以是 HTML 格式的，例如：

`在当前页打开的链接:   <a href="apifox://link/pages/4275322">Apifox Markdown 概述</a>      在新标签页打开的链接:   <a href="https://markdown.apifox.cn/4275322m0">Apifox Markdown 概述</a>      锚点链接（新标签页打开）:   <a href="https://markdown.apifox.cn/4275322m0#如何使用-apifox-markdown">Apifox Markdown 概述</a>      锚点链接（当前页打开）：   <a href="apifox://link/pages/4275322#如何使用-apifox-markdown">Apifox Markdown 概述</a>`

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHo5KkDQzWkK5MVjVLr6lNeASPNAPibGx91kKlSG5FmvOWzseGyVmj7qA/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=10005&wx_lazy=1)

**容器+列表实现左右排版**

当需要展示新旧版本差异、功能对比、并列信息等内容时，可以使用「容器」组件嵌套「列表」组件，创建视觉上醒目的左右分栏布局。

这样的排版方式能让读者直观地比较不同内容，提升信息传递效率。

示例：

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2H8txxtseqQXlXOKQ3BzSRbRYdiaw8geOlEwMKia1yU0iaVxkpfVxr1yrg/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

**操作方法**

在 Markdown 文档中先选择「容器」，再选择「多列 - 2 列」即可。你可以在每一列中填入对应内容，比如“旧版本”与“新版本”说明。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2Ak6vZWaSiboph0vbRVfj5BhoDTvSsaOxLXKZfckUXxUOrFTFdSBJFcA/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

示例 Markdown 语法参考：

`<Container>          <Columns>         <Column>           **旧版本**         </Column>         <Column>           **新版本**         </Column>          </Columns>              ---          <Columns>         <Column>           这里是旧版本的内容           <DataSchema id="151301629" />         </Column>                    <Column>           这里是新版本的内容           <DataSchema id="151301749" />         </Column>       </Columns>   </Container   `

**💡** 上述 Markdown 中的`<DataSchema id="xxxxxx" />`引用的是项目中的数据模型。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHUwEgu04p2sXka3m1k20QfHcCavygwjKy3SsbIbcRVgQEHEb3pVkylQ/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=10005&wx_lazy=1)

**步骤组件实现视觉引导**

对于实操性强的教程或功能指南，可以使用 Apifox Markdown 里的「步骤」组件，将复杂流程分解为清晰可见的步骤序列。

引导式设计能降低用户理解难度，提升操作成功率，尤其适合新手用户或复杂功能介绍。

「步骤」组件会自动为每个步骤添加序号，并以醒目的视觉样式引导用户按顺序完成操作。

示例：

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2N8PUjowZSO14kjYkMjajz7V4yKo86RVT9H9czJhwHdicYJtOsn9LPVw/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

**操作方法**

在 Markdown 文档中选择「步骤」组件即可，你可以为每一步输入详细内容。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2BibYmkibXWicboO5UYIooAF5SefG9sZA91I3ft0zCYOaccf8mKec4VBkg/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

示例 Markdown 语法参考：

`<Steps>       <Step title="第一步">           这里是第一步的内容           <Background>             ![](https://cdn.apifox.com/app/static/example/markdown-image-example.png)           </Background>       </Step>       <Step title="第二步">           这里是第二步的内容       </Step>       <Step title="第三步">           这里是第三步的内容       </Step>   </Steps>   `

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHqLFibkWJe0DSWrzKXy4MGdrOKrpZvRmxtibS0XBw9Pcx094C4ibl4ebqQ/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=10005&wx_lazy=1)

**图片呈现与排版技巧**

在 Apifox Markdown 中插入图片很简单，你可以将图片从本地上传或者直接复制粘贴到编辑界面。

图片链接可以来自第三方 CDN，如：

`![Hifox](https://assets.hifox.cn/www/_next/static/media/feature-image-generation.bf35e616.webp)   `

支持使用 Markdown 或 HTML 两种语法插入图片，如：

`![Apifox](https://api.apifox.com/api/v1/projects/4997831/resources/511936/image-preview)      <img src="https://api.apifox.com/api/v1/projects/4997831/resources/511936/image-preview" alt="Apifox"/>   `

采用 HTML 格式的图片可以添加 CSS 行内样式，比如给图片指定宽高并居中：

`<img src="https://api.apifox.com/api/v1/projects/4997831/resources/511936/image-preview" alt="Apifox" style="width: 400px;display: block; margin: 0 auto"/>      // 或      <div style="text-align: center;">     <img src="https://api.apifox.com/api/v1/projects/4997831/resources/511936/image-preview" alt="Apifox" style="width: 400px;" />   </div>   `

在暗黑模式下，如果图片有透明度，那么会默认将图片背景置为白色。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2XJ9ZiaotLGP0FLkn96niahTgokXFxRIF3FxREicdKrKGrordv9Bia26BnQ/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

要移除白色背景，也可以通过 CSS 的行内样式来设置，例如：

`<img src="https://api.apifox.com/api/v1/projects/4997831/resources/511936/image-preview" alt="Apifox" style="background-color: transparent;"/>`

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2HeQE0lAaoIMke3GmBMOtib1dkpzkyBDsuiclJEmlwAKMYYwl9iafSxPdQ/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

为了让图片的呈现效果更加专业美观，你可以给图片添加背景：

`<Background>     ![image](https://api.apifox.com/api/v1/projects/4444182/resources/450190/image-preview)</Background>`

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2WaMYtSK0CicGYj7CHuvwjyXp2FaC3nD7Zk9Dq19giaEhTD0picoSFK3OQ/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

或者给图片添加边框与说明：

`<Frame caption="外观偏好设置">     ![image](https://api.apifox.com/api/v1/projects/4444182/resources/450190/image-preview)</Frame>`

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2ldrIicDZwzCPbwKvCpE8chZCbUIqK611RRzZqJEBpzTjO2HZcfv1ZsQ/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHmCZv7HRa0TvgtBl7rhInPLNlkmX7LrHT2Ve9L3HoxMGic8sSBIBIMIScnicXJcAcxMLESFD5jm5OZQ/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=10005&wx_lazy=1)

**用表格清晰展示结构信息**

表格是展示结构化数据的最佳选择。Apifox Markdown 不仅支持标准 Markdown 表格语法，还允许使用 HTML 样式调整表格外观，使其更加专业美观。

基本表格：

`| 功能模块 |   状态     | 备注说明        |     |   ---   |   ---     |   ---          |     | 用户认证 | ✅ 已完成   | 支持多种登录方式 |     | 数据同步 | ⚠️ 进行中   | 预计下周完成    |     | 报表导出 | ❌ 未开始   | 计划Q3实施     |   `

调整列宽：使用 HTML 行内样式控制列宽，避免表格排版混乱。

`| <div style="width: 100px;">功能模块</div> | <div style="width: 100px;">状态</div> | <div style="width: 200px;">备注说明</div> |     | --- | --- | --- |     | 用户认证 | ✅ 已完成 | 支持多种登录方式 |     | 数据同步 | ⚠️ 进行中 | 预计下周完成 |  | 报表导出 | ❌ 未开始 | 计划Q3实施 |`

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2o0Kfb0m7XxDGXFkOEvKFMKVaiaKpVAmib2aUSPaj2amF7JaYCE9hdrCA/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHmCZv7HRa0TvgtBl7rhInPLAPDF20ibsPGibz2iausfywL9euysdtdgYAPyhV84reJ4MNVtW4CaUMMHw/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=10005&wx_lazy=1)

**用折叠块收纳 FAQ**

使用折叠块是组织常见问题（FAQ）或非必要细节的理想方式。这种结构可以让读者按需展开感兴趣的内容，避免页面被冗长的文字占满，保持文档的整洁性。

折叠块特别适合用于：常见问题解答、可选功能介绍、高级配置选项、故障排除指南等内容，让用户可以快速获取主要信息，同时又不失详尽性。

基本用法：

`<Accordion title="如何重置密码？" >   1. 点击登录页的「忘记密码」链接   2. 输入您注册时使用的邮箱地址   3. 点击「发送重置邮件」按钮   4. 在收到的邮件中点击重置链接，设置新密码   </Accordion>   `

设置默认状态（默认折叠）：

`<Accordion title="高级配置选项（可选）" defaultOpen={false}>   本节介绍一些高级但非必要的配置项，适合有特殊需求的用户。   - 自定义请求头设置   - 代理服务器配置   - 性能优化选项   </Accordion>`

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2h20Zia5nOpV4J9dgQadTZibSWhNYYJ93P9ewPcOkCKc0Jiabict2iaqDoMw/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHmCZv7HRa0TvgtBl7rhInPLfpfIrIa16mS5vcCKqPvAPxo25doaSC0tHJfD7YxWaMos4eo4HECn5A/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=10005&wx_lazy=1)

**用告示突出重要提示信息**

使用告示和高亮块可以有效突出重要信息，如注意事项、提示或警告，让用户不会错过关键内容。

告示：

`:::tip   这是一个提示类告示   :::      :::warning   这是一个警告类告示   :::      :::caution   这是一个注意类告示   :::      :::danger   这是一个危险类告示   :::      :::check   这是一个检查类告示   :::      :::info   这是一个信息类告示   :::      :::note   这是一个备注类告示   :::`

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2CN5u29227VnfJvsPTZaWoia7IfRgDuUgTqic17XuAcamK1oWaAN9qicAg/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

高亮块：

`:::highlight purple 💡   这里是高亮内容   :::      :::highlight yellow 👋   这里是高亮内容   :::      :::highlight orange 🚀   这里是高亮内容   :::      :::highlight red 🌟   这里是高亮内容   :::      :::highlight gray 🚀   这里是高亮内容   :::       :::highlight blue 📌   这里是高亮内容   :::      :::highlight green 🔑   这里是高亮内容   :::`

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh29jLYB1kUJrmLNjtagRpQWicfTNiaA6xBaibCDmHTJxhpZc3iaia36yIIDNg/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHmCZv7HRa0TvgtBl7rhInPLMyIrZNvq0Q17ROvBRbbDwX61hq4H8p4tXsRfeISicicYaDKuQW5FWcGg/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=10005&wx_lazy=1)

**为术语添加 Hover 和复制功能**

通过`Tooltip`组件，可以实现鼠标悬停在文本上时显示定义的效果。

`<Tooltip tip="这是一个提示！"> Hover 这里有提示</Tooltip>   `

通过`CopyToClipboard`组件，实现点击组件复制文字的效果。

`<CopyToClipboard >点击这里可复制文字</CopyToClipboard>   `

`Tooltip`组件与`CopyToClipboard`组件结合，可以实现“悬停提示+复制文字”的效果。

`<Tooltip tip="这是一个提示，可以复制！"><CopyToClipboard >点击这里可复制文字</CopyToClipboard></Tooltip>`

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh2Sia04sH6ibv663h2Eg16THXpibQRPibZiamiblwdD4d4X6OZUQGVicfGU09Uw/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlSpLBvPcuAgibxxkEqgBdh20PkDI7Zuz27dXicHx7MQr7r2PYLgyZeVPay9EFMePJ6P3fibQm4YKWtg/640?wx_fmt=png&tp=webp&wxfrom=10005&wx_lazy=1)

通过灵活运用 Apifox Markdown 的这些功能，你可以构建既专业又美观的文档。好的文档不仅能减少沟通成本，提高开发效率，还能展现团队的专业素养和产品品质。优秀的文档是产品体验的重要组成部分，值得投入时间和精力去打磨。想要学习更多技巧可以查看 [Apifox Markdown 文档](https://markdown.apifox.cn/)。





```

```
