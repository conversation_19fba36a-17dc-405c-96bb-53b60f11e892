Add an assertion
In our saved endpoint case, we can also add pre/post operations to prepare data or test this endpoint.

1
Switch to Post processors.
2
Hover over Add PostProcessor, select Assertion.
CleanShot 2024-08-28 at <EMAIL>
3
In this case, I want to assert that if the response "id" is a positive integer.
So fill in the Assertion form:
Name: "id" is a positive integer
Target Object: Response JSON
JSONPath expression: $.category.id
Assertion: Greater than 0
4
Click Send, and you will see the Assertion result in the bottom right corner.
image.png
5
Click "Save" to save the endpoint case.
In Apidog, you can visually add assertions, extract variables, perform database operations, and more. Learn more about pre and post processors.

You can also write assertions or implement other operations using scripts by simply adding a "Custom script". Apidog is compatible with Postman scripts, which can run in Apidog without modification. Learn more about Scripts.

