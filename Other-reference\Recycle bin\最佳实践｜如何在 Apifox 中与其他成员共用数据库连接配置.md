# 最佳实践｜如何在 Apifox 中与其他成员共用数据库连接配置

Apifox 作为一款接口文档 + 调试 + mock + 测试的协同工具，其中有一个备受好评的功能就是在发送/接收请求时，支持数据库操作。这个功能极大的方便了大家在请求接口时需要制作请求数据，或在接收到接口响应时需要插入数据到数据库中的操作，从而深得用户喜爱。

但是在大量用户每天大量实际使用这个功能的过程中，也发现了一些可优化点。其中最容易被 cue 到的一条是：

数据库连接的配置能不能团队管理员配置好了之后，其他人可以协同使用啊？每个人都要重新写一遍连接信息真的挺麻烦的。

在这个功能设计之初，我们便考虑到了是否允许数据库配置能够协同使用的问题。但是允许用户将数据库的账号、密码等敏感信息保存在云服务器上，是要慎重考量的事情。所以，**数据安全**是当时没有做协同使用数据库连接配置功能的核心原因。



**云端储存数据库连接配置**

到了如今，Apifox 已经是上百万开发者的主要 API 协同管理工具了，同时 Apifox 本身自己的基础能力也迭代的更加强大。因此，我们重新考虑了协同使用数据库连接配置这个优化需求，尽量做到数据安全与高效使用的平衡，期望让用户们能够满意。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHRrwUuhB2ek6GeMibpmVd9glwEFjYQRW5EiapWKwBtSM4bG9icn4KYickgw/640?wx_fmt=png&from=appmsg&randomid=50djwdh6&tp=webp&wxfrom=10005&wx_lazy=1)

当你的 Apifox 版本更新至 **2.6.50** 及以上，进入「项目设置 -> 数据库连接」，点击新建数据库连接时，即可发现所有连接字段会引导你使用变量形式来填入字段的值。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWySCdRvnSTHp4hOWOD2sBl9AIA4xqvArG1cibYOLxVAWictrmFkkoOibRIw/640?wx_fmt=png&randomid=hktyz3si&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHibEdw9kqjoE8AfibpXFqffXRamPhoL4o4PDWsaTb9n4IOxnltNOrXzzA/640?wx_fmt=png&from=appmsg&randomid=nrfz0ocm&tp=webp&wxfrom=10005&wx_lazy=1)

在环境管理中，于环境变量内设置好不同环境下需要使用的数据库连接的变量，这样即可将这些变量应用在数据库连接配置中。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyG6kiay3GHa0riczjbVKLrDWgXyyPwIduD0LuQ4P6UrEjn7v4SMRS2iaLg/640?wx_fmt=png&randomid=zpk868xc&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHkm6icBUaAzYXTFN8TyIwvX9NL6aTGS8wXFrHvqUF0xRW82WCVdffDkA/640?wx_fmt=png&from=appmsg&randomid=9hia0bsr&tp=webp&wxfrom=10005&wx_lazy=1)

回到数据库连接配置页，你可以使用变量格式手动填入变量，也可以通过“动态值”功能直接引用这个变量。我们推荐使用环境变量填入此处，这样可以随着不同环境的上下文，自动切换不同环境对应的配置。除端口号外，其他字段都最好使用变量形式。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyL2xv7zVsvr0zIFPXSOjOnY3qBiaWFF03wp5Uva9Y2n6q093GyJDAOTw/640?wx_fmt=png&randomid=5a7lyia8&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHo5KkDQzWkK5MVjVLr6lNeASPNAPibGx91kKlSG5FmvOWzseGyVmj7qA/640?wx_fmt=png&from=appmsg&randomid=pi57fyrp&tp=webp&wxfrom=10005&wx_lazy=1)

将使用变量的数据库连接配置进行保存，就可以在接口管理、自动化测试等地方的数据库操作中进行使用了。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyp0x1wKia1EAdr29lY9cfnhndEAav2fZcrT6348EW1uvictBSSf0zNDRA/640?wx_fmt=png&randomid=6jn5lf50&tp=webp&wxfrom=10005&wx_lazy=1)

在使用时，保存在本地与保存在云端的数据库连接配置，实际使用的机制如下：

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlyaV3YyV7vSlmoGicyomnJ4E9D4qiadupibkjtvFnuxFtklMeq6AohL3yI9KlKykxnmSXsodhLYLIaQ/640?wx_fmt=png&randomid=zcwgifkt&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHUwEgu04p2sXka3m1k20QfHcCavygwjKy3SsbIbcRVgQEHEb3pVkylQ/640?wx_fmt=png&from=appmsg&randomid=1x29x53k&tp=webp&wxfrom=10005&wx_lazy=1)

其他项目成员需要使用这个数据库连接配置的话，现在只用去环境管理中，找到对应的变量并填入本地值即可，不用像以前需要去项目管理中进行配置。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyUxznQTZBB6R1UdzYqCRqiaaE0wMpcG255Tz1H1Abtiaph2QiaFiaxUncfA/640?wx_fmt=png&randomid=l4f2lncf&tp=webp&wxfrom=10005&wx_lazy=1)

以上，是使用云端数据库连接的具体操作步骤。因为推荐使用本地值，所以实际配置仍然是保存在本地的，无需担心数据安全风险，只是使用变量形式让大家更方便进行了使用。当然 Apifox 仍然支持数据库连接配置中直接填写实际值，用以兼容以前的数据，以及仍然期望使用本地数据的需求。不过会有强提醒告知用户可以转换成使用变量来保存至云端，提升使用体验。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyLrbN3AulleN3R2vdTLc4GooUYvDzViaLKP6jUYlv6cC6ZHRJg2A8m9Q/640?wx_fmt=png&randomid=4uuq4s9z&tp=webp&wxfrom=10005&wx_lazy=1)

**使用云端数据库连接的注意事项**  

数据库连接配置中，使用了变量，实际保存在云端的内容为变量名，执行数据库连接时，会根据变量使用规则来拼成完整的连接配置，发起连接。

✅ **推荐行为**

❌ **不推荐行为**

■  变量值使用“本地值”或“Vault 变量”

■  端口号可直接填写，无需填入变量，方便使用&无风险

■  变量值使用“远程值”

■  明文与变量混用，导致仍然要每个人在项目设置中单独配置

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHmralwjNCeaB58haZdW1mG216PYSy9icwvWicVqvtjqRVNMmF4EkqZTowudNl726Fk9O8mWiaTicGYscA/640?wx_fmt=png&from=appmsg&randomid=yqe1kywm&tp=webp&wxfrom=10005&wx_lazy=1)

**使用 Vault 变量**

**来保存数据库连接配置**

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmWenDk4xMMmmpfWNsBfhT5wicrXnsMewicVN7YOKg0Qo1blYc599Ozfm6H1qQTbGibgApRou2WNR04Q/640?wx_fmt=png&randomid=vth7f4ag&tp=webp&wxfrom=10005&wx_lazy=1)

数据库连接配置，推荐使用 Vault 变量写入配置中。因为 Vault 变量是从外部专业密钥库获取、会被加密存储在你的本地客户端中，所以这样可兼得保存在云端的协同高效率，与数据安全的最佳效果。

[Apifox 商业旗舰版可使用 Vault Secrets（密钥库）功能。](https://apifox.com/pricing)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHRrwUuhB2ek6GeMibpmVd9glwEFjYQRW5EiapWKwBtSM4bG9icn4KYickgw/640?wx_fmt=png&from=appmsg&randomid=3vikqvbw&tp=webp&wxfrom=10005&wx_lazy=1)

设置 Vault 变量，将数据库连接的明文存入 Vault 变量中。不同环境的数据库连接配置，要在你的提供商中创建不同的 Vault Key。具体方法参考[帮助文档](https://docs.apifox.com/5831220m0)。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyEs5ibF5vSxO9vJNMiapXfwXUv9TnKvq7oqicZCUmvgBK8VazEUicAEHkiaA/640?wx_fmt=png&randomid=luto007m&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHibEdw9kqjoE8AfibpXFqffXRamPhoL4o4PDWsaTb9n4IOxnltNOrXzzA/640?wx_fmt=png&from=appmsg&randomid=ie6zqzsr&tp=webp&wxfrom=10005&wx_lazy=1)

在不同环境中，都创建相同名称的环境变量，例如：dbHost。然后在**远程值**中设置引用该环境对应的 Vault 变量，并保持本地值跟随远程值。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyku25LHmOJicDFqBHO9S3rCtNFg69Y23zY8kRfNcoDVEKDw5O9zkvGSQ/640?wx_fmt=png&randomid=5j6ut7qd&tp=webp&wxfrom=10005&wx_lazy=1)

这样做的目的是：

1. 使用环境变量包一层 Vault 变量，让后续设置数据库连接配置时只需要选择环境变量即可，实际使用数据库连接配置时，会跟着环境上下文自动选择此环境的连接配置；

2. 保存在远程值中，为了让项目成员无需再手动设置一次，提升协同使用效率。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHkm6icBUaAzYXTFN8TyIwvX9NL6aTGS8wXFrHvqUF0xRW82WCVdffDkA/640?wx_fmt=png&from=appmsg&randomid=i6shcst5&tp=webp&wxfrom=10005&wx_lazy=1)

在数据库连接中，填入在环境管理中设置好的数据库连接变量。可以使用“动态值”功能快速引用变量。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyJdukFN7FdoEHUQZlPkaWYsBLQ57NhLQ2ccHICF1x27wWLbeWFRSZGg/640?wx_fmt=png&randomid=nshwz8i1&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHo5KkDQzWkK5MVjVLr6lNeASPNAPibGx91kKlSG5FmvOWzseGyVmj7qA/640?wx_fmt=png&from=appmsg&randomid=e5pxxlcy&tp=webp&wxfrom=10005&wx_lazy=1)

点击测试连接，会提示选择需要测试的环境，注意测试连接的环境里一定要配置好对应的变量实际值。点击确认发现连接成功。如果有问题可根据具体报错信息进行处理。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyicwXoTnVNS9zZ8xONx9PX90IaibYBYoZZImM2vEvEeIC5Ef1C9sO9kGA/640?wx_fmt=png&randomid=tt4b172m&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHUwEgu04p2sXka3m1k20QfHcCavygwjKy3SsbIbcRVgQEHEb3pVkylQ/640?wx_fmt=png&from=appmsg&randomid=o546fmu8&tp=webp&wxfrom=10005&wx_lazy=1)

在某个接口请求的前/后置操作中，添加一个数据库操作，并选中上述已保存在云端的数据库连接配置，发起请求。例如，我需要从数据库表中找到宠物名为“Nancy”的宠物的 id，然后请求接口查询详情。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyEI3ND421szKZudL4tccr75GAErQCLRFX2fj4Y0veVYwD4WMm9CAraw/640?wx_fmt=png&randomid=gr95lth7&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHqLFibkWJe0DSWrzKXy4MGdrOKrpZvRmxtibS0XBw9Pcx094C4ibl4ebqQ/640?wx_fmt=png&from=appmsg&randomid=kj3c7okc&tp=webp&wxfrom=10005&wx_lazy=1)

发现 Apifox 成功执行了数据库操作，在数据库中取出了数据并根据我的要求将 petID 的值保存至 petId 变量中，然后请求出去了。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWysWYB21BM2AeD841noaVyhJib9k1nTURYcNAD9mZhkiaF1fzAdxGHYBKQ/640?wx_fmt=png&randomid=u46ul8h4&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHmCZv7HRa0TvgtBl7rhInPLNlkmX7LrHT2Ve9L3HoxMGic8sSBIBIMIScnicXJcAcxMLESFD5jm5OZQ/640?wx_fmt=png&from=appmsg&randomid=anr41bm4&tp=webp&wxfrom=10005&wx_lazy=1)

如果你已按照上述步骤配置好了数据库连接配置，其他项目成员即可直接在接口请求的数据库操作内指定使用此配置，来对数据库进行操作，无需再自己配置。

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyUxznQTZBB6R1UdzYqCRqiaaE0wMpcG255Tz1H1Abtiaph2QiaFiaxUncfA/640?wx_fmt=png&randomid=xgq9h3jf&tp=webp&wxfrom=10005&wx_lazy=1)

以上，即是使用 Vault 变量来保存数据库连接配置，并且实际应用的实践。其中关键要点：

- 把每个环境的数据库连接配置，除端口之外，都设置一个 Vault 变量。例如测试、正式环境分别有 Vault 变量：testDBHost、prodDBHost；

- 使用环境变量，在每个环境中都添加同名变量，并在这些环境变量的远程值中引用对应环境的 Vault 变量值。例如按照下表方式配置好测试、正式环境两个环境下的变量与具体变量远程值（本地值保持跟随远程值即可）：

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlyaV3YyV7vSlmoGicyomnJ4KuZCKhSmLo21w5IS4mhibGFxSDPQxapQI7ziaQS949oadgSw5QbjDZgw/640?wx_fmt=png&randomid=haqlg4b8&tp=webp&wxfrom=10005&wx_lazy=1)
