After designing/developing your API in Apidog, you can easily share the API with other colleagues or publish it to the public. Apidog allows you to conveniently publish your API documentation as a webpage.

## Share your API documentation
- In Apidog, you can generate beautiful API documentation with a single click from your defined API specifications. Learn more about [Quick share](apidog://link/pages/630189).
- The generated API documentation supports "Try it out" functionality and online code generation. Learn more about [View the API documentation](apidog://link/pages/631148).
- In Apidog, not only can you publish API documentation to a webpage, but you can also mix Markdown with API documentation, making it part of the user documentation. Learn more about [Publish docs sites](apidog://link/pages/631325).
- Apidog also supports [customizing the document's navigation bar](apidog://link/pages/631390), [custom document domain names](apidog://link/pages/631339), [custom page URLs](apidog://link/pages/631392), and more.
- You can [customize the folder style and clicking behavior](apidog://link/pages/646356) in the documentation.
- Apidog supports publishing [multi-version API documentation](apidog://link/pages/645639).

:::highlight purple
Here's an example of published documentation: https://docs.salla.dev/
:::

## API Technologies Supported

Apidog supports writing documentation for almost all types of APIs, including `REST`, `SOAP`, `GraphQL`, `gRPC`, `WebSocket`, `SSE`, and more.

Explore our collection of documentation best practices and real-world API examples.


:::highlight purple
Learn more about [API Technologies Supported](apidog://link/pages/895780).
:::

## Differences between two types of documentation
In Apidog, there are two ways to publish documentation. 

The first, "**Quick Share**," is generally used for intra-team or cross-team collaboration, allowing you to share partial API documentation with collaborators. The second, "**Publish Docs**," is used to publicly publish the entire documentation.

Feature | Quick Share | Publish Docs
--- | --- | ---
Publish partial APIs | Supported | Only supports publishing the entire project
Custom domain | Not supported | Supported
Custom navigation | Not supported | Supported
Custom colors and Logo | Not supported | Supported
Publish to API Hub | Not supported | Supported