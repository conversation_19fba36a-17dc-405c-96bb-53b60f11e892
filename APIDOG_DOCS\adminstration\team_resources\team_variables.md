Team Variables
Team variables allow you to share data across multiple projects, making them ideal for scenarios where variables need to be transferred between projects, such as in microservices architectures. You can manage these variables in Team Resources > Variables.

NOTE
This feature is available only with an Apidog Professional plan or higher.

managing-team-global-variables.jpg
You can manage (such as add, delete or modify) team variables here. Be sure to click the<PERSON><PERSON>button to apply the changes.

Only the initial values of team variables can be set in this section, which serves as the default value when used in projects.

On individual project environment management pages, you can only update the current values of team variables. You cannot add, modify, or delete team variables manually or via scripts. This helps maintain better control and prevent mismanagement.

To view and use existing team variables, go to the Environment Management > Global Variables > Shared within Team in each project.

view-existing-global-team-variables.png
