**Pro Tip:**
*If you want your API and analytics workflow to feel less like a debugging nightmare and more like a developer superpower, check out <PERSON><PERSON><PERSON>—the all-in-one platform for API design, testing, and documentation. It's the secret sauce for teams who want to build, automate, and ship faster (with fewer facepalms).*

# PostHog MCP Server: Analytics Automation for Devs Who'd Rather Be Coding

Ever found yourself drowning in analytics dashboards, clicking through endless menus just to add a simple annotation or check a user trend? Yeah, me too. I used to think analytics was a necessary evil—until I met the **PostHog MCP Server**. Now, I can boss my analytics around with plain English, right from my IDE. Let's walk through how to set it up, why it's a game-changer, and how it'll save you from another late-night dashboard meltdown.

## Meet Your New Data Sidekick: What is PostHog MCP Server?

The **[PostHog MCP Server](https://posthog.com/blog/machine-copy-paste-mcp-intro)** is like having a data wizard in your terminal. It's an open-source Model Context Protocol (MCP) server that lets you command PostHog's analytics platform using natural language—no more tab-hopping or copy-pasting API keys like a sleep-deprived raccoon.

**What can you do with it?**
- **Spin up projects** (without the usual click-fest)
- **Drop annotations** for launches, bugs, or those "oops" moments
- **Query user trends** and insights (and actually get answers)
- **Toggle feature flags** like a boss
- **Debug errors**—right in your IDE, not buried in some web UI

It's automation, but with a sense of humor (and fewer existential crises).

![official website](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-07-01-000607.png)

## Why PostHog MCP Server is the Analytics Tool I Wish I Had Years Ago

Let's be honest: Google Analytics is fine if you like proprietary black boxes and privacy policies that read like a choose-your-own-adventure novel. PostHog MCP Server, on the other hand, is:

- **Open-source** (fork it, hack it, brag about it)
- **Privacy-first** (self-host and keep your data out of the hands of data goblins)
- **All-in-one** (analytics, session replay, feature flags—no more Frankensteining tools)
- **Natural language powered** (because I'd rather type "show me signups" than decipher a dashboard)
- **Community-driven** (actual humans, not just bots on a help forum)

I once annotated a product launch in seconds—no more clicking through five menus and accidentally breaking something else. My past self is jealous.

## The "No More Dashboard Dread" Setup Guide

Let's get this magic running in Cline (VS Code) and Cursor. You'll need Python, a PostHog API key, and a willingness to never touch a clunky dashboard again.

### 1. The Boring Prereqs (But You Only Do This Once)
- Python 3.8+
- `uv` package manager (`pip install uv`)
- VS Code or Cursor (with Cline extension)
- PostHog API key ([get yours here](https://app.posthog.com/settings))
- Docker (optional, for the cool kids)
- ~500MB disk space (don't worry, it's not another Chrome install)

### 2. Clone Like a Pro
```bash
git clone https://github.com/PostHog/mcp.git
cd mcp
```

### 3. Cline in VS Code: Analytics at Your Fingertips
- **Virtual Environment:**
```bash
uv venv
source .venv/bin/activate  # macOS/Linux
.venv\Scripts\activate  # Windows
uv pip install .
```
- **API Key Setup:**
```bash
echo "PERSONAL_API_KEY=your-personal-api-key" > .env
```
- **Cline Extension:**
  - Install Cline from VS Code Extensions
  - Open Cline chat (Ctrl+Shift+P, "Cline: Open Chat")

![install cline](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-30-220844.png)

- **Add MCP Server:**
  - In Cline, prompt:
```text
Install the PostHog MCP Server using uv and configure it with my API key.
```
- **Marketplace Option:**
  - Find "PostHog" in Cline's MCP marketplace and click install.

![install psthog mcp server](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-30-221147.png)

- **Verify:**
  - Type `/tools` in Cline chat to see available tools (e.g., `create_annotation`).

![mcp servers icon](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-07-01-001105.png)

### 4. Cursor: Because Sometimes You Want to Code in Your Pajamas
- **Repeat the clone and venv steps above**
- **Cursor Config:**
  - Go to Settings > Tools & Integrations > MCP Tools > Add Custom MCP Server
  - Add to `.cursor/mcp.json`:

![cursor mcp servers](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-07-01-000010.png)

```json
{
  "mcpServers": {
    "posthog": {
      "command": "/path/to/uv",
      "args": ["--directory", "/path/to/mcp", "run", "posthog_mcp"],
      "env": {
        "PERSONAL_API_KEY": "your-personal-api-key"
      }
    }
  }
}
```
- **Restart Cursor** and refresh MCP Tools
- **Test:** Ask Cursor to "List available PostHog MCP Server tools."

### 5. Show Off: Try the Cool Stuff
- **Add an Annotation:**
```text
Add a PostHog annotation to project 53497 for March 20, 2025: 'Launched new onboarding flow.'
```
- **Query Trends:**
```text
Show user sign-up trends for project 98765 over the last month.
```
- **Sample Output:**
```json
{
  "insight": "user_signups",
  "project_id": 98765,
  "data": [
    {"date": "2025-06-01", "count": 120},
    {"date": "2025-06-02", "count": 135}
  ]
}
```

### 6. Docker: For When You Want to Feel Like a DevOps Wizard

![docker](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-07-01-000510.png)

```bash
docker run -i --rm -e PERSONAL_API_KEY=your-personal-api-key ghcr.io/metorial/mcp-container--posthog--posthog-mcp--posthog-mcp posthog-mcp
```
- Add to your MCP config as above, using `docker` as the command.

### 7. Debugging Nightmares (and How to Wake Up)
- **No MCP Icon?**
  - Check logs (`~/Library/Logs/Claude/mcp*.log` or `%APPDATA%\Claude\logs`)
  - Use absolute paths in config
- **API Key Issues?**
  - Double-check permissions and use env vars
- **Server Not Starting?**
  - Run `uv run posthog_mcp` manually to debug
  - Ensure Python 3.8+ and all dependencies
- **Still stuck?**
  - [PostHog MCP GitHub](https://github.com/PostHog/mcp) and X are your friends (and sometimes, so is a strong coffee)

## Why PostHog MCP Server Leaves Google Analytics in the Dust
- **Open-Source:** No vendor lock-in, self-host for free
- **Privacy:** Full control, no data sent to Google
- **Unified Suite:** Analytics, session replay, feature flags—all in one
- **Automation:** Natural language queries, not just dashboards
- **Community:** Actual humans, not just bots

I switched my side project to PostHog and instantly appreciated the privacy and flexibility—no more Google Analytics headaches or "where did my data go?" moments.

## Level Up: Customizing and Extending (Because You're Not a Basic User)
- **Add Your Own Tools:** Extend the server for custom analytics or SQL queries
- **Multi-Project Support:** Query across all your PostHog projects
- **Feature Flags:** Generate code snippets to toggle flags on demand
- **Contribute:** PRs welcome—help make the server even better!

## The Takeaway: Analytics Shouldn't Suck
The PostHog MCP Server turns analytics into a conversation, not a chore. Its open-source roots and privacy-first approach make it a top pick for devs who want control and automation. Setup is straightforward, and the community is super helpful if you hit a snag (or just need to vent about your last dashboard disaster).

Ready to take your analytics workflow to the next level? Fire up Cline or Cursor, connect the PostHog MCP Server, and start commanding your data like a pro. Can't wait to see what you build!
