Apidog supports writing documentation for almost all types of APIs, including `REST`, `SOAP`, `GraphQL`, `gRPC`, `WebSocket`, `SSE`, and more.

Explore our collection of documentation best practices and real-world API examples.

## API Technologies

<CardGroup cols={2}>
  <Card>
    ### REST API Documentation
    - [API-Design First Approach](https://apidocs.apidog.io/api-design-first-approach-891353m0)
    - [Specify an endpoint](https://apidocs.apidog.io/specify-an-endpoint-891335m0)
    
    `Example` [Pet Store API](https://apidocs.apidog.io/example-pet-store-3301817f0)
  </Card>
  <Card>
    ### SOAP API Documentation
    - [How to Use Apidog to Write SOAP API Documentation](https://apidocs.apidog.io/how-to-use-apidog-to-write-soap-api-documentation-892256m0)
    
    `Example`[WebService APIs](https://apidocs.apidog.io/webservice-number-to-words-15125868e0)  
    `Example`[Mastercard API](https://apidocs.apidog.io/submit-a-purchase-request-15123596e0)
  </Card>  

  
  <Card>
    ### GraphQL API Documentation
    - [How to Write GraphQL API Documentation Using Apidog](https://apidocs.apidog.io/how-to-write-graphql-api-documentation-using-apidog-892526m0)
    
    `Example` [Github API](https://apidocs.apidog.io/introduction-to-graphql-892358m0)
  </Card>
    <Card>
    ### gRPC API Documentation
    - [How to Use Apidog for gRPC API Documentations](https://apidocs.apidog.io/how-to-use-apidog-for-grpc-api-documentation-and-testing-895502m0)

     `Example` [Protocol Documentation](https://apidocs.apidog.io/protocol-documentation-895595m0)
  </Card>
    <Card>
    ### WebSocket API Documentation
    - [How to Use Apidog to Write WebSocket API Documentation](https://apidocs.apidog.io/how-to-use-apidog-to-write-websocket-api-documentation-891045m0)
    
    `Example` [Coinbase API](https://apidocs.apidog.io/overview-890911m0)
  </Card>
  
  <Card>
    ### SSE Documentation
    `Example` [Anthropic API](https://apidocs.apidog.io/messages-15106326e0)
  </Card>

</CardGroup>
