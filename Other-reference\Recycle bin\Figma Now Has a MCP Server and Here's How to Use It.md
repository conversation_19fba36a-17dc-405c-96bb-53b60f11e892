> **Pro Tip:** Want to supercharge your API and design workflows with AI? Try [Apidog MCP Server](https://apidog.com) — the all-in-one platform for connecting your API specs to AI-powered IDEs and automating your development process!

# Unlocking AI Design Automation: How to Use Figma's MCP Server (and Why Apidog MCP Server is Leading the Way for APIs)

AI is rapidly transforming how we build, test, and design digital products. The Model Context Protocol (MCP) is at the heart of this revolution, making it possible for tools like Figma and Apidog to connect seamlessly with large language models (LLMs) and AI-powered IDEs. In this guide, you'll discover how Figma's MCP Server works, how to set it up, and why Apidog MCP Server is setting the standard for API-driven AI automation.

![Apidog all in one image](https://assets.apidog.com/blog-next/2025/03/Screenshot-2025-03-17-215331.png)

## What is Figma-MCP?

**[Figma-MCP](https://github.com/GLips/Figma-Context-MCP?tab=readme-ov-file#figma-mcp-server)** is an open-source server that implements the Model Context Protocol, allowing LLMs and AI tools to interact directly with your Figma designs. This means you can automate design tasks, generate code, and streamline collaboration between designers and developers—all powered by AI.

## How Does Figma-MCP Work?

- **MCP Protocol:** MCP is a universal standard for connecting external applications (like Figma or Apidog) to LLMs. It provides a structured way for AI to understand and manipulate resources, whether they're design files or API specs.
- **Figma Integration:** The Figma MCP server connects to Figma's API, giving AI tools access to files, components, and styles. This enables everything from reading design data to generating new UI elements.
- **AI-Driven Automation:** With Figma-MCP, you can automate repetitive design tasks, generate variations, and even create new components—all with the help of AI.

## Getting Started: Setting Up Figma-MCP

Ready to bring AI into your design process? Here's how to get started with Figma-MCP:

### 1. Prerequisites
- Node.js (v16+)
- npm (v7+) or pnpm (v8+)
- A Figma account (Professional or Enterprise recommended)
- A Figma API access token

### 2. Obtain Your Figma API Access Token
1. [Sign up for Figma](https://www.figma.com/) and log in.
2. Download and install the Figma desktop app for your OS.
3. Open the app, log in, and click your profile icon.
4. Go to **Settings** > **Security** > **Personal Access Tokens**.
5. Click **Generate New Token**, name it (e.g., `Figma_MCP`), and copy it to a secure place.

![open your figma profile settings](https://assets.apidog.com/blog-next/2025/03/Screenshot-2025-03-19-145736.png)
![navigate to settings](https://assets.apidog.com/blog-next/2025/03/Screenshot-2025-03-19-145840.png)
![navigate to security tab](https://assets.apidog.com/blog-next/2025/03/Screenshot-2025-03-19-145950.png)
![generate a personal access token](https://assets.apidog.com/blog-next/2025/03/Screenshot-2025-03-19-150041.png)

**Security Tip:** Store your token in an environment variable (e.g., `export FIGMA_API_TOKEN="your_token_here"`) and never hardcode it in your codebase.

### 3. Install and Run Figma-MCP Server
- **Quick Start:**
  ```bash
  npx figma-developer-mcp --figma-api-key=<your-figma-api-key>
  ```
- **From Source:**
  ```bash
  git clone https://github.com/GLips/Figma-Context-MCP.git
  cd Figma-Context-MCP
  pnpm install
  cp .env.example .env # Add your token
  pnpm run dev
  ```
- Configure via `.env` or command-line arguments as needed.

### 4. Connect Figma-MCP to Your AI Tools

Once your server is running, you can connect it to AI-powered IDEs like Cursor, Windsurf, Cline, or Claude. Here's how to use it with Cursor:

1. Make sure Figma-MCP is running (default port 3333).
2. In Cursor, go to **Settings > MCP > Add New MCP Server**. Choose SSE and enter your server URL (e.g., `http://localhost:3333`).

![add figma mcp server to cursor](https://assets.apidog.com/blog-next/2025/03/Screenshot-2025-03-19-155952.png)

3. For other tools, add the config to your `mcpServers` config file:
   ```json
   {
     "mcpServers": {
       "figma-developer-mcp": {
         "command": "npx",
         "args": ["-y", "figma-developer-mcp", "--stdio"],
         "env": {
           "FIGMA_API_KEY": "<your-figma-api-key>"
         }
       }
     }
   }
   ```
4. Verify the connection (green dot = success).

![verify figma mcp server status](https://assets.apidog.com/blog-next/2025/03/Screenshot-2025-03-19-160015.png)

5. In Figma, select your design, group components as needed, and copy the link (Right-click > Copy/Paste As > Copy Link to Selection).

![group figma design](https://assets.apidog.com/blog-next/2025/03/Screenshot-2025-03-19-163347.png)
![Copy figma design link](https://assets.apidog.com/blog-next/2025/03/Screenshot-2025-03-19-163445.png)

6. In Cursor Composer, enable Agent Mode and paste your Figma link. Now you can:
   - Generate code: "Implement this Figma design in React."
   - Create components: "Convert this design into reusable UI components."
   - Optimize layouts: "Suggest improvements for this layout."

### Pro Tips for Figma-MCP
- Use the MCP Inspector (`pnpm inspect`) to view and debug server responses.
- Use `get_file` and `get_node` to fetch Figma file or node info.
- Batch operations and robust error handling are built in.

## Why Apidog MCP Server is a Game-Changer for API Workflows

Just as Figma-MCP brings AI automation to design, **[Apidog MCP Server](https://apidog.com)** is revolutionizing API development. It lets you connect your API specifications (from Apidog, OpenAPI, or published docs) to AI-powered IDEs like Cursor and VS Code. With Apidog MCP Server, you can:
- Instantly generate or update code, DTOs, and documentation from your API spec
- Search and analyze your API structure with AI
- Keep your API and tests in sync as your project evolves
- Support multiple data sources (Apidog projects, public docs, Swagger/OpenAPI files)

Whether you're a designer using Figma-MCP or a developer working with Apidog MCP Server, MCP is the bridge that brings AI into your workflow—automating the tedious, accelerating the creative, and letting you focus on what matters most.

## Conclusion

Figma MCP and Apidog MCP Server are ushering in a new era of AI-powered productivity for both design and development. By connecting your tools to AI via MCP, you can automate tasks, enhance collaboration, and unlock new creative possibilities. Whether you're building beautiful interfaces or robust APIs, now's the time to embrace MCP and let AI do the heavy lifting!
