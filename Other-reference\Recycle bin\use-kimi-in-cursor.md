**Tutorial for using Kimi-K2 in Cursor with Openrouter:**

 *![:warning:](https://emoji.discourse-cdn.com/fluentui/warning.png?v=14 ":warning:") Disclaimer: agent mode does not work as usual, as it is not a native Cursor integration.*

1. Create an account at `https://openrouter.ai/`

2. Add credits: `https://openrouter.ai/settings/credits`

3. Create an API KEY: `https://openrouter.ai/settings/keys`

4. In Cursor, go to `settings > Models`, then replace the URL with `https://openrouter.ai/api/v1` and paste your API KEY:  
   
   ![Screenshot 2025-07-13 at 12.34.53 PM](https://us1.discourse-cdn.com/cursor1/optimized/3X/8/4/8484306c5c3d83f9ca90a907f00f078593348bfe_2_690x305.png)
   
   

5. Finally, click on `Add Custom Model` and paste `moonshotai/kimi-k2`:  
   
   ![image](https://us1.discourse-cdn.com/cursor1/optimized/3X/3/3/338217c3ce8bb11acb63bb6a73e78252a0d79e2d_2_689x169.png)

6. You can now use Kimi-K2 in Cursor:  
   
   ![image](https://us1.discourse-cdn.com/cursor1/optimized/3X/a/a/aa039491843355530601a72b54ae696a6faaaf70_2_690x307.png)
