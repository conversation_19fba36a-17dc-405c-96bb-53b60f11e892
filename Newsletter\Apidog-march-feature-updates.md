Email Subject:  Apidog’s March Big Update: MCP Support for Vibe Coding, Socket.IO Debugging & More 🚀

Hello Apidog Users,

Spring has arrived, and so have our March updates! This month, we've focused on empowering AI integration capabilities and completely revamping our documentation experience. These powerful new features are designed to streamline your API development process and help your team collaborate more effectively. Here's what's new:

⌨️ Vibe Coding via Apidog MCP Server: Your published API documentation now supports enabling the Vibe Coding(via MCP) feature, allowing documentation readers to use it with AI coding tools. As a vibe coder, you can also connect effortlessly to Apidog projects, public API docs published by Apidog, and any OpenAPI (OAS) files using Apidog MCP Server! This integration enables AI assistants to directly access API specifications, dramatically improving implementation speed and accuracy. Learn more.
[Image]
https://assets.apidog.com/uploads/help/2025/03/26/1562f2ed8710ec754897595552c1b84c.gif

🔍 Smart Ollama LLM Debugging: When debugging endpoints for AI with LLMs deployed locally with Ollama, Apidog can automatically merge message content and display responses in natural language. It supports reasoning models such as DeepSeek R1, allowing you to visualize the deep thought process of these advanced AI models. Learn more.
[Image]
https://assets.apidog.com/uploads/help/2025/03/07/d4a452f1ae63dca32872bab7ff5f720d.gif
📜 Multiple Request Body Examples Support: Request Body now supports adding multiple examples, making your documentation more comprehensive and compatible with OpenAPI (Swagger) standards. Each example can demonstrate different use cases, helping API consumers understand various implementation scenarios. Learn more.
[Image]
https://assets.apidog.com/uploads/help/2025/03/28/583e7817f0f6361508235301f60b36f5.png
📋 Brand-new Documentation UI: The "Published/Shared API Documentation" has been fully upgraded with faster speed, better performance, improved mobile navigation and search, and support for configuring one-column or two-column layouts. Your API consumers will enjoy a more intuitive and responsive documentation experience. 
[Image]
https://assets.apidog.com/uploads/help/2025/03/24/e7aa4607324487a6172568c83db96274.png
✅ Socket.IO Debugging Support: You can now debug Socket.IO endpoints directly in Apidog, sending multiple arguments and acknowledgment simultaneously. This makes working with real-time applications significantly easier and more efficient. Learn more.
[Image]
https://assets.apidog.com/uploads/help/2025/03/06/77fbe5492712f7dc47f44b29754a59e6.gif
📥 SOAP Import from SoapUI: We've added support for importing SOAP projects from SoapUI, making it easier to transition your legacy SOAP projects to Apidog.
✉️ Email Allowlist Optimization: When an email allowlist is configured for the doc site, visitors whose emails are not on the list will no longer see a confusing "no permission" message — they simply won't receive a verification code, creating a cleaner user experience.
🍪 Improved cURL Import: When importing cURL commands, the -b parameter will now be correctly imported as a Cookie, ensuring more accurate imports.
🔑 Enhanced Request and Response Examples: Each request and response example can now include descriptions, OAS key, and OAS extensions. This provides more context and makes your API documentation even more informative and standards-compliant.
⚙️ OAS Extensions Configuration: You can now configure OpenAPI Specification extensions for endpoints, request bodies, and response bodies, giving you more flexibility in how you document your endpoints according to industry standards.

Explore More on Apidog Changelog

✨ Looking Forward 
As we move into Q2, our team is working hard on some exciting new features that will further enhance your API development experience. Stay tuned for announcements in the coming weeks!

💬 Be Part of the Apidog Community!

Your insights matter! Join our Discord or Slack communities to collaborate with other developers, discuss best practices, and get the latest Apidog news.

Keep building amazing APIs! 

Cheers, 
The Apidog Team