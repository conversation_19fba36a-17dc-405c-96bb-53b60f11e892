# Open Lovable: Clone Websites into React Apps—Free, Open-Source, and Yours to Customize

> **Pro Tip:** Ship API-backed apps faster with [Apidog](https://apidog.com/), the all‑in‑one platform for API design, testing, mocking, and documentation. Draft endpoints, validate responses, and publish docs—all in one place.

Ever wished you could turn any website into a clean React app in minutes—without paying for a proprietary service? Meet **Open Lovable**, the open-source alternative to Lovable AI. With a smart pipeline built on E2B Sandbox (safe code execution), Firecrawl (reliable scraping), and a fast coding model like Kimi, Open Lovable recreates sites as modern React applications you can run locally and extend. In this guide, you’ll learn what Open Lovable is, why it’s compelling, and how to install and customize it step by step.

## Why Open Lovable vs. Lovable AI?

**Open Lovable** is an MIT-licensed project from Mendable AI that lets you generate React code from real websites—minus the subscription. The source lives at [github.com/mendableai/open-lovable](https://github.com/mendableai/open-lovable). While Lovable AI offers a polished hosted experience, its full feature set is gated behind paid plans (starting at $25/month) and usage limits. By contrast, Open Lovable is:

- Free and open-source—fork, modify, self-host
- Privacy‑friendly—run locally with your own API keys
- Extensible—swap models, add features, contribute back

![lovable.ai pricing](https://assets.apidog.com/blog-next/2025/08/image-151.png)

If you value control, transparency, and cost efficiency, Open Lovable is an excellent choice—especially for prototypes, education, and internal tools.

## The Tech Behind Open Lovable

Open Lovable combines scraping, AI inference, and isolated execution into a predictable pipeline:

### E2B Sandbox: Safe, Isolated Execution
E2B Sandbox runs generated code in secure, ephemeral environments—ideal for untrusted or experimental outputs. Create an API key at [e2b.dev](https://e2b.dev/). Basic usage is free, with paid tiers for higher concurrency.

![e2b sandbox](https://assets.apidog.com/blog-next/2025/08/image-152.png)

### Kimi AI Model: Fast Long‑Context Coding
Kimi (from Moonshot AI) is a high‑performance LLM tailored for code and long inputs. Many developers access it via Groq for speed and generous context. Grab a key at [console.groq.com](https://console.groq.com/). Kimi is quick, low‑cost, and a great fit for React generation.

![kimi ai model](https://assets.apidog.com/blog-next/2025/08/Screenshot-2025-08-12-013112.png)

### Firecrawl: Robust, Structured Scraping
Firecrawl crawls target sites and returns structured content (HTML, text, images, JSON), handling JavaScript and common anti‑bot hurdles. Get a key at [firecrawl.dev](https://firecrawl.dev/). It’s affordable and dependable for acquiring the raw material your AI needs.

![firecrawl](https://assets.apidog.com/blog-next/2025/08/image-154.png)

### The Repo: Your Control Center
The official repository at [github.com/mendableai/open-lovable](https://github.com/mendableai/open-lovable) contains a Next.js app, integration scripts, and configuration. Clone it, tweak it, and deploy it—your workflow, your rules.

Together, these components enable a repeatable flow: crawl → analyze → generate → run.

## Quick Start: Install and Run Open Lovable

Follow these steps to get a local instance running and clone your first site.

### 1) Clone the Repository and Install Dependencies

Open your terminal and run:

```bash
git clone https://github.com/mendableai/open-lovable.git
cd open-lovable
npm install
```

This fetches the Next.js frontend, AI hooks, and supporting packages.

### 2) Configure Your Environment Variables

Create a `.env.local` file in the project root and set the required keys:

```bash
# Required
E2B_API_KEY=your_e2b_api_key  # From e2b.dev
FIRECRAWL_API_KEY=your_firecrawl_api_key  # From firecrawl.dev for web scraping

# Optional AI Providers (need at least one)
ANTHROPIC_API_KEY=your_anthropic_api_key  # From console.anthropic.com (Claude)
OPENAI_API_KEY=your_openai_api_key  # From platform.openai.com (GPT)
GROQ_API_KEY=your_groq_api_key  # From console.groq.com (for Kimi model)
```

- E2B: sign up at [e2b.dev](https://e2b.dev/)
- Firecrawl: create a key at [firecrawl.dev](https://firecrawl.dev/)
- Groq (for Kimi): get a key at [console.groq.com](https://console.groq.com/)

### 3) Start the Dev Server and Try a Clone

Boot the app locally:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000/) in your browser.

![opening the project on port 3000](https://assets.apidog.com/blog-next/2025/08/image-156.png)

In the UI, paste a URL (for example, [https://example.com](https://example.com/)). Open Lovable will scrape with Firecrawl, analyze with Kimi (via Groq), and generate a React project in E2B Sandbox. Download the code and run it locally with `npm start`.

![cloning the firecrawl website with a neobrutalist style](https://assets.apidog.com/blog-next/2025/08/image-155.png)

Cloning the Firecrawl website with a Neobrutalist style

### 4) Customize, Extend, and Deploy

- Swap models: switch to Claude or GPT by setting the relevant keys in `.env.local`.
- Add features: fork the repo and adjust `src/pages/index.tsx` (e.g., prompts, framework targets).
- Deploy: push to GitHub and deploy using Vercel with `vercel --prod`.
- Scale: if you hit scraping limits, upgrade your Firecrawl plan.

> Pro Tip: When you’re ready to turn your generated UI into a production service, define your contract in Apidog, auto‑mock endpoints, and validate responses against your spec as you build.

## Troubleshooting

- API key errors: verify values in `.env.local` and ensure your keys are active in each provider dashboard.
- Scraping issues: some sites block crawlers—try a different URL, set headers, or review Firecrawl options.
- Slow generation: Kimi via Groq is usually fast; for lighter tasks, consider a smaller model.
- E2B limits: free sandboxes have constraints; upgrade for more concurrency.
- Git issues: confirm Git is installed and connectivity is stable.

## Open Lovable vs. Lovable AI: Which Fits Your Needs?

- Cost: Lovable AI starts at $25/month for unlimited generations. Open Lovable is free, with optional per‑provider API costs (e.g., Groq’s free tier or low per‑token rates for Kimi).
- Control: Open Lovable gives you full source control and local execution. Lovable AI is hosted and managed.
- Convenience: Lovable AI is streamlined out of the box. Open Lovable rewards power users who want to tailor the pipeline.
- Privacy: Run Open Lovable locally with your keys to minimize data exposure.

If you want end‑to‑end ownership and extensibility, Open Lovable is ideal. If you prefer a turnkey hosted UI, Lovable AI is compelling—at a price.

## Practical Use Cases

- Prototyping: clone a layout quickly and iterate in React for A/B tests.
- Learning: study the generated component structure to level up your React skills.
- Modernization: convert legacy HTML sites to maintainable React frontends.
- E‑commerce: replicate a storefront layout as a starting point for a custom UI.

With Kimi’s long‑context coding and E2B’s safety, Open Lovable is well suited for production‑grade prototypes.

## Contribute and Track the Roadmap

Open Lovable is community‑driven—star and fork the repo, file issues, and submit PRs at [github.com/mendableai/open-lovable](https://github.com/mendableai/open-lovable). Expect continued improvements such as broader model support and richer scraping options. Watch the repository for updates.

## Wrap‑Up

You now have everything you need to run **Open Lovable**, clone real websites into React apps, and adapt the results to your workflow. Use Firecrawl for dependable scraping, Kimi for fast, accurate code generation, and E2B for safe execution. When it’s time to connect your UI to real services, define and test your API in Apidog to keep your design, mocks, and documentation in sync.
