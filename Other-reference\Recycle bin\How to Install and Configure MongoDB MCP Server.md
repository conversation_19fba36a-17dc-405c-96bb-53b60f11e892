> **Pro Tip:** Want to supercharge your API and database workflows? [Apidog](https://apidog.com) is the all-in-one platform for designing, testing, and monitoring APIs—trusted by developers worldwide. Try it alongside your AI tools for a seamless experience!

# MongoDB MCP Server: Your Step-by-Step Guide to AI-Driven Database Integration

AI is no longer a distant dream for developers—it's a daily reality. If you want to connect your MongoDB database to the latest AI-powered tools, the **MongoDB Model Context Protocol (MCP) Server** is your gateway. This guide will walk you through installing and configuring MongoDB MCP Server, so you can unlock natural language queries, smart data exploration, and more—all from your favorite AI development environments.

---

## What is MongoDB MCP Server?

[MongoDB MCP Server](https://github.com/mongodb-js/mongodb-mcp-server) is a bridge between your MongoDB deployments (Atlas, Community, or Enterprise) and any AI client that supports the Model Context Protocol (MCP). With MCP, you can:
- Query your database using natural language
- Perform admin tasks and data exploration
- Feed real-time context to Large Language Models (LLMs)

Supported clients include:
- **<PERSON><PERSON><PERSON>** (AI-native code editor)
- **<PERSON><PERSON><PERSON>** (AI-powered code editor)
- **GitHub Copilot in VS Code**
- **Anthropic Claude Desktop**

![](https://assets.apidog.com/blog-next/2025/06/image-478.png)

MCP is an open standard, making it easy to connect your data to a growing ecosystem of AI tools. The MongoDB MCP Server enables two-way communication, so your LLMs always have the latest, most relevant data.

---

## Prerequisites

Before you start, make sure you have:
- **Node.js v18+** (check with `node -v`)
- **MongoDB connection string** or **Atlas API credentials**
- **An MCP client** (Cursor, Claude Desktop, VS Code, etc.)
- **Docker** (optional, for containerized installs)

---

## Installation & Configuration: The Fast Track

The core command to launch the server is:
```bash
npx -y mongodb-mcp-server
```

You'll add a JSON config to your client's settings to tell it how to launch and talk to the MCP server. Here's how to set it up for the most popular clients:

### Setting Up in Cursor

1. **Open Settings:** Click the gear icon in Cursor.
2. **Go to MCP:** Select "MCP" in the left panel.
3. **Add a Global MCP Server:** Click "Add new global MCP server" and edit the JSON config.
4. **Paste Your Config:**
   - **With Atlas API credentials:**
     ```json
     {
       "mongodb": {
         "command": "npx",
         "args": ["-y", "mongodb-mcp-server"],
         "environments": [
           {"name": "ATLAS_API_CLIENT_ID", "value": "your-atlas-api-client-id"},
           {"name": "ATLAS_API_CLIENT_SECRET", "value": "your-atlas-api-client-secret"}
         ]
       }
     }
     ```
   - **With a connection string:**
     ```json
     {
       "mongodb": {
         "command": "npx",
         "args": ["-y", "mongodb-mcp-server", "--connectionString", "your-mongodb-connection-string"]
       }
     }
     ```
5. **Save and Go:** Save the file. Cursor can now use your MongoDB MCP Server for natural language database interaction.

---

### Setting Up in Claude Desktop

1. **Open Config:** Go to `Settings > Developer > Edit Config` in Claude Desktop. This opens `claude_desktop_config.json`.
2. **Add MCP Server:** Add this to the `mcpServers` object:
   ```json
   {
     "mcpServers": {
       "MongoDB": {
         "command": "npx",
         "args": ["-y", "mongodb-mcp-server", "--connectionString", "mongodb+srv://<user>:<password>@<cluster>.mongodb.net/test"]
       }
     }
   }
   ```
3. **Restart Claude:** Close and reopen the app. Click the hammer icon to see your MongoDB MCP Server listed.

---

### Setting Up in Visual Studio Code (with GitHub Copilot)

1. **Open Command Palette:** `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac).
2. **Add MCP Server:** Type "mcp" and select "MCP: Add Servers".
3. **Choose Command Standard I/O.**
4. **Enter Command:**
   - For connection string:
     ```json
     "mcp.servers": {
       "mongodb": {
         "command": "npx",
         "args": ["-y", "mongodb-mcp-server", "--connectionString", "your-mongodb-connection-string"]
       }
     }
     ```
   - For Atlas API credentials:
     ```json
     "mcp.servers": {
       "mongodb": {
         "command": "npx",
         "args": ["-y", "mongodb-mcp-server"],
         "options": {
           "env": {
             "ATLAS_API_CLIENT_ID": "your-atlas-api-client-id",
             "ATLAS_API_CLIENT_SECRET": "your-atlas-api-client-secret"
           }
         }
       }
     }
     ```
5. **Start the Server:** Use the "Start" button in `settings.json` or "MCP: List Servers" in the command palette.

---

## Advanced Options

- **Read-Only Mode:**
  - Prevents write operations for safety.
  - Enable with:
    ```bash
    npx mongodb-mcp-server --readOnly
    # or
    export MDB_MCP_READ_ONLY=true
    ```
- **Disable Tools:**
  - Restrict server capabilities for security.
  - Example:
    ```bash
    npx mongodb-mcp-server --disabledTools create update delete atlas collectionSchema
    # or
    export MDB_MCP_DISABLED_TOOLS="create,update,delete,atlas,collectionSchema"
    ```
- **Disable Telemetry:**
  - Stop anonymous usage data collection.
  - Use:
    ```bash
    npx mongodb-mcp-server --telemetry disabled
    # or
    export MDB_MCP_TELEMETRY=disabled
    ```

---

## Docker Installation

Prefer containers? Run MongoDB MCP Server in Docker for isolation and easy setup.

- **With connection string:**
  ```bash
  docker run --rm -i -e MDB_MCP_CONNECTION_STRING="your-mongodb-connection-string" mongodb/mongodb-mcp-server:latest
  ```
- **With Atlas API credentials:**
  ```bash
  docker run --rm -i -e MDB_MCP_API_CLIENT_ID="your-atlas-api-client-id" -e MDB_MCP_API_CLIENT_SECRET="your-atlas-api-client-secret" mongodb/mongodb-mcp-server:latest
  ```
- **Configure your client** to use `docker` instead of `npx` in the command field.

---

## Wrapping Up

The MongoDB MCP Server is a leap forward for AI-powered database development. With just a few steps, you can connect your MongoDB to Cursor, Claude, VS Code, and more—unlocking natural language queries, smart admin, and context-aware code generation. For the latest features and updates, check the [official MongoDB MCP Server GitHub](https://github.com/mongodb-js/mongodb-mcp-server). The future of database interaction is here—don't miss out!
