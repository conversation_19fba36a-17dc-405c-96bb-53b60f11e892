Import .har file
.har file stands for "HTTP Archive format." It is a JSON-formatted file that is used for logging a web browser's interaction with a site.

.har file contains a record of web requests and responses, including URL, page resources, HTTP headers, HTTP/HTTPS requests and responses, and other data that a web browser sends or receives from a web server.

Export .har File
Right-click on the website you want to inspect, and then select "Network", click the red button to record the HTTP request and response.


Select the HTTP request and response you want to export, and then click the "Save all as HAR with content" button.


Import .har File
Open the project "setting", and click "Import Data", and then upload the .har file.


