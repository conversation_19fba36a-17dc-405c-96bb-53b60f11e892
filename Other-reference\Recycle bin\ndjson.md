NDJSON - Newline delimited JSON
A standard for delimiting JSON in stream protocols.

1. Introduction
1.1 About
There is currently no standard for transporting instances of JSON text within a stream protocol, apart from [Websockets], which is unnecessarily complex for non-browser applications.

A common use case for NDJSON is delivering multiple instances of JSON text through streaming protocols like TCP or UNIX Pipes. It can also be used to store semi-structured data.

1.2 Terminology
The key words "MUST", "MUST NOT", "REQUIRED", "SHALL", "SHALL NOT", "SHOULD", "SHOULD NOT", "RECOMMENDED", "MAY", and "OPTIONAL" in this document are to be interpreted as described in RFC 2119. [RFC2119]

2. Example NDJSON
 {"some":"thing"}
 {"foo":17,"bar":false,"quux":true}
 {"may":{"include":"nested","objects":["and","arrays"]}}
(with \n line separators)

3. Functional Specification
3.1 Serialization
Each JSON text MUST conform to the [RFC8259] standard and MUST be written to the stream followed by the newline character \n (0x0A). The newline character MAY be preceded by a carriage return \r (0x0D). The JSON texts MUST NOT contain newlines or carriage returns.

All serialized data MUST use the UTF8 encoding.

3.2 Parsing
The parser MUST accept newline as line delimiter \n (0x0A) as well as carriage return and newline \r\n (0x0D0A).

If the JSON text is not parsable, the parser SHOULD raise an error. The parser MAY silently ignore empty lines, e.g. \n\n. This behavior MUST be documented and SHOULD be configurable by the user of the parser.

3.3 MediaType and File Extensions
The MediaType [RFC6838] for Newline Delimited JSON SHOULD be application/x-ndjson.

When saved to a file, the file extension SHOULD be .ndjson.

Whats wrong with JSON?
Nothing, JSON is a great format, it is the de-facto standard for data comunication and is supported everywhere.



JSON on the left, newline-delimited JSON (aka ndjson) on the right
So what is ndJSON?
ndJSON is a collection of JSON objects, separated by `\n`

So it’s one valid JSON object per line

Ok but why?

Personally i had a use case where I needed to:

Store some logs
I wanted the log entries to be JSON
I wanted quick insertions
I needed to query the last or last (n) items quickly
The problems
Since i wanted to store JSON a JSON-like database like MongoDB was the obvious choise, but i wanted to keep things very simple, I just wanted to store the logs in a text file.

So using a json file feeld obvious right?

Yes, but…

This would mean that in order to insert or read a record from the file i had to parse the whole file, which is far from ideal

ndJSON to the rescue
Since every entry is valid json it can be parsed/unmarshaled as a standlone JSON doument, no need do read the file in memory before parse

This makes the file streamable, knowing that every new line means a new entry i can read just as much lines i need to get the same amout of records
