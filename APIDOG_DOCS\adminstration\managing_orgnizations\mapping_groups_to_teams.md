Mapping Groups to Teams
Apidog does not support creating or deleting groups using SCIM. However, mapping an identity provider (IdP)'s group to a team within your Apidog organization via SAML is supported.

Modify <PERSON>laim of SSO
To support group mapping via SAML, you should add a group claim of SSO:

Open your Microsoft Entra ID management portal in a browser.

Go to Enterprise applications and open your enterprise application.

On the application's Overview page, click Set up single sign on, and edit Attributes & Claims.

mapping-groups-step-1.png

Click Add a group claim, select All groups, check Customize the name of the group claim, and set "Name" to "groups".

mapping-groups-step-2.png

After this setting, when a user signs in with SSO, Apidog can obtain the unique identifier (object id) of the group to which the user belongs. In addition, Apidog will NOT obtain any information about any groups in Azure.

Configure Mapping
Next, we can configure the mapping between group and team:

Open the Groups page of Microsoft EntraID, you can find that each group has a Name and Object Id.

mapping-groups-step-3.png

Open the SAML Group page in the organization settings of Apidog, then you can paste the name and id of the Azure group.

Set the permissions of members of this Azure group on each Apidog team.

mapping-groups-step-4.png

When a user signs in with SSO, the corresponding team access permissions will be granted according to the configuration.

