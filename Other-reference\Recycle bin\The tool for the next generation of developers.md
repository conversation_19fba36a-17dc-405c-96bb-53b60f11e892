# The tool for the next generation of developers

Verified students get one year of free Cursor Pro.

[Verify](https://www.cursor.com/api/auth/login?redirect_uri=/verifyStudent)

## The code editor of choice for students everywhere

Engineers on campuses all around the world reach for Cursor.

## Supercharge your development

### Cursor Pro for a Year

Unlock a full year of Pro features and advanced AI models — free with your student email.

### Fix Bugs Faster

Spend less time debugging with AI-suggested error resolution and code optimization.

### Assistance with Context

Connect notes, files, or assignment repositories for help tailored to any task at hand.

### Hands-on Learning

Instantly create sandboxes for tasks and experiments.

### Understand Any Code

Master new languages and frameworks with clear AI guidance.

## Bring Cursor to your classroom

If you're an educator or student leader, we'd love to partner with you.

## Frequently asked questions

### How do I get the student discount?

Verify your student status by clicking "Verify Status" at the top of the page and following the instructions. If you are an existing Cursor Pro user, you'll receive an automatic refund for the remaining billing cycle and the discount will be applied automatically. If you don't already have Cursor Pro, you must sign up for Cursor Pro in your [account settings](https://www.cursor.com/settings) after you verify your student status to take advantage of the discount.

---

### What is included with the student discount?

You will have access to all [Pro features](https://docs.cursor.com/account/plans-and-usage) for a year. This includes 500 fast premium requests per month and unlimited slow premium requests. However, you will be charged for any usage over the 500 fast premium requests per month if you enable [usage based pricing](https://docs.cursor.com/account/plans-and-usage#usage-based-pricing).

---

### What happens after the year is up?

Your subscription will automatically renew at the regular price. You can cancel at any time, but you will only keep access through the end of the current billing period.

---

### Can I use the student discount if I already have a Cursor subscription?

Yes, you can use the student discount if you already have a Cursor subscription. We will automatically cancel your existing subscription, refund your unused time, and apply the discount effective immediately to your new subscription.

---

### How does Cursor perform on my codebase?

Cursor efficiently handles massive codebases with hundreds of thousands of files through smart indexing.

---

### Where can I ask more questions?

If you have more questions, feel free to email us directly at [<EMAIL>](mailto:<EMAIL>).
