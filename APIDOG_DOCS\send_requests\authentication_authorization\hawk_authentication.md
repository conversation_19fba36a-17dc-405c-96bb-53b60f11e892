Hawk Authentication
Hawk Authentication is an HTTP request-based authentication protocol designed to provide a simple, flexible, and secure authentication mechanism.



Basic Settings
The basic authentication parameters for Hawk are as follows:

Hawk Auth ID

Used to identify the authentication ID for the current request.

Hawk Auth Key

Used to identify the authentication key for the current request.

Algorithm

The algorithm used to create the message authentication code, supporting SHA-256, SHA-1, etc.

Advanced Settings
You can click the "More" option to add more encryption settings. If left blank, they will be generated automatically.

User

Used to identify the user for the current request.

Nonce

A random string generated by the client.

ext

Any application-specific information sent along with the API request.

app

The application to which the credentials are bound, preventing attackers from impersonating credentials issued to others.

dlg

The application ID that issued the credentials.

Timestamp

A timestamp used to prevent requests outside the time window.

Include payload hash

When checked, the payload hash value will be included.

