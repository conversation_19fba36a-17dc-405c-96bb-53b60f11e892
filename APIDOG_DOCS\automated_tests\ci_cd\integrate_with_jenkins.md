Integrate with <PERSON>
Install <PERSON> is an automated build tool that helps developers automate building process, testing, and deploying applications during the software development process. Here's how to install it on a Linux system:

1.
Add the Jenkins GPG public key:

wget -q -O - https://pkg.jenkins.io/debian/jenkins.io.key | sudo apt-key add -
2.
Add the Jenkins source to the list of APT software sources:

sudo sh -c 'echo deb http://pkg.jenkins.io/debian-stable binary/ > /etc/apt/sources.list.d/jenkins.list'
3.
Update the APT package list:

sudo apt-get update
4.
Install Jenkins:

sudo apt-get install jenkins
5.
Start the Jenkins service:

sudo systemctl start jenkins
After the installation is complete, type http://localhost:8080 in your web browser to access the Jenkins console. The console provides a web API for you to manage and configure Jenkins services.

For more information about <PERSON>, please check the Jenkins website.

Configuring Node.js environment for Jenkins
Before running Apidog CLI, you need to ensure that the Node.js version number is >= v10, so you need to configure NodeJS dependencies in the Jenkins environment first.

1.
Open the Jenkins plugin management and find the NodeJS plugin, install it and restart it.

image.png
2. Create a new NodeJS in the global tool configuration, configure the version number (requires >= v10) and package name apidog-cli .



Run the Apidog CLI command
If apidog-cli has been installed on the Jenkins host and you want to run tasks directly in the Node environment, you can refer to the Node configuration in the figure below and fill in the Node path of the host.



Create a new task and set node information in Build Environment.



Visual Configuration Pipeline
Add the build step Excute shell, copy the ApidogCli command into it, save it and run it.



If the Jenkins host is a Windows system, replace the build step with Windows batch command.



Embed code into pipeline
Copy the CI code from the CI/CD Tools page in CI.

image.png

Paste the code directly into the pipeline in the Jenkins configuration to complete the continuous integration configuration.

image.png

Publish reports with Jenkins
Specify the generated report name ${JOB_NAME}_${BUILD_NUMBER} (Jenkins built-in variable) in the command. Use the HTML Publisher plugin to view the report.

apidog run https://api.apidog.com/api/v1/api-test/ci-config/XXX/detail?token=xxxxx -r html,cli --out-file ${JOB_NAME}_${BUILD_NUMBER}


