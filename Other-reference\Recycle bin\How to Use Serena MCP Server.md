# How to Use Serena MCP Server

> Pro tip: If you build or test APIs alongside <PERSON>’s code workflows, try Apidog—the all‑in‑one API development platform—to design, mock, test, and document endpoints in one place. It pairs well with <PERSON> and <PERSON>urs<PERSON> so you can verify API behavior while you iterate on code.

Looking for a no‑cost way to turn your favorite LLM into a capable coding agent? The **Serena MCP Server** does exactly that. Built by Oraios AI, <PERSON> integrates with tools you already use—<PERSON>, Claude Desktop, and Cursor—and exposes powerful IDE‑like capabilities through MCP/LSP without expensive subscriptions. This guide explains what <PERSON> is and how to set it up across environments.

## Overview: What Serena MCP Server Provides

The **Serena MCP Server** from [Oraios](https://github.com/oraios/serena) AI converts an LLM into a coding agent with features like semantic code analysis, symbol‑level edits, and multi‑language support (Python, JavaScript, Java, and more). Its design leans on the Model Context Protocol (MCP) and Language Server Protocol (LSP) to plan changes, edit safely, and even commit to version control. It emerged as a free alternative to costly IDE subscriptions and API‑metered assistants, and it’s MIT‑licensed for easy customization.

![serena mcp server](https://assets.apidog.com/blog-next/2025/08/image-105.png)

## Set Up Serena in a Claude Code Project

Use **Claude Code** to connect to <PERSON>’s MCP server and work directly in your repo.

### 1) Install prerequisites

1. Install uv (Python package manager). Follow the official installer: [uv](https://docs.astral.sh/uv/getting-started/installation/#standalone-installer).

```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

```powershell
# For windows users:
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
```

Or via Homebrew: `brew install uv`.

![install uv](https://assets.apidog.com/blog-next/2025/08/image-107.png)

2. Clone Serena’s repository:

```bash
git clone https://github.com/oraios/serena
cd serena
```

![serena github project](https://assets.apidog.com/blog-next/2025/08/image-108.png)

3. Prepare environment variables:

```bash
cp .env.example .env
```

If you use a paid model, place its API key in `.env` (Claude free tier doesn’t require keys).

### 2) Configure Serena for Claude Code

1. Start the MCP server:

```bash
uv run serena start-mcp-server
```

This launches a local dashboard at `http://localhost:24282/dashboard/index.html` to view logs and stop the server.

2. Register Serena with Claude Code:

```bash
claude mcp add-json "serena" '{"command":"uvx","args":["--from","git+https://github.com/oraios/serena","serena-mcp-server"]}'
```

This configures Claude Code to use Serena without installing it globally.

![cluade code](https://assets.apidog.com/blog-next/2025/08/claude_code_home.png)

3. Validate in Claude Code:

> Analyze my Python codebase in ./src.

Serena will index your project, save memories in `.serena/memories/`, and return an analysis. Try another task:

> Refactor main.py to use async/await.

### Troubleshooting

- **Server won’t start**: Confirm `uv` is installed and `.env` exists; check the dashboard logs.
- **Claude Code errors**: Recheck the `add-json` command syntax and restart Claude Code.
- **Path issues**: Prefer absolute paths like `/path/to/serena` to avoid resolution errors.

## Use Serena with Claude Desktop

**Claude Desktop** integrates smoothly with Serena’s MCP server on the free Claude tier.

![claude desktop](https://assets.apidog.com/blog-next/2025/08/download_cluade.png)

### 1) Run the MCP server

Start from the Serena repo:

```bash
uv run serena start-mcp-server
```

Or use Docker (experimental) for an isolated setup:

```bash
docker run --rm -i --network host -v /path/to/your/projects:/workspaces/projects ghcr.io/oraios/serena:latest serena start-mcp-server --transport stdio
```

Replace `/path/to/your/projects` with an absolute path to your projects folder.

### 2) Point Claude Desktop to Serena

Open Claude Desktop’s configuration and add Serena:

- macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
- Windows: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "serena": {
      "command": "/path/to/uv",
      "args": ["run", "--directory", "/path/to/serena", "serena-mcp-server"]
    }
  }
}
```

Use real paths (double backslashes `\\` on Windows). Restart Claude Desktop—you should see a hammer icon when Serena’s tools are active.

![add new mcp server to claude desktop](https://assets.apidog.com/blog-next/2025/08/claude_developer_edit_config-1.png)

### 3) Try it out in Claude Desktop

1. Start a conversation and run:

> Summarize my project in ./myproject.

2. Ask for targeted changes:

> Fix bugs in src/app.js based on logs.

3. Use the dashboard at `http://localhost:24282/dashboard/index.html` to inspect logs or shut down the server.

### Tips

- Enable logs by setting `show_logs: true` in `myproject.yml`.
- Add `.serena` to `.gitignore` to keep memory files out of version control.
- Avoid `execute_shell_command` in Agno mode without confirmation.

## Use Serena with Cursor

You can enable Serena globally or per project in **Cursor**.

![the cursor ide](https://assets.apidog.com/blog-next/2025/08/image-109.png)

### 1) Start the server

As before:

```bash
uv run serena start-mcp-server
```

Or reuse the Docker command from the Claude Desktop section.

### 2) Configure Cursor

Global configuration (`~/.cursor/mcp.json` under Tools and Integrations):

```json
{
  "mcpServers": {
    "serena": {
      "command": "uvx",
      "args": ["--from", "git+https://github.com/oraios/serena", "serena-mcp-server"]
    }
  }
}
```

This makes Serena available across all Cursor projects.

![add new mcp server to cursor](https://assets.apidog.com/blog-next/2025/08/Screenshot-2025-07-15-200744-1.png)

Per‑project configuration: add the same JSON to `.cursor/mcp.json` in your repo. Restart Cursor to apply changes.

### 3) Validate in Cursor

1. Open the chat panel and request:

> Generate a REST API in src/api.py.

2. For refactors, highlight code, press `Ctrl+L` (`Cmd+L` on macOS), and prompt:

> Optimize this function for performance.

3. Use `http://localhost:24282/dashboard/index.html` to review logs while iterating.

## Why Serena?

- **Free to use**: Works with Claude’s free tier or open‑weight models via Agno.
- **Capable features**: Semantic retrieval, symbol‑level edits, and multi‑language support rival paid tools.
- **Local and private**: Keep your codebase on your machine.
- **Active community**: Rapid improvements and an MIT license for customization.

Users often call Serena “the strongest coding setup” paired with Claude. With MCP support expanding, expect even more editor options soon.

## Conclusion

With the **Serena MCP Server**, you can bring powerful coding assistance into Claude Code, Claude Desktop, and Cursor—without subscription overhead. Follow the steps above, confirm with the validation prompts, and use the dashboard to monitor runs. Pair this workflow with Apidog when you need high‑quality API design, testing, and docs while you code.
