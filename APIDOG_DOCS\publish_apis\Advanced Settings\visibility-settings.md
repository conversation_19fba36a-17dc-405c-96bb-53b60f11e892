The visibility settings allow owners to flexibly control access permissions for project resources.

You can set the visibility of folders, endpoint documents, cases, Markdown documents, and schemas to "Shared" or "Internal".

- **Shared:** By default, the visibility of resources is set to "**Shared**". Team members can view, edit, and share these resources externally.
- **Internal:** When the visibility of resources is set to "**Internal**", team members can still view, edit, and share them online, but they cannot publish them externally.

:::tip
Apidog version 2.6.16 or later is required for this setting.
:::

## How to Access the Visibility Setting

For folders:
<Background>
![folder-visibility-settings.png](https://api.apidog.com/api/v1/projects/544525/resources/345099/image-preview)
</Background>
For endpoint documents:
<Background>
![endpoint-document-visibility-setting.png](https://api.apidog.com/api/v1/projects/544525/resources/345100/image-preview)
</Background>
For endpoint document editor:
<Background>
![endpoint-document-edittor-visibility.png](https://api.apidog.com/api/v1/projects/544525/resources/345101/image-preview)
</Background>
For endpoint debug mode:
<Background>
![endpoint-debug-mode-visibility.png](https://api.apidog.com/api/v1/projects/544525/resources/345102/image-preview)
</Background>
For cases:
<Background>
![case-visibility-settings.png](https://api.apidog.com/api/v1/projects/544525/resources/345103/image-preview)
</Background>
For Markdown documents:
<Background>
![markdown-document-visibility-setting.png](https://api.apidog.com/api/v1/projects/544525/resources/345104/image-preview)
</Background>
For schemas:
<Background>
![schema-visibility-setting.png](https://api.apidog.com/api/v1/projects/544525/resources/345107/image-preview)
</Background>