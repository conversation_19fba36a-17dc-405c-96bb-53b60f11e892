---
description: Style You Need to Follow for WRITING CONTENT, NOT REVIEWING
globs: 
alwaysApply: false
---
# Rule: High-Quality Technical Content Writing Style for Developers (Tutorial Focused)
# Scope: Use when generating new technical tutorials, how-to guides, or step-by-step walkthroughs.

## Goal
Generate technically **deep**, **precise**, **actionable**, and **comprehensible** tutorials primarily targeted at **[Specify Target Audience if known, e.g., API developers, backend engineers, DevOps, QA testers]**. Content must be accurate, solve specific real-world technical problems through clear steps, and follow best practices for technical instruction. When featuring Apidog, its placement must be **relevant**, **natural**, solve a problem demonstrated in the tutorial, and clearly showcase specific value *in context*.

## Structure

1.  **Title:** Clear & Action-Oriented H1. State the task or outcome (e.g., "How to Automate API Contract Testing with Apidog," "Step-by-Step: Setting Up Mock Servers for Integration Testing," "Debugging Asynchronous APIs: A Practical Tutorial").
2.  **Introduction (The Setup):**
    *   **Define the Problem/Goal:** Clearly state the technical challenge the tutorial addresses or the skill the user will acquire.
    *   **Prerequisites:** List necessary tools, accounts, prior knowledge, or setup required *before* starting.
    *   **Outline:** Briefly mention the main steps or phases covered in the tutorial.
    *   **(If Applicable) Introduce Core Tool:** If the tutorial centers on Apidog, introduce it as the primary tool for achieving the goal, ensuring its relevance is immediate.

3.  **Body (The Steps):**
    *   **Logical, Sequential Sections:** Use H2s/H3s for distinct phases or major steps.
    *   **Numbered Steps:** Use clear, numbered lists for specific actions within each section. Start verbs with action words (e.g., "Configure," "Add," "Run," "Verify").
    *   **Explain the 'Why':** Briefly explain the purpose or rationale behind critical steps or configurations. Don't just provide commands; explain their function in the context of the tutorial's goal.
    *   **Detailed Explanations:** Define technical terms or concepts essential to understanding a step *when introduced*. Link to prerequisite knowledge if necessary.
    *   **Code & Configuration:** Provide necessary code snippets, configuration files, or command examples directly within the relevant steps.
    *   **Expected Results/Verification:** After key steps or sections, describe the expected outcome or provide commands/methods to verify success (e.g., "Check the logs for X," "You should see Y response code," "Verify the setting in the Z panel").

4.  **Conclusion:**
    *   **Recap Achievement:** Briefly summarize what the user has successfully built or configured by following the tutorial.
    *   **Next Steps/Further Learning:** Suggest relevant follow-up actions: exploring advanced features, related tutorials, documentation links, or ways to apply the learned skill. If Apidog was central, suggest relevant next steps *within* Apidog. Avoid generic CTAs.

## Content Requirements

1.  **Technical Accuracy & Precision:**
    *   Ensure all technical details (commands, code, configurations, UI elements, explanations) are correct, specific, and unambiguous. If discussing Apidog, rigorously align with its actual features and terminology.
    *   Use precise technical vocabulary appropriate for the target audience and topic.

2.  **Actionability & Reproducibility:**
    *   Provide all necessary information for a user to successfully replicate the steps.
    *   **Code Snippets:** Ensure code is correct, runnable, well-formatted with language identifiers, and adequately explained. Include necessary context (imports, environment variables if applicable).
    *   **Visual Aids:** Use placeholders (`![Descriptive alt text showing X step/result](mdc:placeholder_image_url)`) to suggest essential screenshots or diagrams that clarify steps or verify outcomes. Mark these clearly.

3.  **Apidog Integration (If Appropriate):**
    *   **Relevance:** Feature Apidog *only* if it is the core tool for the tutorial or directly solves a key step within it.
    *   **Step-by-Step Demonstration:** Show *exactly how* to use specific Apidog features to accomplish the task. Use precise UI names, button labels, and workflow descriptions.
    *   **Contextual Value:** Explain *why* using Apidog (or a specific feature) is beneficial for *this specific step* or problem (e.g., "Using Apidog's environment variables here prevents hardcoding sensitive keys").

4.  **Engagement & Readability (Technical Tutorial Style):**
    *   **Developer Voice:** Write clearly and directly for a technical audience focused on completing a task. Use "you" to guide the user.
    *   **Clarity Above All:** Prioritize unambiguous instructions and explanations.
    *   **Structured Information:** Use lists, code blocks, and formatting effectively to present information clearly.
    *   **High Perplexity:** Vary sentence structure naturally. Combine concise instructions with more detailed explanations where necessary. Avoid overly simplistic or monotonous sentence patterns. Use precise, standard technical terminology confidently, assuming the specified prerequisite knowledge.
    *   **Controlled Burstiness:** Mix shorter, direct action sentences (e.g., "Run the command.") with more complex sentences that provide context or explanation (e.g., "This command initiates the test suite defined earlier, targeting the base URL specified in your current environment configuration."). Ensure the flow remains logical and serves the tutorial's instructional purpose. The variation should enhance clarity, not create confusion.
    *   **(Optional SEO):** Naturally incorporate relevant technical keywords related to the tutorial's topic.

## Tone
*   **Instructive & Prectise:** Clear, direct, and focused on guiding the user through the process.
*   **Authoritative & Accurate:** Confident in the technical details provided.
*   **Helpful & Enabling:** Aim to empower the user to successfully complete the task.
*   **Professional:** Avoid colloquialisms, humor, or overly casual language. The focus is on technical instruction.

## Formatting
*   Use standard Markdown.
*   Use bold (`**text**`) for UI elements, key terms being defined, or crucial parameters.
*   Use inline backticks (`` `code` ``, `` `command` ``, `` `filename` ``) correctly.
*   Use fenced code blocks (```lang ... ```) with language identifiers for multi-line code/config.
*   Use blockquotes (`>`) for important notes, warnings, or tips related to a step.

## Self-Correction / Refinement
*   Before finalizing:
    *   Can a developer with the stated prerequisites follow these steps exactly and achieve the goal?
    *   Are all commands, code snippets, and configurations technically accurate?
    *   Is the purpose of each step clear? Is the 'why' explained where needed?
    *   Is the language precise and unambiguous?
    *   Does the sentence structure vary appropriately (perplexity/burstiness) to maintain clarity and technical depth without being jarring or confusing?
    *   Is the Apidog integration (if any) accurate, relevant, and clearly demonstrated?
    *   Are required visuals indicated?