# What is Apigee?

Apigee is Google Cloud's native API management platform that can be used to build, manage, and secure APIs — for any [use case](https://cloud.google.com/apigee/docs#use-cases), environment, or scale. Apigee offers high performance API proxies to create a consistent, reliable interface for your backend services. The proxy layer gives you granular control over security, rate limiting, quotas, analytics, and more for all of your services.

Apigee supports [REST](https://cloud.google.com/apigee/docs/api-platform/fundamentals/understanding-apis-and-api-proxies), [gRPC](https://cloud.google.com/apigee/docs/api-platform/reference/policies/external-callout-policy), [SOAP](https://cloud.google.com/apigee/docs/api-platform/reference/policies/message-validation-policy), and [GraphQL](https://cloud.google.com/apigee/docs/api-platform/reference/policies/graphql-policy), providing the flexibility to implement any API architectural style.

## What does Apigee provide?

Apigee provides an API proxy layer that sits between your backend services and internal or external clients that want to use your services. Apigee provides a rich array of policies that allow you to add security, traffic management, data mediation, extensions, and other features to the API proxy layer for a robust, enterprise-grade API management solution. You can also add custom code, conditional logic, fault handling, rate limiting, caching, and many other actions. Because the policies and actions are implemented on Apigee, in the API proxy layer, your underlying backend services can remain unchanged.

## Who uses Apigee?

Apigee is designed to benefit two key types of users and the unique API management challenges they face:

- **API producers**: API producers build and manage the APIs that expose their backend services.
- **API consumers**: API consumers use the data provided by the API in their client applications.

### Challenges for API producers

API producers face a specific set of challenges when exposing backend services, such as:

- **Security**: Because API producers make their services available over the web, they must take all necessary steps to secure and protect their services from unauthorized access.
- **Discoverability**: API producers must ensure that client application developers can find their APIs and use them. As services change, the API producer needs to keep app developers aware of updates and ensure that the services remain compatible with other platforms and devices.
- **Measurability**: As an API producer, you should monitor your services to make sure they are always available to client applications. You may also want to measure traffic, set quotas, and gain insight into who is using your services.

### Challenges for API consumers

Client app developers consuming services from numerous providers face a slightly different set of challenges, such as:

- **Flexibility**: There are many technologies available today for a service provider to expose its services. The same client app might have to use one method to consume a service from one provider, and a different method to consume a service from a different provider.
- **Ease of use**: API consumers want to use APIs that are well-designed, easily discoverable, and well-documented so they can get started quickly.
- **Reliability**: API consumers depend upon the reliability of the backend services to keep their own client applications running smoothly. Maintaining a consistent API interface allows API producers to implement backend service changes without disruption to API consumers.

## Key benefits of using Apigee

Apigee API management offers features designed specifically to meet the needs of API producers and consumers.

### API discoverability

Apigee offers API producers a developer-focused portal where client app developers can discover your APIs, find the documentation required to build applications using them, and register as an app developer to stay in sync with any updates or changes. Learn more about developer portals in the [Publishing overview](https://cloud.google.com/apigee/docs/api-platform/publish/publishing-overview).

### API insights

Apigee offers [Advanced API security](https://cloud.google.com/apigee/docs/api-security), [API monitoring](https://cloud.google.com/apigee/docs/api-monitoring), and [analytics](https://cloud.google.com/apigee/docs/api-platform/analytics/analytics-services-overview) features that enable API producers to secure access to their services, monitor uptime and traffic with alerting, and gain insight into how their APIs are being used.

### API reliability

Apigee provides a consistent interface for API management and consumption. You can execute policies for security, traffic management, and efficiency of your APIs at the proxy level. Using Apigee proxies allows you to isolate API consumers from your backend service. This means that you can make changes to your backend while allowing client applications to consume your services without interruption. For more information, see [Building a simple API proxy.](https://cloud.google.com/apigee/docs/api-platform/fundamentals/build-simple-api-proxy)
