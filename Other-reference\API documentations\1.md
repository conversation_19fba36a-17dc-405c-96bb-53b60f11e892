

---

## 📚 Knowledge Base / Documentation Platforms

**Confluence**  
A flagship Atlassian product, Confluence is an enterprise-grade documentation platform designed for team collaboration, internal wikis, and knowledge management. Its seamless integration with Jira and other Atlassian tools, robust templates, and granular permissions make it a favorite for large organizations seeking a centralized knowledge hub.

**Document360**  
Document360 is a modern, AI-powered knowledge base solution built for creating user guides, product documentation, and internal wikis. It offers a dual editor (Markdown and WYSIWYG), advanced analytics, versioning, and workflow automation, making it ideal for teams that need both power and ease of use.

**BookStack**  
BookStack is an open-source, self-hosted documentation platform with a book/chapter/page structure. Its intuitive interface, granular permissions, and flexibility make it a great choice for teams wanting full control over their documentation environment.

**GitBook**  
GitBook is a cloud-based documentation platform tailored for software teams. It supports Markdown, real-time collaboration, and Git integration, making it easy to create, organize, and publish both internal and external docs.

**Helpjuice**  
Helpjuice is an enterprise knowledge base platform focused on customization, advanced analytics, and powerful search. It’s designed to help large teams create, manage, and optimize self-service documentation for both employees and customers.

**HelpDocs**  
HelpDocs is a simple, fast, and user-friendly knowledge base tool. It’s perfect for startups and small businesses that want to launch a branded help center quickly, with minimal setup and maintenance.

**Heroic Knowledge Base**  
Built as a WordPress plugin, Heroic Knowledge Base enables teams to create internal or external documentation sites with no coding required. It features AJAX search, analytics, and unlimited content, making it a cost-effective solution for WordPress users.

**KnowAll**  
KnowAll is a premium WordPress theme for building knowledge bases. It offers easy setup, customizable layouts, and built-in analytics, making it a solid choice for teams already invested in the WordPress ecosystem.

**Notion**  
Notion is an all-in-one workspace that combines notes, docs, wikis, and project management. Its flexible block-based editor and real-time collaboration make it a popular choice for teams seeking a unified platform for documentation and more.

**Nuclino**  
Nuclino is a unified workspace for organizing knowledge, docs, and projects. Its clean interface, real-time editing, and visual organization options (lists, boards, graphs) make it ideal for teams that value speed and simplicity.

**Papyrs**  
Papyrs is a drag-and-drop intranet and documentation platform designed for non-technical users. It’s great for creating internal wikis, HR portals, and company handbooks with minimal effort.

**ProProfs**  
ProProfs is a knowledge base and help authoring tool that enables teams to create searchable, media-rich documentation, FAQs, and help centers. It’s known for its ease of use and built-in analytics.

**ReadMe**  
ReadMe is a developer-focused platform for creating interactive API documentation and developer portals. It features live API explorers, customizable branding, and usage analytics, making it a top pick for API-first companies.

**Tettra**  
Tettra is an internal wiki designed for knowledge sharing within teams. With Slack integration and a simple interface, it’s perfect for fast-growing companies that want to keep everyone on the same page.

**Whatfix**  
Whatfix is a digital adoption platform that overlays in-app guidance, walkthroughs, and self-help widgets on top of your software. It’s ideal for onboarding, training, and supporting users with contextual documentation.

**Zendesk**  
Zendesk is a customer support suite with a robust help center and knowledge base module. It’s widely used for customer-facing documentation, ticketing, and self-service support.

**Wiki.js**  
Wiki.js is a free, open-source documentation platform built on Node.js. It supports Markdown, multiple databases, and a modern UI, making it a flexible choice for technical teams.

**ClickHelp**  
ClickHelp is a cloud-based documentation tool for creating multi-language user manuals, knowledge bases, and help sites. It offers collaboration, analytics, and customizable navigation.

**Atera**  
Atera is an all-in-one IT management platform with built-in documentation features. It’s designed for IT teams and MSPs to document assets, processes, and knowledge base articles, all powered by AI.

**Freshservice**  
Freshservice is an ITSM tool with a built-in knowledge base and documentation features. It’s ideal for IT teams needing asset management, ticketing, and internal documentation in one platform.

**IT Glue**  
IT Glue is a documentation platform tailored for managed service providers (MSPs). It centralizes IT assets, passwords, and procedures, streamlining IT documentation and credential management.

**N‑able Passportal**  
N‑able Passportal is a credential and IT documentation management tool for MSPs. It offers secure password storage, asset tracking, and knowledge base features.

**SuperOps**  
SuperOps is an RMM/PSA platform with IT documentation capabilities. It helps IT teams manage client assets, tickets, and workflows, all from a unified dashboard.

---

## ⚙️ Static Site Generators / Docs Site Builders

**Docusaurus**  
Docusaurus is a React-based, open-source static site generator built by Meta. It’s designed for technical documentation, supporting Markdown/MDX, versioning, localization, and easy customization.

**MkDocs / MkDocs Material**  
MkDocs is a fast, open-source static site generator for building project documentation with Markdown. The Material theme adds a modern, responsive design and enhanced UX features.

**Sphinx**  
Sphinx is a documentation generator popular in the Python community. It uses reStructuredText, supports multi-format output, and is highly extensible with plugins.

**Read the Docs**  
Read the Docs automates building, versioning, and hosting documentation from Git repositories. It’s trusted by open-source projects and teams wanting managed hosting.

**Docsify**  
Docsify is a lightweight, dynamic documentation generator built on Vue.js. It renders Markdown files on the fly and supports offline mode, themes, and plugins.

**GitHub Wiki**  
GitHub Wiki is a built-in feature of GitHub repositories, allowing teams to create and manage documentation alongside their code using Markdown.

**Jekyll**  
Jekyll is a Ruby-based static site generator tightly integrated with GitHub Pages. It’s ideal for developers wanting to publish documentation directly from their repos.

**Hugo**  
Hugo is a lightning-fast static site generator known for its speed, flexibility, and extensive theme ecosystem. It’s great for large documentation sites.

**Slate**  
Slate is a static site generator focused on beautiful, three-panel API documentation. It’s popular for developer-friendly, interactive API docs.

**AsciiDoc / Asciidoctor**  
AsciiDoc is a plain-text markup language for writing technical docs, and Asciidoctor is its toolchain for generating HTML, PDF, and more.

**Nuxt Content**  
Nuxt Content is a headless CMS for the Nuxt.js framework, enabling teams to build static or server-rendered documentation sites with Vue components.

---

## 🧪 API Documentation & Developer Portals

**Apidog**  
Apidog is a modern, all-in-one API platform that combines API design, testing, and documentation. It auto-generates interactive docs from OpenAPI/Swagger, supports real-time collaboration, built-in mock servers, versioning, and LLM-friendly publishing. Apidog is the top choice for API-first teams seeking a seamless workflow.

**Apiary**  
Apiary by Oracle is a collaborative API design and documentation platform. It enables teams to prototype APIs, generate docs, and test endpoints before writing code.

**GitHub**  
GitHub is the world’s leading code hosting platform, offering built-in documentation features like Markdown READMEs, wikis, and GitHub Pages for static sites.

**Read the Docs**  
Read the Docs (see above) is also widely used for API and developer documentation, automating builds and hosting from code repositories.

**GitBook**  
GitBook (see above) is popular for API and product documentation, with Git integration and real-time collaboration.

**Doxygen**  
Doxygen is a documentation generator that creates docs from annotated source code in multiple languages. It’s ideal for codebases needing detailed, auto-generated references.

**Swagger / OpenAPI tools**  
Swagger UI and related OpenAPI tools are industry standards for interactive API documentation, code generation, and testing.

---

## ✍️ Markdown Editors / Writing Tools

**iA Writer**  
iA Writer is a minimalist markdown editor focused on distraction-free writing. Its clean interface and focus mode make it a favorite for technical writers.

**Typora**  
Typora offers a seamless live preview for markdown, combining writing and reading in a single, unified interface.

**MarkdownPad**  
MarkdownPad is a Windows-based markdown editor with live preview and customizable themes, ideal for technical documentation.

**SimpleMDE**  
SimpleMDE is a browser-based markdown editor with a simple, intuitive interface and live preview.

---

## 🧰 Interactive Guide / Process Documentation Tools

**Tango**  
Tango is a Chrome extension that auto-captures your workflow in the browser, turning clicks into step-by-step guides with screenshots and text.

**Scribe**  
Scribe is an AI-powered tool that creates visual, step-by-step guides from your actions. It supports editing, sharing, and embedding guides in other platforms.

**iorad**  
iorad records browser and desktop workflows, generating interactive tutorials that can be shared or embedded in knowledge bases.

**FlowShare**  
FlowShare is a Windows app that automatically captures every click and creates step-by-step guides with screenshots and descriptions, supporting multiple export formats.

**Stepsy**  
Stepsy is a browser-based tool for capturing digital processes and creating shareable guides, ideal for Google Drive and web-based workflows.

**Screensteps**  
Screensteps is a knowledge base and documentation tool with built-in screen capture, editing, and analytics, designed for structured guides and onboarding.

**Snagit**  
Snagit is a screen capture and annotation tool for creating images and videos, perfect for enhancing documentation with visuals.

**Greenshot**  
Greenshot is a free, open-source screenshot tool for Windows and Mac, offering basic annotation and export features.

**FastStone Capture**  
FastStone Capture is a lightweight screen recorder and editor, ideal for creating annotated screenshots and video guides.

**Folge**  
Folge is a desktop app for capturing workflows, editing steps, and exporting guides in multiple formats, popular among technical editors.

**Whale**  
Whale is an AI-driven documentation and training tool for SOPs, onboarding, and process management, with automated workflows and integrations.

---

## 🛠️ Advanced Documentation / Publishing Tools

**Adobe RoboHelp**  
Adobe RoboHelp is a robust help authoring tool for creating and publishing user manuals, knowledge bases, and online help. It features a WYSIWYG editor, multi-format publishing, and automation.

**MadCap Flare**  
MadCap Flare is an enterprise-grade documentation tool with content management, single-source publishing, and advanced search, ideal for large-scale projects.

**Adobe FrameMaker**  
Adobe FrameMaker is designed for creating and publishing complex, long-form technical content, supporting DITA, video, and advanced formatting.







---

## 📚 Knowledge Base / Documentation Platforms

- **[Confluence](https://www.atlassian.com/software/confluence)**  
  Atlassian’s enterprise-grade documentation platform for team collaboration, internal wikis, and knowledge management. Integrates seamlessly with Jira and other Atlassian tools.

- **[Document360](https://document360.com/)**  
  AI-powered knowledge base solution for user guides, product documentation, and internal wikis. Features dual editor, analytics, versioning, and workflow automation.

- **[BookStack](https://www.bookstackapp.com/)**  
  Open-source, self-hosted documentation platform with a book/chapter/page structure. User-friendly and highly flexible for teams wanting full control.

- **[GitBook](https://www.gitbook.com/)**  
  Cloud-based documentation platform for software teams. Supports Markdown, real-time collaboration, and Git integration for internal and external docs.

- **[Helpjuice](https://helpjuice.com/)**  
  Enterprise knowledge base platform focused on customization, analytics, and powerful search. Scalable for large teams and customer self-service.

- **[HelpDocs](https://www.helpdocs.io/)**  
  Simple, fast, and user-friendly knowledge base tool. Ideal for startups and small businesses launching branded help centers.

- **[Heroic Knowledge Base](https://herothemes.com/plugins/heroic-wordpress-knowledge-base/)**  
  WordPress plugin for creating internal or external documentation sites. Features AJAX search, analytics, and unlimited content.

- **[KnowAll](https://herothemes.com/themes/knowall/)**  
  Premium WordPress theme for building knowledge bases. Easy setup, customizable layouts, and built-in analytics.

- **[Notion](https://www.notion.so/)**  
  All-in-one workspace combining notes, docs, wikis, and project management. Flexible block-based editor and real-time collaboration.

- **[Nuclino](https://www.nuclino.com/)**  
  Unified workspace for organizing knowledge, docs, and projects. Clean interface, real-time editing, and visual organization.

- **[Papyrs](https://papyrs.com/)**  
  Drag-and-drop intranet and documentation platform for non-technical users. Great for internal wikis, HR portals, and handbooks.

- **[ProProfs](https://www.proprofs.com/knowledgebase/)**  
  Knowledge base and help authoring tool for searchable, media-rich documentation, FAQs, and help centers.

- **[ReadMe](https://readme.com/)**  
  Developer-focused platform for interactive API documentation and developer portals. Features live API explorers and analytics.

- **[Tettra](https://tettra.com/)**  
  Internal wiki for knowledge sharing within teams. Slack integration and simple interface for fast-growing companies.

- **[Whatfix](https://whatfix.com/)**  
  Digital adoption platform for in-app guidance, walkthroughs, and self-help widgets. Ideal for onboarding and contextual documentation.

- **[Zendesk](https://www.zendesk.com/)**  
  Customer support suite with a robust help center and knowledge base module. Widely used for customer-facing documentation.

- **[Wiki.js](https://js.wiki/)**  
  Free, open-source documentation platform built on Node.js. Supports Markdown, multiple databases, and a modern UI.

- **[ClickHelp](https://clickhelp.com/)**  
  Cloud-based documentation tool for multi-language user manuals, knowledge bases, and help sites. Collaboration and analytics included.

- **[Atera](https://www.atera.com/)**  
  All-in-one IT management platform with built-in documentation features. Designed for IT teams and MSPs.

- **[Freshservice](https://freshservice.com/)**  
  ITSM tool with a built-in knowledge base and documentation features. Asset management, ticketing, and internal docs in one.

- **[IT Glue](https://www.itglue.com/)**  
  Documentation platform for managed service providers (MSPs). Centralizes IT assets, passwords, and procedures.

- **[N‑able Passportal](https://www.n-able.com/products/passportal/)**  
  Credential and IT documentation management tool for MSPs. Secure password storage, asset tracking, and knowledge base.

- **[SuperOps](https://superops.ai/)**  
  RMM/PSA platform with IT documentation capabilities. Manage client assets, tickets, and workflows from a unified dashboard.

---

## ⚙️ Static Site Generators / Docs Site Builders

- **[Docusaurus](https://docusaurus.io/)**  
  React-based, open-source static site generator for technical documentation. Supports Markdown/MDX, versioning, and easy customization.

- **[MkDocs](https://www.mkdocs.org/)**  
  Fast, open-source static site generator for building project documentation with Markdown. [MkDocs Material](https://squidfunk.github.io/mkdocs-material/) adds a modern, responsive design.

- **[Sphinx](https://www.sphinx-doc.org/)**  
  Documentation generator popular in the Python community. Uses reStructuredText, supports multi-format output, and is highly extensible.

- **[Read the Docs](https://readthedocs.org/)**  
  Automates building, versioning, and hosting documentation from Git repositories. Trusted by open-source projects.

- **[Docsify](https://docsify.js.org/)**  
  Lightweight, dynamic documentation generator built on Vue.js. Renders Markdown files on the fly and supports offline mode.

- **[GitHub Wiki](https://docs.github.com/en/communities/documenting-your-project-with-wikis/about-wikis)**  
  Built-in feature of GitHub repositories for creating and managing documentation alongside code using Markdown.

- **[Jekyll](https://jekyllrb.com/)**  
  Ruby-based static site generator tightly integrated with GitHub Pages. Ideal for publishing documentation from repos.

- **[Hugo](https://gohugo.io/)**  
  Lightning-fast static site generator known for speed, flexibility, and a rich theme ecosystem.

- **[Slate](https://github.com/slatedocs/slate)**  
  Static site generator focused on beautiful, three-panel API documentation. Popular for developer-friendly, interactive API docs.

- **[AsciiDoc / Asciidoctor](https://asciidoctor.org/)**  
  Plain-text markup language and toolchain for writing and generating technical docs in multiple formats.

- **[Nuxt Content](https://content.nuxt.com/)**  
  Headless CMS for the Nuxt.js framework, enabling static or server-rendered documentation sites with Vue components.

---

## 🧪 API Documentation & Developer Portals

- **[Apidog](https://apidog.com/)**  
  All-in-one API platform for design, testing, and documentation. Auto-generates interactive docs, supports real-time collaboration, and LLM-friendly publishing.

- **[Apiary](https://apiary.io/)**  
  Collaborative API design and documentation platform by Oracle. Enables prototyping, doc generation, and endpoint testing.

- **[GitHub](https://github.com/)**  
  Leading code hosting platform with built-in documentation features like Markdown READMEs, wikis, and GitHub Pages.

- **[Read the Docs](https://readthedocs.org/)**  
  (See above) Widely used for API and developer documentation, automating builds and hosting from code repositories.

- **[GitBook](https://www.gitbook.com/)**  
  (See above) Popular for API and product documentation, with Git integration and real-time collaboration.

- **[Doxygen](https://www.doxygen.nl/)**  
  Documentation generator that creates docs from annotated source code in multiple languages.

- **[Swagger UI / OpenAPI tools](https://swagger.io/tools/swagger-ui/)**  
  Industry standards for interactive API documentation, code generation, and testing.

---

## ✍️ Markdown Editors / Writing Tools

- **[iA Writer](https://ia.net/writer)**  
  Minimalist markdown editor focused on distraction-free writing.

- **[Typora](https://typora.io/)**  
  Seamless live preview for markdown, combining writing and reading in one interface.

- **[MarkdownPad](http://markdownpad.com/)**  
  Windows-based markdown editor with live preview and customizable themes.

- **[SimpleMDE](https://simplemde.com/)**  
  Browser-based markdown editor with a simple, intuitive interface and live preview.

---

## 🧰 Interactive Guide / Process Documentation Tools

- **[Tango](https://www.tango.us/)**  
  Chrome extension that auto-captures browser workflows, turning clicks into step-by-step guides.

- **[Scribe](https://scribehow.com/)**  
  AI-powered tool that creates visual, step-by-step guides from your actions.

- **[iorad](https://www.iorad.com/)**  
  Records browser and desktop workflows, generating interactive tutorials.

- **[FlowShare](https://getflowshare.com/)**  
  Windows app that automatically captures every click and creates step-by-step guides.

- **[Stepsy](https://stepsy.co/)**  
  Browser-based tool for capturing digital processes and creating shareable guides.

- **[Screensteps](https://www.screensteps.com/)**  
  Knowledge base and documentation tool with built-in screen capture and analytics.

- **[Snagit](https://www.techsmith.com/screen-capture.html)**  
  Screen capture and annotation tool for creating images and videos.

- **[Greenshot](https://getgreenshot.org/)**  
  Free, open-source screenshot tool for Windows and Mac.

- **[FastStone Capture](https://www.faststone.org/FSCaptureDetail.htm)**  
  Lightweight screen recorder and editor for annotated screenshots and video guides.

- **[Folge](https://folge.me/)**  
  Desktop app for capturing workflows, editing steps, and exporting guides.

- **[Whale](https://usewhale.io/)**  
  AI-driven documentation and training tool for SOPs, onboarding, and process management.

---

## 🛠️ Advanced Documentation / Publishing Tools

- **[Adobe RoboHelp](https://www.adobe.com/products/robohelp.html)**  
  Robust help authoring tool for creating and publishing user manuals, knowledge bases, and online help.

- **[MadCap Flare](https://www.madcapsoftware.com/products/flare/)**  
  Enterprise-grade documentation tool with content management and single-source publishing.

- **[Adobe FrameMaker](https://www.adobe.com/products/framemaker.html)**  
  Tool for creating and publishing complex, long-form technical content, supporting DITA and advanced formatting
