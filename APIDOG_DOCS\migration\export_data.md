
Apidog supports importing the following API data format files to facilitate data migration: 

- **[OpenAPI(Swagger) spec](apidog://link/pages/635046)**
- **[<PERSON><PERSON>](apidog://link/pages/635043)**
- **[Insomnia](apidog://link/pages/635047)**
- **[cURL](apidog://link/pages/635068)**
- **Apidog**
- **[.har file](apidog://link/pages/635056)**
- **JMeter**
- **[apiDoc](apidog://link/pages/635052)**
- **RAML**
- **I/O Doc**
- **WSDL**
- **WADL**
- **Google Discovery**
- **Markdown**
- **.proto file**
- **SoapUl**

You can import data either manually or through a scheduled import. 

## Manual import
If you were originally using other tools and now want to migrate to Apidog, you can use [Manual import](apidog://link/pages/633884).

## Scheduled import

If you maintain your API spec outside of Apidog but want to use Apidog for API debugging, testing, and documentation, you can use the [Scheduled import](apidog://link/pages/633932) feature.

## Import Markdowns

Apidog supports batch importing of Markdown documents. However, it does not support recognizing Markdown as API specifications. Learn more about [Import Markdowns](apidog://link/pages/635070).

## Export data

Apidog supports exporting APIs in data formats such as `OpenAPI Specification`, `HTML`, `Markdown`, `Apidog`. Learn more about [Export data](apidog://link/pages/635117).


Export Data
Navigate to the "Settings" → "Export Data" option in the left menu bar, select your preferred data format, and click the "Export" button.


You have the flexibility to export all APIs at once or select specific APIs for export.


Exporting can also be organized by tags to target specific grouped APIs.


For the OpenAPI Specification, we support versions 3.1, 3.0, and Swagger 2.0. Exports can be performed in YAML or JSON formats, incorporating the Apidog Extension OpenAPI Field and organizing exports using the Tags field as folders.


Additionally, it's possible to export the data as an offline document. Click "Open URL" to access and view the RAW content directly in your browser.



Export data from a sprint branch
You can export data from a sprint branch by selecting the branch indicated at the top-left corner of the page as the target for the export. When exporting from a sprint branch, all data from that branch must be exported.

The export process will involve merging the current sprint branch with the main branch to ensure the data is comprehensive. This step is crucial because various data items in Apidog are interconnected through references. If you only export the visible content of the sprint branch, you may end up with incomplete or unusable data.

export-data-from-a-sprint-branch.png
When exporting data from a sprint branch, you cannot choose a specific subset. The export will automatically include all content that has been merged between the sprint branch and the main branch.

OperationID
OperationId is a unique identifier used to distinctly identify an API operation in the OpenAPI specifications. When exporting in the OpenAPI format, the OperationId attribute can be set, and this identifier will be included in the exported OperationId of the Operation object.


FAQ
Q: Why have the number of exported APIs decreased?

A: This may occur when multiple APIs use the same method and path. HTML and Markdown are exported via OpenAPI data conversion, which does not support multiple APIs sharing the same methods and paths.

Q: Why is the API order disorganized when exported to Markdown and HTML?

A: The Swagger specification, which is a part of OpenAPI, lacks explicit support for ordering or grouping APIs. As a result, Markdown and HTML exports might appear unorganized. For orderly exports, consider using Apidog’s native format.

Q: Why do the numbers of exported APIs decrease when multiple APIs have the same URL?

A: The OpenAPI specification does not allow multiple APIs to share the same method and path. Ensure each API uses a unique URL. For further details on ensuring unique API identifiers, refer to Endpoint unique idenfication. Both HTML and Markdown exports are conducted through OpenAPI and are subjected to the same restrictions.

Q: How can I export to PDF?

A: Direct exports to PDF or Word are currently unsupported by Apidog. However, you can export your data in Markdown format first and then use external tools to convert it to the desired format. Typora, for instance, can convert Markdown into PDF, Word, OpenOffice, Epub, and other formats.

Q: Why was only one API exported successfully out of several?

A:

1.
Ensure that no multiple APIs use the same method and path.

2.
OpenAPI specifications prohibit different APIs from sharing identical method and path configurations. Consult Endpoint unique idenfication for more information.

3.
Note that translating to HTML or Markdown via OpenAPI data can cause issues.

Q: How to Export Authentication Values?

A: Apidog automatically exports Auth values within the Swagger format files. No additional setup is required. To locate these values, check the securitySchemes section in the Swagger format file.

