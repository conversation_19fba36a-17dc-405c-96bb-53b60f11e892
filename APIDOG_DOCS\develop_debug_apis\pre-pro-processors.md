Pre and Post-request Processors are a key feature in Apidog. When you send a request, you can use pre-request processors to dynamically control your request, or use post-request processors to handle the received response.

In Postman, all pre and post-request operations need to be implemented using scripts, which is more complex, less maintainable, and has a higher learning curve. In contrast, Apidog allows you to conveniently perform the majority of pre and post-request operations through visual, drag-and-drop actions.

## Getting started with pre/post processors
Here are the steps to use pre and post-request processors in Apidog:

<Steps>
  <Step>
    Open any endpoint and navigate to the Run tab (Design-first Mode) or Request tab (Request-first mode).
      <Tabs>
  <Tab title="Design-first Mode">
<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/352194/image-preview)
</Background>
  </Tab>
  <Tab title="Request-first Mode">
<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/352193/image-preview)
</Background>
    
    </Tab>

</Tabs>
  </Step>
  <Step>
    Switch to the Pre processors or Post processors section, hover over `Add processor`, and select the desired processor to add.
  </Step>
  <Step>
Fill in the necessary fields for the added processor.
  </Step>
  <Step>
    Click `Send`, and the request will execute with the configured pre/post processors.
  </Step>
  <Step>
    In Design-first Mode, click `Save as case` to save the request. In Request-first Mode, click `Save`/`Save as case` to save the request.
<Tabs>
  <Tab title="Design-first Mode">
<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/352190/image-preview)
</Background>
  </Tab>
  <Tab title="Request-first Mode">
<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/352189/image-preview)
</Background>
    </Tab>
</Tabs>
     
  </Step>
</Steps>

In Apidog, you can also create a standalone request and apply pre and post-processors to it. However, the recommended approach is to leverage the pre and post-processors on top of the endpoint specification. This makes the API debugging and testing process more convenient and streamlined.

## What do pre/post processors do

Pre/post processors can collectively achieve various functionalities like below.

### Pre processors

- Setting the values of request parameters and the request body: This involves defining the input data that will be sent to the API endpoint for processing.

- Setting request headers: Specifying necessary headers such as Content-Type, Accept, Authorization, etc., for the API request.

- Adding authentication to the request: Including authentication tokens, API keys, or any other credentials required for accessing protected endpoints.

- Retrieving values from a database to use as request parameters: Fetching relevant data from a database to populate the API request with dynamic or pre-defined values.

- Encrypting the request: Securing the data being transmitted by encrypting the request payload to protect sensitive information.

### Post processors

- Writing assertions to validate the response received from the API against expected results. This can include checking status codes, response messages, specific data values, etc.

- Extracting variables from the response to use them in subsequent requests. This is useful for maintaining state and continuity in API test flows.

- Writing data to a database to store or update information generated during API testing. This can be particularly useful for scenarios where data persistence is required for later analysis or reference.

- Visualizing response data for better analysis and understanding. This can involve generating reports, graphs, or any other visual representation of the API response data to gain insights into the system's behavior and performance.

## List of pre/post-request processors

Apidog supports the following pre/post processors:

- **[Assertion](apidog://link/pages/588467)**: Available only in post processors. Allows you to define validation rules to check the response data against expected values or conditions. It supports various assertion types like status code, response body, headers, etc.

- **[Extract variable](apidog://link/pages/588468)**: Available only in post processors. Enables extracting values from the response (JSON, XML, or plain text) and storing them as variables. It supports using JSONPath and XPath expressions to target specific parts of the response. Variables can be used in subsequent requests or other processors.

- **[Database Operation](apidog://link/pages/588469)**: Available both in pre and post processors. Allows you to connect to databases and execute SQL queries. The query results can be stored as variables for use in further processing. It supports various database types, including SQL and NoSQL databases.

- **[Custom Scripts](apidog://link/pages/593582)**: Available both in pre and post processors. Provides a scripting environment where you can write your own JavaScript code. Scripts can access request and response data, as well as environment variables. It's useful for complex logic or integrations that cannot be easily expressed through the visual processors.

:::highlight purple
We recommend using the **[Apidog Script Generator](https://app.anakin.ai/apps/21857)** to simplify script writing. Simply describe the script you want in natural language, and the generator will create a runnable script for you.
:::


- **[Public Scripts](apidog://link/pages/593613)**: Available both in pre and post processors. Enables creating reusable script snippets that can be shared across multiple processors or scenarios. It's helpful for encapsulating common logic or utility functions.

- **[Wait](apidog://link/pages/588474)**: Available both in pre and post processors. Introduces a delay or wait period before or after a request is sent. It's useful for simulating real-world scenarios or handling asynchronous responses.

## The execution order of pre/post processors

The pre and post-request processors in Apidog can be set at multiple levels:

- **Run/Endpoint case level**: Default level. Processors set at this level will only affect the current run/endpoint case.
- **Endpoint level**: Processors set at the endpoint level will be applied to all cases (requests) associated with that endpoint.
- **Folder level**: Processors configured at the folder level will impact all endpoints within that folder.

A set of pre-operations in a request may look like this:

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342813/image-preview)

In the screenshot above, the first three processors are grouped under the label "**Inherit from parent**". The label on the right-hand side indicates that they are located in the Root folder, Pets folder, and the current endpoint. In the current run, these processors can only be executed or switched on/off, but not edited directly. If you need to edit them, you can click the folder to navigate to the corresponding level.

The "Inherit from parent" processors are collapsed by default, and you can click the checkbox in front of them to choose whether to execute them or not.

The last processor, a custom script, is a new addition in the current run and can be edited within the current run.

### Variable Substitution

You may also notice that at the end of all the pre-processors, there is a step called "**Variable Substitution & Inherit from Parents**".

In Apidog, the "Variable Substitution" pre-processor is a special pre-processor that serves the purpose of replacing variable placeholders like `{{variable}}` with their current values.

Most pre-processors can be executed before variable substitution, which is the default execution order in Apidog. However, some processors require execution after variable substitution to be effective, such as scripts like API signature. In such cases, these processors need to be manually moved after the "Variable Substitution & Inherit from Parents" step.

Additionally, in the parent pre-processor section, you can also drag a processor after the variable substitution. These parent-level operations will then appear in the "Inherit from Parents" section within the "Variable Substitution & Inherit from Parents" step.

### Execution order

The processors execution order for a single request looks like this:
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/342850/image-preview" style="width: 640px" />
</p>


Assertions play a crucial role in API testing. When you need to verify if the response of an request meets your expectations, you have to write an assertion. In tools like Postman, assertions require writing scripts manually. Nevertheless, in Apidog, you can visually create assertions, which is extremely convenient.

## Adding an assertion to post processor

<Steps>
  <Step>
    In the Run tab (Design-first Mode) or the Request tab (Request-first mode), navigate to Post Processors.
      <Tabs>
  <Tab title="Design-first Mode">
<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/352194/image-preview)
</Background>
  </Tab>
  <Tab title="Request-first Mode">
<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/352193/image-preview)
</Background>
    
    </Tab>

</Tabs>
  </Step>
  <Step>
    Hover over "Add Post Processor" and select "Assertion".

<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/352195/image-preview)
</Background>

  </Step>
  <Step>
Select the target for your assertion, which can be various elements in the API response such as headers, body content, or status codes. You can choose the specific element that you want to verify against your expected outcome. (For example:`$.data.status`) Please note that the root object is represented by`$`, and it works for both objects and arrays.

<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/348239/image-preview)
</Background>

    </Step>

  <Step>
If the target is in the JSON body, use JSONPath to extract the desired value.
  </Step>
  <Step>
Set the assertion conditions based on your testing requirements. You can define criteria such as equal to, not equal to, contains, does not contain, greater than, less than, etc. 
In the assertion value field, you can input a static value or use dynamic variables in the format of `{{variable}}`.
  </Step>
  <Step>
Click on the send button to execute the assertions. You can view the results of the assertions in the right-hand side panel.

<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342854/image-preview)
</Background>

    </Step>
</Steps>

:::tip[]
When performing assertions in Apidog, all data is automatically converted to **STRING** for comparison. While this simplifies the process in most cases, you may encounter bad cases such as `4` not being equal to `4.0`. For such specific assertions where data comparison needs to consider more intricate details, you can utilize custom scripts to write assertions manually.
:::

## Setting assersions on actual response

To quickly create an assertion based on the actual response, hover over the response field(e.g., on endpoint response or test reports), and click on`assertion`.

<Background>
![assertion-on-actual-response.png](https://api.apidog.com/api/v1/projects/544525/resources/348240/image-preview)
</Background>

## Using scripts for assertions

In Apidog, you can employ the `pm.test` syntax to create assertions using scripts. Apidog is compatible with Postman scripts, allowing users to leverage existing scripts seamlessly. 

:::highlight purple
Learn more about [scripts](apidog://link/pages/593582).
:::

Apidog supports visually extracting values from the API response and saving them as variables. 

## Extract variables

To get started to extract variables, follow these steps:

<Steps>
  <Step>
      In the Run tab (Design-first Mode) or the Request tab (Request-first mode), navigate to Post Processors.
      <Tabs>
  <Tab title="Design-first Mode">
<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/352194/image-preview)
</Background>
  </Tab>
  <Tab title="Request-first Mode">
<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/352193/image-preview)
</Background>
    
    </Tab>

</Tabs>
  </Step>
  <Step>
   Enter the Variable Name and choose the Variable Scope.
Select the extraction source, such as Response JSON, Response XML, or Response Text, etc.
  </Step>
  <Step>
If the response is in JSON/XML format, you can utilize JSONPath/XPath syntax to parse a specific part of the Response JSON/XML and save it as the value of the variable.
      
<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342756/image-preview)
</Background>

  </Step>
  <Step>
After clicking "Send" to send the request, the variable extraction will be executed, and you can view the logs in the Console.

<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342758/image-preview)
</Background>

  </Step>
</Steps>

## Extract variables from actual response
To quickly extract variables from the actual response, hover over the response field(e.g., on endpoint response or test reports), and click on`Extract variable`.

<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/348242/image-preview)
</Background>

## Extract source

Apidog supports extracting data from various data sources. Below are the different data sources from which data extraction is supported:

- **Response text**: Supports extracting data using Regex expressions.

- **Response JSON**: Supports extracting data using JsonPath expressions.

- **Response XML**: Supports extracting data using XPath expressions.

- **Response Header**: Extract from header name.

- **Response cookie**: Extract from cookie name.

- **Time Consuming**: Extract the time taken for request execution, providing insights into request response times and performance metrics for analysis and optimization purposes.

## JSONPath extraction tool

JSON is the most commonly used response data format, and JSONPath is a widely used syntax for data extraction within JSON structures. Apidog provides a visual JSONPath Extraction tool to simplify the process of crafting JSONPath expressions. Here is a guide on how to use this tool effectively:


<Steps>
  <Step>
Click on the <Icon icon="ph-bold-arrow-square-out"/> icon located to the right of the JSONPath input box.

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342819/image-preview)
      
  </Step>
  <Step>
In the JSONPath Extraction tool that pops up, the left section displays the JSON response. You can input your JSONPath expression in the JSONPath Expression field at the top right. The results section at the bottom will dynamically extract the corresponding data based on your expression.

<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342817/image-preview)
</Background>

  </Step>
  <Step>
If your JSONPath expression includes wildcard characters (*) or similar elements that may result in extracting multiple values, the results will be enclosed within square brackets ([]). If you wish to extract a specific value and **avoid the brackets**, you can toggle the "**Continue extracting**" switch and specify the index of the value you want to extract.
  </Step>
</Steps>

Apidog's Test Scenario feature provides a "Wait" operation that allows you to set a waiting period in milliseconds within your test flow, thereby controlling the execution order and timing of subsequent operations.

This functionality is very useful, especially in the following scenarios:

1. **API Response Latency**
   - When the API you're testing has a certain response delay, you can use the Wait operation to pause for a period of time, ensuring that you can retrieve the complete response data.

2. **Waiting for Operations to Complete**
   - In a test scenario, you may need to wait for some asynchronous operations to finish, such as waiting for a database update or a file upload. Using Wait ensures that the subsequent steps have enough time to execute.

3. **Simulating User Interactions**
   - When testing interactive APIs, you can leverage the Wait operation to simulate the pause time between user actions, thereby more closely mirroring real-world user scenarios.

By flexibly setting the Wait time, you can better control the execution flow of your test scenarios, improving the reliability and realism of your tests, and ultimately ensuring the quality of your APIs.


