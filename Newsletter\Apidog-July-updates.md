- 🔥[New Feature] Online documentation now supports custom CSS and JavaScript.
![Custom CSS and JavaScript](https://assets.apidog.com/uploads/help/2025/07/11/ts4mf-7u.gif)
- 🔥[New Feature] When publishing online documentation, you can now configure whether the left-side folder displays schemas.
- ⚡️[Optimization] When the imported endpoint has security requirements, the Auth can now be set to the corresponding security scheme.
- 🔥[New Feature] Parameter names can now be generated using AI.
- 🔥[New Feature] You can now add `Endpoint Design Guidelines` to a project to help project members and AI write more standardized API documentation.
![Endpoint Design Guidelines](https://assets.apidog.com/uploads/help/2025/07/11/tt65g-sk.gif)
- 🔥[New Feature] Support for connecting to MySQL and PostgreSQL databases with SSL enabled.
- 🔥[New Feature] Supports Webhook and Callback type endpoints.
- 🔥[New Feature] Modules now support module variables, equivalent to collection variables in Postman.
- ⚡️[Optimization] When running endpoints in `Design-first Mode`, you can use the default authentication credentials or set them manually.
- ⚡️[Optimization] Improved the UI for OAuth 2.0 authentication.
- 🐞[Bug Fix] Fixed a UI error when debugging Socket.IO endpoints that return an empty message or ack.
- ⚡️[Optimization] The OpenAPI Generator for generating business code has been upgraded to v7.13.0.
- 🔥[New Feature] Added Offline Space with endpoint debugging capabilities. Simple, fast, and stores files locally.
- 🐞[Bug Fix] Fixed an issue that when exporting data in OpenAPI 3.0 format, the example values ​​of the fields in schemas are exported as `examples` instead of `example`.
- 🐞[Bug Fix] Fixed an issue that when exporting data in OpenAPI 3.0 format,  the NULL-allowed attributes of the schemas are exported as `type: null` instead of `nullable: true`.
- 🐞[Bug Fix] Fixed an issue where the `Discriminator` was lost when exporting OpenAPI/Swagger data.
- 🐞[Bug Fix] Fixed an issue where, during OpenAPI/Swagger import, the request body structure was lost when the `Media Type` was unspecified or set to */*.