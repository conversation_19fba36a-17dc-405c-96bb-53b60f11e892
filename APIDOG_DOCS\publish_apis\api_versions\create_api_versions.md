After entering a project, you can find the "API Versions" option in the sprint branch switching component located above the folder tree. By clicking on it, you'll see all the available API versions within the current project.

<Background>
![API Versions Option](https://assets.apidog.com/help/assets/images/create-api-version-01-276bc910372bbe44970d1cf4cd2aea86.png)
</Background>

Clicking on "New API Version" will prompt you to enter a version number and choose the initial version content. By default, it will create a full copy from other existing API versions. If this option is selected, the new API version will include copies of all resources from the chosen version. Alternatively, selecting a blank option will create a new version with no content.

<Background>
![Create New API Version](https://assets.apidog.com/help/assets/images/create-api-version-02-491bd0501ab9b7b06735e89b772038a7.png)
</Background>
After saving, the new API version will be successfully created and opened automatically. If a copy was created from an existing version, you will see all resources from the selected API version in the new version.

<Background>
![New API Version](https://assets.apidog.com/help/assets/images/create-api-version-03-a2888f8e7cf719e9520739303c597dbd.png)
</Background>
    
In the new API version, clicking on any resource allows you to edit it as you would with the main branch content. Changes made here are independent and do not affect the copied API version.

<Background>
![New API Version Copy](https://assets.apidog.com/help/assets/images/create-api-version-04-dfc561d4ee6307ea6c4463a5485d44de.png)
</Background>
    
By following the above steps, you can create an API version. Depending on your needs, you can modify and adjust specific resources within the API version to create a version with distinct content.