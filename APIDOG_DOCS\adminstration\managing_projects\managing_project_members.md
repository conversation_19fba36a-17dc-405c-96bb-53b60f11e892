Managing Project Members
You can view all the members of your project in the Project Settings > Members.

viewing-project-members.jpg
Members collaborate based on their assigned permissions. Only users with the "Project Admin" role can manage members; other roles can only view member details without making changes.

Adding team members to the project
You can add users from the current team (where the project resides) to the project directly from the project member management page.

NOTE
1.
Only project admins can add team members to the project.

2.
If you’re a project admin but not a team admin, you cannot invite external users to the team or project.

To add a team member to a project, click on the + Add button at the top right of the project member management page.

add-team-members-project.png
And then assign the appropriate project role to the new member.

inviting-team-users-join-project.png
Managing member details & permissions
Click the settings button on the right of a member's name to open the member details page.

project-member-detail-page-entry.jpg
There, you can configure more advanced settings.

assigning-roles-project-members.png
1.
Name: Assign a nickname for easier identification within the project. Members can also modify their own nicknames.

2.
Project Role: Assign a role to control access to project functions (e.g., editing endpoints or running automated tests). Roles are managed at the team or organization level. Learn more here.

3.
Remove Member: Remove a member from the project. They’ll lose access to project data but will still remain part of the team. To completely remove them from the team, you must go to the team member management page.

