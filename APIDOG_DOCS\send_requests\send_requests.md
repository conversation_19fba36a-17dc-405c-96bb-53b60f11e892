When either developing your API, or integrating an external API, you can use Apidog to connect to these APIs by sending requests. These requests are capable of fetching, adding, or removing data, and also allow the transmission of parameters and authorization information.

For example, when you are developing a shop application, you might need to send a login request, then send a request to get the product list, and then send a request to create an order.

## Sending a request

<Video src="https://www.youtube.com/watch?v=_b-Z6mMHcGA"></Video>

<Steps>
  <Step>
In Apidog, to send a request, you only need to click "+" - "New Request"
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/343891/image-preview" style="width: 240px" />
</p>
  </Step>
  <Step>
You can fill in the necessary parts of the request according to the API spec into the Apidog interface, such as the request method, URL, request parameters, authorization, and so on.
<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/343866/image-preview)
</Background>
  </Step>
  <Step>
Click "Send", and you will see the server's response to this request in the lower half of the interface. You can debug and develop based on this response.  
    </Step>
</Steps>

View the following topics to get started sending API requests in Apidog:

- To learn the basics of building requests—including adding parameters, headers, and body data—go to [Create and send API requests in Apidog](apidog://link/pages/626832).

- If the API you're connecting to requires verifying your identity or access, you can learn more about [API authentication and authorization in Apidog](apidog://link/pages/629096).

- Apidog provides tools for viewing or visualizing API response data and for managing cookies. To learn more, go to Work with [API response data and cookies in Apidog](apidog://link/pages/629648).

- Variables enable you to use reuse data throughout your requests and change values based on your working environment. To learn more, go to [Reuse data with variables and environments in Apidog](apidog://link/pages/577908).

- In addition to sending HTTP requests, you can use Apidog to send API requests using different protocols including [GraphQL](apidog://link/pages/629866), [gRPC](apidog://link/pages/629868), [WebSocket](apidog://link/pages/629877), [SSE](apidog://link/pages/629889), and [SOAP/WebService](apidog://link/pages/629910).