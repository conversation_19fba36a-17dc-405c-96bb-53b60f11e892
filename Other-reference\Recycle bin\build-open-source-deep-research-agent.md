Have you ever thought about creating your very own open-source deep research agent instead of relying on proprietary options like OpenAI's Deep Research and Google's Deep Researcher? With powerful Model Context Protocol (MCP) servers like Sequential-Thinking and Exa, you can build an impressive and robust alternative to proprietary tools. 




In this guide, we’ll walk you through setting up and running your own research agent using just two MCP servers:✅ Sequential-Thinking – for structured reasoning and analysis✅ Exa – for powerful AI-driven web searches


We’ll be working with Windsurf IDE and integrating your choice of AI models. For this tutorial, I’ll be using DeepSeek V3, but you can also opt for models like:
Claude Sonnet 3.5 or 3.7 (from Anthropic)
GPT-4o or GPT-3.5 (from OpenAI)
O3-mini and others
Let’s get started! 🚀
What is an Open-Source Deep Research Agent?
So, what actually is this "open-source deep research agent" tool that we'll be working on? At its core, the open-source deep research agent is a tool designed to automate research tasks by leveraging the power of the Model Context Protocol (MCP). It combines the power of AI-driven reasoning with web search capabilities, allowing you to gather, analyze, and summarize information from multiple sources efficiently.
Here’s how it works:
The tool connects to an MCP server, such as Sequential-Thinking or Exa, to process research queries intelligently.
It integrates with AI models to interpret, summarize, and generate insights from the gathered information.
It utilizes web search capabilities to fetch relevant data, ensuring that your research is comprehensive and up to date.
Since it’s built with open-source components, you have full control over your data, ensuring transparency and customization.
Who Is This Open-Source Deep Research Agent Suitable For?
This deep research agent is ideal for:1. Researchers & Academics – Quickly gather and analyze information from various sources to support academic writing, literature reviews, or scientific exploration.2. Journalists & Writers – Automate background research, fact-checking, and content curation for articles, reports, or investigative journalism.3. Developers & AI Enthusiasts – Experiment with AI-powered workflows, build custom research assistants, or integrate MCP servers into their projects.4. Analysts & Policy Makers – Extract insights from vast datasets, reports, and news sources to inform decision-making.5. Students & Lifelong Learners – Streamline study sessions by summarizing key concepts and generating well-structured explanations.
When Is an Open-Source Deep Research Agent Most Useful?
1. Handling Large-Scale Research – When dealing with vast amounts of information across multiple sources, an AI-assisted research agent can save time and effort.2. Automating Repetitive Research Tasks – If you frequently conduct similar searches, this tool can automate the process, reducing manual work.3. Ensuring Unbiased & Transparent Research – Unlike closed-source research tools, an open-source solution allows you to verify how data is processed and maintain full control over your workflow.4. Working with Custom AI Models – If you prefer using a specific LLM (like DeepSeek v3) or need domain-specific AI models, this tool lets you integrate the model of your choice.5. Enhancing Productivity – By combining AI-driven reasoning with web search, you get well-organized insights faster than traditional research methods.
How to Set Up Your Open-Source Deep Research Agent
Prerequisites: 
Windsurf IDE: the latest version of Windsurf can be installed from the official website.
Node.js: v20 or higher is recommended.
npm: recommended to have the latest version of npm, however you should be able to get along fine with v7.
Step 1: Create a New Project Folder
Create a New Folder: Start by creating a new folder for your project, e.g., deep_researcher.
Open with Windsurf IDE: Open this folder using Windsurf IDE, which supports MCP server integration.
Step 2: Install Sequential-Thinking MCP Server
Install Sequential-Thinking MCP: Run the command below to install and configure the Sequential-Thinking MCP server. This will automatically set up the server without requiring manual configuration changes.

Verify Configuration: Check the mcp_config.json file in Windsurf's configuration directory .codeium(if you cannot remember where you installed windsurf try looking at: C:/Users/<USER>/.codeium/windsurf/mcp_config.json) to ensure the Sequential-Thinking server is correctly configured. It should look something like this:

If the file is empty, visit their GitHub repository for the updated configurations or simply copy and paste the one above.
Test the Server: Test the Sequential-Thinking MCP server by running sample commands, such as: 

Step 3: Set Up Exa Web Search MCP Server
Create an Exa Account and Get API Key:
Visit the Exa official website, create an account, and obtain a free API key from the "API Keys" section of your profile.

Clone Exa MCP Server Repository: Clone the Exa MCP server repository from GitHub:

Install Dependencies and Build Project:
To install all dependencies using npm, run the command:

Build the project:

Create a Global Link:
Run the following command to make the Exa MCP server executable from anywhere by running:

Configure Exa MCP Server in Windsurf:
Update the mcp_config.json file with the latest configurations from Exa's GitHub repository. Replace example text with your actual API key. It should look something like this: 

Test Exa MCP Server: to verify that the Exa server is working by running sample prompts, such as:

Using Your Open-Source Deep Research Agent
Now that both MCP servers have been set up and configured correctly, you can use them to create a deep research tool. Here’s a sample prompt to test your setup:

This prompt will generate a structured response with search links used by the model to answer your question.
Web searched links:

Sample output:

Features and Benefits
Flexibility and Control:
By using open-source MCP servers, you maintain full control over your research process and data privacy.
Customization:
You can choose from various AI models like DeepSeek v3, Claude Sonnet 3.5, or GPT 4o, allowing you to tailor your research tool to specific needs.
Cost Efficiency:
Running your own MCP servers can be more cost-effective than relying on proprietary services, especially for frequent or large-scale research tasks.

What to Do When Open Source Deep Research Agent Doesn't Work
When setting up and using your open-source deep research agent with MCP servers, you might encounter some issues. Here are some common problems and their solutions:
MCP Server Not Configured Correctly:
If your MCP server is not working as expected, check the configuration files (e.g., mcp_config.json) for errors. Ensure that API keys are correctly set, and that the server is properly linked.
API Key Issues:
If you encounter errors related to API keys, verify that they are correctly entered in your configuration files. Also, check if your API keys have expired or if you have exceeded usage limits.
Model Not Responding:
If your AI model is not responding, ensure that it is properly installed and configured. Check for any updates to the model or its dependencies.
Web Search Results Not Found:
If web search results are not being returned, check your internet connection and ensure that the search API (e.g., Exa) is functioning correctly.
Conclusion
Building an open-source deep research agent using MCP servers like Sequential-Thinking and Exa offers a powerful alternative to proprietary tools. By integrating these servers with Windsurf IDE and your preferred AI model, you can create a flexible and cost-effective research tool that maintains your data privacy and control.

