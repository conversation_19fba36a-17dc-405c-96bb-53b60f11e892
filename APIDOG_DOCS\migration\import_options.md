Import options
During both Manual import and Scheduled import, there will be a series of import options, as follows:

image.png
Basic auth
If you are importing using a URL, some URLs may use Basic auth encryption. In this case, you can simply turn on the Basic auth toggle and fill in the username and password.

Importing into folder
Supports importing files to specific folders.

While matching the same endpoints
When importing API specs, there may already be previously written API specs in the project. If there are identical endpoints in the imported data and the existing data, how are they handled during import? Are they directly overwritten or processed in some other way?

In Apidog, there're several options:

image.png

Overwrite: When the method & path of two endpoints is the same, the new item file will overwrite the old file.

Ignore: When the method & path of two endpoints is the same, the new item file will not be imported.

Keep both: When the method & path of two endpoints is the same, the new item file will be imported while the old file won't be deleted.

The above overwrite options also apply when importing Markdown, Schema, and Environment. These elements are matched in the following ways respectively:

Markdown: Matched by Markdown document title

Schema: Matched by Schema name

Environment: Matched by environment name

And update folder
When an endpoint is matched, whether to update its containing folder (such as folder name, folder pre/post operations, etc.)

Import cases
Endpoint cases are request cases based on endpoints in Apidog.

If the import data source does not include endpoint cases (such as OAS), enabling this option will automatically generate a "Success" endpoint case under the endpoint.

If the import data source includes endpoint cases (such as Apidog format), enabling this option will import the corresponding cases. If cases with matching names are encountered, they will be overwritten.

Add base path
In earlier versions of OpenAPI (previously known as Swagger), specifically Swagger 2.0, the basePath was used to indicate a common base URL for all API calls. For instance, if you had several API endpoints such as /users, /products, etc., you could set a basePath of /api/v1. Consequently, the full URLs to access these endpoints would be /api/v1/users, /api/v1/products, and so on.

Enabling "Add base path" will cause the imported path to be assembled as /api/v1/users; when not enabled, it will be imported as /users.

