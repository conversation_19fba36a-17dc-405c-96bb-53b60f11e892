In Apidog, each project corresponds to an API Specification or an OpenAPI Specification (OAS) file. You can create a new API project or import an existing one from **Home** - **My Teams** - **Projects** section of the Apidog app.

## Creating a Project

<Steps>
  <Step>
    Click on **New Project**.
  </Step>
  <Step>
    Select the project type. Apidog currently supports two types of projects: `HTTP` and `gRPC`. 
<Background>
<p style="text-align:center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/340914/image-preview" style="width:640px" />
</p>
</Background>

    - `HTTP`: Choose this for APIs like `REST`, `SOAP`, `GraphQL`, `WebSocket`, etc. 
    - `gRPC`: Select this for APIs that use the `gRPC` protocol. Learn more about [gRPC projects](url).
  </Step>
  <Step>
    Enter the name for your project.
  </Step>
  <Step>
      (Optional) Check the **Including Examples** option to create a project with sample data from the PetStore example.
  </Step>
  <Step>
    (For teams with multiple users) Set permissions for other team members during project creation. For more information, see [Project Permissions](url)[Member permissions settings](apidog://link/pages/616186).
  </Step>
  <Step>
    (Optional) Set the **Project language**. For more information, see [Language settings](apidog://link/pages/640826).
  </Step>
</Steps>

## Import existing APIs

If you already have an API Specification, you can easily import it into Apidog. Apidog supports various API formats, making it simple to integrate your existing specifications regardless of the original format. 
<Steps>
  <Step>
    You can initiate the import process either from the **Team** - **Projects** section by selecting **Import Project**, or directly within an existing project by clicking the large `➕` sign and choosing **Import**.
  </Step>
  <Step>
    Choose the type of data you wish to import. Apidog supports a variety of data formats, including `OpenAPI (Swagger)`, `Postman`, `.har`, `apiDoc`, `RAML`, `JMeter`, `I/O Docs`, `WADL`, `Google Discovery`, and more. 
<Background>

<p style="text-align:center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/340910/image-preview" style="width:640px" />
</p>
</Background>

  </Step>
</Steps>

:::highlight purple
For more detailed information about the import process, please refer to the [Import Guidelines](apidog://link/pages/633036).
:::