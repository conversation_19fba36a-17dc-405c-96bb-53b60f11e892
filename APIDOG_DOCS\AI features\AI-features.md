Starting from version `2.7.18`, Apidog has officially introduced AI capabilities into the client. Our goal is to help you generate, test, and publish APIs more efficiently—with the power of AI.

To [enable AI features](apidog://link/pages/1225685), simply configure the API key from a supported AI provider in Apidog.

**Current Available AI Features:**

- Assist in modifying schemas, including auto-generating field descriptions, mock data, and more.

**Coming Soon:**

- Automatically generate endpoint cases;
- Check the compliance of the endpoint definition;
- AI-assisted naming for endpoint parameters;
- Generate test scenarios;
- ...

We’re committed to continuously expanding what AI can do within Apidog — while making the experience smoother and more intuitive. Have ideas or feedback? We'd love to hear from you! Join the discussion in our [Discord](https://discord.com/invite/ZBxrzyXfbJ) or [Slack](https://join.slack.com/t/apidogcommunity/shared_invite/zt-2neie4nh2-4_zhufuNBmCq4EtI6fZUwA) community.


AI features in Apidog are disabled by default. To enable them, go to `Organizations`/`Team` – `Resources` – `AI Features` and switch them on. Once enabled, all projects within your organization or team can start using AI to boost productivity.

:::caution[]
Only organization or team admins (or higher roles) can configure AI features.
:::

## Configure Model Providers

Once `AI features` are enabled, you'll see an option to configure model providers. Click `+ Add Provider` to start configuration.

<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/357707/image-preview)
</Background>

Currently, Apidog supports the following model providers:

- OpenAI
- Anthropic 
- Google Al studio
- Google Vertex

<Background>
![img_v3_02nl_16642d0d-72b2-4618-9a38-4eeef7db106g.png](https://api.apidog.com/api/v1/projects/544525/resources/357480/image-preview)
</Background>

If these providers don’t meet your needs, you can also use `Custom API Configuration` to connect other providers models.

Generally, you can customize the following settings:

1. **API Key**

Enter the API Key provided by your chosen model provider. Use the `Test` function to check if the key is valid.

<Background>
![06-apidog.gif](https://api.apidog.com/api/v1/projects/544525/resources/357705/image-preview)
</Background>

2. **API Base URL**

The actual URL to which requests are sent when using `AI features` in Apidog. For built-in providers, we pre-fill the base URL — you can edit it as needed.

:::tip[]
Each request to the AI model is sent from the Apidog server to this API base URL.
:::

3. **Model List**

The list of models provided by the AI provider. If you’re using a preset provider, some models will appear by default. Only the models enabled in this list can be used for AI features. If a model you need isn’t listed, you can add it manually.

:::tip[]
Apidog’s`AI features`come with preset prompts and invocation flows. To get the best results, **be sure to select advanced, powerful models** (supporting longer context, function calling, etc. for example: GPT-4.1-mini).
:::

4. **API Format**

Under `Custom API Configuration`, you can define the request and response format. Using the wrong format may cause API request to fail. Most models follow the OpenAI format.

## Set Default Model

If a user doesn’t specify a model when using AI features, Apidog will use the default model set here. You’ll see a dropdown with all models currently enabled — just choose the one you want to use by default.

<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/357708/image-preview)
</Background>

The default model is set to "**Auto Select**" by default, which automatically picks an available model based on the order of enabled providers and models.

If you set a specific model as the default but it gets disabled or removed, Apidog will automatically switch back to "**Auto Select**".

## Functions & Prompt

You can manage all Apidog AI features and customize their prompts here. Once a feature is enabled, you’ll see it appear in the relevant section of your project. Apidog provides default prompts for each feature, which you can adjust to better suit your needs.

<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/357709/image-preview)
</Background>

:::warning[]
Custom prompt editing is not yet supported—stay tuned for future updates!
:::

## Inherit AI Configuration from Organization to Team

When using organization management, organization admins or owners can set up AI features at the organization level. These settings can apply to all teams under the organization, helping you maintain consistency and simplify management across projects.

You’ll find the same `AI Features` configuration interface under `Resources` in the organization management page as you do at the team level.

<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/357710/image-preview)
</Background>

Once AI features are set up at the organization level, all teams within the organization can inherit the configured model providers, default model, and functions.

<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/357711/image-preview)
</Background>

**Key points about team inheritance under organizations:**

1. All toggles — such as AI feature switches, model provider settings, default model selections, and functions — follow the organization’s configuration:
- If a feature is enabled at the organization level, teams can choose to turn it on or off.
- If a feature is disabled at the organization level, teams won’t be able to enable it.

This ensures centralized control and consistency across all teams.

<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/357712/image-preview)
</Background>

2. Model providers can either be inherited from the organization or set up independently by each team, depending on your needs.

<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/357713/image-preview)
</Background>

3. Teams can also choose to use the default model defined at the organization level or configure their own.

<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/357714/image-preview)
</Background>

AI can help you quickly refine your schemas by adding field descriptions, mock rules, and more — making it easier to keep your documentation complete and consistent.

On the endpoint edit page or schema pages, hover over any schema section to reveal the AI feature. Just click the icon to get started.

<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/357655/image-preview)
</Background>

Clicking the`AI`button opens a side-by-side pop-up window:

- On the left, you'll see the current schemas.
- On the right, you’ll find the AI assistant, where you can chat naturally and ask for updates to the schemas based on your needs.

<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/357694/image-preview)
</Background>

To make things even easier, we’ve included a few one-click suggestions for common edits — just tap to let the AI generate content for you.


<Background>

    
![05-apidog-01.gif](https://api.apidog.com/api/v1/projects/544525/resources/357696/image-preview)
</Background>

If you need to fine-tune the AI-generated content, you can edit it directly in the pop-up. Once everything looks good, click the `Apply` button at the bottom-right corner to save all changes to the schemas.

<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/357697/image-preview)
</Background>

<Accordion title="The AI feature isn't working or giving poor results—what should I do?" defaultOpen>
If the AI responses are slow, low quality, or not working at all, the first thing to check is which model you're using.  
The AI model you choose has a big impact on how well Apidog’s AI features perform.  

We recommend using the latest, high-performance models (such as GPT-4.1-mini) from each provider.
</Accordion>

<Accordion title="Can I configure an AI provider and API key at the personal level to use AI features just for myself?" defaultOpen={true}>
Currently, individual configuration of AI features is not supported. All AI settings must be configured at the team or organization level.
</Accordion>

<Accordion title="My favorite AI provider isn't listed. Can I still use it?" defaultOpen={true}>
Yes! If your preferred provider or model isn’t in the built-in list, you can still use it by choosing **Custom API Configuration**.  
This lets you manually add the provider and model you want.
</Accordion>

<Accordion title="Is there a way to monitor model usage? Can I see how much each user has used?" defaultOpen={true}>
Apidog doesn’t currently support model usage tracking. To view usage data, please refer to usage stats provided by the model provider.
</Accordion>

<Accordion title="What AI features are coming next? Where can I send feedback or bug reports?" defaultOpen={true}>
For feedback or suggestions related to Apidog’s AI features, please join our [Discord](https://discord.com/invite/ZBxrzyXfbJ) or [Slack](https://join.slack.com/t/apidogcommunity/shared_invite/zt-2neie4nh2-4_zhufuNBmCq4EtI6fZUwA) community and share your thoughts there. 
We appreciate every user who helps us improve and refine these features.
</Accordion>