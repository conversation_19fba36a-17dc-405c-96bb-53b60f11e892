# A Prompt for Smoother <PERSON> Code Onboarding: Building a Documentation Foundation

If you've ever tried to introduce a new AI tool into your team's workflow, you know the pain: onboarding friction, unclear documentation rules, and a review process that feels like it was designed to test your patience. After spending time with Cursor, I found myself using Claude Code more and more—its output was simply too good to ignore. But getting it up and running in a real project? That's where the real challenge began.

This article shares a practical prompt and workflow that dramatically lowers the barrier to adopting Claude Code, making documentation updates and knowledge sharing a breeze for your whole team.

---

## The Real-World Friction of AI Tool Adoption

When I first heard that <PERSON> could "intuitively understand your codebase and supercharge development," I was excited. But the reality? The biggest hurdles weren't about learning the tool—they were about integrating it into our actual workflow. Here's what tripped us up:

1. **Unclear File Structure**
   - New team members often ask, "What's in the `docs/` folder again?"
2. **Vague Update Rules**
   - Is CI output supposed to go in the README, or in `rules/troubleshooting.md`? The decision fatigue is real.
3. **Heavy Review Process**
   - Pull requests, reviewers, documentation teams… sometimes it takes longer to review a doc than to write it.

As engineers, it's our job to turn these pain points into concrete, repeatable systems.

---

## The Solution: An Initial Setup Prompt for Documentation Automation

To tackle these issues, I created a single "initial setup prompt" for Claude Code. The result? It dramatically lowered the onboarding barrier and encouraged more developers to actually try the tool. Here's the core of the prompt and workflow:

### Step 1: Explore Existing Documentation
- Scan all `.md` files in `.cursor/rules/`, `docs/`, and the project root (like `README.md`, `CONTRIBUTING.md`).
- List each document and briefly describe its purpose.

### Step 2: Update `CLAUDE.md` with Automation Rules
- Add a section describing the automated documentation update system.
- List key reference docs for new contributors.
- Define clear update rules:
  - When to propose updates (e.g., after solving an error, discovering a new pattern, updating an API, etc.)
  - How to format proposals (situation, content, candidate files, reasons)
  - Approval process (user selects file, previews changes, confirms update)
- Emphasize constraints:
  - Never update files without user approval
  - Only add content (no deletions or overwrites)
  - Never record secrets or sensitive info
  - Follow project style guides
- Suggest splitting large docs (over 100 lines) into separate files for maintainability.

### Step 3: Propose Missing Documentation
- Analyze the current structure and suggest new docs if needed, such as:
  - `patterns.md` for best practices
  - `troubleshooting.md` for error solutions
  - `dependencies.md` for library usage
  - `remote-integration.md` for Git/CI/CD workflows
- Ask the user which files to create, and generate initial templates for them.

### Step 4: Confirm Setup and Log the Process
- After setup, display a summary of what was configured and which docs were created or updated.
- Optionally, run a test to simulate the update proposal flow.
- Record the setup in a `setup-log.md` file, including date, actions taken, and any notes.

---

## Why This Works: Reducing Friction and Boosting Adoption

Before this prompt, new team members would often ignore documentation for weeks, unsure where to start. With the prompt, a single `claude init`-style command gets everything set up in minutes. The AI can then propose doc updates, generate pull requests, and help the team build a habit of continuous documentation improvement.

**Results:**
- Number of active contributors increased from 4 to 18 in one week
- Documentation updates nearly tripled
- Review queue times dropped by 30%

---

## Lessons Learned: Prompts as Operational Tools

- Think of prompts as interactive UIs, not just specs. The best prompts guide users through the process, reducing the need for manual reading or guesswork.
- Guardrails matter: by enforcing "add-only" updates and requiring user approval, you prevent accidental data loss and build trust.
- The real power of AI isn't just in generating code or docs—it's in creating systems that make good habits easy and scalable for the whole team.

---

## Final Thoughts

Documentation isn't "done" when you write it—it's only valuable when updating it becomes a habit. The true value of AI in engineering is in building systems that make knowledge sharing second nature. Even a rough initial prompt can be the difference between a team that ignores docs and one that builds a real knowledge base.

If you're struggling to get your team to adopt new tools or keep docs up to date, try this approach. Sometimes, all it takes is a little automation and a well-designed prompt to turn "I'll do it later" into "Let's do it now." 