# The 5 principles of API-first development

Before we talk about API-first development, we need to understand what an API is.

> *An application programming interface (API) is a piece of technology that allows two applications to exchange data and functionality.*

Say you have an ecommerce website and want to integrate Algolia’s search functionality. Our search API lets your website and our hosted search engine exchange data and functionality. When a user types a query, your website sends a request to our servers and our servers send the response back — all via the API. This API request-response exchange is how developers can deliver outstanding search and discovery experiences *without* building the technology from scratch.

APIs are all around you — even if you don’t see them. Google shares its map, navigation, and local data via its Maps Platform APIs. OpenWeatherMap offers hyperlocal minute-by-minute rain data to applications and websites. Facebook allows mobile developers to integrate social connections and profile information. The list goes on.

That’s an API, but what is an **API-first** approach to development?

> *API-first is an approach to software design that centers on the API. It should be possible to perform every action via the scripting language and every piece of functionality should be available for other systems to leverage.*

Again, if that sounds simple, it’s because it is. However, problems arise because the definition gets blurred. Often, when developers say they’re API-first, they only mean that APIs are *important*, but not *foundational*.

## The core principles of API-first development

With so many developers using the term API-first incorrectly (or perhaps partially correctly), it can be difficult to know which products are genuinely API-first and which ones aren’t.

It’s because of this murky definition that we’ve collected five core principles of an API-first development approach. 

### 1. Your API is a product

Publishing an API is easy. What’s difficult is preparing it for public consumption. That’s the difference between merely creating APIs and treating them as products. An API-first approach requires you to think about how developers will interact with your API, how you’ll educate them on its functionality, how you’ll maintain it over time, which tools to use to build the API, and how you’ll adhere to standards of compatibility, security, and simplicity.

When a company builds a product, it must meet industry standards. For APIs, this means practicing foundational software design and development cycles that deliver a quality software product. Product development includes upfront design and specifications, peer programming, technology choices and programming languages support that match the API’s goals, the decision between cloud and on-site, testing, and user research.

### 2. Foundational design, not ad hoc retrofit

API-first design requires developers to think about APIs from the very beginning. However, few people ever get a clean slate to work from. Often, software companies will try to adopt an API-first approach *after* building a product. Typically, this means adding APIs on top of an existing platform. This approach isn’t without merit. It can increase automation and improve underlying functionality. But it isn’t an API-first approach.

Simply put, API-first development treats APIs as the foundation. Instead of pre-built or opinionated software solutions or experiences, it ensures that all of a platform’s functionality is accessible to you through the APIs.

### 3. Team collaboration and impact

When dealing with technical subjects, it’s easy to focus solely on technology. But API-first development is more than just technical standards. It’s a way of working and collaborating. Rather than just being concerned with the specifics of each API, an API-first approach is more about looking at your teams and operations that surround those APIs.

Achieving an API-first environment requires strong leadership from a product manager who is both knowledgeable about APIs and equipped with sufficient technical skills. It requires skilled developers, too. Although developers are great at delivering exceptional end-user experiences, creating a delightful *engineering* (or developer) experience (DX) is a different skill set altogether. You need a customer success function to help with implementation and support to field ongoing queries and user feedback.

### 4. API-first supports microservices

The development world is undergoing a huge drive toward reusability. Just as automakers use generic parts, companies are moving away from all-in-one platforms and toward reusable components.

For example, instead of buying an all-in-one ecommerce platform, online retailers are procuring all the best reusable components (inventory management, checkout, payment gateway, order fulfillment, and so on) and combining them to create enterprise products perfectly tailored to their circumstances.

Because your components need to communicate with one another, you can’t have microservices without APIs. Building with an API-first approach ensures that other developers can easily integrate your APIs into theirs with microservice architecture and applications.

### 5. The API contract

We’ve mentioned that an API-first approach is driven by reusability. By creating open and accessible APIs, companies can reuse, redeploy, and share functionality easily. But that’s easier said than done. One way to promote reusability is through consistent API description language. Specifically, this creates a contract dictating how an API is supposed to behave. Establishing a contract involves spending more time thinking about the design of an API and often involves additional planning and collaboration.
