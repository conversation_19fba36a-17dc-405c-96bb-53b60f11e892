Apidog supports OpenAPI specifications from version 2.28 onward. Visit Apidog's [OpenAPI documentation](https://openapi.apidog.io/) for more details.

This tutorial guides you through obtaining an OpenAPI access token for Apidog, which enables you to utilize Apidog's OpenAPI services efficiently.

## Create a New Token

To generate a new token, follow these steps:

1. Hover over your avatar on the top right and navigate through "Account Settings" → "API Access Token".
2. Click on "Create a new personal token".

   ![](https://assets.apidog.com/uploads/help/2024/04/11/6635b66cde699a3643de8939a0a2cd2d.png)

:::caution
This token grants the same functionality and access rights as your account across all teams and projects within Apidog.
:::

3. Enter a name for your token and choose a validity period.
4. Click **"Save and generate token"** to create the token. It will be displayed only once at this moment, so be sure to copy and save it immediately. Once you navigate away from this page, the token will not be visible again.

   ![](https://assets.apidog.com/uploads/help/2024/04/11/e2887ac09c9c7e3ee14fdbd64143f447.png)

:::tip[]
Modifying the name or the expiration date of the token does not regenerate a new token. If you misplace it, you need to create a new token by clicking "New".
:::

## Use the Token

To utilize your new token with the OpenAPI, prepare the following required request parameters:

1. The token (authorization) as generated in the "Project Settings".
2. The Project ID from the project you intend to operate on, available under "Project Settings" → "Basic Settings".

   ![](https://assets.apidog.com/uploads/help/2024/04/11/e84c1a097c4e42748b870a41fd9da748.png)

Next, clone the [Apidog OpenAPI project](https://openapi.apidog.io/) and set up your environment:

![](https://assets.apidog.com/uploads/help/2024/04/11/b60796e8375e5faa6761ecdc7908b2f0.png)

1. Click on the "Environment Management" button in the top right corner.
2. Input the required parameters and save them.

   ![](https://assets.apidog.com/uploads/help/2024/04/11/dee01fd497a7de7da70d775ef9f05c84.png)

Now, you can select the desired OpenAPI service, fill in the request parameters, and execute your API calls:

![](https://assets.apidog.com/uploads/help/2024/04/11/025f882d40c14f2ef6835744c9513633.png)

By following these steps, you'll be able to seamlessly integrate and utilize Apidog's OpenAPI functionalities.