Sometimes, you need to return mock data that corresponds to specific request parameters or maintains logical relationships between different properties. 

:::highlight purple
**Example**
- When requesting user info with an ID of 1001, the response includes this ID, and its value should be 1001.
- Or, when mock data contains related properties like start and end times, the end time should be later than the start time.
:::


These logical relationships can be implemented using mock scripts.

## How it works

The basic implementation principle of mock scripts is as follows:
1. Use Smart mock or other mock features to generate an initial mock response, which may not meet all the required constraints.
2. Use mock scripts to access the `$$.mockResponse` object and/or `$$.mockRequest` object.
3. Retrieve data from mockResponse and mockRequest, and write JavaScript to implement the desired logic.
4. Use the `$$.mockResponse.setBody` method to rewrite the mockResponse with your modified data.
5. The mock engine returns the final mockResponse.


:::caution[]
The Mock Script only works with smart mock; it does not apply to mock expectations or response examples.
:::

## Using Mock Scripts

<Steps>
  <Step>
    Switch to the Mock tab, where the Mock Script section is located at the bottom.
<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/352097/image-preview)
</Background>
  </Step>
  <Step>
    Turn on the script toggle.
  </Step>
  <Step>
    Write your script and save it.
<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/343631/image-preview)
</Background>
  </Step>
</Steps>



## Script reference

### Mock script example
```js
// Get mock data from Smart Mock
var responseJson = $$.mockResponse.json();
// Modify the paged data from responseJson
// Set page as the page in request parameter
responseJson.page = $$.mockRequest.getParam("page");
// Set total as 120
responseJson.total = 120;
// Write the modified json into $$.mockResponse
$$.mockResponse.setBody(responseJson);
```
This script does the following:
1. It starts with an initial mock response (generated automatically).
2. It then modifies this response by:
   - Setting the `page` value to match what was requested.
   - Setting a fixed `total` value.
3. Finally, it updates the mock response with these changes.

### $$.mockRequest object

The `$$.mockRequest` object represents the incoming request in your mock script. It's similar to Postman's `pm.request` object but with some additional features.

1. **getParam(key: string) method**: Retrieves parameters from the request, regardless of their location (query string, body, etc.).
2. **Access to cookies**: Allows you to retrieve cookie values from the request.

#### Usage examples

```javascript
// Get parameters of request
var userId = $$.mockRequest.getParam("userId");

// Get headers of request
var headerUserId = $$.mockRequest.headers.get("userId");

// Get cookies of request
var cookieUserId = $$.mockRequest.cookies.get("userId");

// Get the JSON data in the request body
var requestJsonData = $$.mockRequest.body.toJSON();

// Get the string data in the request body
var requestStringData = $$.mockRequest.body.toString();

// Get the form-data in the request body
var formDataUserId = $$.mockRequest.formdata.get("userId");

// Get the urlencoded data in the request body
var urlencodedUserId = $$.mockRequest.urlencoded.get("userId");
```

### $$.mockResponse object

The `$$.mockResponse` object represents the response that will be sent back. It's similar to Postman's `pm.response` object but with additional methods for greater control over the mock response.

1. **setBody(body: any) method**: Sets the response body.
2. **setCode(code: number) method**: Sets the HTTP status code of the response.
3. **setDelay(duration: number) method**: Adds a delay to the mock response, simulating network latency.

#### Usage examples

```javascript
// Get the auto-generated response
var responseJsonData = $$.mockResponse.json();

// Set body for response with JSON
$$.mockResponse.setBody({ id: "1", name: "Apple" });

// Set body for response with string
$$.mockResponse.setBody("Hello World!");

// Set the HTTP status code returned
$$.mockResponse.setCode(200);

// Set the Mock response delay in milliseconds
$$.mockResponse.setDelay(3000);

// Get HTTP status code
var statusCode = $$.mockResponse.code;

// Get HTTP header
var myHeader = $$.mockResponse.headers.get("X-My-Header");

// Set HTTP header
$$.mockResponse.headers.set("X-My-Header", "hello");
```

## FAQ

**Q: How to use Apidog variables in mock scripts?**
A: Mock scripts cannot use variables. This is because variables are a feature of the Apidog client, but the mock engine is not part of the client, so it cannot reference variables in the client.

**Q: How to use log statements in mock scripts to print to the console?**
A: Mock scripts do not provide logging functionality. This is because the console is a feature of the Apidog client, but the mock engine is not part of the client, so it cannot output logs to the client.

**Q: Why can't I use pm object in mock scripts?**
A: Mock scripts are executed on the mock server, while pre and post scripts are executed on the Apidog client that sends the request. These are completely different environments, so the script syntax cannot be shared between them.

**Q: If both Mock Expectations and Mock Scripts are used at the same time, will the Mock Script take effect?**
A: When a Mock Expectation is matched, the Mock Script will not take effect. The Mock Script only works with Smart Mock; it does not apply to Mock Expectations or response examples.
