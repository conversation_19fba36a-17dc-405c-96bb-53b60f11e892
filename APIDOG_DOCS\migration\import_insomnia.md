Import from Insomnia
Insomnia is an open source desktop application for API design, debugging and testing provided by <PERSON>, a veteran API management provider. Apidog supports importing API data from the Insomnia platform.

Exporting Data from Insomnia
If you are not logged in to Insomnia, click the "Scratch Pad" at the top of the left sidebar, then lightly tap the "Export" button.

Not logged Insomnia
If you logged into Insomnia, click the "Collection" at the top of the left sidebar, then gently tap the "Export" button.

Logged Insomnia
Select the API to export.



Finally specify exporting in Insomnia V4 format.



Importing Insomnia Data into Apidog
Open the 'Settings' panel in Apidog, switch to the 'Manual' tab, and select the Insomnia to import.


Retain Full URL
If you need to retain the full URL in the path, please turn on the "Add Pre Url"(Retain prefix URL in API path) switch.


Import Environment
If you need to import the environment from Insomnia, please specify the specific environment in Apidog. After specifying, Insomnia's environment will be imported and used as Apidog's environment variable, instead of prefix URL.


