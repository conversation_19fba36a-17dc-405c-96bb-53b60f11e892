Microsoft Entra ID
To configure SCIM with Microsoft Entra ID (formerly Azure Active Directory) for your organization, you must have administrator access for both Microsoft Entra ID and Apidog.

Preparation
Before configuring settings in the Microsoft Entra ID dashboard, navigate to the SAML SSO page within your Apidog organization settings. Click on the Generate a SCIM token button, and keep this page open for the next steps.
scim-setting-preparation.png

Modify Claim of SSO
To support SCIM provisioning, you should modify Unique User Identifier claim of SSO:

Open your Microsoft Entra ID management portal in a browser.

Go to Enterprise applications and open your desired application.

On the application's Overview page, click Set up single sign on.

Set up the Unique User Identifier (Name identifier) claim as follows:

Name identifier format to Persistent.

Source attribute to user.objectid.

modify-claim-sso-step-1.png
modify-claim-sso-step-2.png

Configure SCIM Provisioning
To configure your SCIM provisioning, follow these steps:

Open your Microsoft Entra ID management portal in a browser.

Go to Enterprise applications and open your desired application.

On the application's Overview page, click Provision User Accounts.

configure-scim-provisioning-1.png

Click Get started.

configure-scim-provisioning-2.png

Select Automatic for Provisioning Mode, then copy and fill in the information from the Apidog page, and then click Test. The test results will be displayed in the upper right corner. If there is no problem, save it.

configure-scim-provisioning-3.png

After saving, you can configure mapping. First turn off "Groups Mapping".

configure-scim-provisioning-4.png

Then configure "Users Mapping".

configure-scim-provisioning-5.png

Delete externalId.

configure-scim-provisioning-6.png

Edit the first attribute to have a:

source attribute of objectId

target attribute of externalId

matching precedence of 1

configure-scim-provisioning-7.png

configure-scim-provisioning-8.png

Add a new mapping:

source attribute of userPrincipalName

target attribute of userName

configure-scim-provisioning-9.png

Then delete other items and keep only the following items.

configure-scim-provisioning-10.png

Save, then return to the provisioning homepage and click Start provisioning.

configure-scim-provisioning-11.png

After a while, the provisioning results will be displayed.

configure-scim-provisioning-12.png

Test Your SAML Configuration
Go back to Apidog and you can see the users who have been provisioned.

Once these provisioned members sign in using SSO, their status will change to Active and they will occupy paid seats.

Users in provisioned status do not occupy paid seats.

According to Azure's rules, synchronization occurs approximately every 40 minutes.

test-saml-configuration.png

