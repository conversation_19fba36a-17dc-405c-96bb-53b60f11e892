.edu Email Not Available in Bangladesh – Can I Use My University-Issued Email Instead?

Hi Cursor Team,

I’m from Bangladesh, and at our universities, we typically do not receive .edu domain emails. Instead, my university has provided me with an institutional email address in the format: <EMAIL>.

I understand that many academic services and student verifications often rely on .edu emails, but in our region, .ac.bd is the official domain for accredited academic institutions.

I wanted to check if it’s possible to use my university-provided @uttara.ac.bd email for verification or student-related benefits on Cursor. It is a legitimate academic email, and I can provide any necessary proof if needed.

Looking forward to your guidance on this!

Best regards,
<PERSON><PERSON>was Rubel
Student – Uttara University

When I first registered, an error message appeared. After trying again, why is there a limit?

We are unable to verify you at this time. lf you believe you received this in
error, please contact SheerlD support

Note: Please do not request specific countries or schools to be added - we are working on rolling this out to more students soon, but requesting your school will not make any difference to its eligibility

Multiple Country Not Within the List For Student Status Verification
When attempting to verify student status for 1 Year Free Cursor Pro Plan, there are currently multiple countries that are not within the selectable list of countries.

Some countries not included are:

Singapore
Vietnam

study in european universiti,when I tried to do students verification has been requested email with .edu, in my university we have no this type of e-mail.


Cursor Student Verification Error:
1. We are unable to verify you at this time. lf you believe you received this in
error, please contact SheerlD support
2.  We are sorry, the page you requested cannot be found. The URL may be
incorrect or the page you're looking for is no longer available.
3. Verification Limit Exceeded! We're glad you're enthusiastic, but it looks like you've already
redeemed or attempted to redeem this offer.
4. You must be logged in to verify. Log in to your account then try again.   