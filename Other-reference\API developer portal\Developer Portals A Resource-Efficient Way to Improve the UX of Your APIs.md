# Developer Portals: A Resource-Efficient Way to Improve the UX of Your APIs

Navigating a chaotic tech ecosystem can take away a ton of time from your team. Rather than delivering results, they may be looking for information. And that’s true even for product managers.

According to [Stack Overflow’s 2023 survey](https://survey.stackoverflow.co/2023/#section-developer-experience-developer-experience-processes-tools-and-programs-within-an-organization), 49% of managers and developers spend more than 30 minutes a day answering questions. 

- “Is there a sandbox environment for testing?”
- “What are the rate limits for this API?”
- “Where can I find example code or SDKs?”
- “Can someone tell me what this error message means…again?”

All of these valid questions, but equally frustrating when you need to stop wasting time and ensure your project is on track.

Developer portals can solve this problem. Besides hosting API documentation and a catalog of your services, they have the added (and much-needed) benefit of streamlining your developers’ experience. Despite this, you may still get some pushback from stakeholders.

Which is why we wrote this article. 😉

Here, you’ll learn how to design a truly usable developer portal and how to prove its value to stakeholders.

## **What is a developer portal?**

Developer portals are a central hub of resources to access, understand, and test APIs. They provide quick access to API documentation, guides and tutorials, support forums, tools for testing, and any other resource that makes your developer’s life easier—at least when dealing with APIs.

The main benefits of a developer portal are:

- Improving developer onboarding
- Facilitating API integration
- Providing comprehensive documentation
- Offering support and troubleshooting resources
- Fostering collaboration and knowledge-sharing

Having a developer portal is better than being buried in email threads and scattered documentation.

## **Who uses developer portals?**

To understand who uses developer portals, it’s worth clarifying that there are two types: internal and external.

**Internal developer portals** are designed for the specific needs of an organization. These portals give access to internal APIs, documentation, and tools while improving collaboration between internal stakeholders.

**External developer portals** offer public access to APIs and resources for external developers, partners, and customers. This facilitates third-party integrations around your organization’s services.

Here’s how each audience—internal and external—uses developer portals in their day-to-day work.

### **Developers**

Internal developers often spend a lot of time searching for information or tools. A developer portal is a centralized, accessible hub of information. Thanks to community features, they can ask questions to other developers besides that one coworker who helped them the first few days of onboarding.

In contrast, external developers have a more gradual learning curve. The use of developer portals allows them to access code samples and public resources that make development and deployment easier.

### **Technical writers**

Internal technical writers have to maintain up-to-date documentation. But this can be a challenge when iterative development—and the multiple changes that come with it—is so important. However, access to collaboration and version control tools in developer portals solves this issue. 

When it comes to external technical writers, a developer portal helps them create guides and tutorials that help external users integrate their services with your API.  

### **Product managers**

Internal product managers oversee product development and its alignment with business goals. A developer portal gives them the analytic and monitoring tools to keep everything on track.

External product managers use your developer portal to access your API documentation and test your API to propose new solutions for their company.

### **Partners and businesses (External)**

This audience includes any other external stakeholders. Having a developer portal provides them with all the tools and resources they need to smooth out the integration of your services. It also promotes collaboration by offering clear communication channels and support.

## **Why do you need a developer portal?**

Before deciding that a developer portal is the solution you’ve been looking for, you should survey your developers to find out what they need. If you’re going to make this investment and need approval from stakeholders, it’s better to have a series of requirements collected—rather than saying that “someone in management thought dev portals were a good idea.”

Moreover, if you’re getting pushback from stakeholders, it’s important to address their concerns directly. Here are some common concerns (and how you can counter them).

### **Concern 1: Cost / ROI**

Stakeholders may think the costs of developing and maintaining a developer portal outweigh its benefits. After all, it’s not always easy to prove an increase in productivity and engagement.

However, a developer portal can lead to significant long-term cost savings. According to Daan Stolk, co-founder of Alphadoc.io, [50% of support tickets](https://devops.com/can-improving-developer-experience-actually-reduce-costs/) at one of his previous companies could have been solved with the proper resources in place. 

Besides reducing onboarding costs, resources streamlined in a centralized hub means developers are going to spend less time trying to get information and spend more time well…developing. Meaning the time-to-market for new features and integrations will be faster.

### **Concern 2: Resource allocation**

Some may argue that building or maintaining developer portals takes away resources from other critical projects. 

The thing is, when you invest in a developer portal that provides a cohesive workflow, access to resources doesn’t have to be subject to different tools and microservices. Your developer’s brain power doesn’t have to be spent on constant context switching or idle time. Instead, they have time to focus on higher-level projects with more cognitively demanding tasks.

### **Concern 3: Scope creep**

This must be the most tedious reason why stakeholders don’t like the idea of something new. 

“We should have a developer portal that has all our resources in one place, plus a forum, plus our API documentation, plus…”

You can almost feel the collective reluctance to undertake yet another cumbersome project. Endless meetings, coordination, and troubleshooting is enough to make anyone’s head spin.

However, modern developer portals (like ReadMe ✨) can provide a full suite of features, with minimal DIY work on your end. From data synchronization and API management to customization and security, these portals are designed to integrate with existing systems and workflows. Additionally, great customer service and guidance throughout the process guarantees a smooth transition.

## **Building vs buying a developer portal**

Now, it all comes to this:  should you build or buy a developer portal?

**Building a developer portal** involves your team developing a solution…from scratch. The designing, coding, integration of API documentation, support, and interactive tools is up to you. However, you end up with a custom solution for your organization’s needs.

**Buying a developer portal** means getting a pre-built solution from a vendor. These platforms have ready-to-use features that include your API documentation, interactive guides, and support tools on a subscription or licensing basis.

To decide which option is better for you, let’s go over the pros ✅ and cons ❌ of building vs buying developer portals considering the following key factors:

| **Factor**          | **Building a Developer Portal**                                                                                | **Buying a Developer Portal**                                                                      |
| ------------------- | -------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------- |
| Budget              | ✅ No recurring subscription fees.<br>❌ Higher initial development costs, and ongoing maintenance expenses.     | ✅ Lower initial costs, subscription, or license fees.<br>❌ Recurring fees can add up over time.    |
| Time to market      | ✅ Up to you and dependent on your resources.<br>❌ Longer development time.                                     | ✅ Quick deployment and availability.<br>❌ N/A.                                                     |
| Customization       | ✅ Full control over design and functionality.<br>❌ Customization can be time-consuming and costly.             | ✅ Standard features are readily available.<br>❌ Limited ability to customize to specific needs.    |
| Support             | ✅ Custom support (almost immediate).<br>❌ Requires internal resources and expertise for support.               | ✅ Vendor support included, access to expertise.<br>❌ Dependency on the vendor for updates/support. |
| Integration         | ✅ Custom integration with existing systems.<br>❌ Potentially complex and resource-intensive.                   | ✅ Designed for easy integration.<br>❌ Integration with legacy systems can be challenging.          |
| Scalability<br><br> | ✅ Customizable to scale as needed.<br>❌ Scalability depends on the initial architecture and ongoing resources. | ✅ Typically designed to scale easily.<br>❌ May incur additional costs for scaling.                 |
| Ownership<br><br>   | ✅ Complete ownership of the codebase and intellectual property.<br>❌ All responsibilities on your team.        | ✅ N/A.<br>❌ Reliance on vendor, no ownership of codebase.                                          |

A rule of thumb.👍

If you’re a large company that needs to set standards for developers and has lots of resources (e.g. Google, Amazon, or Microsoft), building a developer portal may be worth the effort. On the other hand,  small to medium-sized companies that want a cost-effective and less time-consuming solution should buy a developer portal.

## **How to build a developer portal**

Whether you’re building (or buying) a developer portal, there are key features and best practices to keep in mind.

Let’s start with a granular step-by-step on how to build a developer portal.

### **#1. Plan your developer portal**

This is where you ask all the important questions and set the primary goals of your developer portal. 

For example, who’s your target audience? The first thought that comes to mind is developers. But are these developers internal or external? And what are their needs?

Here’s a list of questions to help you steer in the right direction:

- What’s your target audience?
- Are you building an internal or external developer portal?
- What key functionalities or resources will developers need?
- What integrations with other systems or platforms are necessary?
- What’s the timeline for launching the portal?
- What support and feedback mechanisms will you implement?
- What metrics will you track to measure success?
- How will you structure the documentation?

Make sure the answers to these questions are crystal clear. You don’t want to end up with a portal no one uses.

### **#2. Set up your infrastructure**

To choose your technology stack, remember to ask yourself: are you building from scratch or buying a portal?

**If you’re building a developer portal** where you have ownership and control over the design, functionality, and maintenance, [Jekyll](https://jekyllrb.com/), [Gatsby](https://www.gatsbyjs.com/), and [Backstage](https://backstage.io/) are good options to start.

**If you’re buying a developer portal** with built-in features that helps you with quick development and easier maintenance, [ReadMe](https://readme.com/) is your best option.

### **#3. Design the developer experience (DX)**

Think of developers as customers and think of this project as a product. From there, you can start asking the right questions to gather requirements based on facts, rather than from random developer suggestions.

Still, you have to be able to answer questions beyond what they need and visualize the developer’s journey. For example, how are developers and users signing into your portal? It could be with a password or company email. Or you could allow for two-factor authentication…but that could become tedious when developers want to check something quickly.

According to the position of your developer or stakeholder, you could create a predefined starting point. For example, you could create a list of the most accessed APIs and pin it to the start. Going a step further, you could allow them to choose the APIs or reference pages they want to see pinned at the start. As projects and priorities change, developers can find what they need faster.  

### **#4. Create and iterate your developer portal**

Ever heard of the phrase “consistency over intensity”? It also applies here. Don’t expect to have a developer portal up and running in less than three months (unless you’re buying). 

Start with small but significant increments and present them to stakeholders to gather feedback. Implement that feedback and repeat.

Some best practices you should have in mind for gathering user feedback are:

- **Making it easy for users to provide feedback:** Implement tools within the portal so users can submit feedback directly, like feedback forms, comment sections, or satisfaction ratings
- **Implementing usability testing:** Test your portal with real users. See how they interact with the portal and take note of the modules in which they find difficulties or confusion
- **Asking the right questions to get the right data:** Ask open-ended questions for qualitative data (e.g., “What do you find most challenging about using this feature?”). Or ask closed-ended questions for measurable data (e.g., “On a scale of 1-5, how satisfied are you with this feature?”)
- **Doing surveys, questionnaires, or user interviews:** Conduct these regularly to understand your users’ needs. Ask about specific features and how satisfied are your users with them

It’s worth mentioning that when gathering feedback, you should prioritize not just understanding what users are saying but also comprehending the emotions, motivations, and frustrations behind the feedback.

### **#5. Launch and promote**

If you’re building an external developer portal, promote it through various channels. Social media, developer communities, or newsletters are a good way to start. But if you’re building an internal developer portal, a company email or announcing it during the stand-up meeting is enough.

Just remember to keep making your portal valuable to users by providing continuous updates and improving based on feedback. 

Before we wrap up this article, let’s go over the best practices and essential features for creating a high-quality developer portal.

### **Developer portal best practices**

This part is all about creating standards. You can’t get GitHub or Backstage, throw it over to your developers and hope for the best.

A good developer portal should have:

- Clear workflows and onboarding for developers
- Cohesive design, easy navigation, and a responsive layout
- Up-to-date documentation and resources
- Interactive elements to improve learning and integration
- A menu of options for additional resources (community forums, FAQs, feedback forms, etc.)

### **Developer portal features**

The features needed for creating a great developer portal are mostly about quality and observability. Think API documentation, onboarding of new developers, risk management, and integration.

Here are the basic features you need:

- API documentation with endpoints, parameters, request/response formats, and error codes
- Step-by-step guides and tutorials to help developers get up to speed
- Interactive API explorers so developers can test API calls
- Monitoring and analytic tools to track API usage, performance, and get insights
- Access control and user management to set usage limits

It’s important to note that with these last two features in place, you can get insights into usage patterns. Meaning, you could offer personalized documentation and resources based on the user’s role and access level.

## **Logging out of developer portals**

A developer portal is a must-have, not a nice-to-have.

Many companies make the mistake of thinking that using a basic wiki or shared document is enough for their developer teams. Some even think that API documentation is the only resource needed for onboarding new developers.

But that’s not happening to you.

Developer portals are a central hub where all these tools come together and are at your developers’ fingertips. If you work in a team that needs API integration, collaboration, comprehensive documentation, and robust security, then you definitely need one.
