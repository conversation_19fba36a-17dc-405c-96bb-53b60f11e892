# What is API Stubbing? How is it Different from Mocking?

**The short definition:** API stubbing is a technique used in software development and testing, where a dummy or placeholder implementation of API responses is used as a temporary substitute for the actual API. 

‍

Stubbed responses are one step above dummy data for testing or development, and one step below a [mock API](https://www.wiremock.io/glossary/mock-api). Stubs should maintain the exact number of parameters and produce dummy results that are of the correct type to ensures that the testing process is both smooth and accurate, and provide developers with a reliable method for evaluating their code.

‍

**What’s a stub?** More generally, stubs are controllable replacements for existing dependencies within a system, helping developers test their code without directly interacting with the actual dependency. As one commenter on [Stack Overflow](https://stackoverflow.com/questions/9777822/what-does-to-stub-mean-in-programming) mentions, they act as a programmer's "Lorem Ipsum" – allowing developers to write tests and run examples even before the function, service, or component is complete (and the same applies when a 3rd party service isn’t available).

‍

## Stubbing vs Mocking

Stubbing and mocking are used to isolate software components during testing. However, there are some differences between the two techniques: Primarily, stubs are focused on providing predefined responses to specific input parameters, while mocks are geared towards validating the interactions or behavior of components.

‍

In other words, stubbing is mainly used to replicate the behavior of an API in a controlled manner with predefined responses, which allows the developer to focus on testing the integration and functionality of the code without worrying about external dependencies. On the other hand, mocking involves creating a mocked version of a dependency and asserting that the component being tested interacts with this mock as expected, often concentrating on the expected sequence of calls, data manipulation, or side effects. While both stubbing and mocking help streamline the testing process and ensure accurate results, the choice between them depends on the specific goals and requirements of the test scenario.

## API Stubbing in WireMock

[WireMock](https://wiremock.org/) is an open source framework designed for creating HTTP-based API stubs and mocks. It provides a simple way to define stubbed responses with various matching criteria. Using WireMock, you can create stubs for your API, control their behavior, and use them for testing. You can also use [WireMock Cloud](https://www.wiremock.io/) to define your stubs in a GUI and benefit from other features available only in the SaaS version.

‍

Here’s a basic stubbing example using WireMock in Java:

‍

*@Test*

*public void exactUrlOnly() {*

    *stubFor(get(urlEqualTo("/some/thing"))*

            *.willReturn(aResponse()*

                *.withHeader("Content-Type", "text/plain")*

                *.withBody("Hello world!")));*

‍

    *assertThat(testClient.get("/some/thing").statusCode(), is(200));*

    *assertThat(testClient.get("/some/thing/else").statusCode(), is(404));*

*}*

‍

In this example, we set up a stub for the relative URL "/some/thing" that, when matched, returns a response with a status of 200, "Hello world!" as the body, and a "Content-Type" header set to "text/plain". You can also create and configure stubs using JSON:

‍

*{*

    *"request": {*

        *"method": "GET",*

        *"url": "/some/thing"*

    *},*

    *"response": {*

        *"status": 200,*

        *"body": "Hello world!",*

        *"headers": {*

            *"Content-Type": "text/plain"*

        *}*

    *}*

*}*

‍

API stubbing in WireMock supports various request matching options, response templating, fault simulation, stateful behaviors, and proxying. Stubbing can also be used with different HTTP methods, including GET, POST, PUT, DELETE, HEAD, TRACE, and OPTIONS. Moreover, you can set the priority for your stubs to control the order in which they are executed when multiple stubs match the same request.
