# How to Use the dbt MCP Server

Are you ready to supercharge your AI workflows with structured data? Let’s dive into the **dbt MCP server**, a game-changer for connecting your dbt projects to AI systems. In this tutorial, I’ll walk you through what the **dbt MCP server** is, why it’s awesome, and how to set it up using the updated installation steps. Buckle up for a fun, conversational ride through the world of data and AI!

## What’s dbt All About?

If you’re new to [dbt](https://docs.getdbt.com/) (data build tool), it’s like the Swiss Army knife for data teams. It’s an open-source framework that lets you transform raw data in your data warehouse into clean, reliable datasets for analytics. With dbt, you can:

- Write modular SQL models to shape your data.
- Document your data assets and their relationships.
- Test data quality to keep things trustworthy.
- Track data lineage to see how everything flows.

Think of dbt as the backbone of modern data engineering, making your datasets governed and ready for action.

![dbt dev hub official website](https://assets.apidog.com/blog-next/2025/07/Screenshot-2025-07-03-215654.png)

## Meet the dbt MCP Server

Now, let’s talk about the star of the show: the **[dbt MCP server](https://docs.getdbt.com/blog/introducing-dbt-mcp-server#getting-started)**. This experimental, open-source server is like a bridge that connects your dbt project to AI systems. [MCP](https://www.anthropic.com/news/model-context-protocol) stands for **Model Context Protocol**, a fancy way of saying it’s a standard for AI tools (like Claude Desktop or Cursor) to tap into your dbt project’s metadata, documentation, and semantic layer.

With the **dbt MCP server**, AI agents and business users can explore your data, run queries, and even execute dbt commands—all through natural language or code. It’s like giving your AI a VIP pass to your data warehouse!

![model context protocal](https://assets.apidog.com/blog-next/2025/07/Screenshot-2025-07-03-220131.png)

### Why You’ll Love the dbt MCP Server

Here’s what makes the **dbt MCP server** so cool:

- **Discover Your Data:** AI and users can browse your dbt models, check their structure, and understand how they’re connected.
- **Query with Confidence:** Use the dbt Semantic Layer for consistent metrics or run custom SQL queries for flexibility.
- **Automate Like a Pro:** Run dbt commands (like `run`, `test`, or `build`) directly from AI workflows to keep your pipelines humming.

## How the dbt MCP Server Powers AI Workflows

The **dbt MCP server** is all about bringing structured, governed data to AI. Here’s how it works its magic:

1. **Universal Data Access:** It uses the Model Context Protocol to share your dbt project’s context—models, metrics, and lineage—with any MCP-enabled AI tool. No custom integrations needed!
2. **Smart Data Discovery:** AI agents can list models, check dependencies, and grab metadata, making it easy to answer questions like “What’s our customer data like?”
3. **Governed Querying:** By tapping into the dbt Semantic Layer, the server ensures AI-generated reports stick to your company’s official metrics, keeping things consistent and trustworthy.
4. **Automation Galore:** AI can trigger dbt commands to run models, test data, or build projects, streamlining your data pipelines.
5. **Safe and Scalable:** Run it locally or in a sandbox, with permissions to keep sensitive data locked down. It’s flexible for both testing and production.

![dbt mcp server architecture](https://assets.apidog.com/blog-next/2025/07/d2.png)

## Installing the dbt MCP Server: Step-by-Step

Ready to get the **dbt MCP server** up and running? Let’s follow the updated installation steps to get you set up smoothly. Don’t worry, I’ll keep it simple and fun!

### Prerequisites

Before we start, make sure you have:

- **Python 3.12+**: The server needs a modern Python environment.
- **uv**: A fast Python package installer and resolver ([installation guide](https://docs.astral.sh/uv/getting-started/installation/)).
- **Task**: A task runner/build tool ([installation guide](https://taskfile.dev/installation/)).
- A **dbt project** with a configured `profiles.yml` file pointing to your data warehouse.
- A dbt Cloud account for cloud-based functionality (optional for dbt CLI usage).

### Step 1: Clone the Repository

First, grab the **dbt MCP server** code from GitHub. Open your terminal and run:

```bash
git clone https://github.com/dbt-labs/dbt-mcp.git
cd dbt-mcp
```

This downloads the source code to your local machine and moves you into the project directory.

### Step 2: Install Dependencies

With `uv` and `Task` installed, set up the required Python packages by running:

```bash
task install
```

This creates a virtual environment and installs all necessary dependencies for the **dbt MCP server**.

### Step 3: Configure Environment Variables

Set up your environment by copying the example configuration file:

```bash
cp .env.example .env
```

Open the `.env` file in your favorite text editor and fill in these key variables:

- **DBT_HOST**: Your dbt Cloud instance hostname (e.g., `cloud.getdbt.com`).
- **DBT_TOKEN**: Your dbt Cloud personal access token or service token.
- **DBT_PROD_ENV_ID**: Your dbt Cloud production environment ID.
- **DBT_DEV_ENV_ID**: (Optional) Your dbt Cloud development environment ID.
- **DBT_USER_ID**: (Optional) Your dbt Cloud user ID.
- **DBT_PROJECT_DIR**: Path to your local dbt project (for dbt CLI usage).
- **DBT_PATH**: Path to your dbt CLI executable (find it with `which dbt`).

You can also enable or disable specific tool groups (e.g., Semantic Layer, Discovery) via these variables. Adjust them based on your needs.

### Step 4: Start the dbt MCP Server

Now, let’s fire it up! From the `dbt-mcp` directory, run:

```bash
task start
```

This launches the **dbt MCP server**, making it available for connections from MCP-compatible clients like Claude Desktop or Cursor.

### Step 5: Connect an MCP-Enabled Client

To connect an MCP client, add this configuration to the client’s config file (replace `<path-to-.env-file>` with the path to your `.env` file):

```json
{
  "mcpServers": {
    "dbt-mcp": {
      "command": "uvx",
      "args": ["--env-file", "<path-to-.env-file>", "dbt-mcp"]
    }
  }
}
```

- **Claude Desktop**: Create a `claude_desktop_config.json` file with the above config. Check logs at `~/Library/Logs/Claude` (Mac) or `%APPDATA%\Claude\logs` (Windows) for debugging.

![using the dbt mcp server in claude](https://assets.apidog.com/blog-next/2025/07/videoframe_32315.png)

- **Cursor**: Follow [Cursor’s MCP docs](https://docs.cursor.com/mcp) to input the config.
- **VS Code**:
1. Open Settings (`Command + ,`) and select the appropriate tab (Workspace or User).
2. For WSL users, use the Remote tab via the Command Palette (`F1`) or Settings editor.
3. Enable “Mcp” under Features → Chat.

![enable mcp in vs code](https://assets.apidog.com/blog-next/2025/07/Screenshot-2025-07-03-220717.png)

4. Click “Edit in settings.json” under “Mcp > Discovery” and add:

```json
{
  "mcp": {
    "inputs": [],
    "servers": {
      "dbt": {
        "command": "uvx",
        "args": ["--env-file", "<path-to-.env-file>", "dbt-mcp"]
      }
    }
  }
}
```

You can manage servers via the Command Palette (`Control + Command + P`) with the “MCP: List Servers” command.

### Troubleshooting Tips

- **uvx Not Found?** If clients can’t find `uvx`, use the full path (find it with `which uvx` on Unix systems) in the JSON config.
- **Connection Issues?** Verify your `.env` variables, especially `DBT_HOST` and `DBT_TOKEN`.
- **WSL Users**: Configure WSL-specific settings in VS Code’s Remote tab, as local User settings may not work.

## Available Tools

The **dbt MCP server** supports powerful tools, including:

- **dbt CLI**: Commands like `build`, `compile`, `docs`, `run`, `test`, and `show` for managing your dbt project.
- **Semantic Layer**: Commands like `list_metrics`, `get_dimensions`, and `query_metrics` for working with governed metrics.
- **Discovery**: Commands like `get_all_models` and `get_model_details` for exploring your dbt project.
- **Remote**: Commands like `text_to_sql` and `execute_sql` for generating and running SQL queries (requires a personal access token for `DBT_TOKEN`).

**Note**: Be very cautious, as some commands (e.g., `run`, `build`) can modify your data models or warehouse objects. So, proceed with caution!

## Wrapping Up

And there you have it! The **dbt MCP server** is your ticket to bringing structured, governed data into AI workflows. By connecting your dbt project to AI agents, you’re unlocking a world of data discovery, querying, and automation—all while keeping things secure and scalable. Whether you’re a data engineer or an AI enthusiast, this server is a powerful tool to make your data shine.
