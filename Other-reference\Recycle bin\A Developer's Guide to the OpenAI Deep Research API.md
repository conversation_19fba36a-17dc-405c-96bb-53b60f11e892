---
meta-title: "OpenAI Deep Research API: The Developer's Secret Weapon for Automated Research (2025 Guide)"
meta-description: "Stop drowning in docs! Learn how to use OpenAI's Deep Research API to automate research, generate citation-rich reports, and supercharge your dev workflow—with code, tips, and Apidog integration."
excerpt: "Tired of endless Googling and copy-pasting? Discover how the Deep Research API turns high-level questions into structured, verifiable reports—plus how to connect your own data with MCP servers like Apidog."
---

# OpenAI Deep Research API: The Developer's Secret Weapon for Automated Research (2025 Guide)

> **Pro Tip:** Want your AI research agent to pull real, up-to-date facts from your API docs? Plug in **[Apidog MCP Server](https://docs.apidog.com/apidog-mcp-server)**—the all-in-one platform that lets AI assistants access your API specs, generate code, and kill hallucinations. [Try it free!](https://app.apidog.com/)

---

## Drowning in Docs? Meet Your New AI Research Sidekick

Let's face it: research is a slog. You're buried in PDFs, tabs, and "Ctrl+F" marathons, only to end up with a mountain of half-verified facts and a headache. What if you could automate the whole process—turning a single question into a structured, citation-rich report, complete with web searches, code analysis, and transparent sources?

**Enter OpenAI's Deep Research API.** This isn't just another LLM—it's an agentic research machine that decomposes your query, scours the web, runs code, and spits out a report you can actually trust (with sources!).

This guide will show you how to:
- Make your first Deep Research API call (with code!)
- Choose the right model for your use case
- Extract citations, intermediate steps, and code outputs
- Supercharge your agent with private data using MCP servers (like Apidog!)

---

## Deep Research API Models & Pricing: Pick Your Power Level

![](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-27-at-5.00.43-PM.png)

### Which Model Should You Use?
- **`o3-deep-research`**: The heavyweight champ for deep, nuanced, and citation-heavy reports. It's slower, but the output is gold.
- **`o4-mini-deep-research`**: The lightweight speedster for quick, interactive research. Great for chatbots or latency-sensitive apps.

### What's It Cost?
- **Input:** $10.00 per 1M tokens
- **Output:** $40.00 per 1M tokens (yep, synthesis is hard work)
- **Context Window:** 200,000 tokens (bring on the long-form docs!)
- **Max Output:** 100,000 tokens
- **Knowledge Cutoff:** June 2024—but with `web_search_preview`, you get real-time info.

---

## Your First Deep Research API Call (with Python)

Let's get our hands dirty. First, install the OpenAI Python SDK:

```bash
pip install --upgrade openai
```

Set up your API key (use an environment variable for safety!):

```python
from openai import OpenAI
import os

client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))
```

### Crafting the Request

Suppose you're building a tool for a healthcare team and want a data-driven report on the economic impact of new diabetes meds. Here's how you'd do it:

```python
system_message = """You are a professional researcher preparing a structured, data-driven report on behalf of a global health economics team. Your task is to analyze the health question the user poses.Do:- Focus on data-rich insights: include specific figures, trends, statistics, and measurable outcomes.- When appropriate, summarize data in a way that could be turned into charts or tables.- Prioritize reliable, up-to-date sources: peer-reviewed research, health organizations (e.g., WHO, CDC), etc.- Include inline citations and return all source metadata.Be analytical, avoid generalities, and ensure that each section supports data-backed reasoning."""

user_query = "Research the economic impact of semaglutide on global healthcare systems."

response = client.responses.create(
  model="o3-deep-research", # Or "o3-deep-research-2025-06-26"
  input=[
    {
      "role": "developer",
      "content": [
        {
          "type": "input_text",
          "text": system_message,
        }
      ]
    },
    {
      "role": "user",
      "content": [
        {
          "type": "input_text",
          "text": user_query,
        }
      ]
    }
  ],
  reasoning={
    "summary": "auto"
  },
  tools=[
    {
      "type": "web_search_preview"
    },
    {
      "type": "code_interpreter"
    }
  ]
)
```

**What's happening here?**
- You set the model, system prompt, and user query.
- `reasoning` lets the agent decide how detailed to get.
- `tools` gives it web search and code execution superpowers.

---

## Getting the Final Report (and Citations!)

The main output is a structured, citation-rich report. Grab it like this:

```python
print(response.output[-1].content[0].text)
```

### Extracting Citations

Every claim is backed by a citation, with metadata you can use for clickable footnotes or bibliographies:

```python
annotations = response.output[-1].content[0].annotations
for i, citation in enumerate(annotations):
    print(f"Citation {i+1}:")
    print(f"  Title: {citation.title}")
    print(f"  URL: {citation.url}")
    print(f"  Location: chars {citation.start_index}–{citation.end_index}")
```

---

## Debugging the Agent: See Every Step

Want to peek under the hood? The API logs every step:

- **Reasoning Steps:**

```python
reasoning = next(item for item in response.output if item.type == "reasoning")
for s in reasoning.summary:
    print(s.text)
```

- **Web Search Calls:**

```python
search = next(item for item in response.output if item.type == "web_search_call")
print("Query:", search.action["query"])
```

- **Code Execution:**

```python
code_step = next((item for item in response.output if item.type == "code_interpreter_call"), None)
if code_step:
    print("Code Input:", code_step.input)
    print("Code Output:", code_step.output)
```

---

## Level Up: Connect Your Own Data with MCP Servers (Apidog FTW)

Web search is great, but what if you want your agent to pull from your private docs, databases, or API specs? That's where **Model Context Protocol (MCP)** comes in.

- **MCP lets you build custom tools** that plug your agent into internal knowledge bases, databases, or proprietary APIs.
- **Apidog MCP Server** is a popular choice—connect your API docs, let the agent generate code, and slash hallucinations by grounding answers in real specs.

> **Pro Tip:** Want to see how to set up Apidog MCP Server? [Check the docs.](https://docs.apidog.com/apidog-mcp-server)

---

## Conclusion: Research at the Speed of Thought

The OpenAI Deep Research API is more than just another LLM—it's a research agent that plans, searches, codes, and cites its sources. Whether you're building dashboards, automating literature reviews, or just want to stop drowning in Google results, this API is your new best friend.

And if you want to make your agent even smarter (and more trustworthy), connect it to your own data with Apidog MCP Server. The future of research is here—go build something amazing!
