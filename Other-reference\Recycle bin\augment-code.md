Clone. Contribute. Merge.
Augment is free for open source maintainers and contributors. Powerful codebase knowledge makes working on open source better for everyone.


Program guidelines

Open source
Projects should be open source licensed or available for AI training.


Training permitted
Data uploaded and generated may be used in AI model training.


Support
Get help, share feedback, and join the community on Discord.


Contributors
The knowledge you need to contribute
Get up to speed on the project’s structure, dependencies, patterns and practices. Deep codebase knowledge keeps you unblocked.


Explore and discover
Use chat to find your way around and explain how systems work.


Focus on shipping
Completions automatically pull in dependencies and idioms.


Maintainers
Code that’s ready to merge
Focus on the substance of contributions instead of the style, less back-and-forth with contributors means more code gets merged.


Looks good to me
Speed up feedback and reviews to keep contributors engaged.


Expand your core team
Share all the knowledge of the core team to every contributor.

Get ship done

Chat
Dive into any task with confidence. Quickly find out how a component works, investigate a bug, or work with a new API.


Next Edit
Suggested edits helps you move through the entire codebase one change at a time–code, tests, and docs.


Completions
Blazing fast in-line suggestions that understands the project’s code, dependencies, practices, and style.


Frequently asked questions
What is Augment for Open Source?
Augment for Open Source makes the power of Augment
free to open source contributors and maintainers.
 
Are there restrictions for what projects I can work on with Augment?
No. You can work on any type of project with Augment-open
source licensed or unlicensed projects that permit Al training use.

How will my data be used?
Augment may use the data you upload to the service or data from
your usage of the product for training our Al models and inform future product decisions. You can view all the details in our terms of service.

What if l don't want you to train on my data?
Augment's enterprise terms of service exclude any Al training on your data or usage for paying customers. lf you would like to use Augment as part ofyour organization, please contact us.

