Pricing
Choose the plan that works for you


Monthly

Hobby
Free

Includes
Pro two-week trial
200 completions per month
50 requests per month



Pro
$20/month

Everything in Hobby, plus
Unlimited completions
500 requests per month
Unlimited slow requests
Max mode

Business
$40/user/month

Everything in Pro, plus
Enforce privacy mode org-wide
Centralized team billing
Admin dashboard with usage stats
SAML/OIDC SSO

Questions about enterprise security, procurement, or custom contracts? Contact Sales

Why isn't Cursor completely free?
Large language models cost quite a bit of money to run. To grow Cursor sustainably without compromising our service quality, we need to cover our costs.

What are the premium models?
Examples of premium models are GPT-4, GPT-4o, and Claude 3.5/3.7 Sonnet. You have 500 fast uses and unlimited slow uses each month for these models. Some models like o3-mini use 1/3 of a request. The full list of premium models can be found here.

Who owns the code generated in Cursor?
You! Regardless of whether you use the free, pro or business version of Cursor, all generated code is yours and free to be used however you like, including commercially.

What are fast and slow uses?
Fast uses of premium models are given first priority by our backend. On Pro, once you hit your fast usage limit, you can still use premium models, but your requests may be queued behind others at times of high load.

How do the plan limits work?
If you go over your limit, we'll nicely ask you to upgrade. You can use up to 50 uses of the premium models for free.

What code do you store?
If you enable "Privacy mode", your code is never stored anywhere other than your machine and will never be trained on. Otherwise, we may collect usage and telemetry data (including prompts, code snippets, or editor actions) to help improve Cursor.

What is Cursor Tab?
This is Cursor's native autocomplete feature. It's a more powerful Copilot that suggests entire diffs with especially good memory.

Do all Cursor features work with an API key?
A few important Cursor features (including Tab and Apply from Chat) are powered by custom models and cannot be charged to an API key.

Where can I ask more questions?
You’re welcome to join our forum and share your thoughts! If you prefer a private conversation, feel free to email us directly.

Our Forum
Contact Us