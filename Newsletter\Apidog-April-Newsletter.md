Email Subject: Apidog's April Updates: Secure APIs with Security Schemes & Make Docs LLM-Ready!

Hello Apidog Users,

April brings powerful upgrades to supercharge your API development! This month, we're introducing OpenAPI-compatible Security Schemes for streamlined authentication, LLM-ready documentation for AI integration, and enhanced team collaboration features. These updates are designed to make your API lifecycle more secure, efficient, and future-ready. Let's dive into what's new!

🔥 Reusable Security Schemes: Tired of setting up authentication for every endpoint? Now you can create reusable Security Schemes (API Key, Bearer, OAuth 2.0, etc.) following OpenAPI standards. Define authentication rules once, apply them across multiple endpoints or folders, and enjoy automatic inheritance. Plus, it works seamlessly with sprint branches, versioning, and change history for secure and consistent API evolution. Learn more. 
[Image]
https://assets.apidog.com/uploads/help/2025/04/28/ce0e766a54529a3957c872a44cb9eb3c.png

✍️ LLM-Ready Documentation: Make your published API docs instantly usable by Large Language Models! Every documentation page can now be accessed in clean Markdown format by simply appending .md to its URL – perfect for copying directly into LLM prompts. We've also automatically included llms.txt files in every doc site to improve AI accessibility. Learn more.
[Image]
https://assets.apidog.com/uploads/help/2025/04/28/3ea51e62d04276287dfa0c5b4f5f6414.png

👥 @Mentions in Comments: Need discussions with teammates on an endpoint definition or a test scenario? You can now use the @ to mention specific team members directly in comments. This ensures the right people get notified and can jump into the conversation quickly.
[Image]
https://assets.apidog.com/uploads/help/2025/04/28/e657c2b18cb6169f29cd664520599123.png

🌐 IP Allowlist for Teams: Add an extra layer of protection to your Apidog team environment. You can now configure an IP Allowlist, restricting access only to specified single IP addresses or IP ranges, enhancing control over who can access your team's resources. 

⬇️ OpenAPI Import Enhancement: When importing OpenAPI files where a request body defines multiple media types, Apidog now intelligently defaults to importing the first media type listed, providing a clearer starting point.

✉️ Doc Site Email Login Customization: For published documentation secured by an email allowlist, you can now customize the description text on the login page and the toast message displayed after a user enters their email, giving users a more tailored experience.

🔀 LiteLLM SSE Auto-Merging: We've improved the debugging experience for AI endpoints of LiteLLM. Apidog now automatically merges streaming Server-Sent Events (SSE) message content, presenting the LLM's response fully and more coherently in the timeline view.

📥 Postman Import Upgrade: Moving from Postman is even smoother – imports now include the descriptions associated with collections and folders, preserving valuable context.

🔨 Mock Service Fix: We've resolved an issue where the Mock service could sometimes respond slowly if an endpoint was configured with a No-Content (204) response type.

🚀 What’s Next? 
Our team is already hard at work on the next wave of features and enhancements to make your API development even smoother. Stay tuned for exciting updates coming in May!

💬 Join the Conversation! 
Your feedback shapes Apidog! Have ideas or questions? Join our Discord or Slack communities to connect with fellow developers, share tips, and get the latest Apidog news.

P.S. Explore the full details of all these updates in the Apidog Changelog! 🚀

Happy API Building! 
Regards, The Apidog Team