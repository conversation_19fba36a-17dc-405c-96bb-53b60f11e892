> **Pro Tip:** Supercharge your API development with [<PERSON>pid<PERSON>](https://apidog.com) — the all-in-one platform for building, testing, and managing APIs, trusted by developers worldwide!

# Unlimited Requests? A Fresh Look at Cursor Pro Plan’s New Model (and How Apidog MCP Server Makes It Better)

Cursor has shaken up the AI coding landscape with a [major change to its Pro plan](https://www.cursor.com/blog/new-tier), introducing an "unlimited-with-rate-limits" approach. This update has generated both excitement and uncertainty among developers. Here's a breakdown of what's new, what's not, and how you can maximize your workflow—especially by leveraging [Apidog's free MCP Server](https://docs.apidog.com/apidog-mcp-server).

## The Old Cursor Pro Plan: A Quick Recap

Previously, Cursor's Pro plan was simple but came with boundaries:

- **$20/month** for 500 fast requests
- Unlimited completions (with some tool call restrictions)
- After 500 fast requests, you'd be downgraded to slower, lower-priority requests
- Max Mode and premium models used up your quota faster
- Extra requests were available via usage-based pricing

**Old Pro Plan at a Glance:**

| Plan | Price  | Fast Requests | Slow Requests | Max Mode | Tool Calls |
| ---- | ------ | ------------- | ------------- | -------- | ---------- |
| Pro  | $20/mo | 500/mo        | Unlimited     | Yes      | Limited    |

This system often left users anxious about running out of fast requests, facing unpredictable costs, and juggling quotas—especially when using Max Mode for demanding tasks.

## What's Changed: The New Unlimited-with-Rate-Limits Plan

Cursor's latest Pro plan update brings a new "unlimited-with-rate-limits" model. Here's what's different:

- **No more monthly hard cap:** Unlimited requests, but with rate limits to prevent abuse.
- **Tool call restrictions removed:** Use as many tool calls as you want, within the rate limits.
- **Option to keep the old plan:** Existing users can stick with the 500-request system (Dashboard > Settings > Advanced).

**Old vs. New Pro Plan:**

| Feature       | Old Pro Plan      | New Pro Plan (2025)      |
| ------------- | ----------------- | ------------------------ |
| Fast Requests | 500/mo            | Unlimited (rate-limited) |
| Tool Calls    | Limited           | Limits Lifted            |
| Max Mode      | Yes (quota-based) | Yes (rate-limited)       |
| Price         | $20/mo            | $20/mo                   |

Curious about saving more? Check out [how to cut your Cursor cost](http://apidog.com/blog/save-cursor-costs/).

### What Does "Unlimited-with-Rate-Limits" Really Mean?

- You can send as many requests as you like, but hitting the rate limit means you'll have to pause before sending more.
- The specifics of these rate limits aren't always transparent, which can be confusing for users.
- Some power users worry that strict rate limits could make the new plan feel more restrictive, especially with the [introduction of the $200 Ultra plan](http://apidog.com/blog/cursor-ultra-plan/).

## Developer Questions: Is Unlimited Really Unlimited?

The community has plenty of questions:

- Is this truly unlimited, or just a new way to throttle usage?
- How do the new rate limits compare to the old 500-request cap?
- Will Max Mode and premium models still drain my quota quickly?

**Key Takeaways:**

- "Unlimited" sounds great, but rate limits can still slow down heavy users.
- Lack of clarity around the actual rate limits leaves some uncertainty.
- For most, the new plan means fewer interruptions, but high-volume users may still hit bottlenecks.

## How to Get More from Cursor: Integrate Apidog MCP Server

> **Pro Tip:** Want to avoid rate limit headaches? [Apidog's free MCP Server](https://docs.apidog.com/apidog-mcp-server) is the perfect add-on for Cursor. Here's how to set it up and boost your productivity!

[Apidog MCP Server](http://apidog.com/blog/apidog-mcp-server/) lets you connect your API specs directly to Cursor, enabling smarter code generation, instant API documentation, and seamless automation—all for free.

### Step 1: Prepare Your OpenAPI File

- Obtain your API definition as a URL (e.g., `https://petstore.swagger.io/v2/swagger.json`) or a local file (e.g., `~/projects/api-docs/openapi.yaml`).
- Supported formats: `.json` or `.yaml` (OpenAPI 3.x recommended).

### Step 2: Configure MCP in Cursor

- Open Cursor's `mcp.json` file.

![configuring MCP Server in Cursor](https://assets.apidog.com/blog-next/2025/05/image-415.png)

- Add the following configuration (replace `<oas-url-or-path>` with your actual OpenAPI URL or path):

**For MacOS/Linux:**

```json
{
  "mcpServers": {
    "API specification": {
      "command": "npx",
      "args": [
        "-y",
        "apidog-mcp-server@latest",
        "--oas=https://petstore.swagger.io/v2/swagger.json"
      ]
    }
  }
}
```

**For Windows:**

```json
{
  "mcpServers": {
    "API specification": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "apidog-mcp-server@latest",
        "--oas=https://petstore.swagger.io/v2/swagger.json"
      ]
    }
  }
}
```

### Step 3: Test the Connection

- In Cursor, switch to Agent mode and enter:

```applescript
Please fetch API documentation via MCP and tell me how many endpoints exist in the project.
```

- If everything's set up, you'll get a structured response listing your API endpoints.

> **Pro Tip:** With Apidog MCP Server, you can generate code, search API content, and automate tasks in Cursor—without worrying about rate limits or extra costs!

---

## Conclusion: The Real Power of Cursor + Apidog

Cursor's new Pro plan offers more flexibility for most users, but true freedom and productivity come from integrating Apidog MCP Server.

- **No more quota stress:** Apidog MCP Server is free and gives you direct access to your API specs.
- **Boost your workflow:** Automate code generation, documentation, and testing—right in your IDE.
- **Stay empowered:** Whether you're a casual user or a power developer, Apidog helps you get the most from Cursor's new pricing.

*Take control of your API workflow—optimize, save, and future-proof your development with Apidog. Try it today!*
