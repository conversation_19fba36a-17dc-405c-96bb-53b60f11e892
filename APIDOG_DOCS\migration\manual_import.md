If you were originally using other tools and now want to migrate to Apidog, you can use **manual import**.

:::warning[]
1. Importing in OpenAPI/Swagger format includes endpoints, schemas, and environments.
2. Importing in Postman format includes only endpoints.
:::

You can import data into a sprint branch. The sprint branch displayed at the top-left corner represents the target branch for the import. For more details on importing into sprint branches, refer to [OAS imports](https://docs.apidog.com/design-api-in-a-branch-616423m0#oas-import).

<Background>
![import-data-to-sprint-branch.png](https://api.apidog.com/api/v1/projects/544525/resources/348703/image-preview)
</Background>

## How to manually import?

<Steps>
  <Step>
Go to "Project Settings" - "Import Data".

<Background>
![](https://assets.apidog.com/uploads/help/2024/06/12/9ce8a53955972874149df22dab105ce2.png)
</Background>
    </Step>
  <Step>
Select the data format you need to import.
  </Step>
  <Step>
Fill in the necessary fields according to the guide, and click "Continue".
  </Step>
  <Step>
Preview data and confirm import options.
<Background>
      ![image.png](https://api.apidog.com/api/v1/projects/544525/resources/344133/image-preview)
</Background>
:::highlight purple
Learn more about [Import options](apidog://link/pages/633930).
:::
  </Step>
  <Step>
Complete the import.
<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/344166/image-preview)
</Background>
  </Step>
</Steps>

## Where to import data in Apidog?

In Apidog, you can import data:

- In **APIs**, Click `➕` - **Import**
- In **Project Settings** - **Import Data**
- In the **Home** - **Team** - **Projects** interface, you can **Import Project**.