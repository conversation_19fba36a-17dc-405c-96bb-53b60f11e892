## 准备工作

在 Okta 后台做配置工作之前，请导航到 Apidog 组织设置中的 SAML 单点登录页面。点击 **生成 SCIM Token** 按钮，并在接下来的步骤中保持此页面打开。

![image.png](https://docs.apifox.com/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/485623/image-preview?onlineShareType=apidoc&locale=zh-CN)

## 修改 SSO 配置

为了支持 SCIM 配置，你需要修改 SSO 的 Application username format：

- 在浏览器中打开你的 Okta 后台。
- 前往 **Applications**，点击对应的 App，在 **Sign On** 标签页点击 **Settings** 的 **Edit** 按钮，把 Credentials Details 分类下的 **Application username format ** 设置为 Custom，然后输入 `user.getInternalProperty("id")` 作为表达式。

## 配置 SCIM

请按以下步骤配置你的 SCIM：

- 在浏览器中打开你的 Okta 后台。
- 前往 **Applications**，点击对应的 App，在 **General** 标签页点击 **App Settings** 的 **Edit** 按钮，把 **Provisioning** 从 None 切换为 SCIM。
- 保存之后，App 页面在 Sign On 标签页之后会新增一个 **Provisioning** 标签页，点击它，再点击 **SCIM Connection** 的 **Edit** 按钮。
- 复制 Apidog 的 **SCIM API 端点 URL**，粘贴到 Okta 的 **SCIM connector base URL**。
- Okta 的 **Unique identifier field for users**，输入 `userName`。
- Okta 的 **Supported provisioning actions**，勾选 Import New Users and Profile Updates 和 Push New Users。
- Okta 的 **Authentication Mode**，选择 HTTP Header。
- 复制 Apidog 的 **SCIM Token**，粘贴到 Okta 的 **Authorization** 。
- 配置完成后，点击 Okta 的 **Test Connector Configuration**，如果 User Import、Import Profile Updates、Create Users 这 3 项显示对勾则表示配置成功，可以关闭测试窗口后保存配置。
- 在 Okta App 的 **Provisioning** 标签页的 Settings 的 **To App** 页面，点击 **Provisioning to App** 的 **Edit** 按钮，将 **Create Users** 的 **Enable** 勾选。

## 测试你的 SCIM

返回 Apidog，你可以看到未激活的用户。

- 一旦这些未激活的成员使用 SSO 登录，他们的状态将变为正常，并占用付费席位。

- 处于未激活状态的用户不会占用付费席位。