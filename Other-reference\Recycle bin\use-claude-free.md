**🚀 Quick Start**

1️⃣ Install Node.js (skip if already installed)

Ensure Node.js version ≥ 18.0

```bash
# Ubuntu / Debian users
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo bash -
sudo apt-get install -y nodejs
node --version

# macOS users
sudo xcode-select --install
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
brew install node
node --version
```

2️⃣ Install Claude Code

```bash
npm install -g @anthropic-ai/claude-code
claude --version
```

3️⃣ Start Using

- **Get Auth Token:** `ANTHROPIC_AUTH_TOKEN`: After registration, go to the `API Tokens` page and click `Add Token` to get it (starts with `sk-`)
  - Name can be anything, quota is recommended to be set to unlimited, keep other settings as default

- **API Address:** `ANTHROPIC_BASE_URL`: `https://anyrouter.top` is this site's API service address, **same as the main site address**

Run in your project directory:

```bash
cd your-project-folder
export ANTHROPIC_AUTH_TOKEN=sk-... 
export ANTHROPIC_BASE_URL=https://anyrouter.top
claude
```

After running

- Choose your preferred theme + Enter
- Confirm safety notice + Enter
- Use default Terminal configuration + Enter
- Trust working directory + Enter

Start coding with your AI programming partner in the terminal! 🚀

4️⃣ Configure Environment Variables (Recommended)

To avoid repeating input each time, you can write environment variables to bash_profile and bashrc:

```bash
echo -e '\n export ANTHROPIC_AUTH_TOKEN=sk-...' >> ~/.bash_profile
echo -e '\n export ANTHROPIC_BASE_URL=https://anyrouter.top' >> ~/.bash_profile
echo -e '\n export ANTHROPIC_AUTH_TOKEN=sk-...' >> ~/.bashrc
echo -e '\n export ANTHROPIC_BASE_URL=https://anyrouter.top' >> ~/.bashrc
echo -e '\n export ANTHROPIC_AUTH_TOKEN=sk-...' >> ~/.zshrc
echo -e '\n export ANTHROPIC_BASE_URL=https://anyrouter.top' >> ~/.zshrc
```

After restarting the terminal, use directly:

```bash
cd your-project-folder
claude
```

You can now use Claude Code

❓ FAQ

- This site directly connects to the official Claude Code forwarding and cannot forward non-Claude Code API traffic

- If you encounter API errors, it may be due to unstable forwarding proxy, you can try exiting Claude Code and retry a few times

- If the webpage encounters login errors, you can try clearing this site's cookies and log in again

- How to solve `Invalid API Key · Please run /login`? This indicates that Claude Code did not detect the `ANTHROPIC_AUTH_TOKEN` and `ANTHROPIC_BASE_URL` environment variables, check if the environment variables are properly configured

- Why does it show `offline`? Claude Code determines network status by checking if it can connect to Google. Showing `offline` doesn't affect normal use of Claude Code, it just indicates that Claude Code couldn't connect to Google

- Why do web browsing Fetch requests fail? This is because Claude Code calls Claude's service to determine if a webpage is accessible before accessing it. You need to maintain international internet connection and use global proxy to access Claude's service that determines webpage accessibility

- Why do requests always show fetch failed? This may be due to the network environment in your region, you can try using proxy tools or use the backup API endpoint `ANTHROPIC_BASE_URL=https://pmpjfbhq.cn-nb1.rainapp.top`
