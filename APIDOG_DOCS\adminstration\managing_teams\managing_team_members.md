Managing Team Members
The member management feature in Apidog enables you to manage user collaboration within your team by assigning specific permissions. Only team owner and admins have the authority to manage these settings. Other team members can view the list of team members and their basic information but cannot make any changes.

When you need to collaborate on an API project, you can invite team members or external collaborators to work together.

Inviting users to join the team
To invite members, go to Members and click on <PERSON>vite at the top right.

Tip
Only team admins and team owner can send invitations. Project admins, even if they manage a specific project, cannot invite members to the team.

inviting-users-to-teams.jpg
Invitation methods
There are two methods to invite members to the team:

Invite via Link: Copy the invitation link and share it with users to join the team. When joining through this link:

The user's team role is automatically set toTeam Member.

Their permissions for all team projects are determined by the project role you assign during the invitation.

invite-team-users-via-link.png
Invite via Email: Enter one or more email addresses and send an invitation.

Apidog will email the recipients, prompting them to register (if they are not Apidog users yet) and join the team.

Their permissions for all team projects are determined by the project role you assign during the invitation.

invite-team-users-via-email.png
Inviting users to join specific projects
Users with invitation permissions can invite external collaborators to edit specific projects without granting access to other team projects. To do so, click on Invite on the left side of the project page.

invite-team-users-join-specific-projects.jpg
There are two ways to invite other users to join the current project, and these methods are the same as those used for inviting users to the team. But:

Invitations sent from a project are limited to that project only.

For other projects in the team, permissions default toForbidden, meaning the invited member cannot view them.

inviting-users-join-project.jpg
Guests
Users who gain project access through "Project Invitations" are considered guests at the team level.

Guests can only see the projects they're invited to. Use "Project Invitations" to allow external collaborators to edit specific projects without accessing other team projects.

Guests have the same team permissions as team members, but are excluded from permission settings for newly created team projects.

NOTE
Guests are also counted as team members and will be billed according to the total number of team members.

Managing team member details & permissions
Click the settings button on the right of a member's name to open the member details page.

member-setting-page-entry.jpg
There, you can configure more advanced settings.

manage-member-details.jpg
1.
Nickname: Assign a nickname for easier identification. The member can also change it.

2.
Role: Set a member's team role to control their access to team functions, such as inviting or removing members and creating projects.

New members are assigned the "Team Member" role by default.

Be cautious when assigning "Team Admin" roles, as it can impact data security.

3.
Project Role: Set the member’s role for each project in the team, defining what they can do in each project. Check out member roles & permission settings.

4.
Remove Member: You can remove members from the team. Once removed, they will no longer have access to your team’s data.

