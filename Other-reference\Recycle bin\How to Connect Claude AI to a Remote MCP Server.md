# How to Connect Claude AI to a Remote MCP Server

Hey there! If you’ve been itching to supercharge your workflows with **<PERSON> AI**, you’re in the right place. I recently set up **Claude AI** with a remote MCP (Model Context Protocol) server on my windows computer, and wow—it’s like giving <PERSON> a superpower to connect with tools like **Atlassian** and **Zappier**! In this tutorial, we’ll walk through how to connect **Claude AI** to a remote MCP server, using examples like managing Jira tickets with **Atlassian** and automating tasks with **Z<PERSON>pier**. Trust me, it’s easier than you think, and by the end, you’ll be ready to make <PERSON> your ultimate work assistant. Let’s get started!

## What is <PERSON> AI with Remote MCP Server?

**[<PERSON> AI](https://www.anthropic.com/news/integrations)**, developed by <PERSON><PERSON><PERSON>, is a powerful AI assistant that shines in tasks like coding, research, and content creation. But what makes it even cooler? Its ability to connect to remote MCP servers! MCP (Model Context Protocol) is an open standard that lets **Claude AI** interact with external tools and data sources—like **Atlassian**’s <PERSON>ra and Confluence for project management, or **Zappier** for automating workflows across thousands of apps.

Announced on May 1, 2025, by Anthropic, remote MCP support (also called Integrations) lets you connect **Claude AI** to cloud-hosted servers, making it a seamless collaborator. Imagine asking Claude to create Jira tickets in **Atlassian** or pull sales data via **Zappier**—all in one chat! [Posts on X](https://x.com/search?q=claude&src=typeahead_click) are buzzing about this feature, with users loving how it reduces context switching and boosts productivity. Let’s set it up and see it in action!

## Setting Up Your Environment: The Basics

Let’s get your system ready to connect **Claude AI** to a remote MCP server. This is super beginner-friendly, so don’t worry—I’ve got you covered.

**Check Prerequisites**: Make sure you have these ready:

- **Claude AI Account**: You’ll need a paid plan (Max, Team, or Enterprise) to access Integrations, as noted on Anthropic’s site. Sign up at [claude.ai](https://claude.ai/login?returnTo=%2F%3F) if you haven’t already.
- **Node.js**: Needed for running some MCP servers. Check with `node --version` or download from nodejs.org.
- **Hardware**: A 4+ core CPU, 16GB+ RAM, and 10GB+ free storage to handle AI processing smoothly.

**Install Claude Desktop (Optional)**: While you can use **Claude AI** on the web (claude.ai), the Claude Desktop app is great for local testing. Download it from the [official site](https://claude.ai/download) for macOS or Windows. We’ll focus on the web version for this tutorial, but the steps are similar.

![claude desktop](https://assets.apidog.com/blog-next/2025/05/Screenshot-2025-05-06-020051.png)

**Create a Project Folder**: Let’s stay organized:

```bash
mkdir claude-mcp
cd claude-mcp
```

This folder will be your workspace for **Claude AI** and MCP integrations.

## Connecting Claude AI to Atlassian’s Remote MCP Server

Let’s start by connecting **Claude AI** to **Atlassian**’s remote MCP server, which lets Claude interact with Jira and Confluence. **Atlassian** announced this beta feature on May 1, 2025, and it’s perfect for managing projects without leaving your chat.

1. **Sign In to Claude AI**:

- Head to claude.ai and log in with your Max, Team, or Enterprise account. Integrations aren’t available on the free plan, as per Anthropic’s support docs.

2. **Access Integrations**:

- In the Claude web app, go to the settings menu (click your profile icon, then “Settings”).
- Look for the “Integrations” section—this is where you’ll add remote MCP servers.

![claude integrations](https://assets.apidog.com/blog-next/2025/05/Screenshot-2025-04-29-at-6.11.38-PM.png)

3. **Add Atlassian’s Remote MCP Server**:

- Click “Add More” and search for **Atlassian**’s Jira and Confluence integration.

![claude integrations 2](https://assets.apidog.com/blog-next/2025/05/Screenshot-2025-04-29-at-6.12.47-PM.png)

- According to the Atlassian community article on using the Remote MCP Server Beta, you need to join the beta program at atlassian.com/remote-mcp-beta to get the server URL. Once enrolled, you’ll receive a URL like `https://mcp.atlassian.com/v1`.
- Paste this URL into the “Server URL” field in Claude’s “Add Integration” form, then click “Save.”

4. **Authenticate with Atlassian**:

- Claude will initiate an OAuth flow. You’ll be redirected to an **Atlassian** login page—sign in with your Jira/Confluence Cloud credentials.
- Approve the requested permissions (e.g., read/write access to Jira issues and Confluence spaces). The community article notes that **Atlassian** uses OAuth with your existing permission controls, ensuring Claude only accesses what you allow.
- After approval, you’ll return to Claude with the integration active.

5. **Test the Connection**:

- Back in Claude’s chat, click "connect" in the "search and tools" menu.

![claude chat](https://assets.apidog.com/blog-next/2025/05/Screenshot-2025-04-29-at-6.17.42-PM.png)

- Then, type: “Create a Jira ticket in my project ‘DEV-TEAM’ with the title ‘Fix login bug’.”
- Claude will use the **Atlassian** MCP server to create the ticket and confirm: “I’ve created a Jira ticket titled ‘Fix login bug’ in project DEV-TEAM.” If it fails, verify your beta enrollment or check your **Atlassian** permissions.

This setup lets **Claude AI** manage Jira tickets or summarize Confluence pages—like scaling your work by creating multiple pages at once—without leaving the chat. Pretty handy, right?

## Connecting Claude AI to Zappier’s Remote MCP Server

Now, let’s connect **Claude AI** to **Zappier**’s remote MCP server to automate tasks across 7,000+ apps. **Zappier** announced this integration on May 1, 2025, and it’s a game-changer for workflows.

1. **Create a Zapier MCP Server**

- **Log in to Zapier** and navigate to the [Zapier MCP dashboard](https://zapier.com/mcp).
- Click **"+ New MCP Server"** and select **Claude** as the MCP client.
- Name your server (e.g., "Zapier MCP") or use the default suggestion, then click **"Create MCP Server"**.

![zapier mcp](https://assets.apidog.com/blog-next/2025/05/Screenshot-2025-05-06-050933.png)

2. **Configure Actions**

- In your MCP server, click **"+ Add Tool"**.
- Type the name of an app (e.g., "Google Calendar") and select the desired action (e.g., "Create Event").
- Connect your app account by authorizing Zapier to access it.
- Fill out any required fields. You can allow AI to assign values for certain fields. Click **"Save"**.
- Repeat this process for additional actions as needed.

![hubspot](https://assets.apidog.com/blog-next/2025/05/Screenshot-2025-05-06-051249.png)

3. **Connect Zapier MCP to Claude**

- In your Zapier MCP server, click the **"Connect"** tab.
- Click **"Copy URL"** to obtain your unique MCP server URL.
- Open [Claude.ai](https://claude.ai/) in your browser and log in.
- Click your initials in the bottom-left corner and select **"Settings"**.
- Under **"Integrations"**, click **"Add more"**.
- In the pop-up, name your server (e.g., "Zapier MCP") and paste the integration URL.
- Check the box to accept the security notice and click **"Add"**.

![zapier mcp url](https://assets.apidog.com/blog-next/2025/05/Screenshot-2025-05-06-051356.png)

4. **Authenticate and Enable Actions**

- Start a new conversation in Claude.
- Click the **search and tools** icon.
- At the bottom of the modal, you'll see a tool named after your MCP server. Click **"Connect"**.
- You'll be redirected to a screen to approve the connection between your server and Claude. Click **"Allow"**.
- Use the toggle buttons to enable or disable actions that Claude can access.

![claude mcp integration](https://assets.apidog.com/blog-next/2025/05/Screenshot-2025-05-06-051407.png)

5. **Test the Connection**:

- In Claude’s chat, try: “Pull the latest prospect info from HubSpot for my meeting with Sarah at 3 PM.”
- Claude will use **Zappier**’s MCP server to fetch the data and respond with details like: “Sarah’s company is TechCorp, last contacted on April 20, 2025.” If it doesn’t work, ensure your **Zappier** app permissions are set correctly.

With **Zappier**, **Claude AI** can automate tasks like assigning Asana tasks or updating spreadsheets across tools—all in one conversation. It’s like having a personal assistant who never sleeps!

## Testing Claude AI with Remote MCP Servers

Let’s test both integrations to see **Claude AI** in action with **Atlassian** and **Zappier**.

**Atlassian Test**:

- Ask Claude: “Summarize my Confluence page ‘Project Roadmap’ in DEV-TEAM space.”
- Claude will fetch the page via **Atlassian**’s MCP server and reply with a summary, like: “The Project Roadmap outlines Q2 goals, including launching Feature X by June 2025.” If you see errors, check your Confluence permissions.

**Zappier Test**:

- Try: “Assign a task in Asana to John for ‘Prepare Q3 budget’ due next Friday.”
- Claude will use **Zappier** to create the task and confirm: “Task ‘Prepare Q3 budget’ assigned to John in Asana, due Friday, May 9, 2025.” If it fails, verify your Asana connection in **Zappier**.

These tests show how **Claude AI** can seamlessly interact with your tools, saving you time and effort. Pretty impressive, huh?

## Tips for Using Claude AI with Remote MCP Servers

To get the most out of **Claude AI** with **Atlassian** and **Zappier**:

- **Be Specific**: Use clear prompts like “Create a Jira ticket in project DEV-TEAM” to avoid confusion.
- **Check Permissions**: Ensure Claude has access to your **Atlassian** spaces or **Zappier** apps—OAuth issues are common culprits.
- **Monitor Usage**: Remote MCP servers may have rate limits, so keep an eye on your plan’s quotas in Claude’s settings.
- **Explore More Integrations**: Anthropic lists other MCP servers (e.g., Cloudflare, Intercom) in their Integrations page—try them out!

## My Takes on Claude AI with Remote MCP

After setting up **Claude AI** with remote MCP servers, here’s what I think:

- **Game-Changer**: Managing Jira tickets via **Atlassian** without leaving Claude’s chat is a huge time-saver.
- **Automation Magic**: **Zappier** lets Claude handle repetitive tasks—like pulling HubSpot data—in seconds.
- **Security First**: Both **Atlassian** and **Zappier** use OAuth, so I felt confident my data was safe.

Some users on Reddit noted occasional OAuth hiccups, but restarting the connection usually fixes it. Overall, it’s a solid upgrade for **Claude AI**!

## Wrapping Up: Your Claude AI and MCP Adventure

You’ve just connected **Claude AI** to remote MCP servers, unlocking a world of productivity with **Atlassian** and **Zappier**! From creating Jira tickets to automating workflows, Claude is now your go-to collaborator. Try experimenting with other MCP servers like Cloudflare or Intercom, and share your wins on X—I’d love to hear about them! For more details, check Anthropic’s support docs at support.anthropic.com. Keep rocking with **Claude AI**, **Zappier**, and **Atlassian**!


