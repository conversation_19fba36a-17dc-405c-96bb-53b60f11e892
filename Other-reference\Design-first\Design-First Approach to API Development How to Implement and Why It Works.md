# Design-First Approach to API Development: How to Implement and Why It Works

### 

Key Takeaways

- Identify which approach to your API program makes the most sense for your team and the benefits/drawbacks of each approach.
- The most important thing is to ensure positive experiences for all stakeholders including the end-user, the developer and the company (a design-first approach and customer-centric language is key to this).
- Viewing your API as a product saves time, expands revenue opportunities, increases collaboration and innovation.
- A focus on decentralized governance, increased automation and consistency through style guides will improve your API program by minimizing complexity for API consumers.
- There are guidelines and patterns for taking a design-first approach to API development

Let's think about the world's greatest masterpieces for a second.

<PERSON>'s "Hamlet." <PERSON><PERSON><PERSON>ci's "Mona Lisa." <PERSON>'s architectural beauty that is "Fallingwater." <PERSON><PERSON><PERSON>'s "Four Seasons" concerto. I could go on and on, and if it were up to me, I would include the world's best pick-up truck ever created, as well. But, I digress.

All of these masterpieces have one thing in common: they were built with the design first in mind. You can't build a house without a blueprint, and you can bet <PERSON><PERSON><PERSON><PERSON> didn't just wing it on the "Mona Lisa."

Starting with a design isn't a new idea, and the world's greatest creators have practiced it throughout generations, but having a "design-first" approach to Application Programming Interfaces (API) is.

Huh? You may ask yourself. Let me explain!

Thanks to the explosion of the digital economy, there are currently 27 million software developers in the world, and that number is expected to reach well over [45 million by 2030](https://www.developernation.net/developer-reports/de20). Today, 19 million of those developers are [API developers](https://www.programmableweb.com/news/apis-show-faster-growth-rate-2019-previous-years/research/2019/07/17), and the Application Development Software Market Size is projected to grow 196% by 2027. Now, I don't know about you, but I would consider that a significant market.

With this rapid growth of the API industry, it follows that more developers and technology leaders both need to know how to make these efforts successful. Increasingly, folks in these essential roles need to understand what it takes to build out a solid, successful, scalable API program that will drive business value.

Where do you start? What are the most important factors to consider?

There is complexity for sure, but I'm here to tell you that the most important factor to consider is getting started with a design-first approach to your API strategy. As developers, we can have the next API masterpiece at our fingertips; we just have to design it first.  

## Approaches to Your API Strategy

Okay, so you have an excellent idea for an API that will benefit the business, but what next?

You could start by writing out the business requirements for the API and then proceed to code it. Or, perhaps you decide first to describe your API in a specification language, such as OpenAPI. You could even convince your organization to take your idea and turn it into an API-first product. Which is it?

Let's go through the most common approaches — Code-First, API-First, or Design-First — and review some pros and cons of each:

- **Code-First**: When a developer needs to deploy an API fast, going right to coding after receiving the business requirements will speed up API deployment. Sometimes, this is the quickest option if the API has only a few endpoints. Code-first, in a nutshell, is completely oriented to developers and unconcerned about other potential API users.

The problem with a code-first approach is that even well-designed APIs developed this way are predestined to fail because the success of an API is based on its adoption and use by more and more users. As adoption trends up, ***most of your new users won't be developers or technically-minded people*** — they will just be people trying to solve problems as quickly and efficiently as possible. If an API is not intuitive and applicable to their context, the average person simply won't use it.

- **API-First**: This is an increasingly promoted approach. Basically, it means that your organization treats APIs as the core focus with the understanding that they are critical business assets upon which the organization operates. This process is initiated with a contract written in an API description language such as OpenAPI. There's nothing wrong with this way, and it's wise to standardize early across a platform or set of products. The problem is that the chosen language and its particulars often dominate and even limit a company's ability to scale and build for the future. If your API is the most important thing, what does that say about all the people developing it and/or consuming it?

- **Design-First**: This approach fundamentally means any API effort — whether one or many in a program — starts with a design process. In this model, APIs are defined in an iterative way that both humans and computers can understand — before any code is ever written. The goal is that every team speaks the same language, and every tool they use leverages the same API design. The crucial difference here compared to an API-first approach is that, while the API is incredibly important, the design process is what ensures all stakeholders are involved, and their needs are satisfied in the creation.

Design-First begins with both technical and non-technical individuals from each of the functions involved participating in the process of writing a contract that defines the purpose and function of the API (or set of APIs). Obviously, this approach requires some time upfront spent on planning. This phase aims to ensure that when it comes time to start coding, developers are writing code that won't need to be scrapped and rewritten later down the line. This helps create iterative, useful APIs that, in turn, lead to a better, more scalable API program — and value to your business — as a whole.

Regardless of which approach you choose, the most critical thing to think about is how to deliver positive experiences for stakeholders, including end-users, third-party or in-house developers, and even folks from the rest of the company who may have a role. I think of APIs like technology ambassadors — the digital face of a brand — as they form a network of internal and external connections. And as such, they should be designed and crafted with care, just like any other product or service that your company offers.

Successful API programs engage users early and keep them involved in the design process to ensure development tracks with users' expectations. This step is equally important for internal and external APIs. The users involved in the process are likely to become the early adopters who will initially drive acceptance and use of the product.

## The Benefits of a Design-First Approach to API Development

'But, Steve! (you may be asking) How can this approach benefit developers, end users, internal partners, and more?' Great question. Let's start with the most valuable API development asset: developers.

- **Improved Developer Experience**: There are numerous benefits to the Design-First approach but by far one the most important, and (dare I say 'buzziest'), is creating a positive developer experience. As someone who came up through the developer path myself, I can tell you that being tasked with fixing poorly written APIs is a nightmare.

Without intentional design, the development process can become chaotic, clunky, and disjointed. It creates disconnects between the developers, security, governance, and documentation teams. The end result is vast amounts of pressure heaped on the developers' shoulders to push an API all the way to the end.

The API specs, governance, design, development, and documentation start from the same page and are developed and maintained simultaneously by taking a design-first approach. This built-in coordination keeps developers focused on developing solutions and prevents them from slowing down due to API clean-up work. Design-first is developer-friendly, keeping them engaged and focused on the end result, not distracted or delayed by poorly-written and inconsistent APIs. Ultimately, happy developers = a happy API program.

- **Engineering Efficiency and Cost Savings**: When quality components are developed and maintained using a design-first approach, they can be reused for future APIs. You only need to build each component once, saving a ton of time and money for your technology team. Reusability for all components allows for significant cost savings in development time while also getting new APIs to market faster.

- **Improved API Security**: Although APIs have blossomed in the past few years, that also makes them a prime target for hackers and malware due to their visibility and reputation for having design flaws and vulnerabilities. Without a proper design in place, exposed API endpoints can be easily exploited by hackers. As a technology leader, security is always a top concern of mine, and intentional design ensures that security is built into your API strategy from the ground up.

- **Better Coordination Among Internal Teams**: Large and cross-functional teams are notoriously hard to coordinate, and it's even more of a challenge to bring additional stakeholders in midstream and keep everyone on the same page. With this approach, relevant stakeholders are involved from the outset and can build their input into developing the API. Including all stakeholders, even those who may be non-technical users of that API ensures that the API is designed to be inclusive and meet all possible needs.

- **Increased Innovation & Growth Opportunities**: APIs are catalysts for growth, increased innovation, and improved user experiences. It's easier than you may expect to go from an expensive, inefficient process to something that works better for your developers, customers, and bottom line. Over 40% of large companies today report utilizing 250 or more APIs, according to the [Developer Survey 2020 report](https://rapidapi.com/wp-content/uploads/2021/01/2020-2021-Developer-Survey-Report.pdf), and that opportunity for continued growth will only rise. It is estimated that [2000 new public APIs are published daily](https://www.programmableweb.com/news/apis-show-faster-growth-rate-2019-previous-years/research/2019/07/17).

With how prolific and widespread APIs have become, they now touch virtually every industry, sector, and location in the world. As the designated integration point between technologies (by name!) APIs are prevalent in all kinds of companies, from car manufacturers to healthcare. For example, one of our largest customers is a global beer manufacturer. I'm going to "go out on a limb" and say: that's not the first company you would think of as a leader in API design and development.

We know that it's no joke that many of the most significant opportunities for innovation — across industries and the public and private sector — are through APIs. That is why it's critical they be designed correctly to scale with that growth.

## Okay, But does Design-First *Actually* Work?

I get it; you may not want to take my word that design-first is a game-changer for your API program, but here are a few stories that speak for themselves.

Transact, is a campus services technology company. They cut 80% of their API development time by adopting the design-First approach to their API program. (Remember that time efficiency and cost savings I mentioned previously? Voila.)

> "Even better than the reduced development time is the increased collaboration with other teams when we use the design-first approach, we get feedback earlier and the result ends up being more polished and professional," shared Paul Trevino, Senior Engineering Manager at Transact.

Another, Calendly, the meeting scheduling software (which I'm not shy to admit I use constantly), has an API team that built an entirely new API platform using a design-first approach.

> "The design-first approach, for us, leads to a higher-quality implementation, consistency, governance, and documentation that's never out of date. That's something that definitely speaks to developers both producing and consuming the API; documentation that's never out of date and that's high quality, detailed, all that good stuff that you expect from a mature platform," shared Calendly's Dmitry Pashkevich, an Application Architect.

## How to Implement a Design-First Approach

Alright, so you've stuck with me this long, which means you're probably ready to hear how you can implement one of these fancy Design-First approaches to your own organization's API strategy. Focusing on decentralized governance, increased automation, and consistency through a tool such as style guides will lead you down a design-first path of success and improve your API program by minimizing complexity for API consumers.

### Start With Consistency & Good Governance

They say if you have nothing else to your name or reputation, at least you can have consistency. There's a reason a company like Starbucks does so well: it is because we know what to expect. There is comfort in having a predictable experience, no matter where in the world you are when you walk into one.

What is true for real-world human interfaces such as Starbucks is also true for APIs. It is essential to keep the design of an API consistent — to make it a predictable experience. As we say here at Stoplight, consistency is the primary difference between a bunch of APIs and something that feels like one cohesive platform.

To enforce consistency and keep that thoughtful design going, implementing guidelines into your API strategy will make it possible to enforce standards. API style guides are easily consumable instructions for all relevant information that a developer or team of developers will need to create or work with APIs. Style guide references typically provide a basic understanding of the API(s), what they are, and best practices for how to create, use, and enforce them at your organization.

Along with that consistency comes a need for strong governance of your API program. Standardization will soon fall to the wayside if your program is not designed with API governance in mind. API governance is all about applying rules and policies to your APIs, such as descriptions, contracts, designs, protocols, reviews, etc. This standardization in the designing of your API helps ensure that all your APIs remain consistent even when different developers design and build them. An effective governance program helps organizations ensure that every API is high-quality and discoverable — whether they create a few APIs or a few thousand.

### View Your APIs as Products

The next step in applying the design-first approach is to make sure you are viewing your APIs as products. API-as-a-Product is an established and increasingly popular software development concept, which essentially just means that you're operating your API with a lens that it's just like any other product someone consumes or uses. APIs, like products, should be easy-to-use and actively maintained and supported.

The process of API design includes the planning and architectural decisions you'd make for a product. Just like product design, API design informs the user experience, and good design principles meet initial expectations and continue to behave consistently and predictably.

### Gaining Stakeholder Buy-In

One of the most important parts of implementing a design-first approach is gaining the favor of all relevant stakeholders (which is, *again*, the primary point of operating from a design-first approach). Creating the foundation for your API program means that you need developer buy-in, executive buy-in, feedback from early end-users, and the opinions of any other partners or internal folks that may interact with the API.

My recommendation to woo the business leaders is to focus on (probably not surprisingly) the business value, ideally, including solid metrics and proof-of-concept. Even if you only have minimal data to show off at first, take them up the chain and show constant improvement over time. The momentum will speak for itself as you get the ball rolling. Once you have that buy-in, think about how to parlay this mandate into transforming the organization and what key players are needed to make it happen.

To woo the developers, create a safe space that they feel comfortable voicing their opinions in and a space to test out what will work (with a set of guidelines, of course). Find stakeholders with relational clout within a development organization and establish a way to collaborate with them on your API strategy (best enabled through an API Design tool, I might shamelessly add). Your API program team must be a multidisciplinary one, with a wide range of experiences, use cases, and skills.

Lastly, be sure and get feedback from early adopters and end-users (throughout) about the actual experience of consuming the API. Keep in mind that if it's not used, it's not useful, so it needs to be a positive experience. Developers can sometimes lose sight of that end-user experience, but a design-first approach ensures that this is always top of mind.

## So, Did I Convince You?

I appreciate you joining me in delving into the design-first discovery. I hope you can now see how it is a linchpin for ensuring a sound, robust, and easily scalable - or, heck, your masterpiece - API and even API program. (And, if you choose not to heed my warning, here's [what happens when you don't follow a design-first approach](https://blog.stoplight.io/the-wrong-way-to-api-what-happens-when-you-dont-follow-a-design-first-approach).)

So, if you need a guide to get you going, I strongly recommend (though I am biased) that you check out Stoplight's resources around [API design.](https://stoplight.io/api-design-guide/) 

Overall, we know APIs are taking the world by storm, and nearly everyone is looking for a way to build successful programs to innovate and grow their business. By now, I hope you can see that you won't create an API masterpiece if you don't design first.

Personally, I like to think that developers are some of the world's best designers and creators, and I can't wait to see what gets created next with the design-first approach in mind.
