:::tip[]
Vault secrets is available on [Apidog Enterprise plan](https://apidog.com/pricing).
:::

When using Apidog, you can fetch secrets from external vaults such as HashiCorp Vault, Azure Key Vault and AWS Secrets Manager, and use them like global variables when sending requests.

Administrators can configure integration with external vaults for teams and projects, and then users only need to log in with OAuth2.0 or enter their own access token to fetch secrets from the external vault.

The fetched secrets are encrypted and stored in your local client, ensuring they remain private and are not shared with anyone.

## Configure vault providers

- On the team resources page, you can configure multiple vault providers for your team. Each provider can be assigned to different projects based on requirements.

![SCR-20241212-qpuy.png](https://api.apidog.com/api/v1/projects/544525/resources/348641/image-preview)

- Within the project, you have the option to either customize the vault provider for that specific project or use a provider configured at the team level.

![SCR-20241213-pnah.png](https://api.apidog.com/api/v1/projects/544525/resources/348673/image-preview)

Learn more details here:
- [Hashi<PERSON><PERSON><PERSON> Vault](/hashicorp-vault-780714m0)
- [Azure Key Vault](/azure-key-vault-781845m0)
- [AWS Secrets Manager](/aws-secrets-manager-781902m0)

## Link and fetch secrets

- Click the button next to the environment menu (upper-right corner of the project) and select`Vault Secrets`.
- In the`value`input box, configure the metadata for the secret stored in the external vault (e.g., engine, path, key). The required metadata varies depending on the vault provider.

![SCR-20241213-pnfw.png](https://api.apidog.com/api/v1/projects/544525/resources/348675/image-preview)

- Click`Fetch Secrets`to fetch the secret, which will be securely encrypted and stored on your local client.

![SCR-20241213-pnhv.png](https://api.apidog.com/api/v1/projects/544525/resources/348676/image-preview)

## Use secrets

- Secrets can be used in any context where a variable is supported, following the syntax `{{vault:key}}`.

![SCR-20241213-pnnc.png](https://api.apidog.com/api/v1/projects/544525/resources/348677/image-preview)

- Within the script, you can use `await pm.vault.get("key")` to get the value of the secret. If you use `console.log` to print the value, the value will be masked.

![SCR-20241213-pojf.png](https://api.apidog.com/api/v1/projects/544525/resources/348678/image-preview)

- Secret values are never shared with team members. However, variable names and metadata are shared to eliminate the need for reconfiguration. Team members can fetch the secrets using proper authorization, ensuring a balance between collaboration and privacy.

## Advantages of using vaults for enterprises

- Secure Storage of Secrets: Vaults provide a secure way to store sensitive information such as API keys, passwords, certificates, and tokens, ensuring that they are protected against unauthorized access.
- Access Control: Vaults allow organizations to define strict access control policies, ensuring that only authorized users or services can access particular secrets.
- Encryption: Vaults often provide built-in encryption to protect data both at rest and in transit, adding an extra layer of security.
- Auditing and Monitoring: Vaults offer auditing and monitoring capabilities to keep track of who accessed which secret and when. This helps in compliance and in detecting any unauthorized access attempts.
- Integration with Other Services: Vaults are designed to integrate seamlessly with other cloud services (including Apidog) and DevOps tools, making it easy to manage secrets in a variety of environments.
- Centralized Management: Vaults provide a centralized way to manage secrets across different applications, services, and environments, simplifying the overhead associated with secret management.
- Risk Reduction: By reducing the chance of credentials being hardcoded into applications or leaking into source code, vaults help mitigate the risk of credential exposure.

## Prerequisites for vault secrets with Apidog

- Secrets must already be stored in [HashiCorp Vault](apidog://link/pages/780714), [Azure Key Vault](apidog://link/pages/781845), or [AWS Secrets Manager](apidog://link/pages/781902).
- The organization or team must be subscribed to the [Apidog Enterprise Plan](https://apidog.com/pricing/).

:::tip[]
Vault secrets is available on [Apidog Enterprise plan](https://apidog.com/pricing).
:::

Apidog supports integration with both the community and cloud editions of HashiCorp Vault. It supports two auth methods: token and OIDC.

## Configure vault provider: Community edition

### Token

- Enter URL. By default, the local Vault service runs on `http://127.0.0.1:8200`.
- Enter [token](https://developer.hashicorp.com/vault/docs/commands/token). The token is NOT uploaded to the server and is NOT shared with others on the team.
- Click "Test Connection". If everything is correct, "Succeeded" will be displayed.

![SCR-20241216-pfss.png](https://api.apidog.com/api/v1/projects/544525/resources/348706/image-preview)

### OIDC

Please [enable and configure OIDC](https://developer.hashicorp.com/vault/tutorials/auth-methods/oidc-auth) auth method first. When configuring a third-party OAuth2.0 service provider, please add Apidog's callback URL.

Then, proceed to Apidog:

- Enter URL. By default, the local Vault service runs on `http://127.0.0.1:8200`.
- Enter auth URL. By default, it is `http://127.0.0.1:8200/v1/auth/oidc/oidc/auth_url`.
- Enter access token URL. By default, it is `http://127.0.0.1:8200/v1/auth/oidc/oidc/callback`.
- Click "Test Connection", and the OAuth2.0 login window will pop up. After you log in, "Succeeded" will be displayed.

![SCR-20241216-pmdt.png](https://api.apidog.com/api/v1/projects/544525/resources/348708/image-preview)

## Configure vault provider: HCP Vault Dedicated (Cloud edition)

### Token

- Enter URL, which you can find in the HashiCorp Cloud Portal. 
- Enter [namespace](https://developer.hashicorp.com/vault/docs/enterprise/namespaces#usage). The default namespace called `admin`.
- Enter token. You can click "Generate Token" in the HashiCorp Cloud Portal to create one. The token is NOT uploaded to the server and is NOT shared with others on the team.
- Click "Test Connection". If everything is correct, "Succeeded" will be displayed.

![SCR-20241216-prxa.png](https://api.apidog.com/api/v1/projects/544525/resources/348709/image-preview)

### OIDC

Please [enable and configure OIDC](https://developer.hashicorp.com/vault/tutorials/auth-methods/oidc-auth) auth method first. When configuring a third-party OAuth2.0 service provider, please add Apidog's callback URL.

Then, proceed to Apidog:

- Enter [namespace](https://developer.hashicorp.com/vault/docs/enterprise/namespaces#usage). The default namespace called `admin`.
- Enter auth URL. The format is `{{VAULT_ADDR}}/v1/auth/oidc/oidc/auth_url`.
- Enter access token URL. The format is  `{{VAULT_ADDR}}/v1/auth/oidc/oidc/callback`.
- Click "Test Connection", and the OAuth2.0 login window will pop up. After you log in, "Succeeded" will be displayed.

![SCR-20241216-pzsl.png](https://api.apidog.com/api/v1/projects/544525/resources/348710/image-preview)

## Link secrets

Whether you use the community edition or the cloud edition of HashiCorp Vault, the way to link secrets is the same.

If you created the secret using the CLI, you will see output like this on the console:

```shell
$ vault kv put -mount=secret hello foo=world

== Secret Path ==
secret/data/hello

======= Metadata =======
Key                Value
---                -----
created_time       2022-06-15T19:36:54.389113Z
custom_metadata    <nil>
deletion_time      n/a
destroyed          false
version            1
```

In the Web UI, the secret just created will appear as shown below:

![SCR-20241216-qgfy.png](https://api.apidog.com/api/v1/projects/544525/resources/348711/image-preview)

To link the secret created above, you need to enter the metadata as shown below:

![SCR-20241216-qeld.png](https://api.apidog.com/api/v1/projects/544525/resources/348712/image-preview)

Finally, click the "Fetch Secrets" button. Then click the eye icon on the right to view the value of the secret.

![SCR-20241216-qoiu.png](https://api.apidog.com/api/v1/projects/544525/resources/348713/image-preview)

:::tip[]
Vault secrets is available on [Apidog Enterprise plan](https://apidog.com/pricing).
:::

Apidog supports integration with Azure Key Vault via OIDC auth method.

## Configure Microsoft Entra ID

To configure your OIDC application, do the following:

- Open your Microsoft Entra ID management portal in a browser.
- Go to **App registrations** and select **New registration**.
- Enter the name of the application, such as "Vault Integration", and click **Register**.
- On the application's **Overview** page in App registrations, copy the **Application (client) ID** and paste it to **Client ID**  field in Apidog.
- Click **Endpoints**.
- Copy the **OAuth 2.0 authorization endpoint (v2)** in Microsoft Entra ID, and paste it to the **Auth URL** field in Apidog.
- Copy the **OAuth 2.0 token endpoint (v2)** in Microsoft Entra ID, and paste it to the **Access Token URL** field in Apidog.
- Go to the application's **Authentication** page in App registrations, click **Add a platform**, then click **Single-page application**.
- Copy the **Callback URL** in Apidog, and paste it to the **Redirect URIs**.
- Return to the home page of Microsoft Entra ID management portal, go to **Enterprise Applications**, and click the application you just registered on.
- On the **Users and groups** page in Enterprise Applications, add users or groups who can access the key vault.

![SCR-20241217-ndqe.png](https://api.apidog.com/api/v1/projects/544525/resources/348724/image-preview)

## Test connection

- Enter the key vault name in Apidog.
- Click "Test Connection", and the OAuth2.0 login window will pop up. After you log in, "Succeeded" will be displayed.

![SCR-20241217-nsro.png](https://api.apidog.com/api/v1/projects/544525/resources/348725/image-preview)

## Link secrets

Assume there is a secret named "foo" in Azure Key Vault as follows:

![SCR-20241217-nsmi.png](https://api.apidog.com/api/v1/projects/544525/resources/348726/image-preview)

To link the secret, you need to enter the metadata as shown below:

![SCR-20241217-ntkv.png](https://api.apidog.com/api/v1/projects/544525/resources/348727/image-preview)

Finally, click the "Fetch Secrets" button. Then click the eye icon on the right to view the value of the secret.

![SCR-20241217-ntqj.png](https://api.apidog.com/api/v1/projects/544525/resources/348728/image-preview)


:::tip[]
Vault secrets is available on [Apidog Enterprise plan](https://apidog.com/pricing).
:::

Apidog supports integration with AWS Secrets Manager via the access key pair for IAM users.

## Configure AWS

Please create IAM users for people who need to use AWS Secrets Manager, and create the access key pair (access key ID and secret access key).

## Test connection

- Enter the AWS [region](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/using-regions-availability-zones.html#concepts-regions) code, your Access Key ID, and Secret Access Key. The access key pair is NOT uploaded to the server and is NOT shared with others on the team.

- Click "Test Connection". If everything is correct, "Succeeded" will be displayed.

![SCR-20241217-ochp.png](https://api.apidog.com/api/v1/projects/544525/resources/348729/image-preview)

## Link secrets

Assume there is a secret named "test/foo" in AWS Secrets Manager as follows:

![SCR-20241217-oamd.png](https://api.apidog.com/api/v1/projects/544525/resources/348730/image-preview)

To link the secret, you need to enter the metadata as shown below:

![SCR-20241217-oizq.png](https://api.apidog.com/api/v1/projects/544525/resources/348731/image-preview)

Finally, click the "Fetch Secrets" button. Then click the eye icon on the right to view the value of the secret.

![SCR-20241217-ojbd.png](https://api.apidog.com/api/v1/projects/544525/resources/348732/image-preview)

If you want to extract the value in a key-value pair, select "Key/Value" as the secret type and enter the name of the key.

![SCR-20241217-okgy.png](https://api.apidog.com/api/v1/projects/544525/resources/348733/image-preview)