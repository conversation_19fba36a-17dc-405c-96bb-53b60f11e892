In API development, maintaining consistency across endpoints is critical. Apidog addresses this by offering the `Common Fields` feature, which allows for the reuse of fields with identical names and descriptions across different endpoints.

## Configure common fields

To modify or set up your common fields, follow these steps:


<Steps>
  <Step title="Access Field Settings">
    Click on the menu button located on the Description section within the endpoint spec interface.
      
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/353290/image-preview" style="width: 640px" />
</p>
   
      
:::highlight purple
Alternatively, you can manage common fields under `Settings` - `Project Resources` - `Common Fields`.
:::
  </Step>
  <Step title="Enter Details">
    Type in the field name, description, and any other necessary information.
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/353291/image-preview" style="width: 640px" />

</p>
  </Step>
  <Step title="Save the Field">
    Confirm your entries by clicking on the "Save" button.
  </Step>
</Steps>

### Import / Bulk Edit

Supports importing or bulk editing commonly used fields using either comma or colon as separators.

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341115/image-preview" style="width: 440px" />
</p>

## Utilizing Common Fields

When entering field names in an endpoint, you will be presented with pre-defined Common Fields for selection. Choosing a Common Field will automatically populate its Description.

<p style="text-align: center">

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/353292/image-preview)
</p>