# Hands on with <PERSON>, the Open Source Claude Code GUI

For Developers, Anthropic's Claude Code has emerged as a powerful and flexible tool for developers. Its agentic nature and command-line interface (CLI) provide a high degree of control and customizability. However, the CLI-first approach can sometimes lead to what some developers call "terminal chaos," with a lack of session history, visual dashboards, and intuitive project management.

Enter Claudia, a new, open-source graphical user interface (GUI) for Claude Code. Developed by the Y Combinator-backed startup Asterisk, <PERSON> aims to bridge the gap between the power of Claude Code and the user-friendliness of a desktop application. It provides a beautiful and intuitive command center for all your Claude Code activities, from managing interactive sessions to creating custom AI agents.

This comprehensive tutorial will guide you through everything you need to know to get started with <PERSON>, explore its powerful features, and supercharge your AI-assisted development workflow.

### **Getting Started with <PERSON>**

![](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-20-at-9.38.16-PM.png)

At the time of writing, <PERSON> is a new project, and the easiest way to get started is by building it from the source. While native installers for macOS, Windows, and Linux are on the horizon, building from source is a straightforward process for anyone comfortable with the command line.

#### **Prerequisites**

Before you can build <PERSON>, you'll need to have the following software installed on your system:

- **Bun:** A fast JavaScript runtime and bundler. You can find installation instructions at [bun.sh](https://bun.sh/).
- **Rust:** A systems programming language. You can install it from [rust-lang.org](https://rust-lang.org/).

#### **Installation**

First, you'll need to clone the Claudia GitHub repository to your local machine. Open your terminal and run the following command:Bash

```bash
git clone https://github.com/getAsterisk/claudia.git
```

Once the repository is cloned, navigate into the new `claudia` directory. From there, you can install the necessary dependencies using `bun`:Bash

```mipsasm
cd claudia
bun install
```

With the dependencies installed, you are ready to build the application. For development purposes, running the application with hot reload is ideal. To do this, use the `tauri dev` command:Bash

```routeros
bun run tauri dev
```

If you prefer to create a final, optimized production build, use the `tauri build` command instead. This will generate a native installer for your specific operating system, which you can find in the `src-tauri/target/release/bundle/` directory.

#### **First-Time Setup**

Upon launching Claudia for the first time, you'll be greeted by a welcome screen. The application will automatically detect your `~/.claude` directory, where your Claude Code projects and settings are stored. You'll then be presented with two main options: "CC Agents" and "CC Projects."

### **Core Features of Claudia - A Deep Dive**

Claudia is packed with features designed to make your interaction with Claude Code as seamless and productive as possible. Let's take a deep dive into the core functionalities.

#### **Project and Session Management**

One of the most significant advantages of Claudia is its visual project and session management. Instead of navigating through directories in your terminal, you get a clean and organized overview of all your Claude Code projects.

- **Project View:** The main screen displays a list of your projects. You can easily see the project name and other relevant details.
- **Session View:** Clicking on a project will take you to the session view. Here, you'll see a list of all the coding sessions you've had within that project, with the first message and timestamp for each.
- **Resume or Start New:** From the session view, you can either resume a previous session with a single click or start a new one. This makes it incredibly easy to pick up where you left off.

#### **CC Agents (Custom Agents)**

This is where the power of Claudia truly shines. "CC Agents" are custom, reusable AI agents that you can design to perform specific tasks. This feature allows you to automate repetitive coding tasks and create a personalized AI assistant tailored to your workflow.

**Creating a Custom Agent:**

The process of creating a new agent is highly intuitive. You begin by navigating to the "CC Agents" section and selecting the option to create a new agent. From there, a configuration screen allows you to define your agent's behavior and identity.

You'll give your agent a descriptive name and choose an icon to make it easily identifiable. The most crucial step is crafting the **system prompt**. This prompt defines the agent's purpose, personality, and instructions, guiding its behavior during interactions. Next, you select the specific Claude model you want the agent to use, such as Claude 3.5 Sonnet. For security, you can apply a **sandbox profile** to restrict the agent's access to your filesystem and network, ensuring it only operates within the boundaries you set.

**Example of a Custom Agent:**

You could create a "React Component Generator" agent with a system prompt that instructs it to generate a new React component file with a specific file structure, boilerplate code, and a corresponding Storybook file.

#### **Timeline and Checkpoints (Session Versioning)**

This is a game-changing feature that brings the concept of version control to your AI coding sessions. The timeline and checkpoints feature allows you to save snapshots of your session at any point, creating a visual history that you can navigate and revert to.

- **Create Checkpoints:** At any point during a session, you can create a checkpoint, which is like a commit in Git.
- **Visual Timeline:** Claudia displays your session history as a branching timeline, making it easy to see the evolution of your code.
- **Restore and Fork:** You can instantly jump back to any checkpoint with a single click. You can also "fork" a session from a specific checkpoint to explore a different approach without losing your original work.
- **Diff Viewer:** Claudia includes a built-in diff viewer, so you can see exactly what changed between checkpoints.

#### **Usage Analytics Dashboard**

If you're a heavy user of the Claude API, you know how important it is to keep track of your usage and costs. Claudia provides a comprehensive analytics dashboard to monitor your API consumption in real-time.

- **Cost Tracking:** See how much you're spending on the Claude API, broken down by project and model.
- **Token Analytics:** Get a detailed breakdown of your token usage, helping you optimize your prompts and reduce costs.
- **Visual Charts:** The dashboard features beautiful charts and graphs that visualize your usage trends over time.

#### **MCP Server Management**

For advanced users, Claudia simplifies the management of Model Context Protocol (MCP) servers. The app provides a central UI to register, configure, and test your MCP servers, eliminating the need for manual configuration files.

#### **CLAUDE.md Management**

Claudia recognizes the importance of documentation and provides a built-in editor for `CLAUDE.md` files. These files can be used to store project-specific instructions, notes, and context for Claude. The editor features a live preview and syntax highlighting, making it a pleasure to use.

### **Security and Advanced Topics**

#### **Security**

Claudia is built with security in mind. It implements multiple layers of security to protect your code and data:

- **Process Isolation:** Agents run in separate, sandboxed processes.
- **Filesystem Access Control:** You can define whitelist-based file access for your agents.
- **Network Restrictions:** Control the external network connections that your agents can make.
- **Local-First Design:** All your data stays on your machine. Claudia does not collect any data.

#### **Contributing to Claudia**

Claudia is an open-source project, and the developers welcome contributions from the community. If you're interested in helping to improve Claudia, you can find the project on GitHub. You can contribute by fixing bugs, adding new features, improving the documentation, or enhancing the UI/UX.

### **Conclusion**

Claudia is a powerful and promising new tool that has the potential to revolutionize the way developers interact with Claude Code. Its intuitive GUI, powerful features like custom agents and session versioning, and commitment to security make it an essential addition to any AI-assisted development workflow.

Whether you're a seasoned Claude Code user or just getting started with AI-powered development, Claudia is a tool that is well worth exploring. It's a testament to the power of open-source software and the creativity of the developer community. As Claudia continues to evolve, we can expect to see even more innovative features that will further blur the lines between human and artificial intelligence in the world of software development.
