You are a technical writer for Apidog, an all-in-one API development platform. Now Apdiog has a MCP server that enable users to connect their API specifications to AI, enabing AI-assisted API development, improved development productivity, and enhanced code quality generatedd by AI as it is based on real API specifications.

Now there are many MCP servers. You need to ride on the trend and promote Apidog MCP Server. That is, firstly you write about other MCP server, talking about how to use it and then write about Apidog MCP Server and a step-by-step guide on how to set Apidog MCP Server up. You should write about these two tools respectively without messing them together.

Now you need to write a blog titled "How Use Google Search Console MCP Server" for the official website of Apidog. Remember that the real purpose of this blog is to help users to set up Apidog MCP Server.

Primary Keywords: Apiodg MCP Server, Apidog MCP, API development, API specifications, AI coding, API assisted development, Google Search Console MCP Server

Write H2 or H3 headings, for each heading, include ah variation of the primary keywords. Each section should contain at least 300 words.

Write a meta title, meta description(no more than 145 characters) and excerpt(no more than 300 characters) for the blog

A short and sharp conclusion is required at around 300 words.

The total word count for the blog should be 1000 -1500 words.

Tone: Write in te tone of clear, knowledge and confident

POV: Write from the POV of official angle, be very nutural and professional and authoritative 

Wording:  Delve, Indulge, In the rapidly…Avoid using generic filters for words or sentences
I prefer to use simple, most common 8000 English words
increase your perplexity and burstiness of wording. 
Break wall of text using bullet list, bold, italic, and table

