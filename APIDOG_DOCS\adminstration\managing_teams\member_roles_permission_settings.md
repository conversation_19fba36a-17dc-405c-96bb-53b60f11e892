Member Roles & Permission Settings
Member roles and permissions are divided into two categories: team and project.

Team roles and permissions
Default roles and permissions
Apidog offers built-in team roles with predefined permissions designed to cater to various needs. By assigning a role to a team member, you can effectively manage their permissions and access levels within the team.

The team-level permissions are structured around four primary roles: Team Owner, Team Admin, Team Member, and Guest. Below is a detailed breakdown of their respective permissions:

Resource Category	Resource Name	Permissions	Team Owner	Team Admin	Team Member	Guest
Team Management	Members/Roles	View Team Member details	✅	✅	✅	❌
Invite Team Members	✅	✅	❌	❌
Assign/Remove Team Member Roles	✅	✅	❌	❌
View Project Roles	✅	✅	❌	❌
Add/Edit/Delete Project Roles	✅	✅	❌	❌
Team Settings	Edit Team Name	✅	✅	❌	❌
Transfer Team	✅	❌	❌	❌
Dismiss Team	✅	❌	❌	❌
Project Operations	Create New Projects	✅	✅	❌	❌
Clone a Project	✅	✅	❌	❌
Delete/Transfer a Project	✅	✅	❌	❌
Edit Project Name	✅	✅	❌	❌
NOTE
Custom permissions for team roles are not yet supported.

Guest
Users from "Project invitations" but not "Team invitations"are considered Guests at the team level.

Guests can only access projects they're invited to.

Guests have similar team permissions as team members but are excluded from new project permission settings.

Guests count towards user limits in plan selection.

Guests are also counted as team members and will be billed according to the total number of team members.

Setting team permissions
Set team-level permissions by assigning roles via My Team > Member page > Member Details > Role.

setting-team-roles.png
Project roles and permissions
Default roles and permissions
Apidog offers built-in project roles with predefined permissions to streamline access management. The project roles are categorized into four types: Admin, Editor, Read-only, and Forbidden. Below is a detailed breakdown of the permissions for each role.

Resource Category	Resource Name	Permissions	Admin	Editor	Read-only	Forbidden
Branch Management	Sprint Branch	View, Switch Branches	✅	✅	✅	❌
Merge Branches	✅	✅	❌	❌
View/Submit Merge Request	✅	✅	❌	❌
Add, Delete, Modify, Merge Protected Branch Content	✅	❌	❌	❌
API Versions	View, Switch API Versions	✅	✅	✅	❌
Endpoint Management	Endpoints (including Cases, Markdown, WebSocket, API Documentation, etc.)	View, Run Endpoints	✅	✅	✅	❌
Add, Delete, Modify Endpoints	✅	✅	❌	❌
Generate Code	✅	✅	❌	❌
Add, Delete, Modify Cases	✅	✅	❌	❌
Schemas	View, Reference Schemas	✅	✅	✅	❌
Add, Delete, Modify Schemas	✅	✅	❌	❌
Components	View, Reference Components	✅	✅	✅	❌
Add, Delete, Modify Components	✅	✅	❌	❌
Requests	View, Send Requests	✅	✅	✅	❌
Add, Delete, Modify Requests	✅	✅	❌	❌
Trash	View	✅	✅	❌	❌
Restore	✅	✅	❌	❌
Permanently Delete	✅	❌	❌	❌
Automated Tests	Test Scenarios	View, Run Functional Tests	✅	✅	✅	❌
Run Performance Tests	✅	✅	✅	❌
Add, Delete, Modify	✅	✅	❌	❌
Export to External Programs	✅	✅	❌	❌
Scheduled Tasks	View/Run Now	✅	✅	✅	❌
Add, Delete, Modify	✅	✅	❌	❌
Test Reports	Delete	✅	✅	❌	❌
Environment Management	Global Variables	View, Edit Current Values	✅	✅	✅	❌
Add, Delete, Modify	✅	✅	❌	❌
Global Params	View	✅	✅	✅	❌
Add, Delete, Modify	✅	✅	❌	❌
Vault Secrets	Add, Delete, Modify, Fetch	✅	✅	❌	❌
Environments	View, Edit Current Values	✅	✅	✅	❌
Add, Delete, Modify	✅	✅	❌	❌
Documentation Sharing	Quick Share	View	✅	✅	✅	❌
Add, Delete, Modify	✅	✅	❌	❌
Publish Doc Sites	View, Preview	✅	✅	✅	❌
Publish Settings	✅	❌	❌	❌
Project Settings	Basic Settings	View	✅	✅	✅	❌
Modify	✅	❌	❌	❌
Clone Project	✅	❌	❌	❌
Member Management	View	✅	❌	❌	❌
Add	✅	❌	❌	❌
Assign/Remove Member Roles	✅	❌	❌	❌
Feature Settings	View	✅	✅	✅	❌
Add, Delete, Modify	✅	❌	❌	❌
Notification Targets	View	✅	✅	✅	❌
Add, Delete, Modify	✅	✅	❌	❌
Notification Events	View	✅	✅	✅	❌
Add, Delete, Modify	✅	✅	❌	❌
Common Parameters	View, Reference	✅	✅	✅	❌
Add, Delete, Modify	✅	✅	❌	❌
Sprint Branches	View	✅	✅	✅	❌
Add, Delete, Modify, Protect, Archive, Restore	✅	✅	❌	❌
API Versions	View	✅	✅	✅	❌
Add, Delete, Modify	✅	✅	❌	❌
Public Scripts	View, Reference	✅	✅	✅	❌
Add, Delete, Modify	✅	✅	❌	❌
Database Connections	View, Reference	✅	✅	✅	❌
Add, Delete, Modify	✅	✅	❌	❌
Custom Functions	View, Reference	✅	✅	✅	❌
Add, Delete, Modify	✅	✅	❌	❌
Import Data	Manual Import	✅	✅	❌	❌
Scheduled Import (Manual Trigger)	✅	✅	❌	❌
Scheduled Import Settings	✅	❌	❌	❌
Export Data	Export data	✅	✅	❌	❌
Request History	Local Request History	View	✅	✅	✅	❌
Share	✅	✅	✅	❌
Shared Request History	View	✅	✅	✅	❌
Delete	✅	✅	❌	❌
Setting project permissions
1.
When sending an invitation, specify the project and set the invitee's permission level.


2.
Alternatively, set project-level permissions via My Team > Members > Project Role.

Team Permissions Settings

Custom roles and permissions
The Roles and permissions feature allows you to customize permissions for specific roles, ensuring they align with the needs of users in various scenarios. Currently, this feature supports custom configurations for project roles only. Customization for team or organization roles is not yet available.

Customizing project roles
If the built-in project roles in Apidog don't meet your specific needs for permission control, you can create custom project roles to address this gap.

NOTE
This feature is available on Apidog Enterprise plan.

To create a custom project role, navigate to the "Team -> Members -> Roles and permissions" or "Organization -> Members -> Roles and Permissions". Click "+ Add" to start creating a custom project role.

You can freely set the custom role name, and then select the desired project permissions for it.

Important Notes:

1.
If you check the "All Permissions" option for a specific module, any new features added to that module will automatically be assigned to the custom role.

2.
Default roles (such as Editor or Read-only) cannot be renamed or edited. However, you can copy these roles and modify them to quickly create a new custom role.

