Apidog supports multiple languages for dynamic mock data generation through the locale feature of Faker.js. This allows you to generate localized data, such as names and addresses, in the language or culture of your choice.

## Default Locale Behavior

By default, the faker language follows your project language setting.  
- You can set your project language in **Project Settings > Basic Settings**.  
- Your project's language will be used as the default locale for all dynamic values.

<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/354049/image-preview)
</Background>
    
## Customizing Faker Locale

If you need to set the faker locale independently, you can do so in **Project Settings > Feature Settings > Mock Settings**.  
- This setting allows you to override the default and generate mock data using a specific locale, regardless of the project language.

<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/354050/image-preview)
</Background>

## Specifying Locale in Expressions

You can also override the locale for individual fields using the `locale` parameter in the mock expression:

- **Mock Syntax:**  
  ```
  {{$person.fullName(locale='ja')}}
  ```
- **Example Result:**  
  ```
  田中 太郎
  ```

This flexibility allows you to mix different languages within the same mock response if needed.


## Setting Time Zone for Mock Data

When generating date and time fields, you can also control the time zone:

- **Project-Level Time Zone:**  
  Set a default time zone for all dynamic date values in **Project Settings > Feature Settings > Mock Settings**. This ensures that all generated dates/times use the specified time zone unless overridden.

- **Field-Level (Expression) Time Zone:**  
  Override the time zone for individual date or time fields by specifying the `timeZone` parameter in the dynamic value expression.

  - **Mock Syntax:**  
    ```
    {{$date.recent(days=1, timeZone='Asia/Tokyo')}}
    ```
  - **Example Result:**  
    ```
    2024-06-13T16:48:12+09:00
    ```

  - **Another field-level example:**  
    ```
    {{$date.between(from='2024-01-01', to='2024-12-31', timeZone='Europe/London', format='yyyy-MM-dd HH:mm:ss')}}
    ```
  - **Example Result:**  
    ```
    2024-03-15 04:26:52
    ```

This flexibility allows you to generate date and time data that accurately reflects any required region or time zone in your mock responses.