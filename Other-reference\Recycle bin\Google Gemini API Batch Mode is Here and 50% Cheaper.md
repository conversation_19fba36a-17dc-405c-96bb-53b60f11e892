> **Pro Tip:** Want to streamline your API development and testing? Apidog is the all-in-one platform for designing, debugging, and documenting APIs—trusted by modern teams. Try it for free!

# Google Gemini API Batch Mode: High-Volume AI at Half the Price

Google’s Gemini API just got a major upgrade: Batch Mode. This new feature is a game-changer for anyone running large, asynchronous AI jobs—and it’s now 50% cheaper than standard API calls.

- Batch Mode is built for scale, letting you process huge jobs (up to 2GB JSONL files) with results delivered within 24 hours, all at half the usual price.
- Optimizations like Context Caching and built-in tools (including Google Search) make it even more efficient.
- The workflow is simple: upload your data, create a job, and retrieve your results—no complex setup required.

Let’s dive into how Batch Mode works and why it’s a must-have for high-throughput AI projects.

## Gemini API Batch Mode Pricing: 50% Off

The headline feature? Cost. Every job submitted via Batch Mode is priced at **half the standard rate** for the same Gemini model. Whether you use `gemini-2.5-pro`, `gemini-2.5-flash`, or another supported model, both input and output tokens are billed at a 50% discount. This makes it practical to run massive jobs—like analyzing terabytes of data or generating content at scale—without breaking the bank.

## How to Use Gemini API Batch Mode: Step-by-Step

Batch Mode is designed for simplicity. Here’s how to get started using the Google GenAI Python SDK.

![](https://assets.apidog.com/blog-next/2025/07/image-143.png)

### 1. Prepare Your Input File

Batch Mode expects a **JSON Lines (JSONL)** file, with each line representing a single request. Each JSON object must include:
- `key`: A unique identifier for the request
- `request`: The payload, just like a standard Gemini API call

**Example `batch_requests.jsonl`:**

```json
{"key": "request_1", "request": {"contents": [{"parts": [{"text": "Explain how AI works in a few words"}]}]}}
{"key": "request_2", "request": {"contents": [{"parts": [{"text": "Summarize the key benefits of context caching in LLMs."}]}]}}
{"key": "request_3", "request": {"contents": [{"parts": [{"text": "Write a python function to reverse a string."}]}]}}
```

### 2. Upload and Create a Batch Job

Use the Python SDK to upload your file and create a batch job:

```python
import google.generativeai as genai

# genai.configure(api_key="YOUR_API_KEY")
uploaded_batch_requests = genai.upload_file(path="batch_requests.jsonl")

batch_job = genai.create_batch_job(
    model="gemini-2.5-flash",  # Or "gemini-2.5-pro", etc.
    requests=uploaded_batch_requests,
    config={
        'display_name': "MyFirstBatchJob-1",
    },
)
print(f"Created batch job: {batch_job.name}")
print(f"Initial state: {batch_job.state.name}")
```

The job is created instantly and starts in the `JOB_STATE_PENDING` state.

### 3. Monitor Your Job

Batch jobs are asynchronous. You can check the status at any time:

```python
retrieved_job = genai.get_batch_job(name=batch_job.name)
print(f"Current job state: {retrieved_job.state.name}")
```

Possible states include:
- `JOB_STATE_PENDING`, `JOB_STATE_RUNNING`, `JOB_STATE_SUCCEEDED`, `JOB_STATE_FAILED`, `JOB_STATE_CANCELLING`, `JOB_STATE_CANCELLED`

Jobs always finish within 24 hours.

### 4. Download and Process Results

When your job is done (`JOB_STATE_SUCCEEDED`), download the results as a JSONL file. Each line matches your original request by `key`.

```python
if retrieved_job.state.name == 'JOB_STATE_SUCCEEDED':
    result_file_metadata = retrieved_job.result_file
    result_file_content_bytes = genai.download_file(name=result_file_metadata.name).read()

    file_content = result_file_content_bytes.decode('utf-8')
    for line in file_content.splitlines():
        print(line)
elif retrieved_job.state.name == 'JOB_STATE_FAILED':
    print(f"Job failed with error: {retrieved_job.error}")
```

**Example output line:**

```json
{"key": "request_1", "response": {"candidates": [{"content": {"parts": [{"text": "Artificial intelligence enables machines to learn and reason."}]}}]}}
```

## Advanced Features: Context Caching & Tool Use

Batch Mode isn’t just about scale and savings. It also supports:

- **Context Caching:** Cache a large shared context (like a long document) so it’s not re-processed for every request. This saves tokens and speeds up processing.
- **Built-in Tools:** Use Google Search and other tools at scale. For example, analyze thousands of URLs or summarize web content in bulk.

**Real-world examples:**
- **Reforged Labs** uses Batch Mode to analyze and label video ads, cutting costs and boosting speed.
- **Vals AI** benchmarks foundation models with huge query sets, bypassing synchronous API rate limits.

## Why Gemini API Batch Mode Matters

Gemini API Batch Mode is a breakthrough for large-scale, asynchronous AI processing. With 50% lower costs, a simple file-based workflow, and advanced features like context caching, it’s the go-to solution for anyone looking to run high-volume AI jobs efficiently and affordably.
