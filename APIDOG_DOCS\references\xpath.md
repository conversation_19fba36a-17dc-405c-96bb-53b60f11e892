XPath
You can use XPath to select nodes or sets of nodes in an XML document. Nodes are selected by following a path or steps.

XML Example
We will use this XML document in the following example.

<?xml version="1.0" encoding="UTF-8"?>
<bookstore>
  <book>
    <title lang="eng"><PERSON>&</title>
    <price>29.99</price>
  </book>
  <book>
    <title lang="eng">Learning XML</title>
    <price>39.95</price>
  </book>
</bookstore>
Node Selection
You can use XPath to select nodes or sets of nodes in an XML document. Nodes are selected by following a path or steps.

Below are some of the most common path expressions.

Expression	Description
nodename	Select all the children of this node
/	Select from the root node.
//	Select nodes in the document from the current node that matches the selection, regardless of their positions
.	Select the current node
..	Select the parent node of the current node
@	Select an attribute
In the following table, we have listed some path expressions and the results of the expressions.

Path Expression	Result
bookstore	Select all children of the bookstore element
/bookstore	Select the root element bookstore. If the path starts with a forward slash ( / ), then this path always represents an absolute path to an element
bookstore/book	Select all book elements that are children of the bookstore
//book	Select all book elements regardless of their position in the document
bookstore//book	Select all book elements that are children of bookstore elements, regardless of where they are located under the bookstore
//@lang	Select all attributes named lang
Predicates
You can use predicates to find a specific node or a node containing a specified value.

The predicate is enclosed in square brackets.

In the following table, we have listed some path expressions with predicates and the results of the expressions.

Path Expression	Result
/bookstore/book[1]	Select the first book element of the bookstore child elements
/bookstore/book[last()]	Select the last book element of the bookstore child elements
/bookstore/book[last()-1]	Select the second to the last book element of the bookstore child elements
/bookstore/book[position()]	Select the first two book elements of the bookstore child elements
//title[@lang]	Select all title elements that have an attribute named lang
//title[@lang='eng']	Select all title elements that have a lang attribute with the value eng
/bookstore/book[price>35.00]	Select all book elements of the bookstore with the value of the price element greater than 35.00
/bookstore/book[price>35.00]//title	Select all the title elements of the book element in the bookstore with the price element greater than 35.00
Selecting Unknown Nodes
The XPath wildcard can be used to select unknown XML elements.

Wildcard	Description
*	Match any element node
@*	Match any attribute node
node()	Match any type of node
In the following table, we have listed some path expressions and the results of these expressions.

Path Expression	Result
/bookstore/*	Select all child elements of the bookstore element
//*	Select all elements in the document
//title[@*]	Select all title elements with attributes
Select a Number of Paths
You can select several paths by using the "|" operator in a path expression.

In the following table, we have listed some path expressions and the results of these expressions.

Path Expression	Result
//book/title | //book/price	Selects all title and price elements of the book element.
//title | //price	Select all title and price elements in the document
/bookstore/book/title | //price	Select all the title elements of the book element of the bookstore element and all the price elements in the document
