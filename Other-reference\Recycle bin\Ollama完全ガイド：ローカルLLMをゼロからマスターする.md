# プロのヒント：API開発はApidogで一元管理！

> **ApidogはAPI設計・テスト・ドキュメント・モックを一つのプラットフォームで実現。AIとAPIの連携もスムーズに！今すぐ[Apidog](https://apidog.com/jp/)をチェック！**

---

# Ollamaで始めるローカルLLM活用術

AIの進化とともに、ローカルで大規模言語モデル（LLM）を動かすニーズが高まっています。クラウド依存から脱却し、手元のPCでAIを自在に使いたい方に最適なのが**Ollama**。本記事では、Ollamaの導入からカスタマイズ、API連携、トラブル解決まで、実践的なノウハウを新しい視点で解説します。

## Ollamaとは？

Ollamaは、Llama 3やMistral、Gemma、Phiなどの最新LLMを、面倒なセットアップなしでローカル実行できるツールです。CLIやREST API、モデル管理、カスタマイズ機能が充実し、初心者から上級者まで幅広く支持されています。

> 💡 **Apidogとの連携でAPI開発も効率化！**
> [Apidog](https://apidog.com/jp/)を使えば、AIコーディングとAPIワークフローが一気に加速します。

### Ollamaの主なメリット

- **プライバシー重視**：データはすべてローカル。個人情報や機密データも安心。
- **コスト削減**：クラウドAPIの従量課金やサブスク不要。ローカルなら使い放題。
- **オフライン対応**：一度モデルをダウンロードすれば、ネット接続なしでAI利用OK。
- **柔軟なカスタマイズ**：Modelfileでパラメータやプロンプトを自在に調整。
- **高速推論**：GPU対応でクラウドよりも高速な応答も可能。
- **オープンソース＆コミュニティ**：透明性と拡張性、活発なサポート体制。

---

## Ollamaとllama.cppの違い

- **llama.cpp**はC/C++製の高性能推論エンジン。モデルの読み込みやトークン生成を担い、CPU/GPU最適化も万全。
- **Ollama**はllama.cppをベースに、CLIやAPI、モデル管理、カスタマイズ機能を統合した"使いやすいパッケージ"。初心者でも簡単にローカルLLMを扱えます。

---

## Ollamaのインストール方法

### Windows
1. [公式サイト](https://ollama.com/)から`OllamaSetup.exe`をダウンロード。
2. インストーラーを実行し、指示に従ってセットアップ。
3. インストール後は`ollama`コマンドがターミナルで利用可能に。
4. APIサーバーは自動で`http://localhost:11434`で起動。

**GPU活用のポイント**
- NVIDIA/AMDの最新ドライバーをインストール。
- 対応GPUは自動検出され、推論が高速化。

### macOS
1. DMGファイルをダウンロードし、`Ollama.app`をアプリケーションフォルダへ。
2. 起動後、CLIもPATHに追加される。
3. Apple SiliconならMetal経由でGPU自動利用。

### Linux
- 公式スクリプトで一発インストール：
  ```bash
  curl -fsSL https://ollama.com/install.sh | sh
  ```
- systemdサービスや手動インストールも可能。
- GPUドライバー（NVIDIA/ROCm）も要確認。

### Docker
- CPU/GPU/ROCm対応の公式イメージあり。
- モデル永続化には`-v`でボリューム指定を推奨。

---

## モデルの保存場所と管理

- **Windows**：`C:\Users\<USER>\.ollama\models`
- **macOS/Linux**：`~/.ollama/models`（systemd時は`/usr/share/ollama/.ollama/models`）
- **Docker**：`/root/.ollama/models`（ホスト側はボリューム内）
- **カスタム保存先**：`OLLAMA_MODELS`環境変数で変更可能

---

## Ollamaの基本操作

### モデルのダウンロード
```bash
ollama pull llama3.2
ollama pull mistral:7b
ollama pull gemma3
```

### モデルの実行
```bash
ollama run llama3.2
```

### モデル一覧・詳細
```bash
ollama list
ollama show llama3.2:8b-instruct-q5_K_M
```

### モデル削除・コピー
```bash
ollama rm mistral:7b
ollama cp llama3.2 my-custom-llama3.2-setup
```

### 現在ロード中のモデル確認
```bash
ollama ps
```

---

## モデル選びのコツ

- **チャット/アシスタント**：`llama3.2`, `mistral`, `gemma3`
- **コーディング**：`codellama`, `phi4`, `starcoder2`, `deepseek-coder`
- **軽量モデル**：`phi4-mini`, `gemma3:1b`
- **高性能モデル**：`llama3.2:8b-instruct-q5_K_M`, `mistral:7b-instruct-v0.2-q5_K_M`
- **画像対応**：`llava`, `moondream`
- **リソースに応じて量子化やパラメータを調整**

---

## モデルカスタマイズの基礎：Modelfile活用

Ollamaの真骨頂はカスタマイズ性。`Modelfile`でベースモデルやパラメータ、テンプレート、LoRAアダプタなどを柔軟に設定できます。

### Modelfile例
```modelfile
FROM llama3.2:8b-instruct-q5_K_M
PARAMETER temperature 0.6
PARAMETER num_ctx 8192
TEMPLATE """<|im_start|>system\n{{ .System }}<|im_end|>{{ range .Messages }}\n<|im_start|>{{ .Role }}\n{{ .Content }}<|im_end|>{{ end }}\n<|im_start|>assistant\n"""
SYSTEM "あなたは親切なAIアシスタントです。"
```

- **LoRAアダプタ適用**や**外部モデル（GGUF/Safetensors）インポート**も可能。
- `ollama create`でカスタムモデルをビルド。

---

## API連携とOpenAI互換

OllamaはREST APIを標準搭載。さらにOpenAI API互換エンドポイント（`/v1/`）も用意されており、既存のOpenAIクライアントやツールとほぼそのまま連携できます。

### Pythonでの利用例
```python
from openai import OpenAI
client = OpenAI(base_url="http://localhost:11434/v1", api_key="ollama")
chat_completion = client.chat.completions.create(
    model="llama3.2",
    messages=[{"role": "system", "content": "あなたは役立つアシスタントです。"},
              {"role": "user", "content": "Ollamaとllama.cppの違いは？"}]
)
print(chat_completion.choices[0].message.content)
```

---

## パフォーマンス最適化とトラブル対策

- **GPU利用状況は`ollama ps`で確認**
- **環境変数でAPIポートやモデル保存先、CORSなど柔軟に設定**
- **ログ確認**：Windowsは`%LOCALAPPDATA%\Ollama\server.log`、Linuxは`journalctl -u ollama`など
- **よくあるエラー**：ポート競合（11434）、GPU未検出、権限エラーなどは公式ドキュメントやログで解決策をチェック

---

## Ollamaのアンインストール方法

- **Windows**：設定→アプリ→Ollama→アンインストール、その後`%USERPROFILE%\.ollama`を手動削除
- **macOS**：`Ollama.app`をゴミ箱へ、`~/.ollama`を削除
- **Linux**：`sudo systemctl stop ollama`→バイナリ/サービス/モデルディレクトリ削除
- **Docker**：`docker stop`/`rm`/`rmi`/`volume rm`で完全削除

---

## まとめ：ローカルAIの力を手に入れよう

Ollamaは、ローカルでLLMを最大限に活用するための最適解です。プライバシー、コスト、カスタマイズ性、API連携、パフォーマンス…どれを取っても一級品。Apidogと組み合わせれば、API開発もAI活用も一気に加速します。今すぐOllamaを導入し、あなたのマシンでAIの可能性を解き放ちましょう！

---

> **ApidogでAPI開発をもっとスマートに！**
> [Apidog](https://apidog.com/jp/)は、設計・テスト・ドキュメント・モックを一元化。AI時代のAPI開発はApidogで決まり！
