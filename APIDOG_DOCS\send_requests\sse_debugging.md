SSE (*Server-Sent Events*) is a real-time communication technology built upon the HTTP protocol. It's commonly used when AI models need to stream responses. With SSE, a continuous, one-way connection is established between the client and the server. This allows the server to send real-time updates directly to the client, enabling users to instantly see the thought process of the reasoning models and the generated content.

## Initiating an SSE Connection

> Apidog Version Should Be 2.3.10 or Later.

<Steps>
  <Step title="Create a New Endpoint">
    In Apidog, start by creating a new HTTP project. Then, add a new endpoint and enter the URL for the AI model's endpoint.
  </Step>
  <Step title="Send the Request">
    Upon sending the request, if the response header `Content-Type` includes `text/event-stream`, A<PERSON>og will automatically parse the returned data as SSE events.
  </Step>
  <Step title="View Real-time Responses">
    You can see the real-time messages in the `Timeline` view of the response panel, where the content can be displayed in natural language.
  </Step>
</Steps>

<Background>
![sse-timeline-auto-merge.gif](https://api.apidog.com/api/v1/projects/544525/resources/350377/image-preview)
</Background>

## Auto-Merge Message

> Apidog Version Should Be 2.6.49 or Later.

Apidog comes with built-in support for popular AI models and can automatically recognize and merge streaming responses in the following formats:

- OpenAI API Compatible Format <span style="color: gray; opacity: 0.8;">(The vast majority of AI model providers use this format.)</span>
- Gemini API Compatible Format
- Claude API Compatible Format
- Ollama API Compatible Format <span style="color: gray; opacity: 0.8;">(AI models deployed locally using Ollama use this format, which is JSON Streaming or NDJSON.)</span>

If the AI model's response matches any of these formats, Apidog will automatically merge message fragments into complete reply.

For certain models, such as DeepSeek R1, Apidog also supports displaying the model's thought process in the timeline, giving you a clearer, more intuitive view of how the AI is reasoning.

<!--
<Background>
![Auto-merge SSE messages](https://assets.apidog.com/uploads/help/2025/02/14/a53394bc6c44993efafcde9b01b6255f.gif)
</Background>
-->

## Customize Merging Rules

If the `Auto-Merge` feature does not work properly, you can take the following measures based on the actual situation:

### 1. Configure JSONPath Extraction Rules

When the SSE returns content that is in JSON format but does not conform to the built-in recognition rules of OpenAI, Gemini, Claude, etc., you can manually configure [JSONPath](apidog://link/pages/645606) to extract the required content. For example, for the following raw SSE response:

```
data: {"choices":[{"index":0,"message":{"role":"assistant","content":"H"},"logprobs":null,"finish_reason":"stop"}]}

data: {"choices":[{"index":0,"message":{"role":"assistant","content":"i"},"logprobs":null,"finish_reason":"stop"}]}
```
To extract the`content`field, use this JSONPath configuration: `$.choices[0].message.content`.

Here’s what each part of this JSONPath means:

- `$` refers to the root of the JSON
- `choices[0]` selects the first element of the choices array
- `message.content` specifies the content attribute under the message object of that element.

This configuration will extract the content:

```
Hi
```

### 2. Use Post Processor Script

For non-JSON SSE messages, you can:

- Use [post processor scripts](apidog://link/pages/593611) to manually handle the data.
- Contact [technical support](https://docs.apidog.com/apidog-support-center-748035m0#still-need-help) to provide feedback on the model format you are using, and we will consider adding built-in support for that format.

## FAQ

<Accordion title="What to do if the SSE timeline does not display messages?" defaultOpen>
    
This issue usually happens when the server's response doesn't follow the SSE format guidelines. The standard SSE message format requires: 

- The message content must come after`data:`
- Each message must be separated by two newline characters (i.e., a blank line between messages). 

For more details on the SSE message format, check out the *[MDN documentation - Using Server-Sent Events](https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events/Using_server-sent_events)*.
</Accordion>


## Learn More

<Card title="How to Use the Deepseek API (R1 & V3)？" href="https://apidog.com/blog/how-to-use-deepseek-api-r1-v3/">
A complete guide to obtaining the DeepSeek API key and debugging the API.
</Card>