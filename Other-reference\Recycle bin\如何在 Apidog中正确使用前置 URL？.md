# How to Properly Use Base URLs in Apidog

A base URL lets you extract the repeated part of your API endpoint addresses for unified management.

For example, if your endpoint is https://api.example.com/v1/users, you can set https://api.example.com/v1 as the base URL, and only write /users in the endpoint definition.

When sending a request, <PERSON><PERSON><PERSON> will automatically concatenate the base URL and the endpoint path to form the complete request address. The benefit is that if the server address changes, you only need to update the base URL—no need to modify each endpoint individually.


![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6ZzryaXG3F5GToVs32QY7xHzUEbRt4W7AiaZVLxYjibdYrVdXE6mS3rMYkYgQQ/640?wx_fmt=png&from=appmsg&randomid=azxxogjo&tp=webp&wxfrom=10005&wx_lazy=1)

**Steps to Set a Base URL**

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6ZzryaoibhFAVjVfZCfgk3Kddr0LJ2wdYAChCjgiboHDHICgxoTfBYGO7qNGHQ/640?wx_fmt=png&from=appmsg&randomid=1uaywb02&tp=webp&wxfrom=10005&wx_lazy=1)

Open your Apidog project and find "Environment Management" in the upper right corner. Apidog will create several common environments by default, such as development, testing, and production. You can use these presets or create new environments as needed.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6Zzrya9dwVcnbo3ia7s2QictkOm9icw1hibrqfrukicB708B32sUxsNTnlDW7yWiaw/640?wx_fmt=png&from=appmsg&randomid=8vjasf79&tp=webp&wxfrom=10005&wx_lazy=1)

After selecting an environment, you'll see the "Base URL" input box. Enter the base address starting with the protocol (http:// or https://), such as https://test.server.com, or include a version number like https://api.example.com/v1.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6ZzryaLIib5NZTHnabxJGYtfzIUPkkHEdFFhJSOXSpuI0BzOjHKViclGEf8EDg/640?wx_fmt=png&from=appmsg&randomid=lqtply8o&tp=webp&wxfrom=10005&wx_lazy=1)

Be sure not to add a trailing slash. According to the OpenAPI specification, the base URL should not end with a /, while the endpoint path should start with a /.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6Zzryac3oNAAAEoynvtArU7Pvqnaz61PeYufMxWDOmnxx05duG8SWaPZfqhQ/640?wx_fmt=png&from=appmsg&randomid=mkq9e6dt&tp=webp&wxfrom=10005&wx_lazy=1)

We recommend following the OpenAPI specification for better compatibility and a more complete feature experience in Apidog.

**Using the Base URL in Endpoints**

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6ZzryaoibhFAVjVfZCfgk3Kddr0LJ2wdYAChCjgiboHDHICgxoTfBYGO7qNGHQ/640?wx_fmt=png&from=appmsg&randomid=n8h9u9px&tp=webp&wxfrom=10005&wx_lazy=1)

Now, when you create a new endpoint, you only need to enter the relative path in the URL field. For example, to test the user list endpoint, just enter /users, and Apidog will automatically combine it into https://api.example.com/v1/users.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6ZzryaZzIqYXnhklpsfRQn0J7Syh67N9pweXbRowljLo2wPk8zxiaSBepB0YQ/640?wx_fmt=png&from=appmsg&randomid=lna37np8&tp=webp&wxfrom=10005&wx_lazy=1)

If your endpoint path has multiple levels, such as /users/123/profile, use the same approach—the base URL will be automatically combined with your path.

Note: If you enter a full address (starting with http:// or https://) in the endpoint URL, the base URL will not be used. Apidog will prioritize the full address you provide.

**Managing Base URLs Across Multiple Environments**

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6ZzryaoibhFAVjVfZCfgk3Kddr0LJ2wdYAChCjgiboHDHICgxoTfBYGO7qNGHQ/640?wx_fmt=png&from=appmsg&randomid=6ft2zd2u&tp=webp&wxfrom=10005&wx_lazy=1)

Most projects have multiple environments (development, testing, production), each with a different server address. You can set a different base URL for each environment.

For example:
- Development: https://dev-api.example.com/v1
- Testing: https://test-api.example.com/v1
- Production: https://api.example.com/v1

When you switch environments in the upper right, all endpoints will automatically use the corresponding server address.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6Zzrya7HZID8KRibNRibwFYJkOXrg7ibNa7bticicw7qLUXUSROlIdz7fk4GWnUxg/640?wx_fmt=png&from=appmsg&randomid=qsqj3hsz&tp=webp&wxfrom=10005&wx_lazy=1)

You can also select the environment directly in the endpoint address bar, where the default base URL for each environment is displayed. This is equivalent to switching in "Environment Management."

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6ZzryaljN0ibJbYtVNdMkBqydkSBvOiaPCg9ViagBGJnj6CvSiacVdgibkNUovQPw/640?wx_fmt=png&from=appmsg&randomid=f1pru2wl&tp=webp&wxfrom=10005&wx_lazy=1)

Note: Even if multiple base URLs are configured for an environment, only the default one is shown in the address bar. If you want certain endpoints to use a non-default base URL, specify it manually in the endpoint or manage base URLs via modules.

This multi-base URL setup is common in microservice architectures, where different endpoints need to use different service addresses. Here’s how to handle it:

**Managing Base URLs in Microservices**

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6ZzryaoibhFAVjVfZCfgk3Kddr0LJ2wdYAChCjgiboHDHICgxoTfBYGO7qNGHQ/640?wx_fmt=png&from=appmsg&randomid=ubchj5bp&tp=webp&wxfrom=10005&wx_lazy=1)

If your project uses a microservice architecture and not all endpoints share the same base URL, you have two ways to manage base URLs in Apidog.

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHmralwjNCeaB58haZdW1mG2MesiblQ6yeXzcicaLOjbiaOl86EoA8LnlE4v444uuy7XbHrdIkTLfMhIQ/640?wx_fmt=png&from=appmsg&randomid=16whilc4&tp=webp&wxfrom=10005&wx_lazy=1)

**Manually Specify Base URLs Within a Module**

You can group endpoints from multiple services into a single module and assign different base URLs to different directories or endpoints. This is flexible and works well for projects that want centralized management.

As shown below, a module is configured with multiple base URLs for different services.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6Zzrya58yY3Kh80UkialAJ5Tr7kxeGjnX2W0JL4fJSFdSVnJib3olcAUiaRTphg/640?wx_fmt=png&from=appmsg&randomid=drt12z84&tp=webp&wxfrom=10005&wx_lazy=1)

After configuration, you can set the "User Service" address for the user directory and the "Order Service" address for the order directory. All endpoints in that directory will use the corresponding base URL.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmtzz2yjicbAibcIwibeHR2tLA9TJAWNJb62A35icELZvTTc1ynrQHysDbgrRNJWwd4bRaIvTXeRMnp0Q/640?wx_fmt=png&from=appmsg&randomid=5d42qosm&tp=webp&wxfrom=10005&wx_lazy=1)

If you don’t want to set by directory, you can also specify the base URL for a specific endpoint. In the "Edit Endpoint" page, find the base URL dropdown and select the desired address.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmtzz2yjicbAibcIwibeHR2tLAmDMTxJt4bpPiaOYMtbksbMTXubasPNicia0BESHP3As0yujyEKqvonDEA/640?wx_fmt=png&from=appmsg&randomid=9p0c5qn3&tp=webp&wxfrom=10005&wx_lazy=1)

However, this can become cumbersome as the number of services grows, since you’ll need to set and maintain these configurations in many places. For smaller projects, this is fine, but for larger ones, maintenance can be a challenge.

That’s why we recommend another approach: split each service into its own module and manage base URLs by module.

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHmralwjNCeaB58haZdW1mG2mhcV3xLMHBvKITiacuYedc0macAQPkYN6D8ytwz79379unxGucmDenw/640?wx_fmt=png&from=appmsg&randomid=kanwf6fw&tp=webp&wxfrom=10005&wx_lazy=1)

**Service-by-Module (Recommended)**

Create a separate module for each service and configure the base URL for each module in "Environment Management" for every environment. This is more structured and better for team collaboration and long-term maintenance.

For example, create modules for user service, order service, and product service, each with its own Swagger/OpenAPI spec file.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6ZzryaOlvWcFbC8oLdfLTLjjxyDGibtbBjToWK6lDawIRnWy1CzPM4S0Boqpg/640?wx_fmt=png&from=appmsg&randomid=irpzl39c&tp=webp&wxfrom=10005&wx_lazy=1)

After creating modules, go to the environment management page and you’ll see base URL settings grouped by module.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6ZzryavDPWxmwgZBOlnSnxzIgibOwRB0HFL14vVLagIiaRTvqqCEeEUmPuzricg/640?wx_fmt=png&from=appmsg&randomid=8gmy4nhr&tp=webp&wxfrom=10005&wx_lazy=1)

Each environment has the same module structure, but each module has a different base URL. Now you can set the base URL for each module in each environment, for example:

Production:
- Product Service Module: https://product.example.com
- User Service Module: https://user.example.com
- Order Service Module: https://order.example.com

Testing:
- Product Service Module: http://************:8080
- User Service Module: http://************:8080
- Order Service Module: http://************:8080

Development:
- Product Service Module: http://localhost:3000
- User Service Module: http://localhost:3001
- Order Service Module: http://localhost:3002

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6ZzryakZLTAHUry5ABCphcHwfialvBKJFMAyvrJeUNfLWqiasbjPvtCGjQ3FBg/640?wx_fmt=png&from=appmsg&randomid=czsdcw7k&tp=webp&wxfrom=10005&wx_lazy=1)

With this setup, when you create a new endpoint in a module, it will use the base URL for the current module and environment by default—no manual selection needed. For example:

- In the "User Service Module & Production Environment," the default base URL is https://user.example.com

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6ZzryavvtdC9ODkV1c77NWTvJ5aEWV9pCicmx9zTTHx4NXed5wQDFN0nEhPKg/640?wx_fmt=png&from=appmsg&randomid=0pfh1tmm&tp=webp&wxfrom=10005&wx_lazy=1)

- In the "Order Service Module & Testing Environment," it’s http://************:8080

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6ZzryaZSUXeg7NOMLXfmfo9XibFKuznJd1LOibM6IqhjY2LUP0iaTrUrSd0jtKw/640?wx_fmt=png&from=appmsg&randomid=1btys3jp&tp=webp&wxfrom=10005&wx_lazy=1)

This "module + environment" combination acts like a coordinate system, precisely determining the address used for each request. No matter how many modules or environment switches, as long as these two dimensions are set, the base URL is always correct—Apidog will match it automatically.

You no longer need to manually check "which address is this endpoint using"—as long as modules are organized and the environment is selected, requests will always go to the right service.

**💡 Tip**  
To learn more about "modules," check the [official documentation](https://docs.apifox.com/module).

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6Zzryae9o4BUy5h9Eclojgo0K7SIia9ZCacCSwlLo9kDN24I9j9e4NsbULTbg/640?wx_fmt=png&from=appmsg&randomid=z3xvqb1z&tp=webp&wxfrom=10005&wx_lazy=1)

**Practical Tips for Base URLs**

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmxcN30aMTAmuKuXg6ZzryaoibhFAVjVfZCfgk3Kddr0LJ2wdYAChCjgiboHDHICgxoTfBYGO7qNGHQ/640?wx_fmt=png&from=appmsg&randomid=jzkmbhmx&tp=webp&wxfrom=10005&wx_lazy=1)

If your API has a version number, it’s recommended to include it in the base URL. This way, when the API is upgraded, you only need to update the base URL for all endpoints to use the new version.

For special cases, you can always use a full URL in a specific endpoint to override the base URL. For example, if an endpoint needs to call a third-party service with a completely different address, just enter the full address for that endpoint.

Visit the help docs for more feature instructions and usage guides.