Apidog provides a left-side folder tree setting feature for API documentation, allowing you to customize the style and layout according to your needs.

## Access the folder tree settings

Clicking on the folder you want to modify within <PERSON>pidog will take you to the folder tree settings, where you can customize a specific folder style.
<Background>
![Apidog Share docs - Accessing Settings](https://assets.apidog.com/help/assets/images/folder-tree-settings-01-eeeebc09f935deac4cfd2261d1a1a12f.png)
</Background>
### Folder tree display style

**General Folder**: By default, folders are displayed in a folder format. You can expand or collapse the folder by clicking on it.
<Background>
![Apidog Share Split - General Folder Display Mode](https://assets.apidog.com/help/assets/images/folder-tree-settings-02-f4f920d46d9ea6ffddac15a099d5aec0.png)
</Background>
**Module Title**: Displays as pure titles, used to differentiate between various modules.
<Background>
![Apidog Share Split - Module Title Display Mode](https://assets.apidog.com/help/assets/images/folder-tree-settings-03-0f46adbce0690156446978051b84a8ba.png)
</Background>
**Go to the Subfolder**: When a folder contains extensive content, you can use this style. Clicking on the folder will navigate to a sub-page focused on the current folder's content.
<Background>
![Apidog Share Split - Go to the Subfolder Display Mode](https://assets.apidog.com/uploads/help/2024/08/07/edc6d719591172e7930d324456808954.gif)
</Background>
## Expand/collapse the folder by default
<Background>
![](https://assets.apidog.com/help/assets/images/folder-tree-settings-05-9746330aa0a9db60e3fdd5b4fba4501c.png)
</Background>
**Global Automatic**: This is the default option, which automatically adjusts the expansion or collapse of folders based on the number of items they contain, ensuring a rich left-side folder tree when the document is opened.
<Background>
![Apidog Share Split - Default Expanded Folder](https://assets.apidog.com/help/assets/images/folder-tree-settings-06-2088aed04b2bc72a74fe703e4437fc57.png)
</Background>
**Manual Setting**: With this option enabled, you can configure which folder should expand or collapse by default according to your preferences.
<Background>
![Apidog Share Split - Manual Setting](https://assets.apidog.com/help/assets/images/folder-tree-settings-07-4792d7c404e23592983d48a89053902d.png)
</Background>
### Folder contents displaying setting

**Custom Content**: When you click on the folder, it will show the text content that you have personalized by default. This text can serve as an introductory message or provide a brief overview of the folder's contents.
<Background>
![Apidog Share Split - Custom Content Displaying Setting](https://assets.apidog.com/help/assets/images/folder-tree-settings-08-14b16d207806a9f5ac363391827a1d4c.png)
</Background>
**Sub-list**: Clicking on the folder will display an index of the content within that folder, helping readers quickly understand the content.
<Background>
![Apidog Share Split - Sub-list Displaying Setting](https://assets.apidog.com/help/assets/images/folder-tree-settings-09-eb20d888e595bb677cd6366b0ffd1e9d.png)
</Background>
**Hide**: Clicking on the folder will only expand or collapse the folder without displaying any content.