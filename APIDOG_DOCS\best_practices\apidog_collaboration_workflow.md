Apidog collaboration workflow
This article will guide you through the best practices for API collaboration workflow in Apidog.

Best practices
1.
Draft API Documentation: API designer create initial API documentation in Apidog.

2.
Collaborative Review: Front-end and back-end developers work together to review and enhance the documentation, ensuring alignment on API use cases.

3.
Development Kickoff with Mock Data: Front-end developers can start development using automatically generated mock data based on API documentation in Apidog, eliminating the need for manual mock rule creation.

4.
Debugging with API Use Cases: Back-end developers debug the APIs during development. Completion of API development is confirmed when all use cases pass debugging. Any changes made during development automatically update the API specification, ensuring timely and cost-effective API maintenance.

5.
Saving Functions: Once debugging is complete, back-end developers can save the request as an endpoint case.

6.
API Testing: QA engineers can directly test APIs using predefined endpoint case.

7.
Integration Testing: After all APIs are developed, QA engineers (or back-end developers) can utilize the test collection feature to conduct comprehensive multi-API integration testing, ensuring the smooth functioning of the API calling process.

8.
Joint Debugging: Smooth joint debugging occurs when front-end and back-end development is finished, and front-end developers switch from mock data to real data, following the API specifications meticulously.

