API response in Apidog
The Apidog response viewer provides comprehensive tools to visualize and verify API responses. Apidog allows you to examine response details, including test results, network information, response size, response time, and security alerts.

Response Body
Apidog's Body tab offers several views to help you interpret the response:

Pretty
This view formats JSON or XML responses for improved readability. It highlights links and allows collapsing of large sections for easier navigation.



Force JSON formatting
To enable automatic formatting of the response body in Apidog, the response should include the correct Content-Type header.
However, if you receive a response with a different Content-Type header, you can force JSON formatting manually.

Raw
The Raw view displays the unformatted response body in a text area, useful for identifying minification.

Preview
Preview renders the response in a sandboxed iframe, helpful for debugging HTML errors.



For binary responses, you can select the down arrow next to "Send" and select "Send and Download" to save the response locally.

Visualize
This view renders data according to custom visualization code you add to the post-request scripts.

Learn more about Visualizing responses.


Cookies
The Cookies tab displays server-sent cookies, including name, value, domain, path, and other details.

Learn more about Create and send cookies in Apidog.


Headers
Headers appear as key-value pairs in the Headers tab.

Network Information
Apidog displays network details such as local and remote IP addresses.

The response code returned by the API is prominently displayed. Hover over it for a brief description.

Apidog calculates and displays the response time and size, with detailed breakdowns available on hover.



