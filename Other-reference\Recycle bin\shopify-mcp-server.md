Shopify Dev MCP server
Integrates with Shopify Dev. Supports various tools to interact with different Shopify APIs.
Back to servers
Provider
Shopify
Release date
Mar 31, 2025
Language
TypeScript
Package
npm
Stats
2.6K downloads
119 stars
View on GitHub
The Shopify Dev MCP Server is an implementation of the Model Context Protocol that allows AI tools to interact with various Shopify APIs and documentation. It provides specialized tools for searching Shopify documentation and working with Shopify's GraphQL schema.

Installation
You can run the Shopify MCP server directly using npx:

npx -y @shopify/dev-mcp@latest
Configuration
Setting Up with Cursor or Claude <PERSON>
To integrate the Shopify MCP server with compatible AI tools, add the following configuration to your settings.

For most systems:

{
  "mcpServers": {
    "shopify-dev-mcp": {
      "command": "npx",
      "args": ["-y", "@shopify/dev-mcp@latest"]
    }
  }
}
For Windows systems, you may need this alternative configuration:

{
  "mcpServers": {
    "shopify-dev-mcp": {
      "command": "cmd",
      "args": ["/k", "npx", "-y", "@shopify/dev-mcp@latest"]
    }
  }
}
For more detailed information, refer to the Cursor MCP documentation or the Claude Desktop MCP guide.

Features
Available Tools
The Shopify MCP server provides the following tools:

search_dev_docs - Search through Shopify.dev documentation
introspect_admin_schema - Access and search the Shopify Admin GraphQL schema
Available Prompts
The server also includes specialized prompts:

shopify_admin_graphql - Assistance with writing GraphQL operations for the Shopify Admin API
Usage Examples
Once configured, AI tools like Cursor or Claude Desktop can access these capabilities:

Search Shopify documentation for specific topics
Explore the Admin API GraphQL schema
Get assistance writing GraphQL queries and mutations for Shopify Admin API
The tools will be automatically available within your AI assistant's interface after proper configuration.

How to add this MCP server to Cursor
There are two ways to add an MCP server to Cursor. The most common way is to add the server globally in the ~/.cursor/mcp.json file so that it is available in all of your projects.

If you only need the server in a single project, you can add it to the project instead by creating or adding it to the .cursor/mcp.json file.

Adding an MCP server to Cursor globally
To add a global MCP server go to Cursor Settings > MCP and click "Add new global MCP server".

When you click that button the ~/.cursor/mcp.json file will be opened and you can add your server like this:

{
    "mcpServers": {
        "cursor-rules-mcp": {
            "command": "npx",
            "args": [
                "-y",
                "cursor-rules-mcp"
            ]
        }
    }
}
Adding an MCP server to a project
To add an MCP server to a project you can create a new .cursor/mcp.json file or add it to the existing one. This will look exactly the same as the global MCP server example above.

How to use the MCP server
Once the server is installed, you might need to head back to Settings > MCP and click the refresh button.

The Cursor agent will then be able to see the available tools the added MCP server has available and will call them when it needs to.

You can also explictly ask the agent to use the tool by mentioning the tool name and describing what the function does.