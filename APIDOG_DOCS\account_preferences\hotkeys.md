<table>
  <thead>
    <tr>
      <th>Belongs to the module</th>
      <th>Functions</th>
      <th>Windows / Linux</th>
      <th>macOS</th>
      <th>Desktop version</th>
      <th>Web version</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th rowspan="10">API Management</th>
      <td>Create a new API</td>
      <td><strong>Ctrl + N</strong></td>
      <td><strong>⌘ + N</strong></td>
      <td>✅</td>
      <td>❌</td>
    </tr>
    <tr>
      <td>New Quick Debug</td>
      <td><strong>Ctrl + T</strong></td>
      <td><strong>⌘ + T</strong></td>
      <td>✅</td>
      <td>❌</td>
    </tr>
    <tr>
      <td>Save the file</td>
      <td><strong>Ctrl + S</strong></td>
      <td><strong>⌘ + S</strong></td>
      <td>✅</td>
      <td>✅</td>
    </tr>    
    <tr>
      <td>Copy the file</td>
      <td><strong>Ctrl + D</strong></td>
      <td><strong>⌘ + D</strong></td>
      <td>✅</td>
      <td>✅</td>
    </tr>
    <tr>
      <td>Delete the file</td>
      <td><strong>Delete</strong></td>
      <td><strong>⌫</strong></td>
      <td>✅</td>
      <td>✅</td>
    </tr>    
    <tr>
      <td>Send a request</td>
      <td><strong>Ctrl + ⏎</strong></td>
      <td><strong>⌘ + ⏎</strong></td>
      <td>✅</td>
      <td>✅</td>
    </tr>
    <tr>
      <td>Switch to the Running tab</td>
      <td><strong>Ctrl + ⏎</strong></td>
      <td><strong>⌘ + ⏎</strong></td>
      <td>✅</td>
      <td>✅</td>
    </tr>
    <tr>
      <td>Import data</td>
      <td><strong>Ctrl + O</strong></td>
      <td><strong>⌘ + O</strong></td>
      <td>✅</td>
      <td>✅</td>
    </tr>
    <tr>
      <td>(cURL) Import packet capture data (cURL)</td>
      <td><strong>Ctrl + I</strong></td>
      <td><strong>⌘ + I</strong></td>
      <td>✅</td>
      <td>✅</td>
    </tr>
    <tr>
      <td>Find the API</td>
      <td><strong>Ctrl + F</strong></td>
      <td><strong>⌘ + F</strong></td>
      <td>✅</td>
      <td>❌</td>
    </tr>
    <tr>
      <th rowspan="4"> Data Model</th>      
      <td>Switch to the upper input box</td>    
      <td><strong>↑</strong> </td>
      <td><strong>↑</strong></td>
      <td>✅</td>
      <td>✅</td>
    </tr>
    <tr>  
      <td>Switch to the input box below</td>    
      <td><strong>↓</strong> </td>
      <td><strong>↓</strong></td>
      <td>✅</td>
      <td>✅</td>
    </tr>
    <tr>
      <td>Switch to the next input box</td>
      <td><strong>Tab</strong></td>
      <td><strong>Tab</strong></td>
      <td>✅</td>
      <td>✅</td>
    </tr>
    <tr>
      <td>Switch to the previous input box</td>
      <td><strong>Shift + Tab</strong></td>
      <td><strong>⇧ + Tab</strong></td>
      <td>✅</td>
      <td>✅</td>
    </tr>
    <tr>
      <th rowspan="6">Tabs</th>
      <td>Close the tab</td>
      <td><strong>Ctrl + W</strong></td>
      <td><strong>⌘ + W</strong></td>
      <td>✅</td>
      <td>❌</td>
    </tr>      
    <tr>
      <td>Force the tab to close</td>
      <td><strong>Ctrl + Alt + W</strong></td>
      <td><strong>⌘ + ⌥ + W</strong></td>
      <td>✅</td>
      <td>❌</td>
    </tr>
    <tr>
      <td>Switch to the next tab</td>
      <td><strong>Ctrl + Tab</strong> or Ctrl + PageDown</td>
      <td><strong>⌘ + ⌥ + →</strong> or ⌘ + ⇧ + ]</td>
      <td>✅</td>
      <td>❌</td>
    </tr>
    <tr>
      <td>Tab Switch to the previous tab</td>
      <td><strong>Ctrl + ⇧ + Tab</strong> or Ctrl + PageUp</td>
      <td><strong>⌘ + ⌥ + ←</strong> or ⌘ + ⇧ + [</td>
      <td>✅</td>
      <td>❌</td>
    </tr> 
    <tr>
      <td>Jump to a specific tab</td>
      <td><strong>Ctrl + 1</strong> to Ctrl + 8</td>
      <td><strong>⌘ + 1</strong> to ⌘ + 8</td>
      <td>✅</td>
      <td>❌</td>
    </tr>
    <tr>
      <td>Jump to the last tab</td>
      <td><strong>Ctrl + 9</strong></td>
      <td><strong>⌘ + 9</strong></td>
      <td>✅</td>
      <td>❌</td>
    </tr>    
    <tr>
      <th rowspan="2">Code Editor</th>
      <td>Find</td>
      <td><strong>Ctrl + F</strong></td>
      <td><strong>⌘ + F</strong></td>
      <td>✅</td>
      <td>✅</td>
    </tr>
    <tr>
      <td>replacement</td>
      <td><strong>Ctrl + H</strong></td>
      <td><strong>⌥ + ⌘ + F</strong></td>
      <td>✅</td>
      <td>✅</td>
    </tr>
    <tr>
      <th rowspan="6">General</th>    
      <td>Line break</td>
      <td>⏎ or Shift + ⏎</td>
      <td>⏎ or ⇧ + ⏎</td>
      <td>✅</td>
      <td>✅</td>
    </tr>
    <tr> 
      <td>Apidog Show/hide Apidog</td>
      <td><strong>⇧ + Alt + W</strong></td>
      <td>&nbsp;</td>
      <td>✅</td>
      <td>❌</td>
    </tr>
    <tr> 
      <td>Hide pop-ups</td>
      <td><strong>ESC</strong></td>
      <td><strong>ESC</strong></td>
      <td>✅</td>
      <td>✅</td>
    </tr>    
    <tr>
      <td>Set up</td>
      <td><strong>Ctrl + ,</strong></td>
      <td><strong>⌘ + ,</strong></td>
      <td>✅</td>
      <td>❌</td>
    </tr>
    <tr>
      <td>The API is enlarged</td>
      <td><strong>Ctrl + +</strong></td>
      <td><strong>⌘ + +</strong></td>
      <td>✅</td>
      <td>✅</td>
    </tr>
    <tr>
      <td>The API is reduced</td>
      <td><strong>Ctrl + -</strong></td>
      <td><strong>⌘ + -</strong></td>
      <td>✅</td>
      <td>✅</td>
    </tr>    
  </tbody>
</table>



