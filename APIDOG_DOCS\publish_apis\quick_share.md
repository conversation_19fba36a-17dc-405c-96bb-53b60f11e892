In API development, communication, and collaboration, API documentation is logically the standard, but in practice, there is the problem of passing around files in Word or PDF formats. For this reason, **online document** is advocated to improve the efficiency of communication between teams.

<Video src="https://www.youtube.com/watch?v=LRkuXCAXh3w"></Video>

## Create a quick share

Switch to **Share Docs** module on the left side.
<Background>   
![sharing-01.png](https://api.apidog.com/api/v1/projects/544525/resources/347476/image-preview)
</Background>
Click`+New`to create a new Quick share. A project supports multiple different Quick shares, which can be distributed to different collaborators. Every team member can see all Quick shares created by everyone.
<Background>
![create-documentation-share.jpg](https://api.apidog.com/api/v1/projects/544525/resources/349493/image-preview)
</Background>
In the New Share popup, you can customize the following options:

- **Title**: The name of this share, only visible within the Apidog team, used for internal identification, not visible in the shared documentation.

- **Environments**: Choose the environments available in the online document. By default, no environment is selected. You can select multiple environments. The selected environments will be visible to readers in the documentation and can be used to send requests when using "Try it out".
:::tip[]
If you select the Cloud mock environment when creating the documentation, it's equivalent to enabling a simple sandbox environment for the readers of the documentation.
:::
- **Document content**: The document has two optional components: Base URL and Request samples. You can choose whether to display them in the document.
  - **Base URL**: This is the base URL set in the environment. If selected, each interface in the document will show the complete URL; if not selected, the URL in the document will start with "/".
  - **Owners**: Show oweners of this API documentation.
  - **Request samples**:  Whether to display example code for calling requests. See [request samples](#request-samples) for details.
  - Show 'Last Modified' time above endpoint description.
  - Show 'Last Modified' time at the end of the documentation.
 
- **Allow 'Export', 'Clone' Data**: Whether to allow readers to export & clone data in OAS/Markdown/HTML/Apidog format. 

- **Scope to share**: Choose which API endpoints and Markdown to include in this share. You can select by directory or filter and exclude by tag. You can also choose to [share the entire folder](#share-the-entire-folder).

- **Security**: You can set whether a password is required to access this document, and the expiration time of this document. If no expiration time is selected, it never expires.

- **Show API Fields**: You can set whether these fields are displayed in the document. Including: Owners, Update time, OperationId, Original link, Authorization Required.

- **Language**: The language setting here will affect the functional fixed text in the document, such as "Query Params" "Request samples", etc., but will not affect the text written in the API documentation content.

- **CORS Proxy**: You can deploy and configure the CORS proxy server to handle endpoint requests on API documentation. [Learn more](#cors-proxy).

- **Advanced settings**: You can set Hide 'Try it out' Button and Hide 'Powered by Apidog' here.

### Request samples
You can choose whether to display this module when creating the share.
You can customize Request samples. Here's how:
1. In **Project settings** - **Feature settings** - **Endpoint feature settings** - **Endpoint fields**, enable the Request samples field.
2. Then in **Endpoint** - **Edit**, a Request samples module will appear at the end.
3. You can click **Add request sample** here to add samples in the languages you need. Then it will appear in the documentation.
<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/344099/image-preview)
</Background>

### Share the entire folder

Quick share supports`Share entire folder`. This is suitable for scenarios where, after sharing the link, you may have added or removed documents in the folder, and you want collaborators to be able to see these changes synchronously. 

In the `Scope to Share`, when you select `Manual Selection`, you can see `Share entire folder`. When enabled, collaborators will automatically have access to all documentations in the folder, so you won’t need to adjust the sharing settings each time you add new documents.

<Background>
![sharing-03.png](https://api.apidog.com/api/v1/projects/544525/resources/348448/image-preview)
</Background>

### CORS Proxy

When sharing API documentation online with external users, they may want to test or debug endpoints directly within the documentation. However, this can trigger [cross-origin resource sharing](https://en.wikipedia.org/wiki/Cross-origin_resource_sharing) (CORS) issues in the browser, blocking their requests. To fix this, you can set up a CORS proxy. This proxy routes all endpoint requests from the shared documentation through a designated request proxy agent, effectively bypassing CORS restrictions and allowing seamless debugging.

To set up the CORS proxy, simply click on the setting option when creating a new shared documentation. By default, Apidog offers`Cloud Agent`to manage endpoint requests across all shared documentations.

<Background>
![configuring-cors-proxy.jpg](https://api.apidog.com/api/v1/projects/544525/resources/349497/image-preview)
</Background>

Select the CORS proxy option best suited to your needs:

<Background>
![choose-desired-cors-proxy.jpg](https://api.apidog.com/api/v1/projects/544525/resources/349498/image-preview)
</Background>

- **Cloud Agent**: This uses Apidog's`Cloud Agent`to handle endpoint requests from the shared documentation. Keep in mind that the Cloud Agent cannot access endpoints on internal network.

- **Browser Extensions**: This option uses a browser extension installed in the user's browser to handle requests. If the user hasn't installed the extension, they'll be prompted to do so before initiating any endpoint requests.

- **No Proxy**: Selecting this option means that requests will be directly sent from the user's browser to the endpoint. Ensure the endpoint server settings are configured correctly to handle CORS, preventing potential issues.

- **Self-hosted Request Proxy Agent**: This allows you to use a [Self-hosted Request Proxy Agent](apidog://link/pages/780303) deployed within your team to handle endpoint requests from the shared documentation.

## Share the link

After the setting is complete, copy the link to share with team members.
<Background>
![](https://assets.apidog.com/uploads/help/2023/07/11/cc0b9a9eb05ee500489b03aa51e15ee9.png)
</Background>

See how to [View the API documentation](apidog://link/pages/631148).
