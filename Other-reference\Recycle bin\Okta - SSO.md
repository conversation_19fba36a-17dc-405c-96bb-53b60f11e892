## 准备工作

在 Okta 的后台进行配置之前，请打开 Apidog 组织设置中的 SAML 单点登录页面，开启「需要 SAML 身份验证」开关，并停留在这个页面。

![image.png](https://docs.apifox.com/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/485615/image-preview?onlineShareType=apidoc&locale=zh-CN)

## 配置 Okta

要配置你的 SAML 应用程序，请知晓以下操作：

- 在浏览器打开 Okta 后台。
- 前往 **Applications**，点击 **Create App Integration**。
- 选择 **SAML** 作为登录方法。
- 在 General Settings 页面输入 App name，如 Apidog。
- 复制 Apidog 中的**断言使用者服务 URL**，并将其粘贴到 Okta 中 Configure SAML 页面的 **Single sign-on URL**。
- 复制 Apidog 中的**标识符**，并将其粘贴到 Okta 中 Configure SAML 页面的 **Audience URI (SP Entity ID)**。
- Okta 中 Configure SAML 页面的 **Audience URI (SP Entity ID)** 留空，无需填写。
  - Okta 中 Configure SAML 页面的 **Name ID format** 选择 **Persistent**。
  - Okta 中 Configure SAML 页面的 **Application username**  选择 **Okta username**。
- 其余配置项保持默认即可，保存 Okta 的配置。
- 在 Okta 应用的 Sign on 页面的 SAML 2.0 选项，点击 **More details**，复制 **Sign on URL**，将其粘贴到 Apidog 中的**登录 URL**。
- 复制 Okta 的 **Issuer**，将其粘贴到 Apidog 中的 **Issuer** 字段。
- 在 Okta 中点击 **Signing Certificate** 旁边的 **Download**，用 Visual Studio Code 等代码编辑器打开它，复制文件中的文本，并将其粘贴到 Apidog 中的**证书**。
- 保存 Apidog 的配置。

## 测试你的 SAML 配置

现在你可以返回 Apidog 的主窗口，点击侧边栏中的组织名称，然后点击右侧的单点登录入口。请测试是否可以正常登录。
