Email Subject: Apidog's July New Features Rollout.

Hello Apidog Users,

We hope you’re enjoying the summer! July has been a month of big leaps for Apidog, with a focus on giving you more control, smarter automation, and a smoother workflow. Here’s a look at the latest features and improvements designed to make your API development experience even better:

🎨 Custom CSS & JavaScript for Online Docs: You can now fully personalize your published API documentation by adding your own CSS and JavaScript. Tailor the look, feel, and interactivity of your docs to match your brand or workflow! Learn more.
[Image]
https://assets.apidog.com/uploads/help/2025/07/11/ts4mf-7u.gif

🔔 Support for Webhook & Callback Endpoints: You can now define and test Webhook and Callback endpoints, simplifying work with event-driven APIs.

📁 Schemas Displayed in Online Docs: When publishing online documentation, you can now choose whether the left-side folder displays schemas, giving you more control over how your API structure is presented to consumers. 

⚡️ Smarter Auth Mapping: When importing endpoints with security requirements, you can now map Auth directly to the corresponding security scheme, making setup faster and more accurate.

🤖 AI-Generated Parameter Names: Let AI do the heavy lifting! You can now generate parameter names using AI, speeding up your API design and reducing manual work.
[Image]
https://assets.apidog.com/uploads/help/2025/07/28/o9kay-ed.gif

📐 Endpoint Design Guidelines: Add customEndpoint Design Guidelinesto your project to help team members (and AI) write more standardized, high-quality API documentation. 
[Image]
https://assets.apidog.com/uploads/help/2025/07/11/tt65g-sk.gif

🔒Connecting to Databases with SSL Enabled: Apidog now supports connecting to MySQL and PostgreSQL databases with SSL enabled, ensuring your data connections are secure.

🧩 Module Variables: Modules now support module variables, just like collection variables in Postman. This makes managing environment-specific values across large projects a breeze. Learn more.
[Image]
https://assets.apidog.com/uploads/help/2025/07/11/tquk5-of.png

⚡️ More Flexible Auth under Design-First Mode : When running endpoints in Design-first Mode, you can now use default authentication credentials or set them manually for more flexible testing.

⚡️ OpenAPI Generator Upgrade: The OpenAPI Generator for generating business code has been upgraded to v7.13.0, bringing more compatibility and features.

🔥 Offline Space: IntroducingOffline Spacewith endpoint debugging capabilities! Simple, fast, and stores files locally—perfect for working without an internet connection.

⚡️ OAuth 2.0 UI Upgrade: We’ve improved the user interface for OAuth 2.0 authentication, making it easier to configure and debug secure endpoints.

🐞 Socket.IO Debugging Fix: Fixed a UI error when debugging Socket.IO endpoints that return an empty message or ack, ensuring a smoother real-time debugging experience.

🐞 OpenAPI 3.0 Export Fixes: Resolved issues where schema fields were incorrectly exported—example values were labeled as examples instead of example, and nullable fields used type: null instead of the correct nullable: true.

🐞 OpenAPI/Swagger Import & Export Fixes: Fixed issues where the Discriminator field was lost during export, and request body structures were missing during import when the Media Type was unspecified or set to /.

🌟 Looking Ahead
We’re already working on the next round of features and improvements to make Apidog even more powerful and user-friendly. Your feedback is what drives us—keep it coming!

💬 Join the Conversation!
Have ideas or questions? Join our Discord or Slack communities to connect with fellow developers, share tips, and get the latest Apidog news.

P.S. Explore the full details of all these updates in the Apidog Changelog! 🚀

Happy API Building!
Regards,
The Apidog Team