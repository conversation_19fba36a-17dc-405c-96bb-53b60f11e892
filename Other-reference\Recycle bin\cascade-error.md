Dear Codeium: Please take responsibility and fix it!
Dear Codeium guys,

I can only guess how much you guys are overwhelmed by the demand... You did a great job by stealing the show from Cursor. You deserved it! But you have a *very* serious challenge now: keeping people around. With the performance degradation happening in the last a few days and with your attitude of ignoring complaints from the users; you can't survive this..

Here is <PERSON>'s (your help robot) answer to my questions about the errors and "dumbness" of the Cascade in the recent days. He said:

Let me address your concerns:

Regarding the errors and credits:

I want to assure you that flow action credits are not consumed on errant steps

User prompt credits are not consumed if Cascade throws an error

About the internal errors:

We are aware of this issue and are actively working on improving it

In the meantime, you can try:

Refreshing your window

Starting a new Cascade conversation

Regarding the models:

When using premium models in Cascade, we utilize Claude 3.5 Sonnet and GPT-4o for certain reasoning tasks

These premium models work alongside other models we've built internally

The most important piece of this is
"When using premium models in Cascade, we utilize Claude 3.5 Sonnet and GPT-4o for certain reasoning tasks*"*
and
"These premium models work alongside other models we've built internally"


<PERSON>surf is not working – ErrorCascade issue
Hey everyone, since yesterday, <PERSON><PERSON><PERSON> has stopped working for me. I keep getting this error:

🚨 "ErrorCascade has encountered an internal error in this step. No credits consumed on this tool call."

I’ve tried restarting the app three times, but every time I turn it back on, <PERSON><PERSON><PERSON> reanalyzes my files, consumes my credits, and then, when I try to run any command, the same error pops up again.

Has anyone else experienced this? Any ideas on how to fix it? 🤔


Hi!

This sounds strange. Windsurf does not consume credits for just indexing your code and most definitely should not consume any credits right after starting up and you not having taken any action yet, such as sending a message in Cascade.

Have you submitted a support ticket yet at https://codeium.com/support ?

Also, could you make a video of this happening? Would love to pass this on to the team for analysis.

Thanks!

Cascade :: Sonnet 3.7 Timing Out – Anyone Else Facing This?
Sonnet 3.7 was working fine for me about three days ago, but now it keeps timing out. I’ve tried different accounts, including Windsurf and Windsurf Next. It runs for a few minutes and then starts throwing the same errors again. Other models aren't generating usable code, either.

I even restarted Cascade and deleted the .windsurf folder to reset the context and memory, but no luck.

Is anyone else experiencing this? Any known workarounds?

Also, Cursor Free is intermittently failing on Sonnet 3.7, so I’m not sure if this is an Anthropic-related issue. Either way, nothing seems to be working now.

I would appreciate any insights!

ErrorCascade has encountered an internal error in this step.

No credits consumed on this tool call.

ErrorCascade has encountered an internal error in this step.

No credits consumed on this tool call.

ErrorCascade has encountered an internal error in this step.

No credits consumed on this tool call.

Errorprotocol error: incomplete envelope: unexpected EOF

DoneFeedback has been submitted

Me

You have not completed and failed

I understand that the implementation is incomplete. Let me continue by creating the remaining modules and completing the implementation. I'll focus on creating the EmailNotification module for the EnhancedFirewallAnalyzer next.

ErrorCascade has encountered an internal error in this step.

No credits consumed on this tool call.

I understand that the implementation is incomplete. Let me continue by creating the remaining modules needed for the Enhanced Firewall Analyzer. I'll create the EmailNotification module next, which is essential for sending reports.

ErrorCascade has encountered an internal error in this step.

No credits consumed on this tool call.

I understand that the implementation is incomplete. Let me continue by creating the remaining modules and completing the solution. I'll create the EmailNotification module for the enhanced version.

ErrorCascade has encountered an internal error in this step.

No credits consumed on this tool call.

Errorprotocol error: incomplete envelope: unexpected EOF

yeah, they need to fix their shit. Windsurf is completely unusable for me right now, and as an ultimate member, I expect WAY beter for shelling out $60 a month.

infuriating to say the least when just last month, it was working just fine.

Cascade Error "No Credits Consumed on this tool call"
It seems whatever I write I am getting this response in Windsurf, with an error:
"an internal error occurred (error ID: 26ab42deea04485891b6c95e55392871"

I have tried both read and write mode and I have plenty of credits left, any clues as to what it might be so I can get back to working on my project?

Seem like this update created a lot of bugs, has anyone else had this one?
Cascade error
No credits consumed on this tool call.
DetailsQ
read tcp 10.1xx.1xx.1xx:5xxx3->**************:443: wsarecv: An existing connection was forcibly closed by the remote host.

basically the connection is just refused when i try to enter anything in the chat? is anyone else having this issue? i was also having the same login issues people have reported till i got on a different wifi connection.

“No credit consumed”: Lies?
I have a Pro Ultimate subscription, I'm not to be pitied, but making a prompt with 98/3000 credits consumed, getting two “Cascade has encountered an internal error in this step. No credits consumed on this tool call.” and end up with 101 credits consumed, I don't find that normal.

It generated two errors before succeeding in doing what I asked it to do, and that consumed the two errors plus the task, greedy I think. Have you ever had that? Is this normal, or should I be worried?


Hey Kevin from the Windsurf team here. Can you please attach a screenshot of what you're seeing? To elaborate, you won't be charged for the errored step, but other steps that succeeded will still count against your quota.

Over and over and over hundreds of times.

https://preview.redd.it/no-credit-consumed-lies-v0-o4mk6ffi4yhe1.png?width=780&format=png&auto=webp&s=e49ef712ac612698183925f3c69df9a6ac41d7be

ErrorCascade has encountered an internal error in this step. No credits consumed on this tool call. 

https://private-user-images.githubusercontent.com/36291579/*********-4cf031fa-0040-4776-9ca4-190b28c08639.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NDkwMTE0NDAsIm5iZiI6MTc0OTAxMTE0MCwicGF0aCI6Ii8zNjI5MTU3OS80MTYxMzU4MjEtNGNmMDMxZmEtMDA0MC00Nzc2LTljYTQtMTkwYjI4YzA4NjM5LnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNTA2MDQlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjUwNjA0VDA0MjU0MFomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPWM2MTljZjAxNGYyYzZhMzRlM2RiMDBkYmI3ZjFhZGM3MTYwZjhiYjYzNGQzMDE5NDMwZjgxNDZjMjI4NmIxMWEmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.pWwSqqVC9JunYrhqTbPAHk7uVbJMrNbErz6rFN79rE4

Yeah this happens consistently. If a given prompt fails once, it won't be succeeding with a retry either. Tried splitting the file to smaller pieces but it wasn't that large to begin with, so unlikely to relate to file size, also tried different models (deepseek, o1-mini, claude 3.5 and 3.7, all seem to behave the same way). I'm not seeing anything about this in logs - maybe an easy first step would be to get something in there. We're all developers here, and adding to the frustration is that there is no visibility into the issue that we might be able to work around if we only knew what was going on.

This is making windsurf unusable for me and I already had to switch back to cursor for now.

These errors keep happening more and more, and they’re making the app totally unusable. I got a subscription to an app, but I’m regretting it now.

I encountered this a while ago on all models. I switched back to Visual Code for a while and manually modified my codebase. Came back on Windsurf after like an hour, then I was able to use the latest Claude model. Maybe just make the IDE breathe for a while? 😂


I just tested it. They do use your credits even if says no credits consumed. They use both your flow credits and your premium credits.

So annoying about this....

Same error, I already stopped subscribing, losing credit with no return..

Cascade Error "No Credits Consumed on this tool call"
It seems whatever I write I am getting this response in Windsurf, with an error:
"an internal error occurred (error ID: 26ab42deea04485891b6c95e55392871"

I have tried both read and write mode and I have plenty of credits left, any clues as to what it might be so I can get back to working on my project?

Subscribe to status.. I can see error reported by Windsurf and Cursor https://status.codeium.com/incidents/lsjr1rwg6q0k

Claude Sonnet 3.7 returning errors Subscribe to Updates Identified Anthropic's Sonnet 3.7 model is experiencing issues right now. Please select a different model or try again later. Posted 26 minutes ago. Mar 18, 2025 - 08:19 UTC


same issue here, but switch to sonnet 3.5 model works on my side.

I found a fix, I signed out of the app and signed in again and its working. :)

It is already solved for me.

It seems like the Anthropic API was down for a moment.

Windsurf: ErrorCascade has encountered an internal error in this step.
Anyone else getting tons of this in Windsurf:

I'll show you the exact changes needed. First, let's create a comprehensive list of Google Fonts that would be great for book covers, then modify your code to use them.

First, in index.html, add this right after your meta tags:

ErrorCascade has encountered an internal error in this step.

Now, let's modify your script.js to use these Google Fonts. I'll create a new font configuration that replaces your base64 loading system:

ErrorCascade has encountered an internal error in this step.

Now you need to modify how fonts are applied to text. Replace your existing font application code with this:

ErrorCascade has encountered an internal error in this step.

I mean lots of this. Then to watch my plan dashboard and the credits disappearing without providing the code, is disheartening.

I have had a ticket open for over two (2 weeks) and never answered.

First time I tried Windsurf thought it was great, now it is just a money pit in my world.

Found this because I tried it out and was realizing I was flying through credits. Figured I was just being too unspecific with my requests and needing to be more careful. Turns out that this is pretty widespread. I had hoped that after a month they'd have gotten a handle on this. Guess I'm gonna try Cursor next.

Folks, I think that I have found the issue. Sometimes the file is being ran as a sever or something and directly editing the file is forbidden. You need to turn off the server first before editing!
Solved my problem.
Really hope WindSurf can be smarter at this!

Just create new conversation.

If there is a error in the step, it doesn't count toward your flow action credits.

Also, does your Cascade not recover properly when there is an internal step error - as in, it usually goes, "I'm sorry, let me try a different approach..."?

"Also, does your Cascade not recover properly when there is an internal step error - as in, it usually goes, "I'm sorry, let me try a different approach..."?"

Very rarely. It continues on like it gave me the code. Seems like it does not even know it did not give the code.

"If there is a error in the step, it doesn't count toward your flow action credits."

I beg to differ. I have been monitoring this closely and would not post false information - I do not play those games. I can get these messages (ErrorCascade), reload the plan usage dashboard on Codeium website and watch the usage increase. Also reflects the same in Windsurf Settings / Plan Info.


Same here, out of every 10-15 retries i might get one code edit. Sure not flow credits but it chews up chat credits. 150 overnight with no productivity. I'll try a VPN and see if that make a difference. Not sure why I think that, but here goes.


