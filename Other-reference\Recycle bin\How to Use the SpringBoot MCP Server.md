# How to Use the SpringBoot MCP Server

Ready to make your AI-powered apps even smarter? Today, we’re diving into the **SpringBoot MCP Server**, a fantastic tool that lets AI models like <PERSON> <PERSON> <PERSON><PERSON><PERSON> interact with your data through the Model Context Protocol (MCP). Imagine your AI pulling course information or searching for specific data with just a few commands—all powered by a lightweight Spring Boot app. In this conversational guide, we’ll walk you through setting up and using the **SpringBoot MCP Server**, complete with a test to see it in action. Let’s get coding!

## What Is the SpringBoot MCP Server?

The **SpringBoot MCP Server** is a Spring Boot application that uses the Spring AI MCP framework to expose your data as tools for AI models. It’s like giving your AI a direct line to your app’s data, such as course details, through standardized MCP tools. In this tutorial, we’ll focus on a server that offers two main tools:

- **Get all games**: Retrieves a list of all available games.
- **Search games by title**: Finds a specific game by its title.

This setup is perfect for integrating external data with AI models or building your own MCP servers. Plus, it’s built with Spring Boot, so it’s developer-friendly and easy to extend. Excited? Let’s set it up!

![spring boot](https://assets.apidog.com/blog-next/2025/07/spring-boot-la-gi.webp)

## How to Use the SpringBoot MCP Server

### Prerequisites

Before we start, make sure you have:

- **Java 24**: Download from [oracle.com](https://www.oracle.com/java/technologies/downloads/).
- **Maven 3.8+**: Install from [maven.apache.org](https://maven.apache.org/download.cgi).
- **IntelliJ IDEA**: Get it from [jetbrains.com](https://www.jetbrains.com/idea/) (or use your preferred IDE).
- **Claude Desktop or Cursor**: for testing with an AI client ([anthropic.com](https://www.anthropic.com/) & [curosor.com](https://cursor.com/)).
- **Basic Spring Boot Knowledge**: Being familiarity with Spring Boot basics helps.

### Step 1: Create a New Spring Boot Project

**Visit Spring Initializr**:

- Go to [start.spring.io](https://start.spring.io/).
- Set up your project:
- **Project**: Maven
- **Language**: Java
- **Spring Boot**: 3.5.4 (latest stable as of July 2025)
- **Packaging**: Jar
- **Java Version**: 24 (or your installed version)
- Add the **Spring AI MCP Server** dependency:

![spring initializr](https://assets.apidog.com/blog-next/2025/07/image-504.png)

- Click **Generate** and download the project.

**Open in IntelliJ IDEA**:

- Unzip the downloaded project and open it in IntelliJ IDEA as a new project.

### Step 2: Understand the Project Structure

Your **SpringBoot MCP Server** includes key components:

- **Game.java**: A record for course data (e.g., title and URL).
- **GameService.java**: Contains MCP tools with `@Tool` annotations.
- **TestMcpServerApplication.java**: The main app class that registers tools.
- **application.properties**: Configures the MCP server for STDIO transport.

![project structure](https://assets.apidog.com/blog-next/2025/07/Screenshot-2025-07-31-035418.png)

Here’s a sample `application.properties` setup:

```properties
spring.application.name=test-mcp-server
spring.main.web-application-type=none
spring.ai.mcp.server.name=game-demo-mcp
spring.ai.mcp.server.version=0.0.1
spring.main.banner-mode=off
logging.pattern.console=
```

This configures the app as a non-web server using STDIO transport, perfect for MCP communication.

### Step 3: Define the Data Model

Create a simple `Game.java` record to represent course data:

```java
package com.example.testmcpserver;

public record Game(String title, String url){
}
```

This immutable record holds course details like title and URL, making it easy for AI models to process.

### Step 4: Implement MCP Tools

In `GameService.java`, define tools using the `@Tool` annotation. Here’s an example:

```java
package com.example.testmcpserver;

import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class GameService {

    private static final Logger log = LoggerFactory.getLogger(GameService.class);
    private List<Game> games = new ArrayList<>();

    @Tool(name = "get_games", description = "Get a list of games from the collection")
    public List<Game> getGames() {
        return games;
    }

    @Tool(name = "get_game", description = "Get a single game from from the collection by title")
    public Game getGame(String title) {
        return games.stream().filter(course -> course.title().equals(title)).findFirst().orElse(null);
    }

    @PostConstruct
    public void init() {
        games.addAll(List.of(
                new Game("GTA 6", "https://www.rockstargames.com/VI"),
                new Game("FC 26","https://www.ea.com/en/games/ea-sports-fc/fc-26"),
                new Game("Call of Duty: Black Ops 7","https://www.callofduty.com/blackops7")
        ));
    }

}
```

These tools let AI models retrieve all courses or find a specific course by title.

### Step 5: Register Tools with the MCP Framework

In `TestMcpServerApplication.java`, register the tools:

```java
package com.example.testmcpserver;

import org.springframework.ai.support.ToolCallbacks;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

import java.util.List;

@SpringBootApplication
public class TestMcpServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(TestMcpServerApplication.class, args);
    }

    @Bean
    public List<ToolCallback> danTools(GameService gameService) {
        return List.of(ToolCallbacks.from(gameService));
    }

}
```

The `ToolCallbacks.from()` method scans `GameService` for `@Tool` annotations and registers them with the **SpringBoot MCP Server**.

### Step 6: Run the SpringBoot MCP Server

**Build and Run**:

- In IntelliJ, open a terminal and run:

```bash
mvn spring-boot:run
```

- The server starts as an MCP server using STDIO transport, with no web interface.

**Verify Tools**:

- The server registers two tools: `get_games` (list all games) and `get_game` (find a game by title).

### Step 7: Connect to Cursor or Claude Desktop

To use the **SpringBoot MCP Server** with Claude Desktop:

**Compile the Project**:

- Build the JAR file:

```bash
mvn clean package
```

- Find the JAR in `target/test-mcp-server-0.0.1-SNAPSHOT.jar`.

![target folder with jar file](https://assets.apidog.com/blog-next/2025/07/image-505.png)

**A. Configure Claude Desktop**:

- Open `claude_desktop_config.json` (macOS: `~/Library/Application Support/Claude`, Windows: `%APPDATA%\Claude`).
- Add:

```json
{
  "mcpServers": {
    "game-demo-mcp": {
      "command": "java",
      "args": [
        "-jar",
        "path/to/test-mcp-server-0.0.1-SNAPSHOT.jar"
      ]
    }
  }
}
```

- Replace `path/to/` with the actual path to your JAR file (e.g., `D:/Apidog_Projects/spring-boot-mcp-server/test-mcp-server/target/test-mcp-server-0.0.1-SNAPSHOT.jar`).

![edit claude configuration](https://assets.apidog.com/blog-next/2025/07/claude_developer_edit_config-5.png)

**Restart Claude Desktop**:

- Close and reopen Claude to apply the config.

**B. Configure Cursor**:

1. Navigate to **Settings**, then **Cursor Settings**
2. Select **Tools and Integrations**
3. Click **New MCP server**

![edit cursor mcp configuration](https://assets.apidog.com/blog-next/2025/07/Screenshot-2025-07-30-115653.png)

### Step 8: Test the SpringBoot MCP Server

Let’s test the server with Claude Desktop:

**Open Claude Desktop or Cursor and Issue a Command**:

- In your mcp client, enter:

> Using the MCP server, get all available games.

- Claude (or Cursor) should respond with:

```text
Available games:
- Title: GTA 6, URL: https://www.rockstargames.com/VI
- Title: FC 26, URL: https://www.ea.com/en/games/ea-sports-fc/fc-26
```

![testing the mcp server](https://assets.apidog.com/blog-next/2025/07/Screenshot-2025-07-30-120037.png)

**Try a Specific Game**:

- Enter:

> Using the MCP server, get the game with title "FC 26".

- Expected response:

```text
Game: FC 26, URL: https://www.ea.com/en/games/ea-sports-fc/fc-26
```

This confirms your **SpringBoot MCP Server** is working!

### Step 9: Extend the SpringBoot MCP Server

Want to make it even cooler? Here are some ideas:

- **Add More Courses**: Update `GameService`’s `init()` method to include more `Game` objects.
- **New Tools**: Add methods with `@Tool` annotations, like a search function:

```java
@Tool(name = "search_games", description = "Search games containing a keyword")
public List<Game> searchGames(String keyword) {
    return games.stream()
        .filter(game -> game.title().toLowerCase().contains(keyword.toLowerCase()))
        .collect(Collectors.toList());
}
```

- **Use a Database**: Replace the in-memory `games` list with a database connection.
- **Enhance Search**: Add fuzzy matching or category filters.

### Troubleshooting Tips

- **Server Not Starting?** Ensure Java 24 and Maven are installed, and check `application.properties` for correct settings.
- **Claude Not Connecting?** Verify the JAR path in `claude_desktop_config.json` and restart Claude.
- **Tools Not Found?** Confirm `@Tool` annotations are correct and `ToolCallbacks` is registered.
- **No Output?** Check the terminal running `mvn spring-boot:run` for errors.

## Why Use the SpringBoot MCP Server?

The **SpringBoot MCP Server** is a dream for developers who love Spring Boot’s simplicity and want to integrate AI with their data. It’s lightweight, uses STDIO for secure communication, and lets AI models like Claude access your app’s data in real-time. Whether you’re building a course platform or another data-driven app, this server makes AI integration a breeze.

Our test with Claude Desktop and Cursor, showed how easy it is to fetch game data. Imagine scaling this to handle complex queries or connecting to a real database—endless possibilities!

## Conclusion

And that’s it! You’ve learned how to set up and use the **SpringBoot MCP Server** to bring AI-powered data access to your apps. From creating a Spring Boot project to connecting it with Claude Desktop and testing course queries, you’re now ready to build smarter AI integrations. Try adding new tools or hooking up a database to take it further.
