Create a test scenario
When you want to send a series of requests sequentially, you need to create a test scenario.

1
Switch to the Tests module and click "New Test Scenario". Enter a name to continue.
2
Hover over Add Step and select Import from endpoint case.
3
Select the following two cases in sequence:
a. Add a new pet to the store (Success)
b. Find pet by ID (Pets sold)

4
Now you can see the two requests you just selected on the interface. Currently, I want to first add a pet, and then use the ID to get the information of the just-added pet.
5
Click on Find pet by ID (Pets sold), and you'll see the parameters for this request. In the Path param's petId, delete the original value, and then click the  magic wand button in the value box.
image.png
6
I now want to get the ID of the pet I just added from the return result of the previous step. Select Retrive pre-step data, and choose the previous step Add a new pet to the store (Success).

7
Click the  button to the right of the JSONPath Expression, and you'll see the JSONPath Extraction Tool. In this tool, the left side is the return result of the previous step, and when you enter the JSONPath in the top right, the bottom right will display the extracted result. Now let's enter $.data.id in the top right, and you'll see the result is the ID of the pet I just added.

8
Go back to Retrieve pre-step data and click Insert, and the expression will be written into the Path param of the request.
image.png
9
Click Save in the top right, set the environment at the top to Local mock, and then click Run at the top right.
10
You can see a test report, and click each step to expand the request and response. You can see that the id in the path parameter of the second request is just the pet id returned from the first request. In this way, we have completed the data transfer between the requests.
image.png
A test scenario includes a series of requests. These requests can be imported into the test scenario from endpoint specs or endpoint cases, and can be automatically or manually updated as the API spec changes.

Test scenarios also support logic components such as If, for, forEach, etc. You can pass data between requests, dynamically generate request parameters, and more.

Based on test scenarios, you can also view test reports, run performance tests, manage test data, integrate CI/CD, and more.

