Installing and running Apidog CLI
Apidog CLI is designed to run Apidog test scenarios in the command line. To get started using Apidog CLI, install Node.js, then Apidog CLI. Then you can run your test scenarios.

Installing Apidog CLI
Apidog CLI is built on Node.js. To run Apidog CLI, Please install Node.js before using it.

The Apidog CLI relies on Node.js version number >= v16.

Install Apidog CLI from npm globally on your system, enabling you to run it from anywhere:

npm install -g apidog-cli
Here are some commands to verify if Apidog CLI is installed successfully.

node -v && apidog -v && which node && which npm && which apidog
If installed successfully, it will directly print the version number and installation path after executing commands above.



Updating Apidog CLI
Use the command below to upgrade Apidog CLI.

$ npm install apidog-cli@latest -g
Running Apidog CLI
To run test scenarios using the Apidog CLI, you first need to create and orchestrate a test scenario. Then, you can run the test scenario from the command line using the Apidog CLI, just like running it in the visual interface, and get a test report.

The Apidog CLI supports two ways of running:

1.
Running with online data: This is suitable for live scenarios.

2.
Running with exported test scenarios: This is suitable for offline scenarios.

Run online data in real time
1
In a test scenario, switch to the CI/CD tab.


2
Configure the environment, test data, iterations, delay, etc.
3
In the CI/CD provider section, select "Command line".
4
Click "Add access token" button and then "Generate token".
Learn more about access token.


5
Click the command to copy it.
6
Paste and run the command in the command line. And you'll get a test report in command line.
In Step 2, the configurations you set will automatically determine the options and values utilized in the CLI:

Environment: Maps to -e <environmentId>.

Test data: Maps to -d <testDataId>.

Iterations: Maps to -n <n>.

Delay: Maps to --delay-request <n>.

Environment/Global variables: If selectingExport current value and use it, you can find an export option below to export the current values of the environment/global variables from your project to a file. This affects--variables <path>. You will need to import this file onto the machine running the CLI and specify its path via--variables <path>. Learn more about differences between "Use initial value" and "Export current value and use it" here.

If your test scenario also uses locally stored database connection configurations, the product interface will guide you to export these configurations to a local file, which affects --database-connection <path>. You will then need to import this exported file onto the machine running the CLI and specify its path in--database-connection <path>.

Run export data
1
In a test scenario, switch to the CI/CD tab.
2
Configure the environment, test data, iterations, delay, etc.
3
In the CI/CD provider, select "Command line" and then switch to "Run exported data".


4
Export the test scenario as a JSON file.
5
Copy the command displayed below.
6
Paste and run the command in the command line. You will get a command line test report.
The settings in step 2 (such as "Run Online Data") will automatically affect the options and values used in the CLI below. Note that when running in this way, the environment/global variables use the values from the file exported along with the test scenario. Learn more here.

CLI test report
After running the CLI, you will receive a command-line test report that includes execution statistics for test scenarios and validation and assertions for failed requests.



You can also find the /apidog-reports/ directory in the folder where you ran the CLI. This directory contains the CLI test report in HTML format.

Options
Apidog CLI provides a rich set of options to customize a collection run. Learn more at Apidog CLI Options.

Using Apidog CLI with CI/CD
Apidog CLI supports integration with various pipeline tools, such as Jenkins, GitLab, GitHub Actions, and more. Learn more about integrating CI/CD.

