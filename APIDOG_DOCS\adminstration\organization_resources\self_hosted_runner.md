Self-hosted Runner
The Apidog Self-hosted Runner is an automation tool that can be hosted on an independent server. It performs various tasks, such as running scheduled automated tests within Apidog, importing API documentationat a regular intervals, and returning mock response results.

TIP
Unlike the team-level self-hosted general Runner, the organization-level version can be shared across all projects within the organization's teams.

The setup, deployment, and usage of the organization-level self-hosted general Runner are nearly identical to those for the team-level version. Check out how to set it up here.

