## Add a new schema

<Steps>
  <Step>
    In the `APIs` module, click on the ➕ icon and select `New Schema` to create a new schema.
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/340981/image-preview" style="width: 240px" />
</p>
  </Step>
  <Step>
    Fill in the name and description of the schema.
  </Step>
  <Step>
    [Build the structure](doc-534897) of the schema in the Schema Editor.
  </Step>
</Steps>

## Manage Schemas

In the `Schemas` section of the APIs module, you can manage all schemas within the project—create new ones, rename, delete, and reorder them. 

You can organize schemas by creating folders for better structuring and organization.

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/340996/image-preview" style="width: 440px" />
</p>

:::highlight purple 
You can also create new schemas by importing from database tables or JSON schema files. Learn more about [Generate Schemas from JSON etc.](doc-534963).
:::