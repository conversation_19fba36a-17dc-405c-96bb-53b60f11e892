
Cursor recently announced the support for new Claude 4 models:

"We just added support for the new Claude 4 models: Sonnet and Opus. With this launch, we’re offering them at a 50% discount for around a week. We’ll make sure to announce pricing changes beforehand.

- Sonnet : 0.5 requests for regular 0.75 for thinking
- Opus: Only available in Max mode

Read more about them here: [Cursor – Models & Pricing](https://docs.cursor.com/models)

We’ve been really impressed with Sonnet 4’s coding ability. It’s much easier to guide than 3.7 and does a great job understanding codebases.""


However, Cursor users are facing issues with the new models, mainly in unabling to use the models. Below are some of the issues Cursor users are facing and some of the possible solutions:

The model claude-4-sonnet does not work with your current plan or api key 

We encountered an issue when using your API key: Provider was unable to process your request

(It costs your request but doesn’t work)

The failed attempts are counting against the monthly requests!  

I hope Cursor will give us back those ones.


I get `The model claude-4-sonnet-thinking does not work with your current plan or api key` – Interestingly if I lower it to `claude-3.7-sonnet`, I also get `The model claude-3.7-sonnet-thinking does not work with your current plan or api key`.

Is anybody else getting this issue (lower models also not working)?

Is claude opus and sonnet 4 only available on max and usage based pricing?

Can’t use any of them.

Im in Pro plan.

Linux.

Edit: version 0.50.5

For anyone who can’t request, try restarting Cursor.

It seems like the naming might be wrong?  
they updated the naming from claude-3.5-sonnet to claude-sonnet-4 ? i think thats what it seems.

I am also facing issues with the pro plan and access to sonnet 4. Opus is labeled MAX only.  
I have to enable priced-useage to be able to use sonnet 4? thats the only way i got it to work.

![image](https://us1.discourse-cdn.com/flex020/uploads/cursor1/original/3X/b/d/bd1870ed25a0d4e31bff065d14298bd7fe8e15d7.png)

This what my useage logs are saying.

Ah, starting a new chat did the trick! (Restarting Cursor alone didn’t help).

If anyone still has an issue, please look at possible solutions here, like starting new chat.

claude-4-sonnet and claude-4-sonnet-thinking are the models in cursor settings.

Did you restart Cursor and started a new chat?

Hello,  
I don’t understand, as a Free user of Cursor with my Anthropic API key, I cannot use Claude 4 (sonnet or opus)?

I have tried two things:

- Directly put the model name from the documentation: [/docs/about-claude/models/overview](https://docs.anthropic.com/en/docs/about-claude/models/overview) so `claude-sonnet-4-20250514` and I get this message:

[![image](https://us1.discourse-cdn.com/flex020/uploads/cursor1/original/3X/3/f/3f2306dfd7938aef37f7dead11a531ef47341624.png)

- Directly use the model `claude-4-sonnet` displayed in the Cursor settings and I get this message:

> Anthropic’s latest models are currently only available to paid users. Please upgrade to a paid plan to use these models.

What did I do wrong?

Thanks in advance!

We're experiencing high demand for Claude 4 Opus right now. Please switch to the 'auto-select' model, another model, or try again in a few moments

I think there is for the begin not much possible from Cursors side. Anthropic has heavy load on Claude 4 as everyone (also outside Cursor) wants to test it and use it. Such new model launches cause often temporary overloads and Anthropic has to limit access as they dont have unlimited servers.



Why is there only a max option for using Claude 4 Opus? This is annoying. It makes you search for alternatives. It already sends 1 request every 10 minutes because of the density.



As a brand new model, our capacity for Claude 4 is limited at the moment due to the model’s popularity and therefore is not currently available in the slow pool.

Once it’s usage has stabilised and further capacity from our inference providers is available, it should be available in the slow pool soon, but for now if you are out of fast requests, you will need to enable usage-based pricing to use this model.

Some file edit issue were occurring over the last 12 hours (as flagged by [@RichardChinnis](https://forum.cursor.com/u/richardchinnis) for example), but these should now be resolved.



I have the student premium version, and I turned on usage-pricing just so I can test out Claude 4 sonnet and sonnet thinking, I still have like 200+ premium requests though? I hope this is fixed because I don't want to be charged for it when it should've been using my request



Anthropic's latest models are currently only available to paid users. Please upgrade to a paid plan to use these models.
