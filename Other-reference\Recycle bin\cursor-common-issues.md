<!DOCTYPE html>
<html lang="en" class="dark">
    <head>
        <meta charSet="utf-8"/>
        <meta name="viewport" content="width=device-width"/>
        <link rel="apple-touch-icon" type="image/png" sizes="180x180" href="https://mintlify.s3-us-west-1.amazonaws.com/cursor/_generated/favicon/apple-touch-icon.png?v=3"/>
        <link rel="icon" type="image/png" sizes="32x32" href="https://mintlify.s3-us-west-1.amazonaws.com/cursor/_generated/favicon/favicon-32x32.png?v=3"/>
        <link rel="icon" type="image/png" sizes="16x16" href="https://mintlify.s3-us-west-1.amazonaws.com/cursor/_generated/favicon/favicon-16x16.png?v=3"/>
        <link rel="shortcut icon" type="image/x-icon" href="https://mintlify.s3-us-west-1.amazonaws.com/cursor/_generated/favicon/favicon.ico?v=3"/>
        <meta name="msapplication-config" content="https://mintlify.s3-us-west-1.amazonaws.com/cursor/_generated/favicon/browserconfig.xml?v=3"/>
        <meta name="apple-mobile-web-app-title" content="Cursor"/>
        <meta name="application-name" content="Cursor"/>
        <meta name="msapplication-TileColor" content="#0C0C15"/>
        <meta name="theme-color" content="#ffffff"/>
        <meta name="generator" content="Mintlify"/>
        <link rel="sitemap" type="application/xml" href="/sitemap.xml"/>
        <meta name="charset" content="utf-8"/>
        <meta name="og:type" content="website"/>
        <meta name="og:site_name" content="Cursor"/>
        <meta name="twitter:card" content="summary_large_image"/>
        <meta name="og:title" content="Cursor – Common Issues"/>
        <meta name="twitter:title" content="Cursor – Common Issues"/>
        <meta name="og:description" content="Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs"/>
        <meta name="description" content="Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs"/>
        <meta name="og:image" content="https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/common-issues.png?v=1743819390998"/>
        <meta name="og:locale" content="en_US"/>
        <meta name="og:logo" content="/images/logo/app-logo.svg"/>
        <meta name="article:publisher" content="Anysphere Inc."/>
        <meta name="twitter:description" content="Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs"/>
        <meta name="twitter:url" content="https://cursor.com"/>
        <meta name="twitter:image" content="https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/common-issues.png?v=1743819390998"/>
        <meta name="twitter:site" content="@cursor_ai"/>
        <meta name="og:image:width" content="1200"/>
        <meta name="og:image:height" content="630"/>
        <meta property="twitter:image:width" content="1200"/>
        <meta property="twitter:image:height" content="630"/>
        <title>Cursor – Common Issues</title>
        <meta name="og:url" content="/troubleshooting/common-issues"/>
        <link rel="canonical" href="/troubleshooting/common-issues"/>
        <meta name="next-head-count" content="36"/>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.css" integrity="sha384-Xi8rHCmBmhbuyyhbI88391ZKP2dmfnOl4rT9ZfRI7mLTdk1wblIUnrIq35nqwEvC" crossorigin="anonymous"/>
        <link rel="preload" href="/mintlify-assets/_next/static/media/a34f9d1faa5f3315-s.p.woff2" as="font" type="font/woff2" crossorigin="anonymous" data-next-font="size-adjust"/>
        <link rel="preload" href="/mintlify-assets/_next/static/media/bb3ef058b751a6ad-s.p.woff2" as="font" type="font/woff2" crossorigin="anonymous" data-next-font="size-adjust"/>
        <script id="mode-toggle" data-nscript="beforeInteractive">
            try {
                if (localStorage.isDarkMode === 'true') {
                    document.documentElement.classList.add('dark');
                } else if (localStorage.isDarkMode === 'false') {
                    document.documentElement.classList.remove('dark');
                } else if ((false && !('isDarkMode'in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches) || false) {
                    document.documentElement.classList.add('dark');
                } else {
                    document.documentElement.classList.remove('dark');
                }
            } catch (_) {}
        </script>
        <link rel="preload" href="/mintlify-assets/_next/static/css/ce91198cc577b7f8.css" as="style"/>
        <link rel="stylesheet" href="/mintlify-assets/_next/static/css/ce91198cc577b7f8.css" data-n-g=""/>
        <link rel="preload" href="/mintlify-assets/_next/static/css/8c76fb7d3768dfe7.css" as="style"/>
        <link rel="stylesheet" href="/mintlify-assets/_next/static/css/8c76fb7d3768dfe7.css" data-n-p=""/>
        <noscript data-n-css=""></noscript>
        <script defer="" nomodule="" src="/mintlify-assets/_next/static/chunks/polyfills-42372ed130431b0a.js"></script>
        <script src="/mintlify-assets/_next/static/chunks/webpack-67f6fe4845d1eac1.js" defer=""></script>
        <script src="/mintlify-assets/_next/static/chunks/framework-822fcadcf2c4b2cf.js" defer=""></script>
        <script src="/mintlify-assets/_next/static/chunks/main-62e52a310a4175a3.js" defer=""></script>
        <script src="/mintlify-assets/_next/static/chunks/pages/_app-cb80c03ee4693f5b.js" defer=""></script>
        <script src="/mintlify-assets/_next/static/chunks/2edb282b-a83f7ffd007bccf0.js" defer=""></script>
        <script src="/mintlify-assets/_next/static/chunks/e893f787-f6a1094a35763a0d.js" defer=""></script>
        <script src="/mintlify-assets/_next/static/chunks/086d643d-6f7196a364073d16.js" defer=""></script>
        <script src="/mintlify-assets/_next/static/chunks/6400-31fb6e73beadaaa9.js" defer=""></script>
        <script src="/mintlify-assets/_next/static/chunks/5094-367d16afbf96e822.js" defer=""></script>
        <script src="/mintlify-assets/_next/static/chunks/9099-a09015289c17ce8b.js" defer=""></script>
        <script src="/mintlify-assets/_next/static/chunks/474-140d983a37f4ad6d.js" defer=""></script>
        <script src="/mintlify-assets/_next/static/chunks/pages/_sites/%5Bsubdomain%5D/%5B%5B...slug%5D%5D-2871928329fe8f21.js" defer=""></script>
        <script src="/mintlify-assets/_next/static/xsFVauHHhYjewKg9sNuSJ/_buildManifest.js" defer=""></script>
        <script src="/mintlify-assets/_next/static/xsFVauHHhYjewKg9sNuSJ/_ssgManifest.js" defer=""></script>
        <style id="__jsx-168234603">
            :root {
                --font-inter: '__Inter_a4e4e2', '__Inter_Fallback_a4e4e2';
                --font-jetbrains-mono: '__JetBrains_Mono_3bbdad', '__JetBrains_Mono_Fallback_3bbdad'
            }
        </style>
    </head>
    <div id="__next">
        <main class="jsx-168234603">
            <style>
                :root {
                    --primary: 12 12 21;
                    --primary-light: 255 255 255;
                    --primary-dark: 12 12 21;
                    --background-light: 255 255 255;
                    --background-dark: 0 0 0;
                    --gray-50: 243 243 243;
                    --gray-100: 238 238 238;
                    --gray-200: 222 222 223;
                    --gray-300: 206 206 206;
                    --gray-400: 158 158 159;
                    --gray-500: 112 112 112;
                    --gray-600: 80 80 80;
                    --gray-700: 62 62 63;
                    --gray-800: 37 37 37;
                    --gray-900: 23 23 23;
                    --gray-950: 10 10 10;
                }
            </style>
            <style>
                ::-webkit-scrollbar {
                    width: 6px;
                }

                ::-webkit-scrollbar-track {
                    background: white;
                }

                ::-webkit-scrollbar-thumb {
                    background-color: #eeee;
                    border-radius: 10px;
                    border: none;
                }

                html {
                    scrollbar-width: thin;
                    scrollbar-color: #eeee white;
                    scrollbar-gutter: stable;
                }

                html.dark {
                    scrollbar-width: thin;
                    scrollbar-color: #222 #222;
                    scrollbar-gutter: stable;
                }

                html.dark ::-webkit-scrollbar-track {
                    background: #222;
                }

                html.dark ::-webkit-scrollbar-thumb {
                    background-color: #222;
                }

                html.dark ::-webkit-scrollbar-thumb:hover {
                    background-color: #333;
                }

                a:has(> code) {
                    border-bottom: none !important;
                }

                a>code {
                    cursor: pointer;
                    border-bottom: #0c0c15 1px solid !important;
                }

                a>code:hover {
                    border-bottom: #0c0c15 2px solid !important;
                }

                #content-area>div.leading-6.mt-14>footer>div.sm\:flex>a {
                    display: none;
                }

                * {
                    /* Use system font stack as the default font */
                    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                }

                /* h1,
h2,
h3,
h4,
h5,
h6, */
                code * {
                    font-family: monospace;
                }

                #header h1 {
                    font-size: 2.25rem;
                }

                /* #content-container h2 {
  font-size: 2rem;
}

#content-container h3 {
  font-size: 1.75rem;
}

#content-container h4 {
  font-size: 1.25rem;
} */
                .full-width-table table {
                    display: table;
                }

                .full-width-table table th {
                    text-align: left;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .full-width-table table th:nth-child(3), .full-width-table table th:nth-child(4), .full-width-table table th:nth-child(5) {
                    text-align: center;
                }

                @media (max-width: 640px) {
                    .full-width-table table th:nth-child(2), .full-width-table table td:nth-child(2) {
                        display: none;
                    }
                }

                :not(.dark) .prose :where(a):not(:where([class~=not-prose], [class~=not-prose] *)):not(h1, h1 *, h2, h2 *, h3, h3 *, h4, h4 *, h5, h5 *, h6, h6 *) {
                    border-bottom: #666666 1px solid;
                }

                /* img {
  border-radius: 0.5rem;
} */
                .max-pill {
                    background-color: white;
                    color: #0c0c15;
                    padding: 1px 4px;
                    border-radius: 8px;
                    font-size: 0.6rem;
                    font-weight: 500;
                    display: inline-block;
                    margin-left: 2px;
                    margin-right: 4px;
                    line-height: 1.2;
                    border: 1px solid #0c0c15;
                }

                .dark .max-pill {
                    background-color: #0c0c15;
                    color: white;
                    border-color: white;
                }

                .footnotes-container {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    grid-auto-flow: row;
                    margin-top: -14px;
                    padding-bottom: 1rem;
                    border-bottom: 1px solid rgb(var(--gray-800)/.5);
                    line-height: 1.6;
                }

                @media (max-width: 640px) {
                    .footnotes-container {
                        grid-template-columns: 1fr;
                    }
                }

                .footnotes-container p {
                    font-size: 0.8rem;
                    opacity: 0.9;
                    margin: 0 0 0 0.35em;
                }

                .dark .footnotes-container {
                    border-bottom-color: rgb(var(--gray-800)/.5);
                }

                .no-wrap {
                    white-space: nowrap;
                }

                .flowchart {
                    margin: 0 auto;
                }
            </style>
            <span class="fixed inset-0 bg-background-light dark:bg-background-dark -z-10"></span>
            <span class="block absolute dark:hidden inset-0 overflow-hidden"></span>
            <span class="hidden absolute dark:block inset-0 overflow-hidden"></span>
            <div class="relative antialiased text-gray-500 dark:text-gray-400">
                <div id="navbar" class="z-30 fixed lg:sticky top-0 w-full">
                    <div id="navbar-transition" class="absolute w-full h-full backdrop-blur flex-none transition-colors duration-500 border-b border-gray-500/5 dark:border-gray-300/[0.06] supports-backdrop-blur:bg-background-light/60 dark:bg-transparent"></div>
                    <div class="max-w-8xl mx-auto relative">
                        <div>
                            <div class="relative">
                                <div class="flex items-center lg:px-12 h-16 min-w-0 mx-4 lg:mx-0">
                                    <div class="h-full relative flex-1 flex items-center gap-x-4 min-w-0 border-b border-gray-500/5 dark:border-gray-300/[0.06]">
                                        <div class="flex-1 flex items-center gap-x-4">
                                            <a href="/">
                                                <span class="sr-only">Cursor
                                                <!-- -->
                                                home page</span>
                                                <img class="nav-logo w-auto h-7 relative object-contain block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/logo/app-logo.svg" alt="light logo"/>
                                                <img class="nav-logo w-auto h-7 relative object-contain hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/logo/app-logo.svg" alt="dark logo"/>
                                            </a>
                                            <div class="flex items-center gap-x-2"></div>
                                        </div>
                                        <div class="relative hidden lg:flex items-center gap-2.5 flex-1">
                                            <button type="button" class="flex pointer-events-auto rounded-xl w-full items-center text-sm leading-6 h-9 pl-3.5 pr-3 shadow-sm text-gray-500 dark:text-white/50 bg-background-light dark:bg-background-dark dark:brightness-[1.1] dark:ring-1 dark:hover:brightness-[1.25] ring-1 ring-gray-400/20 hover:ring-gray-600/25 dark:ring-gray-600/30 dark:hover:ring-gray-500/30 focus:outline-primary justify-between truncate gap-2 min-w-[43px]" id="search-bar-entry">
                                                <div class="flex items-center gap-2 min-w-[42px]">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search min-w-4 flex-none text-gray-700 hover:text-gray-800 dark:text-gray-400 hover:dark:text-gray-200">
                                                        <circle cx="11" cy="11" r="8"></circle>
                                                        <path d="m21 21-4.3-4.3"></path>
                                                    </svg>
                                                    <div class="truncate min-w-0">Search...</div>
                                                </div>
                                            </button>
                                            <button type="button" class="flex-none hidden lg:flex items-center justify-center gap-1.5 pl-3 pr-3.5 h-9 rounded-xl shadow-sm bg-background-light dark:bg-background-dark dark:brightness-[1.1] dark:ring-1 dark:hover:brightness-[1.25] ring-1 ring-gray-400/20 hover:ring-gray-600/25 dark:ring-gray-600/30 dark:hover:ring-gray-500/30 focus:outline-primary">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" class="w-4 h-4 shrink-0 text-gray-700 hover:text-gray-800 dark:text-gray-400 hover:dark:text-gray-200">
                                                    <g fill="currentColor">
                                                        <path d="M5.658,2.99l-1.263-.421-.421-1.263c-.137-.408-.812-.408-.949,0l-.421,1.263-1.263,.421c-.204,.068-.342,.259-.342,.474s.138,.406,.342,.474l1.263,.421,.421,1.263c.068,.204,.26,.342,.475,.342s.406-.138,.475-.342l.421-1.263,1.263-.421c.204-.068,.342-.259,.342-.474s-.138-.406-.342-.474Z" fill="currentColor" data-stroke="none" stroke="none"></path>
                                                        <polygon points="9.5 2.75 11.412 7.587 16.25 9.5 11.412 11.413 9.5 16.25 7.587 11.413 2.75 9.5 7.587 7.587 9.5 2.75" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></polygon>
                                                    </g>
                                                </svg>
                                                <span class="text-sm text-gray-500 dark:text-white/50 whitespace-nowrap">Ask AI</span>
                                            </button>
                                        </div>
                                        <div class="flex-1 relative hidden lg:flex items-center ml-auto justify-end space-x-4">
                                            <nav class="text-sm">
                                                <ul class="flex space-x-6 items-center">
                                                    <li class="navbar-link">
                                                        <a href="https://cursor.com/settings" class="flex items-center gap-1.5 whitespace-nowrap font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" target="_blank">Sign in</a>
                                                    </li>
                                                    <li class="block lg:hidden">
                                                        <a class="flex items-center gap-1.5 whitespace-nowrap font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="https://cursor.com">Download</a>
                                                    </li>
                                                    <li class="whitespace-nowrap hidden lg:flex" id="topbar-cta-button">
                                                        <a target="_blank" class="group px-4 py-1.5 relative inline-flex items-center text-sm font-medium" href="https://cursor.com">
                                                            <span class="absolute inset-0 bg-primary-dark rounded-full group-hover:opacity-[0.9]"></span>
                                                            <div class="mr-0.5 space-x-2.5 flex items-center">
                                                                <span class="z-10 text-white">Download</span>
                                                                <svg width="3" height="24" viewBox="0 -9 3 24" class="h-5 rotate-0 overflow-visible text-white/90">
                                                                    <path d="M0 0L3 3L0 6" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
                                                                </svg>
                                                            </div>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </nav>
                                            <div class="flex items-center">
                                                <button class="group p-2 flex items-center justify-center" aria-label="Toggle dark mode">
                                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" stroke="currentColor" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 block text-gray-400 dark:hidden group-hover:text-gray-600">
                                                        <g clip-path="url(#clip0_2880_7340)">
                                                            <path d="M8 1.11133V2.00022" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                            <path d="M12.8711 3.12891L12.2427 3.75735" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                            <path d="M14.8889 8H14" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                            <path d="M12.8711 12.8711L12.2427 12.2427" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                            <path d="M8 14.8889V14" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                            <path d="M3.12891 12.8711L3.75735 12.2427" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                            <path d="M1.11133 8H2.00022" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                            <path d="M3.12891 3.12891L3.75735 3.75735" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                            <path d="M8.00043 11.7782C10.0868 11.7782 11.7782 10.0868 11.7782 8.00043C11.7782 5.91402 10.0868 4.22266 8.00043 4.22266C5.91402 4.22266 4.22266 5.91402 4.22266 8.00043C4.22266 10.0868 5.91402 11.7782 8.00043 11.7782Z" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                        </g>
                                                        <defs>
                                                            <clipPath id="clip0_2880_7340">
                                                                <rect width="16" height="16" fill="white"></rect>
                                                            </clipPath>
                                                        </defs>
                                                    </svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-moon h-4 w-4 hidden dark:block text-gray-500 dark:group-hover:text-gray-300">
                                                        <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="flex lg:hidden items-center gap-3">
                                            <button type="button" class="text-gray-500 w-8 h-8 flex items-center justify-center hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300" id="search-bar-entry-mobile">
                                                <span class="sr-only">Search...</span>
                                                <svg class="h-4 w-4 bg-gray-500 dark:bg-gray-400 hover:bg-gray-600 dark:hover:bg-gray-300" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/magnifying-glass.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/magnifying-glass.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                            </button>
                                            <button>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" class="size-4.5 text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                                    <g fill="currentColor">
                                                        <path d="M5.658,2.99l-1.263-.421-.421-1.263c-.137-.408-.812-.408-.949,0l-.421,1.263-1.263,.421c-.204,.068-.342,.259-.342,.474s.138,.406,.342,.474l1.263,.421,.421,1.263c.068,.204,.26,.342,.475,.342s.406-.138,.475-.342l.421-1.263,1.263-.421c.204-.068,.342-.259,.342-.474s-.138-.406-.342-.474Z" fill="currentColor" data-stroke="none" stroke="none"></path>
                                                        <polygon points="9.5 2.75 11.412 7.587 16.25 9.5 11.412 11.413 9.5 16.25 7.587 11.413 2.75 9.5 7.587 7.587 9.5 2.75" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></polygon>
                                                    </g>
                                                </svg>
                                            </button>
                                            <button aria-label="More actions" class="h-7 w-5 flex items-center justify-end">
                                                <svg class="h-4 w-4 bg-gray-500 dark:bg-gray-400 hover:bg-gray-600 dark:hover:bg-gray-300" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/ellipsis-vertical.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/ellipsis-vertical.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="flex items-center h-14 py-4 px-5 lg:hidden">
                                    <div class="text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300">
                                        <span class="sr-only">Navigation</span>
                                        <svg class="h-4" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                                            <path d="M0 96C0 78.3 14.3 64 32 64H416c17.7 0 32 14.3 32 32s-14.3 32-32 32H32C14.3 128 0 113.7 0 96zM0 256c0-17.7 14.3-32 32-32H416c17.7 0 32 14.3 32 32s-14.3 32-32 32H32c-17.7 0-32-14.3-32-32zM448 416c0 17.7-14.3 32-32 32H32c-17.7 0-32-14.3-32-32s14.3-32 32-32H416c17.7 0 32 14.3 32 32z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4 flex text-sm leading-6 whitespace-nowrap min-w-0 space-x-3">
                                        <div class="flex items-center space-x-3">
                                            <span>Troubleshooting</span>
                                            <svg width="3" height="24" viewBox="0 -9 3 24" class="h-5 rotate-0 overflow-visible fill-gray-400">
                                                <path d="M0 0L3 3L0 6" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
                                            </svg>
                                        </div>
                                        <div class="font-semibold text-gray-900 truncate dark:text-gray-200">Common Issues</div>
                                    </div>
                                </button>
                            </div>
                            <div class="hidden lg:flex px-12 h-12">
                                <div class="nav-tabs h-full flex text-sm space-x-6">
                                    <a class="link nav-tabs-item group relative h-full flex items-center hover:text-gray-800 dark:hover:text-gray-300 text-gray-800 dark:text-gray-200 font-semibold" href="/welcome">
                                        Documentation<div class="absolute bottom-0 h-[1.5px] w-full bg-primary dark:bg-primary-light"></div>
                                    </a>
                                    <a class="link nav-tabs-item group relative h-full flex items-center font-medium text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300" href="/guides/working-with-context">
                                        Guides<div class="absolute bottom-0 h-[1.5px] w-full group-hover:bg-gray-200 dark:group-hover:bg-gray-700"></div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="max-w-8xl px-4 mx-auto lg:px-8">
                    <div class="z-20 hidden lg:block fixed bottom-0 right-auto w-[18rem]" id="sidebar" style="top:7rem">
                        <div class="absolute inset-0 z-10 stable-scrollbar-gutter overflow-auto pr-8 pb-10" id="sidebar-content">
                            <div class="relative lg:text-sm lg:leading-6">
                                <div class="sticky top-0 h-8 z-10 bg-gradient-to-b from-background-light dark:from-background-dark"></div>
                                <div id="navigation-items">
                                    <li class="list-none">
                                        <a href="https://cursor.com/" target="_blank" rel="noreferrer" class="link nav-anchor pl-4 group flex items-center lg:text-sm lg:leading-6 mb-5 sm:mb-4 font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300">
                                            <div class="mr-4 rounded-md p-1 shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 group-hover:brightness-100 group-hover:ring-0 ring-1 ring-gray-950/5 dark:ring-gray-700/40">
                                                <svg class="h-4 w-4 secondary-opacity group-hover:fill-primary-dark group-hover:bg-white bg-gray-400 dark:bg-gray-500" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/duotone/globe.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/duotone/globe.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                            </div>
                                            Website
                                        </a>
                                    </li>
                                    <li class="list-none">
                                        <a href="https://forum.cursor.com/" target="_blank" rel="noreferrer" class="link nav-anchor pl-4 group flex items-center lg:text-sm lg:leading-6 mb-5 sm:mb-4 font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300">
                                            <div class="mr-4 rounded-md p-1 shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 group-hover:brightness-100 group-hover:ring-0 ring-1 ring-gray-950/5 dark:ring-gray-700/40">
                                                <svg class="h-4 w-4 secondary-opacity group-hover:fill-primary-dark group-hover:bg-white bg-gray-400 dark:bg-gray-500" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/duotone/newspaper.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/duotone/newspaper.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                            </div>
                                            Forum
                                        </a>
                                    </li>
                                    <li class="list-none">
                                        <a href="mailto:<EMAIL>" target="_blank" rel="noreferrer" class="link nav-anchor pl-4 group flex items-center lg:text-sm lg:leading-6 mb-5 sm:mb-4 font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300">
                                            <div class="mr-4 rounded-md p-1 shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 group-hover:brightness-100 group-hover:ring-0 ring-1 ring-gray-950/5 dark:ring-gray-700/40">
                                                <svg class="h-4 w-4 secondary-opacity group-hover:fill-primary-dark group-hover:bg-white bg-gray-400 dark:bg-gray-500" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/duotone/headset.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/duotone/headset.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                            </div>
                                            Support
                                        </a>
                                    </li>
                                    <div class="mt-12 lg:mt-8">
                                        <div class="sidebar-group-header flex items-center gap-2.5 pl-4 mb-3.5 lg:mb-2.5 font-semibold text-gray-900 dark:text-gray-200">
                                            <h5 id="sidebar-title">Get Started</h5>
                                        </div>
                                        <ul id="sidebar-group">
                                            <li id="/welcome" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/welcome">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Welcome to Cursor</div>
                                                    </div>
                                                </a>
                                            </li>
                                            <li id="/get-started/installation" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/get-started/installation">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Installation</div>
                                                    </div>
                                                </a>
                                            </li>
                                            <li id="/faq" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/faq">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">FAQ</div>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="mt-12 lg:mt-8">
                                        <div class="sidebar-group-header flex items-center gap-2.5 pl-4 mb-3.5 lg:mb-2.5 font-semibold text-gray-900 dark:text-gray-200">
                                            <h5 id="sidebar-title">Editor</h5>
                                        </div>
                                        <ul id="sidebar-group">
                                            <li>
                                                <div class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem">
                                                    <div class="">Tab</div>
                                                    <svg width="8" height="24" viewBox="0 -9 3 24" class="transition-transform text-gray-400 overflow-visible group-hover:text-gray-600 dark:text-gray-600 dark:group-hover:text-gray-400 -mr-0.5">
                                                        <path d="M0 0L3 3L0 6" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
                                                    </svg>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem">
                                                    <div class="">Chat</div>
                                                    <svg width="8" height="24" viewBox="0 -9 3 24" class="transition-transform text-gray-400 overflow-visible group-hover:text-gray-600 dark:text-gray-600 dark:group-hover:text-gray-400 -mr-0.5">
                                                        <path d="M0 0L3 3L0 6" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
                                                    </svg>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem">
                                                    <div class="">Inline Edit (⌘K)</div>
                                                    <svg width="8" height="24" viewBox="0 -9 3 24" class="transition-transform text-gray-400 overflow-visible group-hover:text-gray-600 dark:text-gray-600 dark:group-hover:text-gray-400 -mr-0.5">
                                                        <path d="M0 0L3 3L0 6" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
                                                    </svg>
                                                </div>
                                            </li>
                                            <li id="/models" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/models">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Models &amp;Pricing</div>
                                                    </div>
                                                </a>
                                            </li>
                                            <li id="/kbd" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/kbd">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Keyboard Shortcuts</div>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <div class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem">
                                                    <div class="">Features</div>
                                                    <svg width="8" height="24" viewBox="0 -9 3 24" class="transition-transform text-gray-400 overflow-visible group-hover:text-gray-600 dark:text-gray-600 dark:group-hover:text-gray-400 -mr-0.5">
                                                        <path d="M0 0L3 3L0 6" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
                                                    </svg>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="mt-12 lg:mt-8">
                                        <div class="sidebar-group-header flex items-center gap-2.5 pl-4 mb-3.5 lg:mb-2.5 font-semibold text-gray-900 dark:text-gray-200">
                                            <h5 id="sidebar-title">Context</h5>
                                        </div>
                                        <ul id="sidebar-group">
                                            <li id="/context/codebase-indexing" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/context/codebase-indexing">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Codebase Indexing</div>
                                                    </div>
                                                </a>
                                            </li>
                                            <li id="/context/rules" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/context/rules">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Rules</div>
                                                    </div>
                                                </a>
                                            </li>
                                            <li id="/context/management" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/context/management">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Managing Context</div>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <div class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem">
                                                    <div class="">@ Symbols</div>
                                                    <svg width="8" height="24" viewBox="0 -9 3 24" class="transition-transform text-gray-400 overflow-visible group-hover:text-gray-600 dark:text-gray-600 dark:group-hover:text-gray-400 -mr-0.5">
                                                        <path d="M0 0L3 3L0 6" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
                                                    </svg>
                                                </div>
                                            </li>
                                            <li id="/context/ignore-files" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/context/ignore-files">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Ignore Files</div>
                                                    </div>
                                                </a>
                                            </li>
                                            <li id="/context/model-context-protocol" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/context/model-context-protocol">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Model Context Protocol</div>
                                                    </div>
                                                </a>
                                            </li>
                                            <li id="/context/max-mode" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/context/max-mode">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Max Mode</div>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="mt-12 lg:mt-8">
                                        <div class="sidebar-group-header flex items-center gap-2.5 pl-4 mb-3.5 lg:mb-2.5 font-semibold text-gray-900 dark:text-gray-200">
                                            <h5 id="sidebar-title">Account</h5>
                                        </div>
                                        <ul id="sidebar-group">
                                            <li id="/account/plans-and-usage" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/account/plans-and-usage">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Plans &amp;Usage</div>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <div class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem">
                                                    <div class="">Business</div>
                                                    <svg width="8" height="24" viewBox="0 -9 3 24" class="transition-transform text-gray-400 overflow-visible group-hover:text-gray-600 dark:text-gray-600 dark:group-hover:text-gray-400 -mr-0.5">
                                                        <path d="M0 0L3 3L0 6" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
                                                    </svg>
                                                </div>
                                            </li>
                                            <li id="/account/dashboard" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/account/dashboard">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Dashboard</div>
                                                    </div>
                                                </a>
                                            </li>
                                            <li id="/account/billing" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/account/billing">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Billing</div>
                                                    </div>
                                                </a>
                                            </li>
                                            <li id="https://cursor.com/pricing" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" target="_blank" href="https://cursor.com/pricing">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Pricing</div>
                                                        <div class="">
                                                            <svg class="h-2.5 text-gray-400 overflow-visible group-hover:text-gray-600 dark:text-gray-600 dark:group-hover:text-gray-400 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" fill="currentColor">
                                                                <path d="M328 96c13.3 0 24 10.7 24 24V360c0 13.3-10.7 24-24 24s-24-10.7-24-24V177.9L73 409c-9.4 9.4-24.6 9.4-33.9 0s-9.4-24.6 0-33.9l231-231H88c-13.3 0-24-10.7-24-24s10.7-24 24-24H328z"></path>
                                                            </svg>
                                                        </div>
                                                    </div>
                                                </a>
                                            </li>
                                            <li id="/account/privacy" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/account/privacy">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Privacy &amp;Security</div>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="mt-12 lg:mt-8">
                                        <div class="sidebar-group-header flex items-center gap-2.5 pl-4 mb-3.5 lg:mb-2.5 font-semibold text-gray-900 dark:text-gray-200">
                                            <h5 id="sidebar-title">Settings</h5>
                                        </div>
                                        <ul id="sidebar-group">
                                            <li id="/settings/api-keys" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/settings/api-keys">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Custom API Keys</div>
                                                    </div>
                                                </a>
                                            </li>
                                            <li id="/settings/beta" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/settings/beta">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Early Access Program</div>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="mt-12 lg:mt-8">
                                        <div class="sidebar-group-header flex items-center gap-2.5 pl-4 mb-3.5 lg:mb-2.5 font-semibold text-gray-900 dark:text-gray-200">
                                            <h5 id="sidebar-title">Troubleshooting</h5>
                                        </div>
                                        <ul id="sidebar-group">
                                            <li id="/troubleshooting/common-issues" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl bg-primary/10 text-primary font-semibold dark:text-primary-light dark:bg-primary-light/10" style="padding-left:1rem" href="/troubleshooting/common-issues">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Common Issues</div>
                                                    </div>
                                                </a>
                                            </li>
                                            <li id="/troubleshooting/troubleshooting-guide" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/troubleshooting/troubleshooting-guide">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Troubleshooting Guide</div>
                                                    </div>
                                                </a>
                                            </li>
                                            <li id="/troubleshooting/request-reporting" class="relative scroll-m-4 first:scroll-m-20">
                                                <a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/troubleshooting/request-reporting">
                                                    <div class="flex-1 flex items-center space-x-2.5">
                                                        <div class="">Getting a Request ID</div>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="" id="content-container">
                        <div class="flex flex-row gap-12 box-border w-full pt-40 lg:pt-10">
                            <div class="relative grow box-border flex-col w-full mx-auto px-1 lg:pl-[23.7rem] lg:-ml-12 xl:w-[calc(100%-28rem)]" id="content-area">
                                <header id="header" class="relative">
                                    <div class="mt-0.5 space-y-2.5">
                                        <div class="flex flex-row items-center gap-1.5">
                                            <div class="inline-flex items-center gap-1.5 text-sm text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300">
                                                <a href="/troubleshooting/common-issues">Troubleshooting</a>
                                            </div>
                                        </div>
                                        <div class="flex items-center relative gap-2">
                                            <h1 id="page-title" class="inline-block text-2xl sm:text-3xl font-bold text-gray-900 tracking-tight dark:text-gray-200">Common Issues</h1>
                                        </div>
                                    </div>
                                    <div class="mt-2 text-lg prose prose-gray dark:prose-invert">
                                        <p>Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs</p>
                                    </div>
                                </header>
                                <div class="flex flex-col gap-8"></div>
                                <div class="mdx-content relative mt-8 prose prose-gray dark:prose-invert">
                                    <p>While we strive to make Cursor as stable as possible, sometimes issues can arise. Below are some common issues and how to resolve them.</p>
                                    <h3 class="flex whitespace-pre-wrap group font-semibold" id="networking-issues-http%2F2">
                                        <div class="absolute">
                                            <a href="#networking-issues-http%2F2" class="-ml-10 flex items-center opacity-0 border-0 group-hover:opacity-100" aria-label="Navigate to header">
                                                ​
                                                <div class="w-6 h-6 rounded-md flex items-center justify-center shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 bg-white ring-1 ring-gray-400/30 dark:ring-gray-700/25 hover:ring-gray-400/60 dark:hover:ring-white/20">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="gray" height="12px" viewBox="0 0 576 512">
                                                        <path d="M0 256C0 167.6 71.6 96 160 96h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C98.1 144 48 194.1 48 256s50.1 112 112 112h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C71.6 416 0 344.4 0 256zm576 0c0 88.4-71.6 160-160 160H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c61.9 0 112-50.1 112-112s-50.1-112-112-112H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c88.4 0 160 71.6 160 160zM184 232H392c13.3 0 24 10.7 24 24s-10.7 24-24 24H184c-13.3 0-24-10.7-24-24s10.7-24 24-24z"></path>
                                                    </svg>
                                                </div>
                                            </a>
                                        </div>
                                        <span class="cursor-pointer">Networking Issues (HTTP/2)</span>
                                    </h3>
                                    <p>Cursor relies on the HTTP/2 protocol for many of it’s AI features, due to it’s ability to handle streamed responses. If HTTP/2 is not supported by your network, this can cause issues such as failure to index your code, and the inability to use Cursor’s AI features.</p>
                                    <p>This can be the case when on corpoorate networks, using VPNs, or using a proxy like Zscaler.</p>
                                    <p>
                                        To resolve this, Cursor now comes with a HTTP/1.1 fallback, which is slower, but will allow you to use Cursor’s AI features. You can enable this yourself in the app settings (not the Cursor settings), by pressing <code>CMD/CTRL + ,</code>
                                        and then searching for <code>HTTP/2</code>
                                        .
                                    </p>
                                    <p>
                                        You should then enable the <code>Disable HTTP/2</code>
                                        option, which will force Cursor to use HTTP/1.1, and should resolve the issue.
                                    </p>
                                    <p>We hope to add automatic detection and fallback in the future!</p>
                                    <h3 class="flex whitespace-pre-wrap group font-semibold" id="resource-issues-cpu%2C-ram%2C-etc">
                                        <div class="absolute">
                                            <a href="#resource-issues-cpu%2C-ram%2C-etc" class="-ml-10 flex items-center opacity-0 border-0 group-hover:opacity-100" aria-label="Navigate to header">
                                                ​
                                                <div class="w-6 h-6 rounded-md flex items-center justify-center shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 bg-white ring-1 ring-gray-400/30 dark:ring-gray-700/25 hover:ring-gray-400/60 dark:hover:ring-white/20">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="gray" height="12px" viewBox="0 0 576 512">
                                                        <path d="M0 256C0 167.6 71.6 96 160 96h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C98.1 144 48 194.1 48 256s50.1 112 112 112h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C71.6 416 0 344.4 0 256zm576 0c0 88.4-71.6 160-160 160H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c61.9 0 112-50.1 112-112s-50.1-112-112-112H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c88.4 0 160 71.6 160 160zM184 232H392c13.3 0 24 10.7 24 24s-10.7 24-24 24H184c-13.3 0-24-10.7-24-24s10.7-24 24-24z"></path>
                                                    </svg>
                                                </div>
                                            </a>
                                        </div>
                                        <span class="cursor-pointer">Resource Issues (CPU, RAM, etc.)</span>
                                    </h3>
                                    <p>Some users see high CPU or RAM usage in Cursor, which can cause their machine to slow down, or to show warnings about high RAM usage.</p>
                                    <p>While Cursor can use a lot of resources when working on large codebases, this is usually not the case for most users, and is more likely to be an issue with Cursor’s extensions or settings.</p>
                                    <div class="callout my-4 px-5 py-4 overflow-hidden rounded-2xl flex gap-3 border border-sky-500/20 bg-sky-50/50 dark:border-sky-500/30 dark:bg-sky-500/10" data-callout-type="note">
                                        <div class="mt-0.5 w-4" data-component-part="callout-icon">
                                            <svg width="14" height="14" viewBox="0 0 14 14" fill="currentColor" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-sky-500" aria-label="Note">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M7 1.3C10.14 1.3 12.7 3.86 12.7 7C12.7 10.14 10.14 12.7 7 12.7C5.48908 12.6974 4.0408 12.096 2.97241 11.0276C1.90403 9.9592 1.30264 8.51092 1.3 7C1.3 3.86 3.86 1.3 7 1.3ZM7 0C3.14 0 0 3.14 0 7C0 10.86 3.14 14 7 14C10.86 14 14 10.86 14 7C14 3.14 10.86 0 7 0ZM8 3H6V8H8V3ZM8 9H6V11H8V9Z"></path>
                                            </svg>
                                        </div>
                                        <div class="text-sm prose min-w-0 w-full text-sky-900 dark:text-sky-200" data-component-part="callout-content">
                                            <p>
                                                If you are seeing a low RAM warning on <strong>MacOS</strong>
                                                , please note that there is a bug for some users that can show wildly incorrect values. If you are seeing this, please open the Activity Monitor and look at the “Memory” tab to see the correct memory usage.
                                            </p>
                                        </div>
                                    </div>
                                    <p>If you’re experiencing high CPU or RAM usage in Cursor, here are steps to diagnose and resolve the issue:</p>
                                    <div class="accordion-group [&amp;&gt;div]:border-0 [&amp;&gt;div]:rounded-none [&amp;&gt;div&gt;button]:rounded-none [&amp;&gt;div]:mb-0 overflow-hidden mt-0 mb-3 rounded-xl prose prose-gray dark:prose-invert divide-y divide-inherit border dark:border-gray-800/50">
                                        <div role="button" class="accordion border-standard rounded-2xl mb-3 overflow-hidden bg-background-light dark:bg-codeblock cursor-default">
                                            <button class="relative not-prose flex flex-row items-center content-center w-full py-4 px-5 space-x-2 hover:bg-gray-100 hover:dark:bg-gray-800 rounded-t-xl" aria-controls="check-your-extensions accordion children" aria-expanded="false" data-component-part="accordion-button">
                                                <div id="check-your-extensions" class="absolute -top-[8rem]"></div>
                                                <div class="mr-0.5" data-component-part="accordion-caret-right">
                                                    <svg class="h-3 w-3 transition bg-gray-700 dark:bg-gray-400 duration-75" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                                </div>
                                                <div class="leading-tight text-left" contenteditable="false" data-component-part="accordion-title-container">
                                                    <p class="m-0 font-medium text-gray-900 dark:text-gray-200" data-component-part="accordion-title">Check Your Extensions</p>
                                                </div>
                                            </button>
                                            <div id="check-your-extensions accordion children" role="contentinfo" class="mt-2 mb-4 mx-6 hidden overflow-x-auto cursor-default" data-component-part="accordion-content">
                                                <p>While many extensions can be useful, some can significantly impact performance!</p>
                                                <p>
                                                    To test this, you can try to run <code>cursor --disable-extensions</code>
                                                    from the command line to launch Cursor without any extensions enabled. If the performance improves, gradually re-enable extensions one by one to identify the problematic ones.
                                                </p>
                                                <p>
                                                    You can also try to use the Extension Bisect feature, which will help you identify which extension is causing the issue. You can read more about it <a href="https://code.visualstudio.com/blogs/2021/02/16/extension-bisect#_welcome-extension-bisect" target="_blank" rel="noreferrer" class="link">here</a>
                                                    , but note that this may only be useful if the issues are immediate and obvious, and not an issue that worsens over time.
                                                </p>
                                            </div>
                                        </div>
                                        <div role="button" class="accordion border-standard rounded-2xl mb-3 overflow-hidden bg-background-light dark:bg-codeblock cursor-default">
                                            <button class="relative not-prose flex flex-row items-center content-center w-full py-4 px-5 space-x-2 hover:bg-gray-100 hover:dark:bg-gray-800 rounded-t-xl" aria-controls="use-the-process-explorer accordion children" aria-expanded="false" data-component-part="accordion-button">
                                                <div id="use-the-process-explorer" class="absolute -top-[8rem]"></div>
                                                <div class="mr-0.5" data-component-part="accordion-caret-right">
                                                    <svg class="h-3 w-3 transition bg-gray-700 dark:bg-gray-400 duration-75" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                                </div>
                                                <div class="leading-tight text-left" contenteditable="false" data-component-part="accordion-title-container">
                                                    <p class="m-0 font-medium text-gray-900 dark:text-gray-200" data-component-part="accordion-title">Use the Process Explorer</p>
                                                </div>
                                            </button>
                                            <div id="use-the-process-explorer accordion children" role="contentinfo" class="mt-2 mb-4 mx-6 hidden overflow-x-auto cursor-default" data-component-part="accordion-content">
                                                <p>
                                                    The <strong>Process Explorer</strong>
                                                    is a built in tool in Cursor that allows you to see which processes are consuming resources.
                                                </p>
                                                <p>
                                                    To open it, open the Command Palette (<code>Cmd/Ctrl + Shift + P</code>
                                                    ) and run the <code>Developer: Open Process Explorer</code>
                                                    command.
                                                </p>
                                                <p>This should open a new window, with a list of all the processes Cursor is running, both as part of it’s own executation, as well as any processes needed to run extensions and any terminals you may have running. This should immediately identify any processes that are consuming a lot of resources.</p>
                                                <p>
                                                    If the process is listed under the 
                                                    <strong>
                                                        <code>extensionHost</code>
                                                    </strong>
                                                    dropdown, this suggests an extension is causing the issue, and you should try to find and disable the problematic extension.
                                                </p>
                                                <p>
                                                    If the process is listended under the 
                                                    <strong>
                                                        <code>ptyHost</code>
                                                    </strong>
                                                    dropdown, this suggests a terminal is consuming a lot of resources. The Process Explorer will show you each terminal that is running, and what command is running within it, so that you can try to kill it, or diagnose it’s high resource usage.
                                                </p>
                                                <p>
                                                    If the usage is from another process, please let us know in the <a href="https://forum.cursor.com/" target="_blank" rel="noreferrer" class="link">forum</a>
                                                    and we’ll be happy to help diagnose the issue.
                                                </p>
                                            </div>
                                        </div>
                                        <div role="button" class="accordion border-standard rounded-2xl mb-3 overflow-hidden bg-background-light dark:bg-codeblock cursor-default">
                                            <button class="relative not-prose flex flex-row items-center content-center w-full py-4 px-5 space-x-2 hover:bg-gray-100 hover:dark:bg-gray-800 rounded-t-xl" aria-controls="monitor-system-resources accordion children" aria-expanded="false" data-component-part="accordion-button">
                                                <div id="monitor-system-resources" class="absolute -top-[8rem]"></div>
                                                <div class="mr-0.5" data-component-part="accordion-caret-right">
                                                    <svg class="h-3 w-3 transition bg-gray-700 dark:bg-gray-400 duration-75" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                                </div>
                                                <div class="leading-tight text-left" contenteditable="false" data-component-part="accordion-title-container">
                                                    <p class="m-0 font-medium text-gray-900 dark:text-gray-200" data-component-part="accordion-title">Monitor System Resources</p>
                                                </div>
                                            </button>
                                            <div id="monitor-system-resources accordion children" role="contentinfo" class="mt-2 mb-4 mx-6 hidden overflow-x-auto cursor-default" data-component-part="accordion-content">
                                                <p>Depending on your operating system, you can use a number of different tools to monitor your system’s resources.</p>
                                                <p>This will help you identify if the issue is Cursor-specific, or if it’s a system-wide issue.</p>
                                            </div>
                                        </div>
                                        <div role="button" class="accordion border-standard rounded-2xl mb-3 overflow-hidden bg-background-light dark:bg-codeblock cursor-default">
                                            <button class="relative not-prose flex flex-row items-center content-center w-full py-4 px-5 space-x-2 hover:bg-gray-100 hover:dark:bg-gray-800 rounded-t-xl" aria-controls="testing-a-minimal-installation accordion children" aria-expanded="false" data-component-part="accordion-button">
                                                <div id="testing-a-minimal-installation" class="absolute -top-[8rem]"></div>
                                                <div class="mr-0.5" data-component-part="accordion-caret-right">
                                                    <svg class="h-3 w-3 transition bg-gray-700 dark:bg-gray-400 duration-75" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                                </div>
                                                <div class="leading-tight text-left" contenteditable="false" data-component-part="accordion-title-container">
                                                    <p class="m-0 font-medium text-gray-900 dark:text-gray-200" data-component-part="accordion-title">Testing a Minimal Installation</p>
                                                </div>
                                            </button>
                                            <div id="testing-a-minimal-installation accordion children" role="contentinfo" class="mt-2 mb-4 mx-6 hidden overflow-x-auto cursor-default" data-component-part="accordion-content">
                                                <p>While the above steps should help the majority of users, if you are still experiencing issues, you can try testing a minimal installation of Cursor to see if the issue persists.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <h2 class="flex whitespace-pre-wrap group font-semibold" id="general-faqs">
                                        <div class="absolute">
                                            <a href="#general-faqs" class="-ml-10 flex items-center opacity-0 border-0 group-hover:opacity-100" aria-label="Navigate to header">
                                                ​
                                                <div class="w-6 h-6 rounded-md flex items-center justify-center shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 bg-white ring-1 ring-gray-400/30 dark:ring-gray-700/25 hover:ring-gray-400/60 dark:hover:ring-white/20">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="gray" height="12px" viewBox="0 0 576 512">
                                                        <path d="M0 256C0 167.6 71.6 96 160 96h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C98.1 144 48 194.1 48 256s50.1 112 112 112h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C71.6 416 0 344.4 0 256zm576 0c0 88.4-71.6 160-160 160H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c61.9 0 112-50.1 112-112s-50.1-112-112-112H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c88.4 0 160 71.6 160 160zM184 232H392c13.3 0 24 10.7 24 24s-10.7 24-24 24H184c-13.3 0-24-10.7-24-24s10.7-24 24-24z"></path>
                                                    </svg>
                                                </div>
                                            </a>
                                        </div>
                                        <span class="cursor-pointer">General FAQs</span>
                                    </h2>
                                    <div class="accordion-group [&amp;&gt;div]:border-0 [&amp;&gt;div]:rounded-none [&amp;&gt;div&gt;button]:rounded-none [&amp;&gt;div]:mb-0 overflow-hidden mt-0 mb-3 rounded-xl prose prose-gray dark:prose-invert divide-y divide-inherit border dark:border-gray-800/50">
                                        <div role="button" class="accordion border-standard rounded-2xl mb-3 overflow-hidden bg-background-light dark:bg-codeblock cursor-default">
                                            <button class="relative not-prose flex flex-row items-center content-center w-full py-4 px-5 space-x-2 hover:bg-gray-100 hover:dark:bg-gray-800 rounded-t-xl" aria-controls="i-see-an-update-on-the-changelog-but-cursor-wont-update accordion children" aria-expanded="false" data-component-part="accordion-button">
                                                <div id="i-see-an-update-on-the-changelog-but-cursor-wont-update" class="absolute -top-[8rem]"></div>
                                                <div class="mr-0.5" data-component-part="accordion-caret-right">
                                                    <svg class="h-3 w-3 transition bg-gray-700 dark:bg-gray-400 duration-75" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                                </div>
                                                <div class="leading-tight text-left" contenteditable="false" data-component-part="accordion-title-container">
                                                    <p class="m-0 font-medium text-gray-900 dark:text-gray-200" data-component-part="accordion-title">I see an update on the changelog but Cursor won &#x27;t update</p>
                                                </div>
                                            </button>
                                            <div id="i-see-an-update-on-the-changelog-but-cursor-wont-update accordion children" role="contentinfo" class="mt-2 mb-4 mx-6 hidden overflow-x-auto cursor-default" data-component-part="accordion-content">
                                                <p>If the update is very new, it might not have rolled out to you yet. We do staged rollouts, which means we release new updates to a few randomly selected users first before releasing them to everyone. Expect to get the update in a couple days!</p>
                                            </div>
                                        </div>
                                        <div role="button" class="accordion border-standard rounded-2xl mb-3 overflow-hidden bg-background-light dark:bg-codeblock cursor-default">
                                            <button class="relative not-prose flex flex-row items-center content-center w-full py-4 px-5 space-x-2 hover:bg-gray-100 hover:dark:bg-gray-800 rounded-t-xl" aria-controls="i-have-issues-with-my-github-login-in-cursor-how-do-i-log-out-of-github-in-cursor accordion children" aria-expanded="false" data-component-part="accordion-button">
                                                <div id="i-have-issues-with-my-github-login-in-cursor-how-do-i-log-out-of-github-in-cursor" class="absolute -top-[8rem]"></div>
                                                <div class="mr-0.5" data-component-part="accordion-caret-right">
                                                    <svg class="h-3 w-3 transition bg-gray-700 dark:bg-gray-400 duration-75" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                                </div>
                                                <div class="leading-tight text-left" contenteditable="false" data-component-part="accordion-title-container">
                                                    <p class="m-0 font-medium text-gray-900 dark:text-gray-200" data-component-part="accordion-title">I have issues with my GitHub login in Cursor / How do I log out of GitHub in Cursor?</p>
                                                </div>
                                            </button>
                                            <div id="i-have-issues-with-my-github-login-in-cursor-how-do-i-log-out-of-github-in-cursor accordion children" role="contentinfo" class="mt-2 mb-4 mx-6 hidden overflow-x-auto cursor-default" data-component-part="accordion-content">
                                                <p>
                                                    You can try using the <code>Sign Out of GitHub</code>
                                                    command from the command palette <code>Ctrl/⌘ + Shift + P</code>
                                                    .
                                                </p>
                                            </div>
                                        </div>
                                        <div role="button" class="accordion border-standard rounded-2xl mb-3 overflow-hidden bg-background-light dark:bg-codeblock cursor-default">
                                            <button class="relative not-prose flex flex-row items-center content-center w-full py-4 px-5 space-x-2 hover:bg-gray-100 hover:dark:bg-gray-800 rounded-t-xl" aria-controls="i-cant-use-github-codespaces accordion children" aria-expanded="false" data-component-part="accordion-button">
                                                <div id="i-cant-use-github-codespaces" class="absolute -top-[8rem]"></div>
                                                <div class="mr-0.5" data-component-part="accordion-caret-right">
                                                    <svg class="h-3 w-3 transition bg-gray-700 dark:bg-gray-400 duration-75" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                                </div>
                                                <div class="leading-tight text-left" contenteditable="false" data-component-part="accordion-title-container">
                                                    <p class="m-0 font-medium text-gray-900 dark:text-gray-200" data-component-part="accordion-title">I can &#x27;t use GitHub Codespaces</p>
                                                </div>
                                            </button>
                                            <div id="i-cant-use-github-codespaces accordion children" role="contentinfo" class="mt-2 mb-4 mx-6 hidden overflow-x-auto cursor-default" data-component-part="accordion-content">
                                                <p>Unfortunately, we don’t support GitHub Codespaces yet.</p>
                                            </div>
                                        </div>
                                        <div role="button" class="accordion border-standard rounded-2xl mb-3 overflow-hidden bg-background-light dark:bg-codeblock cursor-default">
                                            <button class="relative not-prose flex flex-row items-center content-center w-full py-4 px-5 space-x-2 hover:bg-gray-100 hover:dark:bg-gray-800 rounded-t-xl" aria-controls="i-have-errors-connecting-to-remote-ssh accordion children" aria-expanded="false" data-component-part="accordion-button">
                                                <div id="i-have-errors-connecting-to-remote-ssh" class="absolute -top-[8rem]"></div>
                                                <div class="mr-0.5" data-component-part="accordion-caret-right">
                                                    <svg class="h-3 w-3 transition bg-gray-700 dark:bg-gray-400 duration-75" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                                </div>
                                                <div class="leading-tight text-left" contenteditable="false" data-component-part="accordion-title-container">
                                                    <p class="m-0 font-medium text-gray-900 dark:text-gray-200" data-component-part="accordion-title">I have errors connecting to Remote SSH</p>
                                                </div>
                                            </button>
                                            <div id="i-have-errors-connecting-to-remote-ssh accordion children" role="contentinfo" class="mt-2 mb-4 mx-6 hidden overflow-x-auto cursor-default" data-component-part="accordion-content">
                                                <p>
                                                    Currently, we don’t support SSHing into Mac or Windows machines. If you’re not using a Mac or Windows machine, please report your issue to us in the <a href="https://forum.cursor.com/" target="_blank" rel="noreferrer" class="link">forum</a>
                                                    . It would be helpful to include some logs for better assistance.
                                                </p>
                                            </div>
                                        </div>
                                        <div role="button" class="accordion border-standard rounded-2xl mb-3 overflow-hidden bg-background-light dark:bg-codeblock cursor-default">
                                            <button class="relative not-prose flex flex-row items-center content-center w-full py-4 px-5 space-x-2 hover:bg-gray-100 hover:dark:bg-gray-800 rounded-t-xl" aria-controls="ssh-connection-problems-on-windows accordion children" aria-expanded="false" data-component-part="accordion-button">
                                                <div id="ssh-connection-problems-on-windows" class="absolute -top-[8rem]"></div>
                                                <div class="mr-0.5" data-component-part="accordion-caret-right">
                                                    <svg class="h-3 w-3 transition bg-gray-700 dark:bg-gray-400 duration-75" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                                </div>
                                                <div class="leading-tight text-left" contenteditable="false" data-component-part="accordion-title-container">
                                                    <p class="m-0 font-medium text-gray-900 dark:text-gray-200" data-component-part="accordion-title">SSH Connection Problems on Windows</p>
                                                </div>
                                            </button>
                                            <div id="ssh-connection-problems-on-windows accordion children" role="contentinfo" class="mt-2 mb-4 mx-6 hidden overflow-x-auto cursor-default" data-component-part="accordion-content">
                                                <p>If you encounter the error “SSH is only supported in Microsoft versions of VS Code”, follow these steps:</p>
                                                <ol>
                                                    <li>
                                                        <p>Uninstall the current Remote-SSH extension:</p>
                                                        <ul>
                                                            <li>
                                                                Open the Extensions view (<code>Ctrl + Shift + X</code>
                                                                )
                                                            </li>
                                                            <li>Search for “Remote-SSH”</li>
                                                            <li>Click on the gear icon and select “Uninstall”</li>
                                                        </ul>
                                                    </li>
                                                    <li>
                                                        <p>Install version 0.113 of Remote-SSH:</p>
                                                        <ul>
                                                            <li>Go to the Cursor marketplace</li>
                                                            <li>Search for “Remote-SSH”</li>
                                                            <li>Find version 0.113 and install it</li>
                                                        </ul>
                                                    </li>
                                                    <li>
                                                        <p>After installation:</p>
                                                        <ul>
                                                            <li>Close all VS Code instances that have active SSH connections</li>
                                                            <li>Restart Cursor completely</li>
                                                            <li>Try connecting via SSH again</li>
                                                        </ul>
                                                    </li>
                                                </ol>
                                                <p>If you still experience issues, make sure your SSH configuration is correct and that you have the necessary SSH keys set up properly.</p>
                                            </div>
                                        </div>
                                        <div role="button" class="accordion border-standard rounded-2xl mb-3 overflow-hidden bg-background-light dark:bg-codeblock cursor-default">
                                            <button class="relative not-prose flex flex-row items-center content-center w-full py-4 px-5 space-x-2 hover:bg-gray-100 hover:dark:bg-gray-800 rounded-t-xl" aria-controls="cursor-tab-and-cmd-k-do-not-work-behind-my-corporate-proxy accordion children" aria-expanded="false" data-component-part="accordion-button">
                                                <div id="cursor-tab-and-cmd-k-do-not-work-behind-my-corporate-proxy" class="absolute -top-[8rem]"></div>
                                                <div class="mr-0.5" data-component-part="accordion-caret-right">
                                                    <svg class="h-3 w-3 transition bg-gray-700 dark:bg-gray-400 duration-75" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                                </div>
                                                <div class="leading-tight text-left" contenteditable="false" data-component-part="accordion-title-container">
                                                    <p class="m-0 font-medium text-gray-900 dark:text-gray-200" data-component-part="accordion-title">Cursor Tab and Cmd K do not work behind my corporate proxy</p>
                                                </div>
                                            </button>
                                            <div id="cursor-tab-and-cmd-k-do-not-work-behind-my-corporate-proxy accordion children" role="contentinfo" class="mt-2 mb-4 mx-6 hidden overflow-x-auto cursor-default" data-component-part="accordion-content">
                                                <p>
                                                    Cursor Tab and Cmd K use HTTP/2 by default, which allows us to use less resources with lower latency. Some corporate proxies (e.g. Zscaler in certain configurations) block HTTP/2. To fix this, you can set <code>&quot;cursor.general.disableHttp2 &quot;: true</code>
                                                    in the settings (<code>Cmd/Ctrl + ,</code>
                                                    and then search for <code>http2</code>
                                                    ).
                                                </p>
                                            </div>
                                        </div>
                                        <div role="button" class="accordion border-standard rounded-2xl mb-3 overflow-hidden bg-background-light dark:bg-codeblock cursor-default">
                                            <button class="relative not-prose flex flex-row items-center content-center w-full py-4 px-5 space-x-2 hover:bg-gray-100 hover:dark:bg-gray-800 rounded-t-xl" aria-controls="i-just-subscribed-to-pro-but-i-m-still-on-the-free-plan-in-the-app accordion children" aria-expanded="false" data-component-part="accordion-button">
                                                <div id="i-just-subscribed-to-pro-but-i-m-still-on-the-free-plan-in-the-app" class="absolute -top-[8rem]"></div>
                                                <div class="mr-0.5" data-component-part="accordion-caret-right">
                                                    <svg class="h-3 w-3 transition bg-gray-700 dark:bg-gray-400 duration-75" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                                </div>
                                                <div class="leading-tight text-left" contenteditable="false" data-component-part="accordion-title-container">
                                                    <p class="m-0 font-medium text-gray-900 dark:text-gray-200" data-component-part="accordion-title">I just subscribed to Pro but I &#x27;m still on the free plan in the app</p>
                                                </div>
                                            </button>
                                            <div id="i-just-subscribed-to-pro-but-i-m-still-on-the-free-plan-in-the-app accordion children" role="contentinfo" class="mt-2 mb-4 mx-6 hidden overflow-x-auto cursor-default" data-component-part="accordion-content">
                                                <p>Try logging out and logging back in from the Cursor Settings</p>
                                            </div>
                                        </div>
                                        <div role="button" class="accordion border-standard rounded-2xl mb-3 overflow-hidden bg-background-light dark:bg-codeblock cursor-default">
                                            <button class="relative not-prose flex flex-row items-center content-center w-full py-4 px-5 space-x-2 hover:bg-gray-100 hover:dark:bg-gray-800 rounded-t-xl" aria-controls="when-will-my-usage-reset-again accordion children" aria-expanded="false" data-component-part="accordion-button">
                                                <div id="when-will-my-usage-reset-again" class="absolute -top-[8rem]"></div>
                                                <div class="mr-0.5" data-component-part="accordion-caret-right">
                                                    <svg class="h-3 w-3 transition bg-gray-700 dark:bg-gray-400 duration-75" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                                </div>
                                                <div class="leading-tight text-left" contenteditable="false" data-component-part="accordion-title-container">
                                                    <p class="m-0 font-medium text-gray-900 dark:text-gray-200" data-component-part="accordion-title">When will my usage reset again?</p>
                                                </div>
                                            </button>
                                            <div id="when-will-my-usage-reset-again accordion children" role="contentinfo" class="mt-2 mb-4 mx-6 hidden overflow-x-auto cursor-default" data-component-part="accordion-content">
                                                <p>
                                                    If you’re subscribed to Pro you can click on <code>Manage Subscription</code>
                                                    from the <a href="https://cursor.com/settings" target="_blank" rel="noreferrer" class="link">Dashboard</a>
                                                    and your plan renewal date will be displayed at the top.
                                                </p>
                                                <p>If you’re a free user you can check when you got the first email from us in your inbox. Your usage will reset every month from that date.</p>
                                            </div>
                                        </div>
                                        <div role="button" class="accordion border-standard rounded-2xl mb-3 overflow-hidden bg-background-light dark:bg-codeblock cursor-default">
                                            <button class="relative not-prose flex flex-row items-center content-center w-full py-4 px-5 space-x-2 hover:bg-gray-100 hover:dark:bg-gray-800 rounded-t-xl" aria-controls="my-chat-composer-history-disappeared-after-an-update accordion children" aria-expanded="false" data-component-part="accordion-button">
                                                <div id="my-chat-composer-history-disappeared-after-an-update" class="absolute -top-[8rem]"></div>
                                                <div class="mr-0.5" data-component-part="accordion-caret-right">
                                                    <svg class="h-3 w-3 transition bg-gray-700 dark:bg-gray-400 duration-75" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                                </div>
                                                <div class="leading-tight text-left" contenteditable="false" data-component-part="accordion-title-container">
                                                    <p class="m-0 font-medium text-gray-900 dark:text-gray-200" data-component-part="accordion-title">My Chat/Composer history disappeared after an update</p>
                                                </div>
                                            </button>
                                            <div id="my-chat-composer-history-disappeared-after-an-update accordion children" role="contentinfo" class="mt-2 mb-4 mx-6 hidden overflow-x-auto cursor-default" data-component-part="accordion-content">
                                                <p>If you notice that your Chat or Composer history has been cleared following an update, this is likely due to low disk space on your system. Cursor may need to clear historical data during updates when disk space is limited. To prevent this from happening:</p>
                                                <ol>
                                                    <li>Ensure you have sufficient free disk space before updating</li>
                                                    <li>Regularly clean up unnecessary files on your system</li>
                                                    <li>Consider backing up important conversations before updating</li>
                                                </ol>
                                            </div>
                                        </div>
                                        <div role="button" class="accordion border-standard rounded-2xl mb-3 overflow-hidden bg-background-light dark:bg-codeblock cursor-default">
                                            <button class="relative not-prose flex flex-row items-center content-center w-full py-4 px-5 space-x-2 hover:bg-gray-100 hover:dark:bg-gray-800 rounded-t-xl" aria-controls="how-do-i-uninstall-cursor accordion children" aria-expanded="false" data-component-part="accordion-button">
                                                <div id="how-do-i-uninstall-cursor" class="absolute -top-[8rem]"></div>
                                                <div class="mr-0.5" data-component-part="accordion-caret-right">
                                                    <svg class="h-3 w-3 transition bg-gray-700 dark:bg-gray-400 duration-75" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                                </div>
                                                <div class="leading-tight text-left" contenteditable="false" data-component-part="accordion-title-container">
                                                    <p class="m-0 font-medium text-gray-900 dark:text-gray-200" data-component-part="accordion-title">How do I uninstall Cursor?</p>
                                                </div>
                                            </button>
                                            <div id="how-do-i-uninstall-cursor accordion children" role="contentinfo" class="mt-2 mb-4 mx-6 hidden overflow-x-auto cursor-default" data-component-part="accordion-content">
                                                <p>
                                                    You can follow <a href="https://code.visualstudio.com/docs/setup/uninstall" target="_blank" rel="noreferrer" class="link">this guide</a>
                                                    to uninstall Cursor. Replace every occurrence of “VS Code” or “Code” with “Cursor”, and “.vscode” with “.cursor”.
                                                </p>
                                            </div>
                                        </div>
                                        <div role="button" class="accordion border-standard rounded-2xl mb-3 overflow-hidden bg-background-light dark:bg-codeblock cursor-default">
                                            <button class="relative not-prose flex flex-row items-center content-center w-full py-4 px-5 space-x-2 hover:bg-gray-100 hover:dark:bg-gray-800 rounded-t-xl" aria-controls="how-do-i-delete-my-account accordion children" aria-expanded="false" data-component-part="accordion-button">
                                                <div id="how-do-i-delete-my-account" class="absolute -top-[8rem]"></div>
                                                <div class="mr-0.5" data-component-part="accordion-caret-right">
                                                    <svg class="h-3 w-3 transition bg-gray-700 dark:bg-gray-400 duration-75" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                                </div>
                                                <div class="leading-tight text-left" contenteditable="false" data-component-part="accordion-title-container">
                                                    <p class="m-0 font-medium text-gray-900 dark:text-gray-200" data-component-part="accordion-title">How do I delete my account?</p>
                                                </div>
                                            </button>
                                            <div id="how-do-i-delete-my-account accordion children" role="contentinfo" class="mt-2 mb-4 mx-6 hidden overflow-x-auto cursor-default" data-component-part="accordion-content">
                                                <p>
                                                    You can delete your account by clicking on the <code>Delete Account</code>
                                                    button in the <a href="https://cursor.com/settings" target="_blank" rel="noreferrer" class="link">Dashboard</a>
                                                    . Note that this will delete your account and all data associated with it.
                                                </p>
                                            </div>
                                        </div>
                                        <div role="button" class="accordion border-standard rounded-2xl mb-3 overflow-hidden bg-background-light dark:bg-codeblock cursor-default">
                                            <button class="relative not-prose flex flex-row items-center content-center w-full py-4 px-5 space-x-2 hover:bg-gray-100 hover:dark:bg-gray-800 rounded-t-xl" aria-controls="how-do-i-open-cursor-from-the-command-line accordion children" aria-expanded="false" data-component-part="accordion-button">
                                                <div id="how-do-i-open-cursor-from-the-command-line" class="absolute -top-[8rem]"></div>
                                                <div class="mr-0.5" data-component-part="accordion-caret-right">
                                                    <svg class="h-3 w-3 transition bg-gray-700 dark:bg-gray-400 duration-75" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                                </div>
                                                <div class="leading-tight text-left" contenteditable="false" data-component-part="accordion-title-container">
                                                    <p class="m-0 font-medium text-gray-900 dark:text-gray-200" data-component-part="accordion-title">How do I open Cursor from the command line?</p>
                                                </div>
                                            </button>
                                            <div id="how-do-i-open-cursor-from-the-command-line accordion children" role="contentinfo" class="mt-2 mb-4 mx-6 hidden overflow-x-auto cursor-default" data-component-part="accordion-content">
                                                <p>
                                                    You can open Cursor from the command line by running <code>cursor</code>
                                                    in your terminal. If you’re missing the <code>cursor</code>
                                                    command, you can
                                                </p>
                                                <ol>
                                                    <li>
                                                        Open the command palette <code>⌘⇧P</code>
                                                    </li>
                                                    <li>
                                                        Type <code>install command</code>
                                                    </li>
                                                    <li>
                                                        Select <code>Install &#x27;cursor &#x27;command</code>
                                                        (and optionally the <code>code</code>
                                                        command too which will override VS Code’s <code>code</code>
                                                        command)
                                                    </li>
                                                </ol>
                                            </div>
                                        </div>
                                        <div role="button" class="accordion border-standard rounded-2xl mb-3 overflow-hidden bg-background-light dark:bg-codeblock cursor-default">
                                            <button class="relative not-prose flex flex-row items-center content-center w-full py-4 px-5 space-x-2 hover:bg-gray-100 hover:dark:bg-gray-800 rounded-t-xl" aria-controls="unable-to-sign-in-to-cursor accordion children" aria-expanded="false" data-component-part="accordion-button">
                                                <div id="unable-to-sign-in-to-cursor" class="absolute -top-[8rem]"></div>
                                                <div class="mr-0.5" data-component-part="accordion-caret-right">
                                                    <svg class="h-3 w-3 transition bg-gray-700 dark:bg-gray-400 duration-75" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/caret-right.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                                </div>
                                                <div class="leading-tight text-left" contenteditable="false" data-component-part="accordion-title-container">
                                                    <p class="m-0 font-medium text-gray-900 dark:text-gray-200" data-component-part="accordion-title">Unable to Sign In to Cursor</p>
                                                </div>
                                            </button>
                                            <div id="unable-to-sign-in-to-cursor accordion children" role="contentinfo" class="mt-2 mb-4 mx-6 hidden overflow-x-auto cursor-default" data-component-part="accordion-content">
                                                <p>If you click Sign In on the General tab of Cursor’s Settings tab but are redirected to cursor.com and then return to Cursor still seeing the Sign In button, try disabling your firewall or antivirus software, which may be blocking the sign-in process.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="leading-6 mt-14">
                                    <div class="feedback-toolbar pb-16 w-full">
                                        <div class="flex flex-col gap-y-4 xl:flex-col xl:gap-6 min-[1400px]:flex-row md:flex-row md:justify-end">
                                            <div class="flex flex-row gap-5 items-center grow justify-between md:justify-start xl:justify-between min-[1400px]:justify-start">
                                                <p class="text-sm text-gray-600 dark:text-gray-400">Was this page helpful?</p>
                                                <div class="flex flex-row gap-3 items-center">
                                                    <button class="px-3.5 py-2 flex flex-row gap-3 items-center border-standard rounded-xl text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 bg-white/50 dark:bg-codeblock/50 hover:border-gray-500 hover:dark:border-gray-500">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" class="fill-current">
                                                            <path d="M10.1187 1.08741C8.925 0.746789 7.67813 1.43741 7.3375 2.63116L7.15938 3.25616C7.04375 3.66241 6.83438 4.03741 6.55 4.34991L4.94688 6.11241C4.66875 6.41866 4.69062 6.89366 4.99687 7.17179C5.30312 7.44991 5.77813 7.42804 6.05625 7.12179L7.65938 5.35929C8.1 4.87491 8.42188 4.29679 8.6 3.66866L8.77812 3.04366C8.89062 2.64679 9.30625 2.41554 9.70625 2.52804C10.1063 2.64054 10.3344 3.05616 10.2219 3.45616L10.0437 4.08116C9.86562 4.70304 9.58437 5.29054 9.2125 5.81554C9.05 6.04366 9.03125 6.34366 9.15938 6.59366C9.2875 6.84366 9.54375 6.99991 9.825 6.99991H14C14.275 6.99991 14.5 7.22491 14.5 7.49991C14.5 7.71241 14.3656 7.89679 14.175 7.96866C13.9438 8.05616 13.7688 8.24992 13.7094 8.49054C13.65 8.73117 13.7125 8.98429 13.875 9.16866C13.9531 9.25616 14 9.37179 14 9.49991C14 9.74366 13.825 9.94679 13.5938 9.99054C13.3375 10.0405 13.1219 10.2187 13.0312 10.4624C12.9406 10.7062 12.9813 10.9843 13.1438 11.1905C13.2094 11.2749 13.25 11.3812 13.25 11.4999C13.25 11.7093 13.1187 11.8937 12.9312 11.9655C12.5719 12.1062 12.3781 12.4937 12.4812 12.8655C12.4937 12.9062 12.5 12.953 12.5 12.9999C12.5 13.2749 12.275 13.4999 12 13.4999H8.95312C8.55937 13.4999 8.17188 13.3843 7.84375 13.1655L5.91563 11.8812C5.57188 11.6499 5.10625 11.7437 4.875 12.0905C4.64375 12.4374 4.7375 12.8999 5.08437 13.1312L7.0125 14.4155C7.5875 14.7999 8.2625 15.003 8.95312 15.003H12C13.0844 15.003 13.9656 14.1405 14 13.0655C14.4563 12.6999 14.75 12.1374 14.75 11.503C14.75 11.3624 14.7344 11.228 14.7094 11.0968C15.1906 10.7312 15.5 10.153 15.5 9.50304C15.5 9.29991 15.4688 9.10304 15.4125 8.91866C15.775 8.55304 16 8.05304 16 7.49991C16 6.39679 15.1063 5.49991 14 5.49991H11.1156C11.2625 5.17491 11.3875 4.83741 11.4844 4.49366L11.6625 3.86866C12.0031 2.67491 11.3125 1.42804 10.1187 1.08741ZM1 5.99991C0.446875 5.99991 0 6.44679 0 6.99991V13.9999C0 14.553 0.446875 14.9999 1 14.9999H3C3.55313 14.9999 4 14.553 4 13.9999V6.99991C4 6.44679 3.55313 5.99991 3 5.99991H1Z"></path>
                                                        </svg>
                                                        <small class="text-sm font-normal leading-4 ">Yes</small>
                                                    </button>
                                                    <button class="px-3.5 py-2 flex flex-row gap-3 items-center border-standard rounded-xl text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 bg-white/50 dark:bg-codeblock/50 hover:border-gray-500 hover:dark:border-gray-500">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" class="fill-current">
                                                            <path d="M10.1187 14.9124C8.925 15.253 7.67813 14.5624 7.3375 13.3687L7.15938 12.7437C7.04375 12.3374 6.83438 11.9624 6.55 11.6499L4.94688 9.8874C4.66875 9.58115 4.69062 9.10615 4.99687 8.82803C5.30312 8.5499 5.77813 8.57178 6.05625 8.87803L7.65938 10.6405C8.1 11.1249 8.42188 11.703 8.6 12.3312L8.77812 12.9562C8.89062 13.353 9.30625 13.5843 9.70625 13.4718C10.1063 13.3593 10.3344 12.9437 10.2219 12.5437L10.0437 11.9187C9.86562 11.2968 9.58437 10.7093 9.2125 10.1843C9.05 9.95615 9.03125 9.65615 9.15938 9.40615C9.2875 9.15615 9.54375 8.9999 9.825 8.9999H14C14.275 8.9999 14.5 8.7749 14.5 8.4999C14.5 8.2874 14.3656 8.10303 14.175 8.03115C13.9438 7.94365 13.7688 7.7499 13.7094 7.50928C13.65 7.26865 13.7125 7.01553 13.875 6.83115C13.9531 6.74365 14 6.62803 14 6.4999C14 6.25615 13.825 6.05303 13.5938 6.00928C13.3375 5.95928 13.1219 5.78115 13.0312 5.53428C12.9406 5.2874 12.9813 5.0124 13.1438 4.80615C13.2094 4.72178 13.25 4.61553 13.25 4.49678C13.25 4.2874 13.1187 4.10303 12.9312 4.03115C12.5719 3.89053 12.3781 3.50303 12.4812 3.13115C12.4937 3.09053 12.5 3.04365 12.5 2.99678C12.5 2.72178 12.275 2.49678 12 2.49678H8.95312C8.55937 2.49678 8.17188 2.6124 7.84375 2.83115L5.91563 4.11553C5.57188 4.34678 5.10625 4.25303 4.875 3.90615C4.64375 3.55928 4.7375 3.09678 5.08437 2.86553L7.0125 1.58115C7.5875 1.19678 8.2625 0.993652 8.95312 0.993652H12C13.0844 0.993652 13.9656 1.85615 14 2.93115C14.4563 3.29678 14.75 3.85928 14.75 4.49365C14.75 4.63428 14.7344 4.76865 14.7094 4.8999C15.1906 5.26553 15.5 5.84365 15.5 6.49365C15.5 6.69678 15.4688 6.89365 15.4125 7.07803C15.775 7.44678 16 7.94678 16 8.4999C16 9.60303 15.1063 10.4999 14 10.4999H11.1156C11.2625 10.8249 11.3875 11.1624 11.4844 11.5062L11.6625 12.1312C12.0031 13.3249 11.3125 14.5718 10.1187 14.9124ZM1 11.9999C0.446875 11.9999 0 11.553 0 10.9999V3.9999C0 3.44678 0.446875 2.9999 1 2.9999H3C3.55313 2.9999 4 3.44678 4 3.9999V10.9999C4 11.553 3.55313 11.9999 3 11.9999H1Z"></path>
                                                        </svg>
                                                        <small class="text-sm font-normal leading-4 ">No</small>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="flex flex-row gap-3 justify-end"></div>
                                        </div>
                                    </div>
                                    <div id="pagination" class="mb-12 px-0.5 flex items-center text-sm font-semibold text-gray-700 dark:text-gray-200">
                                        <a class="flex items-center space-x-3 group" href="/settings/beta">
                                            <svg viewBox="0 0 3 6" class="h-1.5 stroke-gray-400 overflow-visible group-hover:stroke-gray-600 dark:group-hover:stroke-gray-300">
                                                <path d="M3 0L0 3L3 6" fill="none" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                            </svg>
                                            <span class="group-hover:text-gray-900 dark:group-hover:text-white">Early Access Program</span>
                                        </a>
                                        <a class="flex items-center ml-auto space-x-3 group" href="/troubleshooting/troubleshooting-guide">
                                            <span class="group-hover:text-gray-900 dark:group-hover:text-white">Troubleshooting Guide</span>
                                            <svg viewBox="0 0 3 6" class="rotate-180 h-1.5 stroke-gray-400 overflow-visible group-hover:stroke-gray-600 dark:group-hover:stroke-gray-300">
                                                <path d="M3 0L0 3L3 6" fill="none" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="hidden xl:flex self-start sticky xl:flex-col max-w-[28rem] h-[calc(100vh-9.5rem)] top-[9.5rem]" id="content-side-layout">
                                <div class="z-10 hidden xl:flex pl-10 box-border w-[19rem] max-h-full" id="table-of-contents-layout">
                                    <div class="text-gray-600 text-sm leading-6 w-[16.5rem] overflow-y-auto space-y-2 pb-4 -mt-10 pt-10" id="table-of-contents">
                                        <div class="text-gray-700 dark:text-gray-300 font-medium flex items-center space-x-2">
                                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" stroke="currentColor" stroke-width="2" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3">
                                                <path d="M2.44434 12.6665H13.5554" stroke-linecap="round" stroke-linejoin="round"></path>
                                                <path d="M2.44434 3.3335H13.5554" stroke-linecap="round" stroke-linejoin="round"></path>
                                                <path d="M2.44434 8H7.33323" stroke-linecap="round" stroke-linejoin="round"></path>
                                            </svg>
                                            <span>On this page</span>
                                        </div>
                                        <ul id="table-of-contents-content" class="toc">
                                            <li class="toc-item relative" data-depth="0">
                                                <a href="#networking-issues-http%2F2" class="py-1 block hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300">Networking Issues (HTTP/2)</a>
                                            </li>
                                            <li class="toc-item relative" data-depth="0">
                                                <a href="#resource-issues-cpu%2C-ram%2C-etc" class="py-1 block hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300">Resource Issues (CPU, RAM, etc.)</a>
                                            </li>
                                            <li class="toc-item relative" data-depth="0">
                                                <a href="#general-faqs" class="py-1 block hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300">General FAQs</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <footer id="footer" class="flex flex-col items-center mx-auto border-t border-gray-100 dark:border-gray-800/50">
                    <div class="flex w-full flex-col gap-12 justify-between px-8 py-16 md:py-20 lg:py-28 max-w-[984px] z-0">
                        <div class="flex flex-col md:flex-row gap-8 justify-between min-h-[76px]">
                            <div class="flex md:flex-col justify-between items-center md:items-start min-w-16 md:min-w-20 lg:min-w-48 md:gap-y-24">
                                <a href="/">
                                    <span class="sr-only">Cursor
                                    <!-- -->
                                    home page</span>
                                    <img class="nav-logo w-auto relative object-contain block dark:hidden max-w-48 h-[26px]" src="https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/logo/app-logo.svg" alt="light logo"/>
                                    <img class="nav-logo w-auto relative object-contain hidden dark:block max-w-48 h-[26px]" src="https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/logo/app-logo.svg" alt="dark logo"/>
                                </a>
                                <div class="flex gap-3 min-w-[140px] max-w-[492px] flex-wrap h-fit md:hidden justify-end">
                                    <a href="https://x.com/cursor_ai" target="_blank">
                                        <span class="sr-only">x</span>
                                        <svg class="w-5 h-5 bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/brands/x-twitter.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/brands/x-twitter.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                    </a>
                                    <a href="https://github.com/getcursor/cursor/" target="_blank">
                                        <span class="sr-only">github</span>
                                        <svg class="w-5 h-5 bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/brands/github.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/brands/github.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                    </a>
                                    <a href="https://cursor.com" target="_blank">
                                        <span class="sr-only">website</span>
                                        <svg class="w-5 h-5 bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/earth-americas.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/earth-americas.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                    </a>
                                </div>
                            </div>
                            <div class="flex flex-col sm:grid max-md:!grid-cols-2 gap-8 flex-1" style="grid-template-columns:repeat(3, minmax(0, 1fr))">
                                <div class="flex flex-col gap-4 flex-1 whitespace-nowrap w-full md:items-center">
                                    <div class="flex gap-4 flex-col">
                                        <p class="text-sm font-semibold text-gray-950 dark:text-white mb-1">Product</p>
                                        <a class="text-sm max-w-36 whitespace-normal md:truncate text-gray-950/50 dark:text-white/50 hover:text-gray-950/70 dark:hover:text-white/70" href="https://www.cursor.com/pricing" target="_blank" rel="noreferrer">Pricing</a>
                                        <a class="text-sm max-w-36 whitespace-normal md:truncate text-gray-950/50 dark:text-white/50 hover:text-gray-950/70 dark:hover:text-white/70" href="https://www.cursor.com/downloads" target="_blank" rel="noreferrer">Downloads</a>
                                        <a class="text-sm max-w-36 whitespace-normal md:truncate text-gray-950/50 dark:text-white/50 hover:text-gray-950/70 dark:hover:text-white/70" href="https://docs.cursor.com" target="_blank" rel="noreferrer">Docs</a>
                                        <a class="text-sm max-w-36 whitespace-normal md:truncate text-gray-950/50 dark:text-white/50 hover:text-gray-950/70 dark:hover:text-white/70" href="https://forum.cursor.com" target="_blank" rel="noreferrer">Forum</a>
                                    </div>
                                </div>
                                <div class="flex flex-col gap-4 flex-1 whitespace-nowrap w-full md:items-center">
                                    <div class="flex gap-4 flex-col">
                                        <p class="text-sm font-semibold text-gray-950 dark:text-white mb-1">Company</p>
                                        <a class="text-sm max-w-36 whitespace-normal md:truncate text-gray-950/50 dark:text-white/50 hover:text-gray-950/70 dark:hover:text-white/70" href="https://anysphere.inc" target="_blank" rel="noreferrer">Careers</a>
                                        <a class="text-sm max-w-36 whitespace-normal md:truncate text-gray-950/50 dark:text-white/50 hover:text-gray-950/70 dark:hover:text-white/70" href="https://anysphere.inc" target="_blank" rel="noreferrer">About</a>
                                        <a class="text-sm max-w-36 whitespace-normal md:truncate text-gray-950/50 dark:text-white/50 hover:text-gray-950/70 dark:hover:text-white/70" href="https://cursor.com/security" target="_blank" rel="noreferrer">Security</a>
                                        <a class="text-sm max-w-36 whitespace-normal md:truncate text-gray-950/50 dark:text-white/50 hover:text-gray-950/70 dark:hover:text-white/70" href="https://cursor.com/privacy" target="_blank" rel="noreferrer">Privacy</a>
                                    </div>
                                </div>
                                <div class="flex flex-col gap-4 flex-1 whitespace-nowrap w-full md:items-center">
                                    <div class="flex gap-4 flex-col">
                                        <p class="text-sm font-semibold text-gray-950 dark:text-white mb-1">Resources</p>
                                        <a class="text-sm max-w-36 whitespace-normal md:truncate text-gray-950/50 dark:text-white/50 hover:text-gray-950/70 dark:hover:text-white/70" href="https://cursor.com/terms-of-service" target="_blank" rel="noreferrer">Terms</a>
                                        <a class="text-sm max-w-36 whitespace-normal md:truncate text-gray-950/50 dark:text-white/50 hover:text-gray-950/70 dark:hover:text-white/70" href="https://cursor.com/changelog" target="_blank" rel="noreferrer">Changelog</a>
                                        <a class="text-sm max-w-36 whitespace-normal md:truncate text-gray-950/50 dark:text-white/50 hover:text-gray-950/70 dark:hover:text-white/70" href="https://x.com/cursor_ai" target="_blank" rel="noreferrer">Twitter</a>
                                        <a class="text-sm max-w-36 whitespace-normal md:truncate text-gray-950/50 dark:text-white/50 hover:text-gray-950/70 dark:hover:text-white/70" href="https://github.com/getcursor/cursor" target="_blank" rel="noreferrer">GitHub</a>
                                    </div>
                                </div>
                            </div>
                            <div class="gap-3 min-w-[140px] max-w-[492px] flex-wrap hidden md:flex justify-end">
                                <a href="https://x.com/cursor_ai" target="_blank">
                                    <span class="sr-only">x</span>
                                    <svg class="w-5 h-5 bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/brands/x-twitter.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/brands/x-twitter.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                </a>
                                <a href="https://github.com/getcursor/cursor/" target="_blank">
                                    <span class="sr-only">github</span>
                                    <svg class="w-5 h-5 bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/brands/github.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/brands/github.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                </a>
                                <a href="https://cursor.com" target="_blank">
                                    <span class="sr-only">website</span>
                                    <svg class="w-5 h-5 bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500" style="-webkit-mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/earth-americas.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://mintlify.b-cdn.net/v6.6.0/solid/earth-americas.svg);mask-repeat:no-repeat;mask-position:center"></svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </footer>
                <div class="z-10 fixed right-0 w-[368px] border-l border-gray-500/5 dark:border-gray-300/[0.06] bg-background-light dark:bg-background-dark h-[calc(100vh-7rem)] top-[7rem] invisible">
                    <div id="chat-assistant-sheet" class="absolute inset-0 -top-px min-h-full flex flex-col overflow-hidden shrink-0 chat-assistant-sheet" aria-hidden="true">
                        <div class="w-full flex flex-col pb-4 h-full pt-[7.5rem] lg:pt-3">
                            <div class="chat-assistant-sheet-header hidden lg:flex items-center justify-between px-4">
                                <div class="flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" class="size-5 text-primary dark:text-primary-light">
                                        <g fill="currentColor">
                                            <path d="M5.658,2.99l-1.263-.421-.421-1.263c-.137-.408-.812-.408-.949,0l-.421,1.263-1.263,.421c-.204,.068-.342,.259-.342,.474s.138,.406,.342,.474l1.263,.421,.421,1.263c.068,.204,.26,.342,.475,.342s.406-.138,.475-.342l.421-1.263,1.263-.421c.204-.068,.342-.259,.342-.474s-.138-.406-.342-.474Z" fill="currentColor" data-stroke="none" stroke="none"></path>
                                            <polygon points="9.5 2.75 11.412 7.587 16.25 9.5 11.412 11.413 9.5 16.25 7.587 11.413 2.75 9.5 7.587 7.587 9.5 2.75" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></polygon>
                                        </g>
                                    </svg>
                                    <span class="font-semibold text-gray-900 dark:text-gray-100">Assistant</span>
                                </div>
                                <div class="flex items-center gap-1.5">
                                    <button class="group hover:bg-gray-100 dark:hover:bg-white/5 p-1.5 rounded-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" class="size-4 text-gray-400 dark:text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300">
                                            <g fill="currentColor">
                                                <line x1="17" y1="5" x2="3" y2="5" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></line>
                                                <rect x="8" y="3" width="4" height="2" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" fill="currentColor"></rect>
                                                <path d="m14.95,8l-.355,7.1c-.053,1.064-.932,1.9-1.998,1.9h-5.195c-1.066,0-1.944-.836-1.998-1.9l-.355-7.1" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                                            </g>
                                        </svg>
                                    </button>
                                    <button class="group hover:bg-gray-100 dark:hover:bg-white/5 p-1.5 rounded-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x size-4 text-gray-400 dark:text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300">
                                            <path d="M18 6 6 18"></path>
                                            <path d="m6 6 12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="chat-assistant-sheet-content flex-1 overflow-y-auto relative px-4">
                                <div class="sticky top-0 left-0 right-0 bg-gradient-to-b from-background-light from-50% dark:from-background-dark to-transparent h-8"></div>
                                <div class="flex flex-col items-center text-sm justify-between">
                                    <div class="mx-8 text-center text-gray-400 dark:text-gray-600 text-xs">Responses are generated using AI and may contain mistakes.</div>
                                    <div class="flex flex-col gap-4 text-gray-800 dark:text-gray-200"></div>
                                </div>
                            </div>
                            <div class="px-4">
                                <div class="flex items-end gap-2 relative">
                                    <textarea id="chat-assistant-textarea" autoComplete="off" placeholder="Ask a question..." class="grow text-sm w-full px-3.5 pr-10 py-2.5 bg-background-light dark:bg-background-dark border border-gray-200 dark:border-gray-600/30 rounded-2xl shadow-sm focus:outline-none focus:border-primary dark:focus:border-primary-light text-gray-900 dark:text-gray-100 chat-assistant-input" style="resize:none"></textarea>
                                    <button class="absolute right-2.5 bottom-[9px] flex justify-center items-center p-1 w-6 h-6 rounded-lg bg-primary/30 dark:bg-primary-dark/30 chat-assistant-send-button" aria-label="Send message">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up w-3.5 h-3.5 text-white dark:text-white">
                                            <path d="m5 12 7-7 7 7"></path>
                                            <path d="M12 19V5"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    <script id="__NEXT_DATA__" type="application/json">
        {
            "props": {
                "pageProps": {
                    "mdxSource": {
                        "compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    a: \"a\",\n    code: \"code\",\n    li: \"li\",\n    ol: \"ol\",\n    p: \"p\",\n    strong: \"strong\",\n    ul: \"ul\",\n    ..._provideComponents(),\n    ...props.components\n  }, {Accordion, AccordionGroup, Heading, Note} = _components;\n  if (!Accordion) _missingMdxReference(\"Accordion\", true);\n  if (!AccordionGroup) _missingMdxReference(\"AccordionGroup\", true);\n  if (!Heading) _missingMdxReference(\"Heading\", true);\n  if (!Note) _missingMdxReference(\"Note\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(_components.p, {\n      children: \"While we strive to make Cursor as stable as possible, sometimes issues can arise. Below are some common issues and how to resolve them.\"\n    }), \"\\n\", _jsx(Heading, {\n      level: \"3\",\n      id: \"networking-issues-http%2F2\",\n      isAtRootLevel: \"true\",\n      children: \"Networking Issues (HTTP/2)\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Cursor relies on the HTTP/2 protocol for many of it’s AI features, due to it’s ability to handle streamed responses. If HTTP/2 is not supported by your network, this can cause issues such as failure to index your code, and the inability to use Cursor’s AI features.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This can be the case when on corpoorate networks, using VPNs, or using a proxy like Zscaler.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To resolve this, Cursor now comes with a HTTP/1.1 fallback, which is slower, but will allow you to use Cursor’s AI features. You can enable this yourself in the app settings (not the Cursor settings), by pressing \", _jsx(_components.code, {\n        children: \"CMD/CTRL + ,\"\n      }), \" and then searching for \", _jsx(_components.code, {\n        children: \"HTTP/2\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You should then enable the \", _jsx(_components.code, {\n        children: \"Disable HTTP/2\"\n      }), \" option, which will force Cursor to use HTTP/1.1, and should resolve the issue.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We hope to add automatic detection and fallback in the future!\"\n    }), \"\\n\", _jsx(Heading, {\n      level: \"3\",\n      id: \"resource-issues-cpu%2C-ram%2C-etc\",\n      isAtRootLevel: \"true\",\n      children: \"Resource Issues (CPU, RAM, etc.)\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Some users see high CPU or RAM usage in Cursor, which can cause their machine to slow down, or to show warnings about high RAM usage.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"While Cursor can use a lot of resources when working on large codebases, this is usually not the case for most users, and is more likely to be an issue with Cursor’s extensions or settings.\"\n    }), \"\\n\", _jsx(Note, {\n      children: _jsxs(_components.p, {\n        children: [\"If you are seeing a low RAM warning on \", _jsx(_components.strong, {\n          children: \"MacOS\"\n        }), \", please note that there is a bug for some users that can show wildly incorrect values. If you are seeing this, please open the Activity Monitor and look at the “Memory” tab to see the correct memory usage.\"]\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"If you’re experiencing high CPU or RAM usage in Cursor, here are steps to diagnose and resolve the issue:\"\n    }), \"\\n\", _jsxs(AccordionGroup, {\n      children: [_jsxs(Accordion, {\n        title: \"Check Your Extensions\",\n        children: [_jsx(_components.p, {\n          children: \"While many extensions can be useful, some can significantly impact performance!\"\n        }), _jsxs(_components.p, {\n          children: [\"To test this, you can try to run \", _jsx(_components.code, {\n            children: \"cursor --disable-extensions\"\n          }), \" from the command line to launch Cursor without any extensions enabled. If the performance improves, gradually re-enable extensions one by one to identify the problematic ones.\"]\n        }), _jsxs(_components.p, {\n          children: [\"You can also try to use the Extension Bisect feature, which will help you identify which extension is causing the issue. You can read more about it \", _jsx(_components.a, {\n            href: \"https://code.visualstudio.com/blogs/2021/02/16/extension-bisect#_welcome-extension-bisect\",\n            children: \"here\"\n          }), \", but note that this may only be useful if the issues are immediate and obvious, and not an issue that worsens over time.\"]\n        })]\n      }), _jsxs(Accordion, {\n        title: \"Use the Process Explorer\",\n        children: [_jsxs(_components.p, {\n          children: [\"The \", _jsx(_components.strong, {\n            children: \"Process Explorer\"\n          }), \" is a built in tool in Cursor that allows you to see which processes are consuming resources.\"]\n        }), _jsxs(_components.p, {\n          children: [\"To open it, open the Command Palette (\", _jsx(_components.code, {\n            children: \"Cmd/Ctrl + Shift + P\"\n          }), \") and run the \", _jsx(_components.code, {\n            children: \"Developer: Open Process Explorer\"\n          }), \" command.\"]\n        }), _jsx(_components.p, {\n          children: \"This should open a new window, with a list of all the processes Cursor is running, both as part of it’s own executation, as well as any processes needed to run extensions and any terminals you may have running. This should immediately identify any processes that are consuming a lot of resources.\"\n        }), _jsxs(_components.p, {\n          children: [\"If the process is listed under the \", _jsx(_components.strong, {\n            children: _jsx(_components.code, {\n              children: \"extensionHost\"\n            })\n          }), \" dropdown, this suggests an extension is causing the issue, and you should try to find and disable the problematic extension.\"]\n        }), _jsxs(_components.p, {\n          children: [\"If the process is listended under the \", _jsx(_components.strong, {\n            children: _jsx(_components.code, {\n              children: \"ptyHost\"\n            })\n          }), \" dropdown, this suggests a terminal is consuming a lot of resources. The Process Explorer will show you each terminal that is running, and what command is running within it, so that you can try to kill it, or diagnose it’s high resource usage.\"]\n        }), _jsxs(_components.p, {\n          children: [\"If the usage is from another process, please let us know in the \", _jsx(_components.a, {\n            href: \"https://forum.cursor.com/\",\n            children: \"forum\"\n          }), \" and we’ll be happy to help diagnose the issue.\"]\n        })]\n      }), _jsxs(Accordion, {\n        title: \"Monitor System Resources\",\n        children: [_jsx(_components.p, {\n          children: \"Depending on your operating system, you can use a number of different tools to monitor your system’s resources.\"\n        }), _jsx(_components.p, {\n          children: \"This will help you identify if the issue is Cursor-specific, or if it’s a system-wide issue.\"\n        })]\n      }), _jsx(Accordion, {\n        title: \"Testing a Minimal Installation\",\n        children: _jsx(_components.p, {\n          children: \"While the above steps should help the majority of users, if you are still experiencing issues, you can try testing a minimal installation of Cursor to see if the issue persists.\"\n        })\n      })]\n    }), \"\\n\", _jsx(Heading, {\n      level: \"2\",\n      id: \"general-faqs\",\n      isAtRootLevel: \"true\",\n      children: \"General FAQs\"\n    }), \"\\n\", _jsxs(AccordionGroup, {\n      children: [_jsx(Accordion, {\n        title: \"I see an update on the changelog but Cursor won't update\",\n        children: _jsx(_components.p, {\n          children: \"If the update is very new, it might not have rolled out to you yet. We do staged rollouts, which means we release new updates to a few randomly selected users first before releasing them to everyone. Expect to get the update in a couple days!\"\n        })\n      }), _jsx(Accordion, {\n        title: \"I have issues with my GitHub login in Cursor / How do I log out of GitHub in Cursor?\",\n        children: _jsxs(_components.p, {\n          children: [\"You can try using the \", _jsx(_components.code, {\n            children: \"Sign Out of GitHub\"\n          }), \" command from the command palette \", _jsx(_components.code, {\n            children: \"Ctrl/⌘ + Shift + P\"\n          }), \".\"]\n        })\n      }), _jsx(Accordion, {\n        title: \"I can't use GitHub Codespaces\",\n        children: _jsx(_components.p, {\n          children: \"Unfortunately, we don’t support GitHub Codespaces yet.\"\n        })\n      }), _jsx(Accordion, {\n        title: \"I have errors connecting to Remote SSH\",\n        children: _jsxs(_components.p, {\n          children: [\"Currently, we don’t support SSHing into Mac or Windows machines. If you’re not using a Mac or Windows machine, please report your issue to us in the \", _jsx(_components.a, {\n            href: \"https://forum.cursor.com/\",\n            children: \"forum\"\n          }), \". It would be helpful to include some logs for better assistance.\"]\n        })\n      }), _jsxs(Accordion, {\n        title: \"SSH Connection Problems on Windows\",\n        children: [_jsx(_components.p, {\n          children: \"If you encounter the error “SSH is only supported in Microsoft versions of VS Code”, follow these steps:\"\n        }), _jsxs(_components.ol, {\n          children: [\"\\n\", _jsxs(_components.li, {\n            children: [\"\\n\", _jsx(_components.p, {\n              children: \"Uninstall the current Remote-SSH extension:\"\n            }), \"\\n\", _jsxs(_components.ul, {\n              children: [\"\\n\", _jsxs(_components.li, {\n                children: [\"Open the Extensions view (\", _jsx(_components.code, {\n                  children: \"Ctrl + Shift + X\"\n                }), \")\"]\n              }), \"\\n\", _jsx(_components.li, {\n                children: \"Search for “Remote-SSH”\"\n              }), \"\\n\", _jsx(_components.li, {\n                children: \"Click on the gear icon and select “Uninstall”\"\n              }), \"\\n\"]\n            }), \"\\n\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"\\n\", _jsx(_components.p, {\n              children: \"Install version 0.113 of Remote-SSH:\"\n            }), \"\\n\", _jsxs(_components.ul, {\n              children: [\"\\n\", _jsx(_components.li, {\n                children: \"Go to the Cursor marketplace\"\n              }), \"\\n\", _jsx(_components.li, {\n                children: \"Search for “Remote-SSH”\"\n              }), \"\\n\", _jsx(_components.li, {\n                children: \"Find version 0.113 and install it\"\n              }), \"\\n\"]\n            }), \"\\n\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"\\n\", _jsx(_components.p, {\n              children: \"After installation:\"\n            }), \"\\n\", _jsxs(_components.ul, {\n              children: [\"\\n\", _jsx(_components.li, {\n                children: \"Close all VS Code instances that have active SSH connections\"\n              }), \"\\n\", _jsx(_components.li, {\n                children: \"Restart Cursor completely\"\n              }), \"\\n\", _jsx(_components.li, {\n                children: \"Try connecting via SSH again\"\n              }), \"\\n\"]\n            }), \"\\n\"]\n          }), \"\\n\"]\n        }), _jsx(_components.p, {\n          children: \"If you still experience issues, make sure your SSH configuration is correct and that you have the necessary SSH keys set up properly.\"\n        })]\n      }), _jsx(Accordion, {\n        title: \"Cursor Tab and Cmd K do not work behind my corporate proxy\",\n        children: _jsxs(_components.p, {\n          children: [\"Cursor Tab and Cmd K use HTTP/2 by default, which allows us to use less resources with lower latency. Some corporate proxies (e.g. Zscaler in certain configurations) block HTTP/2. To fix this, you can set \", _jsx(_components.code, {\n            children: \"\\\"cursor.general.disableHttp2\\\": true\"\n          }), \" in the settings (\", _jsx(_components.code, {\n            children: \"Cmd/Ctrl + ,\"\n          }), \" and then search for \", _jsx(_components.code, {\n            children: \"http2\"\n          }), \").\"]\n        })\n      }), _jsx(Accordion, {\n        title: \"I just subscribed to Pro but I'm still on the free plan in the app\",\n        children: _jsx(_components.p, {\n          children: \"Try logging out and logging back in from the Cursor Settings\"\n        })\n      }), _jsxs(Accordion, {\n        title: \"When will my usage reset again?\",\n        children: [_jsxs(_components.p, {\n          children: [\"If you’re subscribed to Pro you can click on \", _jsx(_components.code, {\n            children: \"Manage Subscription\"\n          }), \" from the \", _jsx(_components.a, {\n            href: \"https://cursor.com/settings\",\n            children: \"Dashboard\"\n          }), \" and your plan renewal date will be displayed at the top.\"]\n        }), _jsx(_components.p, {\n          children: \"If you’re a free user you can check when you got the first email from us in your inbox. Your usage will reset every month from that date.\"\n        })]\n      }), _jsxs(Accordion, {\n        title: \"My Chat/Composer history disappeared after an update\",\n        children: [_jsx(_components.p, {\n          children: \"If you notice that your Chat or Composer history has been cleared following an update, this is likely due to low disk space on your system. Cursor may need to clear historical data during updates when disk space is limited. To prevent this from happening:\"\n        }), _jsxs(_components.ol, {\n          children: [\"\\n\", _jsx(_components.li, {\n            children: \"Ensure you have sufficient free disk space before updating\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"Regularly clean up unnecessary files on your system\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"Consider backing up important conversations before updating\"\n          }), \"\\n\"]\n        })]\n      }), _jsx(Accordion, {\n        title: \"How do I uninstall Cursor?\",\n        children: _jsxs(_components.p, {\n          children: [\"You can follow \", _jsx(_components.a, {\n            href: \"https://code.visualstudio.com/docs/setup/uninstall\",\n            children: \"this guide\"\n          }), \" to uninstall Cursor. Replace every occurrence of “VS Code” or “Code” with “Cursor”, and “.vscode” with “.cursor”.\"]\n        })\n      }), _jsx(Accordion, {\n        title: \"How do I delete my account?\",\n        children: _jsxs(_components.p, {\n          children: [\"You can delete your account by clicking on the \", _jsx(_components.code, {\n            children: \"Delete Account\"\n          }), \" button in the \", _jsx(_components.a, {\n            href: \"https://cursor.com/settings\",\n            children: \"Dashboard\"\n          }), \". Note that this will delete your account and all data associated with it.\"]\n        })\n      }), _jsxs(Accordion, {\n        title: \"How do I open Cursor from the command line?\",\n        children: [_jsxs(_components.p, {\n          children: [\"You can open Cursor from the command line by running \", _jsx(_components.code, {\n            children: \"cursor\"\n          }), \" in your terminal. If you’re missing the \", _jsx(_components.code, {\n            children: \"cursor\"\n          }), \" command, you can\"]\n        }), _jsxs(_components.ol, {\n          children: [\"\\n\", _jsxs(_components.li, {\n            children: [\"Open the command palette \", _jsx(_components.code, {\n              children: \"⌘⇧P\"\n            })]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"Type \", _jsx(_components.code, {\n              children: \"install command\"\n            })]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"Select \", _jsx(_components.code, {\n              children: \"Install 'cursor' command\"\n            }), \" (and optionally the \", _jsx(_components.code, {\n              children: \"code\"\n            }), \" command too which will override VS Code’s \", _jsx(_components.code, {\n              children: \"code\"\n            }), \" command)\"]\n          }), \"\\n\"]\n        })]\n      }), _jsx(Accordion, {\n        title: \"Unable to Sign In to Cursor\",\n        children: _jsx(_components.p, {\n          children: \"If you click Sign In on the General tab of Cursor’s Settings tab but are redirected to cursor.com and then return to Cursor still seeing the Sign In button, try disabling your firewall or antivirus software, which may be blocking the sign-in process.\"\n        })\n      })]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsx(MDXLayout, {\n    ...props,\n    children: _jsx(_createMdxContent, {\n      ...props\n    })\n  }) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n",
                        "frontmatter": {
                        },
                        "scope": {
                            "config": {
                                "theme": "mint",
                                "$schema": "https://mintlify.com/docs.json",
                                "name": "Cursor",
                                "colors": {
                                    "primary": "#0C0C15",
                                    "light": "#ffffff",
                                    "dark": "#0C0C15"
                                },
                                "logo": {
                                    "light": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/logo/app-logo.svg",
                                    "dark": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/logo/app-logo.svg"
                                },
                                "favicon": "/images/logo/favicon.svg",
                                "appearance": {
                                    "default": "light"
                                },
                                "background": {
                                    "color": {
                                        "light": "#fff",
                                        "dark": "#000000"
                                    }
                                },
                                "navbar": {
                                    "links": [
                                        {
                                            "label": "Sign in",
                                            "href": "https://cursor.com/settings"
                                        }
                                    ],
                                    "primary": {
                                        "type": "button",
                                        "label": "Download",
                                        "href": "https://cursor.com"
                                    }
                                },
                                "navigation": {
                                    "tabs": [
                                        {
                                            "tab": "Documentation",
                                            "icon": "book-open",
                                            "groups": [
                                                {
                                                    "group": "Get Started",
                                                    "pages": [
                                                        "welcome",
                                                        "get-started/installation",
                                                        "faq"
                                                    ]
                                                },
                                                {
                                                    "group": "Editor",
                                                    "pages": [
                                                        {
                                                            "group": "Tab",
                                                            "pages": [
                                                                "tab/overview",
                                                                "tab/from-gh-copilot",
                                                                "tab/auto-import",
                                                                "tab/advanced-features"
                                                            ]
                                                        },
                                                        {
                                                            "group": "Chat",
                                                            "pages": [
                                                                "chat/overview",
                                                                "chat/agent",
                                                                "chat/ask",
                                                                "chat/manual",
                                                                "chat/custom-modes",
                                                                "chat/tools",
                                                                "chat/apply"
                                                            ]
                                                        },
                                                        {
                                                            "group": "Inline Edit (⌘K)",
                                                            "pages": [
                                                                "cmdk/overview",
                                                                "cmdk/terminal-cmdk"
                                                            ]
                                                        },
                                                        "models",
                                                        "kbd",
                                                        {
                                                            "group": "Features",
                                                            "pages": [
                                                                "more/ai-commit-message",
                                                                "beta/notepads",
                                                                "background-agent"
                                                            ]
                                                        }
                                                    ]
                                                },
                                                {
                                                    "group": "Context",
                                                    "pages": [
                                                        "context/codebase-indexing",
                                                        "context/rules",
                                                        "context/management",
                                                        {
                                                            "group": "@ Symbols",
                                                            "pages": [
                                                                "context/@-symbols/overview",
                                                                "context/@-symbols/@-files",
                                                                "context/@-symbols/@-folders",
                                                                "context/@-symbols/@-code",
                                                                "context/@-symbols/@-docs",
                                                                "context/@-symbols/@-git",
                                                                "context/@-symbols/@-web",
                                                                "context/@-symbols/@-definitions",
                                                                "context/@-symbols/@-link",
                                                                "context/@-symbols/@-lint-errors",
                                                                "context/@-symbols/@-recent-changes",
                                                                "context/@-symbols/@-cursor-rules",
                                                                "context/@-symbols/@-notepads",
                                                                "context/@-symbols/@-past-chats",
                                                                "context/@-symbols/pill-files",
                                                                "context/@-symbols/slash-commands"
                                                            ]
                                                        },
                                                        "context/ignore-files",
                                                        "context/model-context-protocol",
                                                        "context/max-mode"
                                                    ]
                                                },
                                                {
                                                    "group": "Account",
                                                    "pages": [
                                                        "account/plans-and-usage",
                                                        {
                                                            "group": "Business",
                                                            "pages": [
                                                                "account/teams/setup",
                                                                "account/teams/members",
                                                                "account/teams/analytics",
                                                                "account/teams/sso"
                                                            ]
                                                        },
                                                        "account/dashboard",
                                                        "account/billing",
                                                        "account/pricing",
                                                        "account/privacy"
                                                    ]
                                                },
                                                {
                                                    "group": "Settings",
                                                    "pages": [
                                                        "settings/api-keys",
                                                        "settings/beta"
                                                    ]
                                                },
                                                {
                                                    "group": "Troubleshooting",
                                                    "pages": [
                                                        "troubleshooting/common-issues",
                                                        "troubleshooting/troubleshooting-guide",
                                                        "troubleshooting/request-reporting"
                                                    ]
                                                }
                                            ],
                                            "global": {
                                                "anchors": [
                                                    {
                                                        "anchor": "Website",
                                                        "icon": {
                                                            "style": "duotone",
                                                            "name": "globe"
                                                        },
                                                        "href": "https://cursor.com/"
                                                    },
                                                    {
                                                        "anchor": "Forum",
                                                        "icon": {
                                                            "style": "duotone",
                                                            "name": "newspaper"
                                                        },
                                                        "href": "https://forum.cursor.com/"
                                                    },
                                                    {
                                                        "anchor": "Support",
                                                        "icon": {
                                                            "style": "duotone",
                                                            "name": "headset"
                                                        },
                                                        "href": "mailto:<EMAIL>"
                                                    }
                                                ]
                                            }
                                        },
                                        {
                                            "tab": "Guides",
                                            "icon": {
                                                "style": "duotone",
                                                "name": "book-open"
                                            },
                                            "groups": [
                                                {
                                                    "group": "Core",
                                                    "pages": [
                                                        "guides/working-with-context",
                                                        "guides/selecting-models"
                                                    ]
                                                },
                                                {
                                                    "group": "Tutorials",
                                                    "pages": [
                                                        "guides/tutorials/web-development",
                                                        "guides/tutorials/architectural-diagrams"
                                                    ]
                                                },
                                                {
                                                    "group": "Advanced",
                                                    "pages": [
                                                        "guides/advanced/large-codebases",
                                                        "guides/advanced/working-with-documentation"
                                                    ]
                                                },
                                                {
                                                    "group": "Editor Migration",
                                                    "pages": [
                                                        "guides/migration/vscode",
                                                        "guides/migration/jetbrains"
                                                    ]
                                                },
                                                {
                                                    "group": "Languages \u0026 Frameworks",
                                                    "pages": [
                                                        "guides/languages/python",
                                                        "guides/languages/javascript",
                                                        "guides/languages/swift",
                                                        "guides/languages/java"
                                                    ]
                                                }
                                            ],
                                            "global": {
                                                "anchors": [
                                                    {
                                                        "anchor": "Website",
                                                        "icon": {
                                                            "style": "duotone",
                                                            "name": "globe"
                                                        },
                                                        "href": "https://cursor.com/"
                                                    },
                                                    {
                                                        "anchor": "Forum",
                                                        "icon": {
                                                            "style": "duotone",
                                                            "name": "newspaper"
                                                        },
                                                        "href": "https://forum.cursor.com/"
                                                    },
                                                    {
                                                        "anchor": "Support",
                                                        "icon": {
                                                            "style": "duotone",
                                                            "name": "headset"
                                                        },
                                                        "href": "mailto:<EMAIL>"
                                                    }
                                                ]
                                            }
                                        }
                                    ]
                                },
                                "footer": {
                                    "socials": {
                                        "x": "https://x.com/cursor_ai",
                                        "github": "https://github.com/getcursor/cursor/",
                                        "website": "https://cursor.com"
                                    },
                                    "links": [
                                        {
                                            "header": "Product",
                                            "items": [
                                                {
                                                    "label": "Pricing",
                                                    "href": "https://www.cursor.com/pricing"
                                                },
                                                {
                                                    "label": "Downloads",
                                                    "href": "https://www.cursor.com/downloads"
                                                },
                                                {
                                                    "label": "Docs",
                                                    "href": "https://docs.cursor.com"
                                                },
                                                {
                                                    "label": "Forum",
                                                    "href": "https://forum.cursor.com"
                                                }
                                            ]
                                        },
                                        {
                                            "header": "Company",
                                            "items": [
                                                {
                                                    "label": "Careers",
                                                    "href": "https://anysphere.inc"
                                                },
                                                {
                                                    "label": "About",
                                                    "href": "https://anysphere.inc"
                                                },
                                                {
                                                    "label": "Security",
                                                    "href": "https://cursor.com/security"
                                                },
                                                {
                                                    "label": "Privacy",
                                                    "href": "https://cursor.com/privacy"
                                                }
                                            ]
                                        },
                                        {
                                            "header": "Resources",
                                            "items": [
                                                {
                                                    "label": "Terms",
                                                    "href": "https://cursor.com/terms-of-service"
                                                },
                                                {
                                                    "label": "Changelog",
                                                    "href": "https://cursor.com/changelog"
                                                },
                                                {
                                                    "label": "Twitter",
                                                    "href": "https://x.com/cursor_ai"
                                                },
                                                {
                                                    "label": "GitHub",
                                                    "href": "https://github.com/getcursor/cursor"
                                                }
                                            ]
                                        }
                                    ]
                                },
                                "seo": {
                                    "metatags": {
                                        "og:site_name": "Cursor",
                                        "og:title": "Cursor - Build Software Faster",
                                        "og:description": "Built to make you extraordinarily productive, Cursor is the best way to code with AI.",
                                        "og:type": "website",
                                        "og:url": "https://cursor.com",
                                        "og:image": "/images/hero.png",
                                        "og:locale": "en_US",
                                        "og:logo": "/images/logo/app-logo.svg",
                                        "article:publisher": "Anysphere Inc.",
                                        "twitter:title": "Cursor - Build Software Faster",
                                        "twitter:description": "Built to make you extraordinarily productive, Cursor is the best way to code with AI.",
                                        "twitter:url": "https://cursor.com",
                                        "twitter:image": "/images/hero.png",
                                        "twitter:card": "summary_large_image",
                                        "twitter:site": "@cursor_ai",
                                        "og:image:width": "1200",
                                        "og:image:height": "630"
                                    },
                                    "indexing": "navigable"
                                },
                                "styling": {
                                    "eyebrows": "breadcrumbs"
                                },
                                "redirects": [
                                    {
                                        "source": "/ai-review",
                                        "destination": "/chat/agent"
                                    },
                                    {
                                        "source": "/background-agents",
                                        "destination": "/background-agent"
                                    },
                                    {
                                        "source": "/advanced/api-keys",
                                        "destination": "/settings/api-keys"
                                    },
                                    {
                                        "source": "/advanced/keyboard-shortcuts",
                                        "destination": "/kbd"
                                    },
                                    {
                                        "source": "/advanced/models",
                                        "destination": "/settings/models"
                                    },
                                    {
                                        "source": "/advanced/model-context-protocol",
                                        "destination": "/context/model-context-protocol"
                                    },
                                    {
                                        "source": "/billing/faq",
                                        "destination": "/account/billing"
                                    },
                                    {
                                        "source": "/forum",
                                        "destination": "/resources/forum"
                                    },
                                    {
                                        "source": "/composer/overview",
                                        "destination": "/composer"
                                    },
                                    {
                                        "source": "/get-started/usage",
                                        "destination": "/account/usage"
                                    },
                                    {
                                        "source": "/get-started/migrate-from-vs-code",
                                        "destination": "/guides/migration/vscode"
                                    },
                                    {
                                        "source": "/get-started/what-is-cursor",
                                        "destination": "/welcome"
                                    },
                                    {
                                        "source": "/plans/business/getting-started",
                                        "destination": "/account/teams/setup"
                                    },
                                    {
                                        "source": "/plans/business/roles",
                                        "destination": "/account/teams/members"
                                    },
                                    {
                                        "source": "/plans/business/sso",
                                        "destination": "/account/teams/sso"
                                    },
                                    {
                                        "source": "/plans/business/team-management",
                                        "destination": "/account/teams/members"
                                    },
                                    {
                                        "source": "/privacy/privacy",
                                        "destination": "/account/privacy"
                                    },
                                    {
                                        "source": "/settings/ide/features",
                                        "destination": "/settings/preferences"
                                    },
                                    {
                                        "source": "/settings/ide/models",
                                        "destination": "/settings/models"
                                    },
                                    {
                                        "source": "/settings/ide/overview",
                                        "destination": "/settings/preferences"
                                    },
                                    {
                                        "source": "/features/generate-commit-message",
                                        "destination": "/more/ai-commit-message"
                                    },
                                    {
                                        "source": "/features/beta/notepads",
                                        "destination": "/beta/notepads"
                                    },
                                    {
                                        "source": "/context/@-symbols/basic",
                                        "destination": "/context/@-symbols/overview"
                                    },
                                    {
                                        "source": "/get-started/resources",
                                        "destination": "/resources/forum"
                                    },
                                    {
                                        "source": "/agent",
                                        "destination": "/chat/agent"
                                    },
                                    {
                                        "source": "/composer",
                                        "destination": "/chat/overview"
                                    },
                                    {
                                        "source": "/chat/customize",
                                        "destination": "/chat/overview"
                                    },
                                    {
                                        "source": "/chat/codebase",
                                        "destination": "/context/codebase-indexing"
                                    },
                                    {
                                        "source": "/settings/preferences",
                                        "destination": "/settings/beta"
                                    },
                                    {
                                        "source": "/account/usage",
                                        "destination": "/account/plans-and-usage"
                                    },
                                    {
                                        "source": "/account/plans",
                                        "destination": "/account/plans-and-usage"
                                    },
                                    {
                                        "source": "/account/security",
                                        "destination": "/account/privacy"
                                    },
                                    {
                                        "source": "/settings/models",
                                        "destination": "/models"
                                    },
                                    {
                                        "source": "/context/rules-for-ai",
                                        "destination": "/context/rules"
                                    },
                                    {
                                        "source": "/context/@-symbols/@-summarized-composers",
                                        "destination": "/context/@-symbols/@-past-chats"
                                    },
                                    {
                                        "source": "/",
                                        "destination": "/welcome"
                                    }
                                ]
                            },
                            "pageMetadata": {
                                "title": "Common Issues",
                                "description": "Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs",
                                "twitter:title": "Cursor – Common Issues",
                                "twitter:description": "Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs",
                                "og:title": "Cursor – Common Issues",
                                "og:description": "Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs",
                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/common-issues.png?v=1743819390998",
                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/common-issues.png?v=1743819390998",
                                "href": "/troubleshooting/common-issues"
                            }
                        }
                    },
                    "mdxExtracts": {
                        "tableOfContents": [
                            {
                                "title": "Networking Issues (HTTP/2)",
                                "slug": "networking-issues-http%2F2",
                                "depth": 3,
                                "children": [
                                ]
                            },
                            {
                                "title": "Resource Issues (CPU, RAM, etc.)",
                                "slug": "resource-issues-cpu%2C-ram%2C-etc",
                                "depth": 3,
                                "children": [
                                ]
                            },
                            {
                                "title": "General FAQs",
                                "slug": "general-faqs",
                                "depth": 2,
                                "children": [
                                ]
                            }
                        ],
                        "codeExamples": {
                        }
                    },
                    "description": {
                        "compiledSource": "\"use strict\";\nconst {jsx: _jsx} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsx(_components.p, {\n    children: \"Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs\"\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsx(MDXLayout, {\n    ...props,\n    children: _jsx(_createMdxContent, {\n      ...props\n    })\n  }) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n",
                        "frontmatter": {
                        },
                        "scope": {
                        }
                    },
                    "pageData": {
                        "navWithMetadata": [
                            {
                                "group": "",
                                "pages": [
                                    {
                                        "title": "Welcome to Cursor",
                                        "description": "Get started with Cursor IDE \u0026 learn core features: Tab completion, Chat AI pair programming, and Cmd-K editing",
                                        "twitter:title": "Cursor – Welcome to Cursor",
                                        "twitter:description": "Get started with Cursor IDE \u0026 learn core features: Tab completion, Chat AI pair programming, and Cmd-K editing",
                                        "og:title": "Cursor – Welcome to Cursor",
                                        "og:description": "Get started with Cursor IDE \u0026 learn core features: Tab completion, Chat AI pair programming, and Cmd-K editing",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/get-started/welcome.png?v=1743819390622",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/get-started/welcome.png?v=1743819390622",
                                        "href": "/get-started/welcome"
                                    }
                                ]
                            },
                            {
                                "group": "Get Started",
                                "pages": [
                                    {
                                        "title": "Introduction",
                                        "description": "Learn about Cursor's core AI features, settings, and customization options for powerful code development.",
                                        "twitter:title": "Cursor – Introduction",
                                        "twitter:description": "Learn about Cursor's core AI features, settings, and customization options for powerful code development.",
                                        "og:title": "Cursor – Introduction",
                                        "og:description": "Learn about Cursor's core AI features, settings, and customization options for powerful code development.",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/get-started/introduction.png?v=1743819390574",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/get-started/introduction.png?v=1743819390574",
                                        "href": "/get-started/introduction"
                                    },
                                    {
                                        "title": "Installation",
                                        "description": "Guide to installing Cursor, configuring initial settings, and migrating from other code editors",
                                        "twitter:title": "Cursor – Installation",
                                        "twitter:description": "Guide to installing Cursor, configuring initial settings, and migrating from other code editors",
                                        "og:title": "Cursor – Installation",
                                        "og:description": "Guide to installing Cursor, configuring initial settings, and migrating from other code editors",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/get-started/installation.png?v=1743819390538",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/get-started/installation.png?v=1743819390538",
                                        "href": "/get-started/installation"
                                    },
                                    {
                                        "title": "FAQ",
                                        "description": "Common questions about language support, models, project limits, and data management in Cursor with troubleshooting help",
                                        "twitter:title": "Cursor – FAQ",
                                        "twitter:description": "Common questions about language support, models, project limits, and data management in Cursor with troubleshooting help",
                                        "og:title": "Cursor – FAQ",
                                        "og:description": "Common questions about language support, models, project limits, and data management in Cursor with troubleshooting help",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/faq.png?v=1743819390494",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/faq.png?v=1743819390494",
                                        "href": "/faq"
                                    }
                                ]
                            },
                            {
                                "group": "Guides",
                                "pages": [
                                    {
                                        "group": "Editor Migration",
                                        "pages": [
                                            {
                                                "title": "Migrate from VS Code",
                                                "description": "Guide to migrating VS Code settings, extensions, and profiles to Cursor using one-click import or manual methods",
                                                "twitter:title": "Cursor – Migrate from VS Code",
                                                "twitter:description": "Guide to migrating VS Code settings, extensions, and profiles to Cursor using one-click import or manual methods",
                                                "og:title": "Cursor – Migrate from VS Code",
                                                "og:description": "Guide to migrating VS Code settings, extensions, and profiles to Cursor using one-click import or manual methods",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/migration/vscode.png?v=1743819392005",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/migration/vscode.png?v=1743819392005",
                                                "href": "/guides/migration/vscode"
                                            },
                                            {
                                                "title": "Migrate from JetBrains IDEs",
                                                "description": "Guide to migrating from JetBrains IDEs to Cursor: setup extensions, themes, shortcuts, and language-specific tools",
                                                "twitter:title": "Cursor – Migrate from JetBrains IDEs",
                                                "twitter:description": "Guide to migrating from JetBrains IDEs to Cursor: setup extensions, themes, shortcuts, and language-specific tools",
                                                "og:title": "Cursor – Migrate from JetBrains IDEs",
                                                "og:description": "Guide to migrating from JetBrains IDEs to Cursor: setup extensions, themes, shortcuts, and language-specific tools",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/migration/jetbrains.png?v=1743819391909",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/migration/jetbrains.png?v=1743819391909",
                                                "href": "/guides/migration/jetbrains"
                                            }
                                        ]
                                    },
                                    {
                                        "group": "Languages \u0026 Frameworks",
                                        "pages": [
                                            {
                                                "title": "Python",
                                                "description": "Comprehensive guide to setting up Python development in Cursor with essential extensions, linting and tools",
                                                "twitter:title": "Cursor – Python",
                                                "twitter:description": "Comprehensive guide to setting up Python development in Cursor with essential extensions, linting and tools",
                                                "og:title": "Cursor – Python",
                                                "og:description": "Comprehensive guide to setting up Python development in Cursor with essential extensions, linting and tools",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/languages/python.png?v=1743819392199",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/languages/python.png?v=1743819392199",
                                                "href": "/guides/languages/python"
                                            },
                                            {
                                                "title": "JavaScript \u0026 TypeScript",
                                                "description": "Complete guide to JavaScript \u0026 TypeScript development in Cursor, featuring extensions, AI tools, and framework support",
                                                "twitter:title": "Cursor – JavaScript \u0026 TypeScript",
                                                "twitter:description": "Complete guide to JavaScript \u0026 TypeScript development in Cursor, featuring extensions, AI tools, and framework support",
                                                "og:title": "Cursor – JavaScript \u0026 TypeScript",
                                                "og:description": "Complete guide to JavaScript \u0026 TypeScript development in Cursor, featuring extensions, AI tools, and framework support",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/languages/javascript.png?v=1743819392329",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/languages/javascript.png?v=1743819392329",
                                                "href": "/guides/languages/javascript"
                                            },
                                            {
                                                "title": "iOS \u0026 macOS (Swift)",
                                                "description": "Guide to integrating Cursor with Swift development workflows using Xcode, hot reloading, and Sweetpad tools",
                                                "twitter:title": "Cursor – iOS \u0026 macOS (Swift)",
                                                "twitter:description": "Guide to integrating Cursor with Swift development workflows using Xcode, hot reloading, and Sweetpad tools",
                                                "og:title": "Cursor – iOS \u0026 macOS (Swift)",
                                                "og:description": "Guide to integrating Cursor with Swift development workflows using Xcode, hot reloading, and Sweetpad tools",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/languages/swift.png?v=1743819392098",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/languages/swift.png?v=1743819392098",
                                                "href": "/guides/languages/swift"
                                            },
                                            {
                                                "title": "Java",
                                                "description": "Complete guide to setting up Java development in Cursor: JDK setup, extensions, debugging, and Maven/Gradle integration",
                                                "twitter:title": "Cursor – Java",
                                                "twitter:description": "Complete guide to setting up Java development in Cursor: JDK setup, extensions, debugging, and Maven/Gradle integration",
                                                "og:title": "Cursor – Java",
                                                "og:description": "Complete guide to setting up Java development in Cursor: JDK setup, extensions, debugging, and Maven/Gradle integration",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/languages/java.png?v=1743819392298",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/languages/java.png?v=1743819392298",
                                                "href": "/guides/languages/java"
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                "group": "Editor",
                                "pages": [
                                    {
                                        "group": "Tab",
                                        "pages": [
                                            {
                                                "title": "Overview",
                                                "description": "Learn how Tab uses AI to suggest multi-line edits, code fixes, and context-aware completions directly in the editor",
                                                "twitter:title": "Cursor – Overview",
                                                "twitter:description": "Learn how Tab uses AI to suggest multi-line edits, code fixes, and context-aware completions directly in the editor",
                                                "og:title": "Cursor – Overview",
                                                "og:description": "Learn how Tab uses AI to suggest multi-line edits, code fixes, and context-aware completions directly in the editor",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/tab/overview.png?v=1743819391097",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/tab/overview.png?v=1743819391097",
                                                "href": "/tab/overview"
                                            },
                                            {
                                                "title": "Tab vs GitHub Copilot",
                                                "description": "Compares Cursor's multi-line edits and instruction-based completions to GitHub Copilot's single-line insertions",
                                                "twitter:title": "Cursor – Tab vs GitHub Copilot",
                                                "twitter:description": "Compares Cursor's multi-line edits and instruction-based completions to GitHub Copilot's single-line insertions",
                                                "og:title": "Cursor – Tab vs GitHub Copilot",
                                                "og:description": "Compares Cursor's multi-line edits and instruction-based completions to GitHub Copilot's single-line insertions",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/tab/from-gh-copilot.png?v=1743819391065",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/tab/from-gh-copilot.png?v=1743819391065",
                                                "href": "/tab/from-gh-copilot"
                                            },
                                            {
                                                "title": "Auto-import",
                                                "description": "Learn how Cursor's Tab feature automates module imports in TypeScript and Python projects while you code",
                                                "twitter:title": "Cursor – Auto-import",
                                                "twitter:description": "Learn how Cursor's Tab feature automates module imports in TypeScript and Python projects while you code",
                                                "og:title": "Cursor – Auto-import",
                                                "og:description": "Learn how Cursor's Tab feature automates module imports in TypeScript and Python projects while you code",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/tab/auto-import.png?v=1743819391136",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/tab/auto-import.png?v=1743819391136",
                                                "href": "/tab/auto-import"
                                            },
                                            {
                                                "title": "Advanced Features",
                                                "description": "Learn to navigate code efficiently using Tab in peek views, prediction, and partial accepts",
                                                "twitter:title": "Cursor – Advanced Features",
                                                "twitter:description": "Learn to navigate code efficiently using Tab in peek views, prediction, and partial accepts",
                                                "og:title": "Cursor – Advanced Features",
                                                "og:description": "Learn to navigate code efficiently using Tab in peek views, prediction, and partial accepts",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/tab/advanced-features.png?v=1743819391032",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/tab/advanced-features.png?v=1743819391032",
                                                "href": "/tab/advanced-features"
                                            }
                                        ]
                                    },
                                    {
                                        "group": "Chat",
                                        "pages": [
                                            {
                                                "title": "Overview",
                                                "description": "Natural language interface for exploring, editing, and managing code with contextual AI assistance in Chat mode",
                                                "twitter:title": "Cursor – Overview",
                                                "twitter:description": "Natural language interface for exploring, editing, and managing code with contextual AI assistance in Chat mode",
                                                "og:title": "Cursor – Overview",
                                                "og:description": "Natural language interface for exploring, editing, and managing code with contextual AI assistance in Chat mode",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/overview.png?v=1743819391525",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/overview.png?v=1743819391525",
                                                "href": "/chat/overview"
                                            },
                                            {
                                                "title": "Agent Mode",
                                                "description": "Autonomous AI coding agent that independently explores, plans, and executes complex codebase changes with full tools",
                                                "twitter:title": "Cursor – Agent Mode",
                                                "twitter:description": "Autonomous AI coding agent that independently explores, plans, and executes complex codebase changes with full tools",
                                                "og:title": "Cursor – Agent Mode",
                                                "og:description": "Autonomous AI coding agent that independently explores, plans, and executes complex codebase changes with full tools",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/agent.png?v=1743819391458",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/agent.png?v=1743819391458",
                                                "href": "/chat/agent"
                                            },
                                            {
                                                "title": "Ask mode",
                                                "description": "Ask mode lets you explore and learn about codebases through AI search and queries without making changes",
                                                "twitter:title": "Cursor – Ask mode",
                                                "twitter:description": "Ask mode lets you explore and learn about codebases through AI search and queries without making changes",
                                                "og:title": "Cursor – Ask mode",
                                                "og:description": "Ask mode lets you explore and learn about codebases through AI search and queries without making changes",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/ask.png?v=1743819391493",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/ask.png?v=1743819391493",
                                                "href": "/chat/ask"
                                            },
                                            {
                                                "title": "Manual Mode",
                                                "description": "Make precise code changes with explicit file targeting - a focused editing mode with user-controlled tooling",
                                                "twitter:title": "Cursor – Manual Mode",
                                                "twitter:description": "Make precise code changes with explicit file targeting - a focused editing mode with user-controlled tooling",
                                                "og:title": "Cursor – Manual Mode",
                                                "og:description": "Make precise code changes with explicit file targeting - a focused editing mode with user-controlled tooling",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/manual.png?v=1743819391427",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/manual.png?v=1743819391427",
                                                "href": "/chat/manual"
                                            },
                                            {
                                                "title": "Custom Modes",
                                                "description": "Create custom Cursor modes with tailored tools and prompts to personalize AI assistance for specific workflows",
                                                "twitter:title": "Cursor – Custom Modes",
                                                "twitter:description": "Create custom Cursor modes with tailored tools and prompts to personalize AI assistance for specific workflows",
                                                "og:title": "Cursor – Custom Modes",
                                                "og:description": "Create custom Cursor modes with tailored tools and prompts to personalize AI assistance for specific workflows",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/custom-modes.png?v=1743819391588",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/custom-modes.png?v=1743819391588",
                                                "href": "/chat/custom-modes"
                                            },
                                            {
                                                "title": "Tools",
                                                "description": "A guide to all available tools in Cursor's Chat modes for searching, editing, and interacting with your codebase",
                                                "twitter:title": "Cursor – Tools",
                                                "twitter:description": "A guide to all available tools in Cursor's Chat modes for searching, editing, and interacting with your codebase",
                                                "og:title": "Cursor – Tools",
                                                "og:description": "A guide to all available tools in Cursor's Chat modes for searching, editing, and interacting with your codebase",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/tools.png?v=1743819391618",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/tools.png?v=1743819391618",
                                                "href": "/chat/tools"
                                            },
                                            {
                                                "title": "Apply",
                                                "description": "Learn how to apply, accept, or reject code suggestions from chat using Cursor's Apply feature",
                                                "twitter:title": "Cursor – Apply",
                                                "twitter:description": "Learn how to apply, accept, or reject code suggestions from chat using Cursor's Apply feature",
                                                "og:title": "Cursor – Apply",
                                                "og:description": "Learn how to apply, accept, or reject code suggestions from chat using Cursor's Apply feature",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/apply.png?v=1743819391557",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/apply.png?v=1743819391557",
                                                "href": "/chat/apply"
                                            }
                                        ]
                                    },
                                    {
                                        "group": "⌘K",
                                        "pages": [
                                            {
                                                "title": "Overview",
                                                "description": "Learn how to use Cmd/Ctrl K in Cursor to generate, edit code and ask questions with the Prompt Bar",
                                                "twitter:title": "Cursor – Overview",
                                                "twitter:description": "Learn how to use Cmd/Ctrl K in Cursor to generate, edit code and ask questions with the Prompt Bar",
                                                "og:title": "Cursor – Overview",
                                                "og:description": "Learn how to use Cmd/Ctrl K in Cursor to generate, edit code and ask questions with the Prompt Bar",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/cmdk/overview.png?v=1743819390702",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/cmdk/overview.png?v=1743819390702",
                                                "href": "/cmdk/overview"
                                            },
                                            {
                                                "title": "Terminal Cmd K",
                                                "description": "Use Cmd K in Cursor terminal to generate and run commands through a prompt bar interface",
                                                "twitter:title": "Cursor – Terminal Cmd K",
                                                "twitter:description": "Use Cmd K in Cursor terminal to generate and run commands through a prompt bar interface",
                                                "og:title": "Cursor – Terminal Cmd K",
                                                "og:description": "Use Cmd K in Cursor terminal to generate and run commands through a prompt bar interface",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/cmdk/terminal-cmdk.png?v=1743819390658",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/cmdk/terminal-cmdk.png?v=1743819390658",
                                                "href": "/cmdk/terminal-cmdk"
                                            }
                                        ]
                                    },
                                    {
                                        "title": "AI Commit Message",
                                        "description": "Learn how to generate contextual Git commit messages automatically using Cursor's sparkle icon or shortcuts",
                                        "twitter:title": "Cursor – AI Commit Message",
                                        "twitter:description": "Learn how to generate contextual Git commit messages automatically using Cursor's sparkle icon or shortcuts",
                                        "og:title": "Cursor – AI Commit Message",
                                        "og:description": "Learn how to generate contextual Git commit messages automatically using Cursor's sparkle icon or shortcuts",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/more/ai-commit-message.png?v=1743819391656",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/more/ai-commit-message.png?v=1743819391656",
                                        "href": "/more/ai-commit-message"
                                    },
                                    {
                                        "title": "Notepads (Beta)",
                                        "description": "A guide to using Notepads in Cursor for sharing context between Composers and Chat interactions",
                                        "twitter:title": "Cursor – Notepads (Beta)",
                                        "twitter:description": "A guide to using Notepads in Cursor for sharing context between Composers and Chat interactions",
                                        "og:title": "Cursor – Notepads (Beta)",
                                        "og:description": "A guide to using Notepads in Cursor for sharing context between Composers and Chat interactions",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/beta/notepads.png?v=1743819391698",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/beta/notepads.png?v=1743819391698",
                                        "href": "/beta/notepads"
                                    },
                                    {
                                        "title": "Keyboard Shortcuts",
                                        "description": "Complete reference for all keyboard shortcuts in Cursor, including Chat, Tab, Terminal and code selection commands",
                                        "twitter:title": "Cursor – Keyboard Shortcuts",
                                        "twitter:description": "Complete reference for all keyboard shortcuts in Cursor, including Chat, Tab, Terminal and code selection commands",
                                        "og:title": "Cursor – Keyboard Shortcuts",
                                        "og:description": "Complete reference for all keyboard shortcuts in Cursor, including Chat, Tab, Terminal and code selection commands",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/kbd.png?v=1743819390445",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/kbd.png?v=1743819390445",
                                        "href": "/kbd"
                                    }
                                ]
                            },
                            {
                                "group": "Context",
                                "pages": [
                                    {
                                        "title": "Codebase Indexing",
                                        "description": "Learn how to index your codebase in Cursor for more accurate AI assistance and search results",
                                        "twitter:title": "Cursor – Codebase Indexing",
                                        "twitter:description": "Learn how to index your codebase in Cursor for more accurate AI assistance and search results",
                                        "og:title": "Cursor – Codebase Indexing",
                                        "og:description": "Learn how to index your codebase in Cursor for more accurate AI assistance and search results",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/codebase-indexing.png?v=1743819391393",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/codebase-indexing.png?v=1743819391393",
                                        "href": "/context/codebase-indexing"
                                    },
                                    {
                                        "title": "Rules",
                                        "description": "Control how the Agent model behaves with reusable, scoped instructions.",
                                        "og:title": "Cursor – Rules",
                                        "og:description": "Control how the Agent model behaves with reusable, scoped instructions.",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/rules.png?v=1744588154263",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/rules.png?v=1744588154263",
                                        "twitter:title": "Cursor – Rules",
                                        "twitter:description": "Control how the Agent model behaves with reusable, scoped instructions.",
                                        "href": "/context/rules"
                                    },
                                    {
                                        "group": "@ Symbols",
                                        "pages": [
                                            {
                                                "title": "Overview",
                                                "description": "Guide to using @ symbols in Cursor for referencing code, files, documentation and other context in chats",
                                                "twitter:title": "Cursor – Overview",
                                                "twitter:description": "Guide to using @ symbols in Cursor for referencing code, files, documentation and other context in chats",
                                                "og:title": "Cursor – Overview",
                                                "og:description": "Guide to using @ symbols in Cursor for referencing code, files, documentation and other context in chats",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/overview.png?v=1743819393138",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/overview.png?v=1743819393138",
                                                "href": "/context/@-symbols/overview"
                                            },
                                            {
                                                "title": "@Files",
                                                "description": "Learn how to reference files using @ in Cursor's Chat and Cmd K, with preview and chunking features",
                                                "twitter:title": "Cursor – @Files",
                                                "twitter:description": "Learn how to reference files using @ in Cursor's Chat and Cmd K, with preview and chunking features",
                                                "og:title": "Cursor – @Files",
                                                "og:description": "Learn how to reference files using @ in Cursor's Chat and Cmd K, with preview and chunking features",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-files.png?v=1743819392865",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-files.png?v=1743819392865",
                                                "href": "/context/@-symbols/@-files"
                                            },
                                            {
                                                "title": "@Folders",
                                                "description": "Reference folders as context in Chat \u0026 Composer for enhanced AI conversations",
                                                "twitter:title": "Cursor – @Folders",
                                                "twitter:description": "Reference folders as context in Chat \u0026 Composer for enhanced AI conversations",
                                                "og:title": "Cursor – @Folders",
                                                "og:description": "Reference folders as context in Chat \u0026 Composer for enhanced AI conversations",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-folders.png?v=1743819392648",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-folders.png?v=1743819392648",
                                                "href": "/context/@-symbols/@-folders"
                                            },
                                            {
                                                "title": "@Code",
                                                "description": "Learn how to reference code snippets in Cursor using @Code symbol and keyboard shortcuts for adding to Chat",
                                                "twitter:title": "Cursor – @Code",
                                                "twitter:description": "Learn how to reference code snippets in Cursor using @Code symbol and keyboard shortcuts for adding to Chat",
                                                "og:title": "Cursor – @Code",
                                                "og:description": "Learn how to reference code snippets in Cursor using @Code symbol and keyboard shortcuts for adding to Chat",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-code.png?v=1743819393044",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-code.png?v=1743819393044",
                                                "href": "/context/@-symbols/@-code"
                                            },
                                            {
                                                "title": "@Docs",
                                                "description": "Learn how to use, add, and manage custom documentation as context in Cursor using @Docs",
                                                "twitter:title": "Cursor – @Docs",
                                                "twitter:description": "Learn how to use, add, and manage custom documentation as context in Cursor using @Docs",
                                                "og:title": "Cursor – @Docs",
                                                "og:description": "Learn how to use, add, and manage custom documentation as context in Cursor using @Docs",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-docs.png?v=1743819392585",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-docs.png?v=1743819392585",
                                                "href": "/context/@-symbols/@-docs"
                                            },
                                            {
                                                "title": "@Git",
                                                "description": "Reference Git commits, diffs, and PRs as context in Cursor Chat to analyze changes and generate commit messages",
                                                "twitter:title": "Cursor – @Git",
                                                "twitter:description": "Reference Git commits, diffs, and PRs as context in Cursor Chat to analyze changes and generate commit messages",
                                                "og:title": "Cursor – @Git",
                                                "og:description": "Reference Git commits, diffs, and PRs as context in Cursor Chat to analyze changes and generate commit messages",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-git.png?v=1743819392615",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-git.png?v=1743819392615",
                                                "href": "/context/@-symbols/@-git"
                                            },
                                            {
                                                "title": "@Web",
                                                "description": "Enables web search to dynamically add recent online information and documentation as context in Cursor commands",
                                                "twitter:title": "Cursor – @Web",
                                                "twitter:description": "Enables web search to dynamically add recent online information and documentation as context in Cursor commands",
                                                "og:title": "Cursor – @Web",
                                                "og:description": "Enables web search to dynamically add recent online information and documentation as context in Cursor commands",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-web.png?v=1743819393014",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-web.png?v=1743819393014",
                                                "href": "/context/@-symbols/@-web"
                                            },
                                            {
                                                "title": "@Definitions",
                                                "description": "Add nearby code definitions to Cmd K context using the @Definitions symbol",
                                                "twitter:title": "Cursor – @Definitions",
                                                "twitter:description": "Add nearby code definitions to Cmd K context using the @Definitions symbol",
                                                "og:title": "Cursor – @Definitions",
                                                "og:description": "Add nearby code definitions to Cmd K context using the @Definitions symbol",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-definitions.png?v=1743819392909",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-definitions.png?v=1743819392909",
                                                "href": "/context/@-symbols/@-definitions"
                                            },
                                            {
                                                "title": "@Link",
                                                "description": "Learn how to include and manage web links as context in Cursor's AI features by pasting URLs",
                                                "twitter:title": "Cursor – @Link",
                                                "twitter:description": "Learn how to include and manage web links as context in Cursor's AI features by pasting URLs",
                                                "og:title": "Cursor – @Link",
                                                "og:description": "Learn how to include and manage web links as context in Cursor's AI features by pasting URLs",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-link.png?v=1743819392982",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-link.png?v=1743819392982",
                                                "href": "/context/@-symbols/@-link"
                                            },
                                            {
                                                "title": "@Lint Errors",
                                                "description": "Access and reference linting errors in your codebase",
                                                "twitter:title": "Cursor – @Lint Errors",
                                                "twitter:description": "Access and reference linting errors in your codebase",
                                                "og:title": "Cursor – @Lint Errors",
                                                "og:description": "Access and reference linting errors in your codebase",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-lint-errors.png?v=1743819392949",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-lint-errors.png?v=1743819392949",
                                                "href": "/context/@-symbols/@-lint-errors"
                                            },
                                            {
                                                "title": "@Recent Changes",
                                                "description": "Reference recently modified code as context for AI chat using the @Recent Changes symbol",
                                                "twitter:title": "Cursor – @Recent Changes",
                                                "twitter:description": "Reference recently modified code as context for AI chat using the @Recent Changes symbol",
                                                "og:title": "Cursor – @Recent Changes",
                                                "og:description": "Reference recently modified code as context for AI chat using the @Recent Changes symbol",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-recent-changes.png?v=1743819392554",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-recent-changes.png?v=1743819392554",
                                                "href": "/context/@-symbols/@-recent-changes"
                                            },
                                            {
                                                "title": "@Cursor Rules",
                                                "description": "Reference and apply project-specific rules and guidelines using the @Cursor Rules symbol in chats and prompts",
                                                "twitter:title": "Cursor – @Cursor Rules",
                                                "twitter:description": "Reference and apply project-specific rules and guidelines using the @Cursor Rules symbol in chats and prompts",
                                                "og:title": "Cursor – @Cursor Rules",
                                                "og:description": "Reference and apply project-specific rules and guidelines using the @Cursor Rules symbol in chats and prompts",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-cursor-rules.png?v=1743819392763",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-cursor-rules.png?v=1743819392763",
                                                "href": "/context/@-symbols/@-cursor-rules"
                                            },
                                            {
                                                "title": "@Notepads",
                                                "description": "Reference and include notepad contexts in Cursor conversations for reusable development workflows",
                                                "twitter:title": "Cursor – @Notepads",
                                                "twitter:description": "Reference and include notepad contexts in Cursor conversations for reusable development workflows",
                                                "og:title": "Cursor – @Notepads",
                                                "og:description": "Reference and include notepad contexts in Cursor conversations for reusable development workflows",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-notepads.png?v=1743819393107",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-notepads.png?v=1743819393107",
                                                "href": "/context/@-symbols/@-notepads"
                                            },
                                            {
                                                "title": "@Past Chats",
                                                "description": "Include summarized chats from history",
                                                "twitter:title": "Cursor – @Past Chats",
                                                "twitter:description": "Include summarized chats from history",
                                                "og:title": "Cursor – @Past Chats",
                                                "og:description": "Include summarized chats from history",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-past-chats.png?v=1745459170287",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-past-chats.png?v=1745459170287",
                                                "href": "/context/@-symbols/@-past-chats"
                                            },
                                            {
                                                "title": "#Files",
                                                "description": "Select specific files using # prefix in Cursor's input fields, works alongside @ context controls for precision",
                                                "twitter:title": "Cursor – #Files",
                                                "twitter:description": "Select specific files using # prefix in Cursor's input fields, works alongside @ context controls for precision",
                                                "og:title": "Cursor – #Files",
                                                "og:description": "Select specific files using # prefix in Cursor's input fields, works alongside @ context controls for precision",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/pill-files.png?v=1743819392730",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/pill-files.png?v=1743819392730",
                                                "href": "/context/@-symbols/pill-files"
                                            },
                                            {
                                                "title": "/command",
                                                "description": "Use / commands to reference open editor tabs and add them as context for conversations with Cursor AI",
                                                "twitter:title": "Cursor – /command",
                                                "twitter:description": "Use / commands to reference open editor tabs and add them as context for conversations with Cursor AI",
                                                "og:title": "Cursor – /command",
                                                "og:description": "Use / commands to reference open editor tabs and add them as context for conversations with Cursor AI",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/slash-commands.png?v=1743819392696",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/slash-commands.png?v=1743819392696",
                                                "href": "/context/@-symbols/slash-commands"
                                            }
                                        ]
                                    },
                                    {
                                        "title": "Ignore Files",
                                        "description": "Control which files Cursor's AI features and indexing can access using .cursorignore and .cursorindexingignore",
                                        "twitter:title": "Cursor – Ignore Files",
                                        "twitter:description": "Control which files Cursor's AI features and indexing can access using .cursorignore and .cursorindexingignore",
                                        "og:title": "Cursor – Ignore Files",
                                        "og:description": "Control which files Cursor's AI features and indexing can access using .cursorignore and .cursorindexingignore",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/ignore-files.png?v=1743819391316",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/ignore-files.png?v=1743819391316",
                                        "href": "/context/ignore-files"
                                    },
                                    {
                                        "title": "Model Context Protocol",
                                        "description": "Connect external tools and data sources to Cursor using the Model Context Protocol (MCP) plugin system",
                                        "twitter:title": "Cursor – Model Context Protocol",
                                        "twitter:description": "Connect external tools and data sources to Cursor using the Model Context Protocol (MCP) plugin system",
                                        "og:title": "Cursor – Model Context Protocol",
                                        "og:description": "Connect external tools and data sources to Cursor using the Model Context Protocol (MCP) plugin system",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/model-context-protocol.png?v=*************",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/model-context-protocol.png?v=*************",
                                        "href": "/context/model-context-protocol"
                                    }
                                ]
                            },
                            {
                                "group": "Account",
                                "pages": [
                                    {
                                        "title": "Plans \u0026 Usage",
                                        "description": "Guide to Cursor usage tiers, request types, quotas, and billing options for premium models and team accounts",
                                        "twitter:title": "Cursor – Plans \u0026 Usage",
                                        "twitter:description": "Guide to Cursor usage tiers, request types, quotas, and billing options for premium models and team accounts",
                                        "og:title": "Cursor – Plans \u0026 Usage",
                                        "og:description": "Guide to Cursor usage tiers, request types, quotas, and billing options for premium models and team accounts",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/plans-and-usage.png?v=*************",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/plans-and-usage.png?v=*************",
                                        "href": "/account/plans-and-usage"
                                    },
                                    {
                                        "group": "Business",
                                        "pages": [
                                            {
                                                "title": "Get Started",
                                                "description": "Guide to create and manage Cursor business teams with SSO, enterprise features, and MDM deployment options",
                                                "twitter:title": "Cursor – Get Started",
                                                "twitter:description": "Guide to create and manage Cursor business teams with SSO, enterprise features, and MDM deployment options",
                                                "og:title": "Cursor – Get Started",
                                                "og:description": "Guide to create and manage Cursor business teams with SSO, enterprise features, and MDM deployment options",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/teams/setup.png?v=*************",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/teams/setup.png?v=*************",
                                                "href": "/account/teams/setup"
                                            },
                                            {
                                                "title": "Members + Roles",
                                                "description": "Learn about team roles, member management, SSO options, usage controls and billing for organizational teams",
                                                "twitter:title": "Cursor – Members + Roles",
                                                "twitter:description": "Learn about team roles, member management, SSO options, usage controls and billing for organizational teams",
                                                "og:title": "Cursor – Members + Roles",
                                                "og:description": "Learn about team roles, member management, SSO options, usage controls and billing for organizational teams",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/teams/members.png?v=*************",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/teams/members.png?v=*************",
                                                "href": "/account/teams/members"
                                            },
                                            {
                                                "title": "Analytics",
                                                "description": "Track team metrics including usage stats, per-user activity, and active user counts from the dashboard",
                                                "twitter:title": "Cursor – Analytics",
                                                "twitter:description": "Track team metrics including usage stats, per-user activity, and active user counts from the dashboard",
                                                "og:title": "Cursor – Analytics",
                                                "og:description": "Track team metrics including usage stats, per-user activity, and active user counts from the dashboard",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/teams/analytics.png?v=*************",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/teams/analytics.png?v=*************",
                                                "href": "/account/teams/analytics"
                                            },
                                            {
                                                "title": "SSO",
                                                "description": "Guide to setting up SAML 2.0 SSO authentication with your identity provider in Cursor for team accounts",
                                                "twitter:title": "Cursor – SSO",
                                                "twitter:description": "Guide to setting up SAML 2.0 SSO authentication with your identity provider in Cursor for team accounts",
                                                "og:title": "Cursor – SSO",
                                                "og:description": "Guide to setting up SAML 2.0 SSO authentication with your identity provider in Cursor for team accounts",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/teams/sso.png?v=*************",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/teams/sso.png?v=*************",
                                                "href": "/account/teams/sso"
                                            }
                                        ]
                                    },
                                    {
                                        "title": "Dashboard",
                                        "description": "Learn how to manage billing, usage pricing, and team settings in the dashboard for different plans",
                                        "twitter:title": "Cursor – Dashboard",
                                        "twitter:description": "Learn how to manage billing, usage pricing, and team settings in the dashboard for different plans",
                                        "og:title": "Cursor – Dashboard",
                                        "og:description": "Learn how to manage billing, usage pricing, and team settings in the dashboard for different plans",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/dashboard.png?v=*************",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/dashboard.png?v=*************",
                                        "href": "/account/dashboard"
                                    },
                                    {
                                        "title": "Billing",
                                        "description": "Complete guide to managing Cursor billing: subscriptions, refunds, cycles and access through Stripe portal",
                                        "twitter:title": "Cursor – Billing",
                                        "twitter:description": "Complete guide to managing Cursor billing: subscriptions, refunds, cycles and access through Stripe portal",
                                        "og:title": "Cursor – Billing",
                                        "og:description": "Complete guide to managing Cursor billing: subscriptions, refunds, cycles and access through Stripe portal",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/billing.png?v=*************",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/billing.png?v=*************",
                                        "href": "/account/billing"
                                    },
                                    {
                                        "title": "Pricing",
                                        "url": "https://cursor.com/pricing",
                                        "description": null,
                                        "twitter:title": "Cursor – Pricing",
                                        "twitter:description": "",
                                        "og:title": "Cursor – Pricing",
                                        "og:description": "",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/pricing.png?v=*************",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/pricing.png?v=*************",
                                        "href": "/account/pricing"
                                    },
                                    {
                                        "title": "Privacy \u0026 Security",
                                        "description": "Guide to Cursor's privacy settings, data handling, and code indexing with Privacy Mode option",
                                        "twitter:title": "Cursor – Privacy \u0026 Security",
                                        "twitter:description": "Guide to Cursor's privacy settings, data handling, and code indexing with Privacy Mode option",
                                        "og:title": "Cursor – Privacy \u0026 Security",
                                        "og:description": "Guide to Cursor's privacy settings, data handling, and code indexing with Privacy Mode option",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/privacy.png?v=*************",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/privacy.png?v=*************",
                                        "href": "/account/privacy"
                                    }
                                ]
                            },
                            {
                                "group": "Settings",
                                "pages": [
                                    {
                                        "title": "Models",
                                        "description": "Comprehensive guide to Cursor's models: features, pricing, context windows, and hosting details for Chat and CMD+K",
                                        "twitter:title": "Cursor – Models",
                                        "twitter:description": "Comprehensive guide to Cursor's models: features, pricing, context windows, and hosting details for Chat and CMD+K",
                                        "og:title": "Cursor – Models",
                                        "og:description": "Comprehensive guide to Cursor's models: features, pricing, context windows, and hosting details for Chat and CMD+K",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/settings/models.png?v=*************",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/settings/models.png?v=*************",
                                        "href": "/settings/models"
                                    },
                                    {
                                        "title": "Custom API Keys",
                                        "description": "Configure custom API keys for OpenAI, Anthropic, Google and Azure to use your own LLM provider accounts in Cursor",
                                        "twitter:title": "Cursor – Custom API Keys",
                                        "twitter:description": "Configure custom API keys for OpenAI, Anthropic, Google and Azure to use your own LLM provider accounts in Cursor",
                                        "og:title": "Cursor – Custom API Keys",
                                        "og:description": "Configure custom API keys for OpenAI, Anthropic, Google and Azure to use your own LLM provider accounts in Cursor",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/settings/api-keys.png?v=*************",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/settings/api-keys.png?v=*************",
                                        "href": "/settings/api-keys"
                                    },
                                    {
                                        "title": "Early Access Program",
                                        "description": "Learn how to join Cursor's Early Access Program to test experimental features and provide feedback",
                                        "twitter:title": "Cursor – Early Access Program",
                                        "twitter:description": "Learn how to join Cursor's Early Access Program to test experimental features and provide feedback",
                                        "og:title": "Cursor – Early Access Program",
                                        "og:description": "Learn how to join Cursor's Early Access Program to test experimental features and provide feedback",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/settings/beta.png?v=1743819391734",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/settings/beta.png?v=1743819391734",
                                        "href": "/settings/beta"
                                    }
                                ]
                            },
                            {
                                "group": "Troubleshooting",
                                "pages": [
                                    {
                                        "title": "Common Issues",
                                        "description": "Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs",
                                        "twitter:title": "Cursor – Common Issues",
                                        "twitter:description": "Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs",
                                        "og:title": "Cursor – Common Issues",
                                        "og:description": "Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/common-issues.png?v=1743819390998",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/common-issues.png?v=1743819390998",
                                        "href": "/troubleshooting/common-issues"
                                    },
                                    {
                                        "title": "Troubleshooting Guide",
                                        "description": "Guide to troubleshooting common Cursor issues, resetting app data, and submitting detailed bug reports",
                                        "twitter:title": "Cursor – Troubleshooting Guide",
                                        "twitter:description": "Guide to troubleshooting common Cursor issues, resetting app data, and submitting detailed bug reports",
                                        "og:title": "Cursor – Troubleshooting Guide",
                                        "og:description": "Guide to troubleshooting common Cursor issues, resetting app data, and submitting detailed bug reports",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/troubleshooting-guide.png?v=1743819390966",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/troubleshooting-guide.png?v=1743819390966",
                                        "href": "/troubleshooting/troubleshooting-guide"
                                    },
                                    {
                                        "title": "Getting a Request ID",
                                        "description": "Learn how to find and share request IDs in Cursor for better technical support and issue reporting",
                                        "twitter:title": "Cursor – Getting a Request ID",
                                        "twitter:description": "Learn how to find and share request IDs in Cursor for better technical support and issue reporting",
                                        "og:title": "Cursor – Getting a Request ID",
                                        "og:description": "Learn how to find and share request IDs in Cursor for better technical support and issue reporting",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/request-reporting.png?v=1743819390931",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/request-reporting.png?v=1743819390931",
                                        "href": "/troubleshooting/request-reporting"
                                    }
                                ]
                            }
                        ],
                        "docsNavWithMetadata": {
                            "global": null,
                            "tabs": [
                                {
                                    "tab": "Documentation",
                                    "icon": "book-open",
                                    "groups": [
                                        {
                                            "group": "Get Started",
                                            "pages": [
                                                {
                                                    "title": "Welcome to Cursor",
                                                    "description": "Get started with Cursor and learn core features: Tab, Agent, Command-K",
                                                    "twitter:title": "Cursor – Welcome to Cursor",
                                                    "twitter:description": "Get started with Cursor and learn core features: Tab, Agent, Command-K",
                                                    "og:title": "Cursor – Welcome to Cursor",
                                                    "og:description": "Get started with Cursor and learn core features: Tab, Agent, Command-K",
                                                    "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/welcome.png?v=1745803150052",
                                                    "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/welcome.png?v=1745803150052",
                                                    "href": "/welcome"
                                                },
                                                {
                                                    "title": "Installation",
                                                    "description": "Guide to installing Cursor, configuring initial settings, and migrating from other code editors",
                                                    "twitter:title": "Cursor – Installation",
                                                    "twitter:description": "Guide to installing Cursor, configuring initial settings, and migrating from other code editors",
                                                    "og:title": "Cursor – Installation",
                                                    "og:description": "Guide to installing Cursor, configuring initial settings, and migrating from other code editors",
                                                    "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/get-started/installation.png?v=1743819390538",
                                                    "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/get-started/installation.png?v=1743819390538",
                                                    "href": "/get-started/installation"
                                                },
                                                {
                                                    "title": "FAQ",
                                                    "description": "Common questions about language support, models, project limits, and data management in Cursor with troubleshooting help",
                                                    "twitter:title": "Cursor – FAQ",
                                                    "twitter:description": "Common questions about language support, models, project limits, and data management in Cursor with troubleshooting help",
                                                    "og:title": "Cursor – FAQ",
                                                    "og:description": "Common questions about language support, models, project limits, and data management in Cursor with troubleshooting help",
                                                    "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/faq.png?v=1743819390494",
                                                    "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/faq.png?v=1743819390494",
                                                    "href": "/faq"
                                                }
                                            ]
                                        },
                                        {
                                            "group": "Editor",
                                            "pages": [
                                                {
                                                    "group": "Tab",
                                                    "pages": [
                                                        {
                                                            "title": "Overview",
                                                            "description": "Learn how Tab uses AI to suggest multi-line edits, code fixes, and context-aware completions directly in the editor",
                                                            "twitter:title": "Cursor – Overview",
                                                            "twitter:description": "Learn how Tab uses AI to suggest multi-line edits, code fixes, and context-aware completions directly in the editor",
                                                            "og:title": "Cursor – Overview",
                                                            "og:description": "Learn how Tab uses AI to suggest multi-line edits, code fixes, and context-aware completions directly in the editor",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/tab/overview.png?v=1743819391097",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/tab/overview.png?v=1743819391097",
                                                            "href": "/tab/overview"
                                                        },
                                                        {
                                                            "title": "Tab vs GitHub Copilot",
                                                            "description": "Compares Cursor's multi-line edits and instruction-based completions to GitHub Copilot's single-line insertions",
                                                            "twitter:title": "Cursor – Tab vs GitHub Copilot",
                                                            "twitter:description": "Compares Cursor's multi-line edits and instruction-based completions to GitHub Copilot's single-line insertions",
                                                            "og:title": "Cursor – Tab vs GitHub Copilot",
                                                            "og:description": "Compares Cursor's multi-line edits and instruction-based completions to GitHub Copilot's single-line insertions",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/tab/from-gh-copilot.png?v=1743819391065",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/tab/from-gh-copilot.png?v=1743819391065",
                                                            "href": "/tab/from-gh-copilot"
                                                        },
                                                        {
                                                            "title": "Auto-import",
                                                            "description": "Learn how Cursor's Tab feature automates module imports in TypeScript and Python projects while you code",
                                                            "twitter:title": "Cursor – Auto-import",
                                                            "twitter:description": "Learn how Cursor's Tab feature automates module imports in TypeScript and Python projects while you code",
                                                            "og:title": "Cursor – Auto-import",
                                                            "og:description": "Learn how Cursor's Tab feature automates module imports in TypeScript and Python projects while you code",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/tab/auto-import.png?v=1743819391136",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/tab/auto-import.png?v=1743819391136",
                                                            "href": "/tab/auto-import"
                                                        },
                                                        {
                                                            "title": "Advanced Features",
                                                            "description": "Learn to navigate code efficiently using Tab in peek views, prediction, and partial accepts",
                                                            "twitter:title": "Cursor – Advanced Features",
                                                            "twitter:description": "Learn to navigate code efficiently using Tab in peek views, prediction, and partial accepts",
                                                            "og:title": "Cursor – Advanced Features",
                                                            "og:description": "Learn to navigate code efficiently using Tab in peek views, prediction, and partial accepts",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/tab/advanced-features.png?v=1743819391032",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/tab/advanced-features.png?v=1743819391032",
                                                            "href": "/tab/advanced-features"
                                                        }
                                                    ]
                                                },
                                                {
                                                    "group": "Chat",
                                                    "pages": [
                                                        {
                                                            "title": "Overview",
                                                            "description": "Natural language interface for exploring, editing, and managing code with contextual AI assistance in Chat mode",
                                                            "twitter:title": "Cursor – Overview",
                                                            "twitter:description": "Natural language interface for exploring, editing, and managing code with contextual AI assistance in Chat mode",
                                                            "og:title": "Cursor – Overview",
                                                            "og:description": "Natural language interface for exploring, editing, and managing code with contextual AI assistance in Chat mode",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/overview.png?v=1746654491247",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/overview.png?v=1746654491247",
                                                            "href": "/chat/overview"
                                                        },
                                                        {
                                                            "title": "Agent Mode",
                                                            "description": "Autonomous AI coding agent that independently explores, plans, and executes complex codebase changes with full tools",
                                                            "twitter:title": "Cursor – Agent Mode",
                                                            "twitter:description": "Autonomous AI coding agent that independently explores, plans, and executes complex codebase changes with full tools",
                                                            "og:title": "Cursor – Agent Mode",
                                                            "og:description": "Autonomous AI coding agent that independently explores, plans, and executes complex codebase changes with full tools",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/agent.png?v=1746654490611",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/agent.png?v=1746654490611",
                                                            "href": "/chat/agent"
                                                        },
                                                        {
                                                            "title": "Ask mode",
                                                            "description": "Ask mode lets you explore and learn about codebases through AI search and queries without making changes",
                                                            "twitter:title": "Cursor – Ask mode",
                                                            "twitter:description": "Ask mode lets you explore and learn about codebases through AI search and queries without making changes",
                                                            "og:title": "Cursor – Ask mode",
                                                            "og:description": "Ask mode lets you explore and learn about codebases through AI search and queries without making changes",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/ask.png?v=1743819391493",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/ask.png?v=1743819391493",
                                                            "href": "/chat/ask"
                                                        },
                                                        {
                                                            "title": "Manual Mode",
                                                            "description": "Make precise code changes with explicit file targeting - a focused editing mode with user-controlled tooling",
                                                            "twitter:title": "Cursor – Manual Mode",
                                                            "twitter:description": "Make precise code changes with explicit file targeting - a focused editing mode with user-controlled tooling",
                                                            "og:title": "Cursor – Manual Mode",
                                                            "og:description": "Make precise code changes with explicit file targeting - a focused editing mode with user-controlled tooling",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/manual.png?v=1743819391427",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/manual.png?v=1743819391427",
                                                            "href": "/chat/manual"
                                                        },
                                                        {
                                                            "title": "Custom Modes",
                                                            "description": "Create custom Cursor modes with tailored tools and prompts to personalize AI assistance for specific workflows",
                                                            "twitter:title": "Cursor – Custom Modes",
                                                            "twitter:description": "Create custom Cursor modes with tailored tools and prompts to personalize AI assistance for specific workflows",
                                                            "og:title": "Cursor – Custom Modes",
                                                            "og:description": "Create custom Cursor modes with tailored tools and prompts to personalize AI assistance for specific workflows",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/custom-modes.png?v=1746654490925",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/custom-modes.png?v=1746654490925",
                                                            "href": "/chat/custom-modes"
                                                        },
                                                        {
                                                            "title": "Tools",
                                                            "description": "A guide to all available tools in Cursor's Chat modes for searching, editing, and interacting with your codebase",
                                                            "twitter:title": "Cursor – Tools",
                                                            "twitter:description": "A guide to all available tools in Cursor's Chat modes for searching, editing, and interacting with your codebase",
                                                            "og:title": "Cursor – Tools",
                                                            "og:description": "A guide to all available tools in Cursor's Chat modes for searching, editing, and interacting with your codebase",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/tools.png?v=1743819391618",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/tools.png?v=1743819391618",
                                                            "href": "/chat/tools"
                                                        },
                                                        {
                                                            "title": "Apply",
                                                            "description": "Learn how to apply, accept, or reject code suggestions from chat using Cursor's Apply feature",
                                                            "twitter:title": "Cursor – Apply",
                                                            "twitter:description": "Learn how to apply, accept, or reject code suggestions from chat using Cursor's Apply feature",
                                                            "og:title": "Cursor – Apply",
                                                            "og:description": "Learn how to apply, accept, or reject code suggestions from chat using Cursor's Apply feature",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/apply.png?v=1743819391557",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/chat/apply.png?v=1743819391557",
                                                            "href": "/chat/apply"
                                                        }
                                                    ]
                                                },
                                                {
                                                    "group": "Inline Edit (⌘K)",
                                                    "pages": [
                                                        {
                                                            "title": "Overview",
                                                            "description": "Learn how to use Inline Edit (Cmd/Ctrl+K) in Cursor to generate, edit code and ask questions with the Prompt Bar",
                                                            "twitter:title": "Cursor – Overview",
                                                            "twitter:description": "Learn how to use Inline Edit (Cmd/Ctrl+K) in Cursor to generate, edit code and ask questions with the Prompt Bar",
                                                            "og:title": "Cursor – Overview",
                                                            "og:description": "Learn how to use Inline Edit (Cmd/Ctrl+K) in Cursor to generate, edit code and ask questions with the Prompt Bar",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/cmdk/overview.png?v=1746654505155",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/cmdk/overview.png?v=1746654505155",
                                                            "href": "/cmdk/overview"
                                                        },
                                                        {
                                                            "title": "Terminal Cmd K",
                                                            "description": "Use Cmd K in Cursor terminal to generate and run commands through a prompt bar interface",
                                                            "twitter:title": "Cursor – Terminal Cmd K",
                                                            "twitter:description": "Use Cmd K in Cursor terminal to generate and run commands through a prompt bar interface",
                                                            "og:title": "Cursor – Terminal Cmd K",
                                                            "og:description": "Use Cmd K in Cursor terminal to generate and run commands through a prompt bar interface",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/cmdk/terminal-cmdk.png?v=1743819390658",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/cmdk/terminal-cmdk.png?v=1743819390658",
                                                            "href": "/cmdk/terminal-cmdk"
                                                        }
                                                    ]
                                                },
                                                {
                                                    "title": "Models \u0026 Pricing",
                                                    "description": "Available models in Cursor and their pricing",
                                                    "twitter:title": "Cursor – Models \u0026 Pricing",
                                                    "twitter:description": "Available models in Cursor and their pricing",
                                                    "og:title": "Cursor – Models \u0026 Pricing",
                                                    "og:description": "Available models in Cursor and their pricing",
                                                    "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/models.png?v=1746654486098",
                                                    "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/models.png?v=1746654486098",
                                                    "href": "/models"
                                                },
                                                {
                                                    "title": "Keyboard Shortcuts",
                                                    "description": "Complete reference for all keyboard shortcuts in Cursor, including Chat, Tab, Terminal and code selection commands",
                                                    "twitter:title": "Cursor – Keyboard Shortcuts",
                                                    "twitter:description": "Complete reference for all keyboard shortcuts in Cursor, including Chat, Tab, Terminal and code selection commands",
                                                    "og:title": "Cursor – Keyboard Shortcuts",
                                                    "og:description": "Complete reference for all keyboard shortcuts in Cursor, including Chat, Tab, Terminal and code selection commands",
                                                    "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/kbd.png?v=1743819390445",
                                                    "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/kbd.png?v=1743819390445",
                                                    "href": "/kbd"
                                                },
                                                {
                                                    "group": "Features",
                                                    "pages": [
                                                        {
                                                            "title": "AI Commit Message",
                                                            "description": "Learn how to generate contextual Git commit messages automatically using Cursor's sparkle icon or shortcuts",
                                                            "twitter:title": "Cursor – AI Commit Message",
                                                            "twitter:description": "Learn how to generate contextual Git commit messages automatically using Cursor's sparkle icon or shortcuts",
                                                            "og:title": "Cursor – AI Commit Message",
                                                            "og:description": "Learn how to generate contextual Git commit messages automatically using Cursor's sparkle icon or shortcuts",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/more/ai-commit-message.png?v=1743819391656",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/more/ai-commit-message.png?v=1743819391656",
                                                            "href": "/more/ai-commit-message"
                                                        },
                                                        {
                                                            "title": "Notepads (Beta)",
                                                            "description": "A guide to using Notepads in Cursor for sharing context between Composers and Chat interactions",
                                                            "twitter:title": "Cursor – Notepads (Beta)",
                                                            "twitter:description": "A guide to using Notepads in Cursor for sharing context between Composers and Chat interactions",
                                                            "og:title": "Cursor – Notepads (Beta)",
                                                            "og:description": "A guide to using Notepads in Cursor for sharing context between Composers and Chat interactions",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/beta/notepads.png?v=1743819391698",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/beta/notepads.png?v=1743819391698",
                                                            "href": "/beta/notepads"
                                                        },
                                                        {
                                                            "title": "Background Agents (Preview)",
                                                            "description": "How to use background agents to parallelize your work.",
                                                            "twitter:title": "Cursor – Background Agents",
                                                            "twitter:description": "How to use background agents toi parallelize your work.",
                                                            "og:title": "Cursor – Background Agents",
                                                            "og:description": "How to use background agents toi parallelize your work.",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/background-agent.png?v=1747020336832",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/background-agent.png?v=1747020336832",
                                                            "href": "/background-agent"
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            "group": "Context",
                                            "pages": [
                                                {
                                                    "title": "Codebase Indexing",
                                                    "description": "Learn how to index your codebase in Cursor for more accurate AI assistance and search results",
                                                    "twitter:title": "Cursor – Codebase Indexing",
                                                    "twitter:description": "Learn how to index your codebase in Cursor for more accurate AI assistance and search results",
                                                    "og:title": "Cursor – Codebase Indexing",
                                                    "og:description": "Learn how to index your codebase in Cursor for more accurate AI assistance and search results",
                                                    "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/codebase-indexing.png?v=1746654499296",
                                                    "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/codebase-indexing.png?v=1746654499296",
                                                    "href": "/context/codebase-indexing"
                                                },
                                                {
                                                    "title": "Rules",
                                                    "description": "Control how the Agent model behaves with reusable, scoped instructions.",
                                                    "twitter:title": "Cursor – Rules",
                                                    "twitter:description": "Control how the Agent model behaves with reusable, scoped instructions.",
                                                    "og:title": "Cursor – Rules",
                                                    "og:description": "Control how the Agent model behaves with reusable, scoped instructions.",
                                                    "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/rules.png?v=1746654500529",
                                                    "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/rules.png?v=1746654500529",
                                                    "href": "/context/rules"
                                                },
                                                {
                                                    "title": "Managing Context",
                                                    "description": "Use the context menu to quickly access and manage your codebase",
                                                    "og:title": "Cursor – Managing Context",
                                                    "og:description": "Use the context menu to quickly access and manage your codebase",
                                                    "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/management.png?v=1746654499603",
                                                    "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/management.png?v=1746654499603",
                                                    "twitter:title": "Cursor – Managing Context",
                                                    "twitter:description": "Use the context menu to quickly access and manage your codebase",
                                                    "href": "/context/management"
                                                },
                                                {
                                                    "group": "@ Symbols",
                                                    "pages": [
                                                        {
                                                            "title": "Overview",
                                                            "description": "Guide to using @ symbols in Cursor for referencing code, files, documentation and other context in chats",
                                                            "twitter:title": "Cursor – Overview",
                                                            "twitter:description": "Guide to using @ symbols in Cursor for referencing code, files, documentation and other context in chats",
                                                            "og:title": "Cursor – Overview",
                                                            "og:description": "Guide to using @ symbols in Cursor for referencing code, files, documentation and other context in chats",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/overview.png?v=1743819393138",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/overview.png?v=1743819393138",
                                                            "href": "/context/@-symbols/overview"
                                                        },
                                                        {
                                                            "title": "@Files",
                                                            "description": "Learn how to reference files using @ in Cursor's Chat and Cmd K, with preview and chunking features",
                                                            "twitter:title": "Cursor – @Files",
                                                            "twitter:description": "Learn how to reference files using @ in Cursor's Chat and Cmd K, with preview and chunking features",
                                                            "og:title": "Cursor – @Files",
                                                            "og:description": "Learn how to reference files using @ in Cursor's Chat and Cmd K, with preview and chunking features",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-files.png?v=1743819392865",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-files.png?v=1743819392865",
                                                            "href": "/context/@-symbols/@-files"
                                                        },
                                                        {
                                                            "title": "@Folders",
                                                            "description": "Reference folders as context in Chat \u0026 Composer for enhanced AI conversations",
                                                            "twitter:title": "Cursor – @Folders",
                                                            "twitter:description": "Reference folders as context in Chat \u0026 Composer for enhanced AI conversations",
                                                            "og:title": "Cursor – @Folders",
                                                            "og:description": "Reference folders as context in Chat \u0026 Composer for enhanced AI conversations",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-folders.png?v=1746654498987",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-folders.png?v=1746654498987",
                                                            "href": "/context/@-symbols/@-folders"
                                                        },
                                                        {
                                                            "title": "@Code",
                                                            "description": "Learn how to reference code snippets in Cursor using @Code symbol and keyboard shortcuts for adding to Chat",
                                                            "twitter:title": "Cursor – @Code",
                                                            "twitter:description": "Learn how to reference code snippets in Cursor using @Code symbol and keyboard shortcuts for adding to Chat",
                                                            "og:title": "Cursor – @Code",
                                                            "og:description": "Learn how to reference code snippets in Cursor using @Code symbol and keyboard shortcuts for adding to Chat",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-code.png?v=1743819393044",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-code.png?v=1743819393044",
                                                            "href": "/context/@-symbols/@-code"
                                                        },
                                                        {
                                                            "title": "@Docs",
                                                            "description": "Learn how to use, add, and manage custom documentation as context in Cursor using @Docs",
                                                            "twitter:title": "Cursor – @Docs",
                                                            "twitter:description": "Learn how to use, add, and manage custom documentation as context in Cursor using @Docs",
                                                            "og:title": "Cursor – @Docs",
                                                            "og:description": "Learn how to use, add, and manage custom documentation as context in Cursor using @Docs",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-docs.png?v=1743819392585",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-docs.png?v=1743819392585",
                                                            "href": "/context/@-symbols/@-docs"
                                                        },
                                                        {
                                                            "title": "@Git",
                                                            "description": "Use Git-related symbols to analyze working changes, compare branches, and review uncommitted files",
                                                            "twitter:title": "Cursor – @Git",
                                                            "twitter:description": "Use Git-related symbols to analyze working changes, compare branches, and review uncommitted files",
                                                            "og:title": "Cursor – @Git",
                                                            "og:description": "Use Git-related symbols to analyze working changes, compare branches, and review uncommitted files",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-git.png?v=1743819392615",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-git.png?v=1743819392615",
                                                            "href": "/context/@-symbols/@-git"
                                                        },
                                                        {
                                                            "title": "@Web",
                                                            "description": "Enables web search to dynamically add recent online information and documentation as context in Cursor commands",
                                                            "twitter:title": "Cursor – @Web",
                                                            "twitter:description": "Enables web search to dynamically add recent online information and documentation as context in Cursor commands",
                                                            "og:title": "Cursor – @Web",
                                                            "og:description": "Enables web search to dynamically add recent online information and documentation as context in Cursor commands",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-web.png?v=1743819393014",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-web.png?v=1743819393014",
                                                            "href": "/context/@-symbols/@-web"
                                                        },
                                                        {
                                                            "title": "@Definitions",
                                                            "description": "Add nearby code definitions to Cmd K context using the @Definitions symbol",
                                                            "twitter:title": "Cursor – @Definitions",
                                                            "twitter:description": "Add nearby code definitions to Cmd K context using the @Definitions symbol",
                                                            "og:title": "Cursor – @Definitions",
                                                            "og:description": "Add nearby code definitions to Cmd K context using the @Definitions symbol",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-definitions.png?v=1743819392909",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-definitions.png?v=1743819392909",
                                                            "href": "/context/@-symbols/@-definitions"
                                                        },
                                                        {
                                                            "title": "@Link",
                                                            "description": "Learn how to include and manage web links as context in Cursor's AI features by pasting URLs",
                                                            "twitter:title": "Cursor – @Link",
                                                            "twitter:description": "Learn how to include and manage web links as context in Cursor's AI features by pasting URLs",
                                                            "og:title": "Cursor – @Link",
                                                            "og:description": "Learn how to include and manage web links as context in Cursor's AI features by pasting URLs",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-link.png?v=1743819392982",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-link.png?v=1743819392982",
                                                            "href": "/context/@-symbols/@-link"
                                                        },
                                                        {
                                                            "title": "@Lint Errors",
                                                            "description": "Access and reference linting errors in your codebase",
                                                            "twitter:title": "Cursor – @Lint Errors",
                                                            "twitter:description": "Access and reference linting errors in your codebase",
                                                            "og:title": "Cursor – @Lint Errors",
                                                            "og:description": "Access and reference linting errors in your codebase",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-lint-errors.png?v=1743819392949",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-lint-errors.png?v=1743819392949",
                                                            "href": "/context/@-symbols/@-lint-errors"
                                                        },
                                                        {
                                                            "title": "@Recent Changes",
                                                            "description": "Reference recently modified code as context for AI chat using the @Recent Changes symbol",
                                                            "twitter:title": "Cursor – @Recent Changes",
                                                            "twitter:description": "Reference recently modified code as context for AI chat using the @Recent Changes symbol",
                                                            "og:title": "Cursor – @Recent Changes",
                                                            "og:description": "Reference recently modified code as context for AI chat using the @Recent Changes symbol",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-recent-changes.png?v=1743819392554",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-recent-changes.png?v=1743819392554",
                                                            "href": "/context/@-symbols/@-recent-changes"
                                                        },
                                                        {
                                                            "title": "@Cursor Rules",
                                                            "description": "Reference and apply project-specific rules and guidelines using the @Cursor Rules symbol in chats and prompts",
                                                            "twitter:title": "Cursor – @Cursor Rules",
                                                            "twitter:description": "Reference and apply project-specific rules and guidelines using the @Cursor Rules symbol in chats and prompts",
                                                            "og:title": "Cursor – @Cursor Rules",
                                                            "og:description": "Reference and apply project-specific rules and guidelines using the @Cursor Rules symbol in chats and prompts",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-cursor-rules.png?v=1743819392763",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-cursor-rules.png?v=1743819392763",
                                                            "href": "/context/@-symbols/@-cursor-rules"
                                                        },
                                                        {
                                                            "title": "@Notepads",
                                                            "description": "Reference and include notepad contexts in Cursor conversations for reusable development workflows",
                                                            "twitter:title": "Cursor – @Notepads",
                                                            "twitter:description": "Reference and include notepad contexts in Cursor conversations for reusable development workflows",
                                                            "og:title": "Cursor – @Notepads",
                                                            "og:description": "Reference and include notepad contexts in Cursor conversations for reusable development workflows",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-notepads.png?v=1743819393107",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-notepads.png?v=1743819393107",
                                                            "href": "/context/@-symbols/@-notepads"
                                                        },
                                                        {
                                                            "title": "@Past Chats",
                                                            "description": "Include summarized chats from history",
                                                            "twitter:title": "Cursor – @Past Chats",
                                                            "twitter:description": "Include summarized chats from history",
                                                            "og:title": "Cursor – @Past Chats",
                                                            "og:description": "Include summarized chats from history",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-past-chats.png?v=1745459170287",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/@-past-chats.png?v=1745459170287",
                                                            "href": "/context/@-symbols/@-past-chats"
                                                        },
                                                        {
                                                            "title": "#Files",
                                                            "description": "Select specific files using # prefix in Cursor's input fields, works alongside @ context controls for precision",
                                                            "twitter:title": "Cursor – #Files",
                                                            "twitter:description": "Select specific files using # prefix in Cursor's input fields, works alongside @ context controls for precision",
                                                            "og:title": "Cursor – #Files",
                                                            "og:description": "Select specific files using # prefix in Cursor's input fields, works alongside @ context controls for precision",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/pill-files.png?v=1743819392730",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/pill-files.png?v=1743819392730",
                                                            "href": "/context/@-symbols/pill-files"
                                                        },
                                                        {
                                                            "title": "/command",
                                                            "description": "Use / commands to reference open editor tabs and add them as context for conversations with Cursor AI",
                                                            "twitter:title": "Cursor – /command",
                                                            "twitter:description": "Use / commands to reference open editor tabs and add them as context for conversations with Cursor AI",
                                                            "og:title": "Cursor – /command",
                                                            "og:description": "Use / commands to reference open editor tabs and add them as context for conversations with Cursor AI",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/slash-commands.png?v=1743819392696",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/@-symbols/slash-commands.png?v=1743819392696",
                                                            "href": "/context/@-symbols/slash-commands"
                                                        }
                                                    ]
                                                },
                                                {
                                                    "title": "Ignore Files",
                                                    "description": "Control which files Cursor's AI features and indexing can access using .cursorignore and .cursorindexingignore",
                                                    "twitter:title": "Cursor – Ignore Files",
                                                    "twitter:description": "Control which files Cursor's AI features and indexing can access using .cursorignore and .cursorindexingignore",
                                                    "og:title": "Cursor – Ignore Files",
                                                    "og:description": "Control which files Cursor's AI features and indexing can access using .cursorignore and .cursorindexingignore",
                                                    "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/ignore-files.png?v=1743819391316",
                                                    "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/ignore-files.png?v=1743819391316",
                                                    "href": "/context/ignore-files"
                                                },
                                                {
                                                    "title": "Model Context Protocol",
                                                    "description": "Connect external tools and data sources to Cursor using the Model Context Protocol (MCP) plugin system",
                                                    "keywords": [
                                                        "MCP"
                                                    ],
                                                    "twitter:title": "Cursor – Model Context Protocol",
                                                    "twitter:description": "Connect external tools and data sources to Cursor using the Model Context Protocol (MCP) plugin system",
                                                    "og:title": "Cursor – Model Context Protocol",
                                                    "og:description": "Connect external tools and data sources to Cursor using the Model Context Protocol (MCP) plugin system",
                                                    "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/model-context-protocol.png?v=1746654500231",
                                                    "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/model-context-protocol.png?v=1746654500231",
                                                    "href": "/context/model-context-protocol"
                                                },
                                                {
                                                    "title": "Max Mode",
                                                    "description": "Experience enhanced AI capabilities with Max Mode in Cursor",
                                                    "og:title": "Cursor – Max Mode",
                                                    "og:description": "Experience enhanced AI capabilities with Max Mode in Cursor",
                                                    "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/max-mode.png?v=1746654499914",
                                                    "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/context/max-mode.png?v=1746654499914",
                                                    "twitter:title": "Cursor – Max Mode",
                                                    "twitter:description": "Experience enhanced AI capabilities with Max Mode in Cursor",
                                                    "href": "/context/max-mode"
                                                }
                                            ]
                                        },
                                        {
                                            "group": "Account",
                                            "pages": [
                                                {
                                                    "title": "Plans \u0026 Usage",
                                                    "description": "Guide to Cursor usage tiers, request types, quotas, and billing options for models and team accounts",
                                                    "twitter:title": "Cursor – Plans \u0026 Usage",
                                                    "twitter:description": "Guide to Cursor usage tiers, request types, quotas, and billing options for models and team accounts",
                                                    "og:title": "Cursor – Plans \u0026 Usage",
                                                    "og:description": "Guide to Cursor usage tiers, request types, quotas, and billing options for models and team accounts",
                                                    "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/plans-and-usage.png?v=*************",
                                                    "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/plans-and-usage.png?v=*************",
                                                    "href": "/account/plans-and-usage"
                                                },
                                                {
                                                    "group": "Business",
                                                    "pages": [
                                                        {
                                                            "title": "Get Started",
                                                            "description": "Guide to create and manage Cursor business teams with SSO, enterprise features, and MDM deployment options",
                                                            "twitter:title": "Cursor – Get Started",
                                                            "twitter:description": "Guide to create and manage Cursor business teams with SSO, enterprise features, and MDM deployment options",
                                                            "og:title": "Cursor – Get Started",
                                                            "og:description": "Guide to create and manage Cursor business teams with SSO, enterprise features, and MDM deployment options",
                                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/teams/setup.png?v=*************",
                                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/teams/setup.png?v=*************",
                                                            "href": "/account/teams/setup"},
                                                            {
                                                                "title": "Members + Roles",
                                                                "description": "Learn about team roles, member management, SSO options, usage controls and billing for organizational teams",
                                                                "twitter:title": "Cursor – Members + Roles",
                                                                "twitter:description": "Learn about team roles, member management, SSO options, usage controls and billing for organizational teams",
                                                                "og:title": "Cursor – Members + Roles",
                                                                "og:description": "Learn about team roles, member management, SSO options, usage controls and billing for organizational teams",
                                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/teams/members.png?v=*************",
                                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/teams/members.png?v=*************",
                                                                "href": "/account/teams/members"
                                                            },
                                                            {
                                                                "title": "Analytics",
                                                                "description": "Track team metrics including usage stats, per-user activity, and active user counts from the dashboard",
                                                                "twitter:title": "Cursor – Analytics",
                                                                "twitter:description": "Track team metrics including usage stats, per-user activity, and active user counts from the dashboard",
                                                                "og:title": "Cursor – Analytics",
                                                                "og:description": "Track team metrics including usage stats, per-user activity, and active user counts from the dashboard",
                                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/teams/analytics.png?v=*************",
                                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/teams/analytics.png?v=*************",
                                                                "href": "/account/teams/analytics"
                                                            },
                                                            {
                                                                "title": "SSO",
                                                                "description": "Guide to setting up SAML 2.0 SSO authentication with your identity provider in Cursor for team accounts",
                                                                "twitter:title": "Cursor – SSO",
                                                                "twitter:description": "Guide to setting up SAML 2.0 SSO authentication with your identity provider in Cursor for team accounts",
                                                                "og:title": "Cursor – SSO",
                                                                "og:description": "Guide to setting up SAML 2.0 SSO authentication with your identity provider in Cursor for team accounts",
                                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/teams/sso.png?v=*************",
                                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/teams/sso.png?v=*************",
                                                                "href": "/account/teams/sso"
                                                            }
                                                        ]
                                                    },
                                                    {
                                                        "title": "Dashboard",
                                                        "description": "Learn how to manage billing, usage pricing, and team settings in the dashboard for different plans",
                                                        "twitter:title": "Cursor – Dashboard",
                                                        "twitter:description": "Learn how to manage billing, usage pricing, and team settings in the dashboard for different plans",
                                                        "og:title": "Cursor – Dashboard",
                                                        "og:description": "Learn how to manage billing, usage pricing, and team settings in the dashboard for different plans",
                                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/dashboard.png?v=*************",
                                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/dashboard.png?v=*************",
                                                        "href": "/account/dashboard"
                                                    },
                                                    {
                                                        "title": "Billing",
                                                        "description": "Complete guide to managing Cursor billing: subscriptions, refunds, cycles and access through Stripe portal",
                                                        "twitter:title": "Cursor – Billing",
                                                        "twitter:description": "Complete guide to managing Cursor billing: subscriptions, refunds, cycles and access through Stripe portal",
                                                        "og:title": "Cursor – Billing",
                                                        "og:description": "Complete guide to managing Cursor billing: subscriptions, refunds, cycles and access through Stripe portal",
                                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/billing.png?v=*************",
                                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/billing.png?v=*************",
                                                        "href": "/account/billing"
                                                    },
                                                    {
                                                        "title": "Pricing",
                                                        "url": "https://cursor.com/pricing",
                                                        "description": null,
                                                        "twitter:title": "Cursor – Pricing",
                                                        "twitter:description": "",
                                                        "og:title": "Cursor – Pricing",
                                                        "og:description": "",
                                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/pricing.png?v=*************",
                                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/pricing.png?v=*************",
                                                        "href": "/account/pricing"
                                                    },
                                                    {
                                                        "title": "Privacy \u0026 Security",
                                                        "description": "Guide to Cursor's privacy settings, data handling, and code indexing with Privacy Mode option",
                                                        "twitter:title": "Cursor – Privacy \u0026 Security",
                                                        "twitter:description": "Guide to Cursor's privacy settings, data handling, and code indexing with Privacy Mode option",
                                                        "og:title": "Cursor – Privacy \u0026 Security",
                                                        "og:description": "Guide to Cursor's privacy settings, data handling, and code indexing with Privacy Mode option",
                                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/privacy.png?v=*************",
                                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/account/privacy.png?v=*************",
                                                        "href": "/account/privacy"
                                                    }
                                                ]
                                            },
                                            {
                                                "group": "Settings",
                                                "pages": [
                                                    {
                                                        "title": "Custom API Keys",
                                                        "description": "Configure custom API keys for OpenAI, Anthropic, Google and Azure to use your own LLM provider accounts in Cursor",
                                                        "twitter:title": "Cursor – Custom API Keys",
                                                        "twitter:description": "Configure custom API keys for OpenAI, Anthropic, Google and Azure to use your own LLM provider accounts in Cursor",
                                                        "og:title": "Cursor – Custom API Keys",
                                                        "og:description": "Configure custom API keys for OpenAI, Anthropic, Google and Azure to use your own LLM provider accounts in Cursor",
                                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/settings/api-keys.png?v=*************",
                                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/settings/api-keys.png?v=*************",
                                                        "href": "/settings/api-keys"
                                                    },
                                                    {"title": "Early Access Program",
                                                    "description": "Learn how to join Cursor's Early Access Program to test experimental features and provide feedback",
                                                    "twitter:title": "Cursor – Early Access Program",
                                                    "twitter:description": "Learn how to join Cursor's Early Access Program to test experimental features and provide feedback",
                                                    "og:title": "Cursor – Early Access Program",
                                                    "og:description": "Learn how to join Cursor's Early Access Program to test experimental features and provide feedback",
                                                    "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/settings/beta.png?v=1743819391734",
                                                    "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/settings/beta.png?v=1743819391734",
                                                    "href": "/settings/beta"
                                                }
                                            ]
                                        },
                                        {
                                            "group": "Troubleshooting",
                                            "pages": [
                                                {
                                                    "title": "Common Issues",
                                                    "description": "Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs",
                                                    "twitter:title": "Cursor – Common Issues",
                                                    "twitter:description": "Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs",
                                                    "og:title": "Cursor – Common Issues",
                                                    "og:description": "Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs",
                                                    "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/common-issues.png?v=1743819390998",
                                                    "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/common-issues.png?v=1743819390998",
                                                    "href": "/troubleshooting/common-issues"
                                                },
                                                {"title": "Troubleshooting Guide",
                                                "description": "Guide to troubleshooting common Cursor issues, resetting app data, and submitting detailed bug reports",
                                                "twitter:title": "Cursor – Troubleshooting Guide",
                                                "twitter:description": "Guide to troubleshooting common Cursor issues, resetting app data, and submitting detailed bug reports",
                                                "og:title": "Cursor – Troubleshooting Guide",
                                                "og:description": "Guide to troubleshooting common Cursor issues, resetting app data, and submitting detailed bug reports",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/troubleshooting-guide.png?v=1743819390966",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/troubleshooting-guide.png?v=1743819390966",
                                                "href": "/troubleshooting/troubleshooting-guide"
                                            },
                                            {
                                                "title": "Getting a Request ID",
                                                "description": "Learn how to find and share request IDs in Cursor for better technical support and issue reporting",
                                                "twitter:title": "Cursor – Getting a Request ID",
                                                "twitter:description": "Learn how to find and share request IDs in Cursor for better technical support and issue reporting",
                                                "og:title": "Cursor – Getting a Request ID",
                                                "og:description": "Learn how to find and share request IDs in Cursor for better technical support and issue reporting",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/request-reporting.png?v=1743819390931",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/request-reporting.png?v=1743819390931",
                                                "href": "/troubleshooting/request-reporting"
                                            }
                                        ]
                                    }
                                ],
                                "global": {
                                    "anchors": [
                                        {
                                            "anchor": "Website",
                                            "icon": {
                                                "style": "duotone",
                                                "name": "globe"
                                            },
                                            "href": "https://cursor.com/"
                                        },
                                        {
                                            "anchor": "Forum",
                                            "icon": {
                                                "style": "duotone",
                                                "name": "newspaper"
                                            },
                                            "href": "https://forum.cursor.com/"
                                        },
                                        {
                                            "anchor": "Support",
                                            "icon": {
                                                "style": "duotone",
                                                "name": "headset"
                                            },
                                            "href": "mailto:<EMAIL>"
                                        }
                                    ]
                                }
                            },
                            {
                                "tab": "Guides",
                                "icon": {
                                    "style": "duotone",
                                    "name": "book-open"
                                },
                                "groups": [
                                    {
                                        "group": "Core",
                                        "pages": [
                                            {
                                                "title": "Working with Context",
                                                "description": "How to work with context in Cursor",
                                                "twitter:title": "Cursor – Working with Context",
                                                "twitter:description": "How to work with context in Cursor",
                                                "og:title": "Cursor – Working with Context",
                                                "og:description": "How to work with context in Cursor",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/working-with-context.png?v=1746228031061",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/working-with-context.png?v=1746228031061",
                                                "href": "/guides/working-with-context"
                                            },
                                            {
                                                "title": "Selecting Models",
                                                "description": "How to select models based on your task at hand",
                                                "og:title": "Cursor – Selecting Models",
                                                "og:description": "How to select models based on your task at hand",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/selecting-models.png?v=1747177021660",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/selecting-models.png?v=1747177021660",
                                                "twitter:title": "Cursor – Selecting Models",
                                                "twitter:description": "How to select models based on your task at hand",
                                                "href": "/guides/selecting-models"
                                            }
                                        ]
                                    },
                                    {
                                        "group": "Tutorials",
                                        "pages": [
                                            {
                                                "title": "Web Development",
                                                "description": "How to set up Cursor for web development",
                                                "twitter:title": "Cursor – Web Development",
                                                "twitter:description": "How to set up Cursor for web development",
                                                "og:title": "Cursor – Web Development",
                                                "og:description": "How to set up Cursor for web development",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/tutorials/web-development.png?v=1746047412138",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/tutorials/web-development.png?v=1746047412138",
                                                "href": "/guides/tutorials/web-development"
                                            },
                                            {
                                                "title": "Architectural Diagrams",
                                                "description": "Learn to generate architectural diagrams using Mermaid to visualize system structure and data flow",
                                                "twitter:title": "Cursor – Architectural Diagrams",
                                                "twitter:description": "Learn to generate architectural diagrams using Mermaid to visualize system structure and data flow",
                                                "og:title": "Cursor – Architectural Diagrams",
                                                "og:description": "Learn to generate architectural diagrams using Mermaid to visualize system structure and data flow",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/tutorials/architectural-diagrams.png?v=1747325120349",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/tutorials/architectural-diagrams.png?v=1747325120349",
                                                "href": "/guides/tutorials/architectural-diagrams"
                                            }
                                        ]
                                    },
                                    {
                                        "group": "Advanced",
                                        "pages": [
                                            {
                                                "title": "Large Codebases",
                                                "description": "How to work with large codebases in Cursor",
                                                "twitter:title": "Cursor – Large Codebases",
                                                "twitter:description": "How to work with large codebases in Cursor",
                                                "og:title": "Cursor – Large Codebases",
                                                "og:description": "How to work with large codebases in Cursor",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/advanced/large-codebases.png?v=1745803151316",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/advanced/large-codebases.png?v=1745803151316",
                                                "href": "/guides/advanced/large-codebases"
                                            },
                                            {
                                                "title": "Working with Documentation",
                                                "description": "How to leverage documentation effectively in Cursor through prompting, external sources, and internal context",
                                                "og:title": "Cursor – Working with Documentation",
                                                "og:description": "How to leverage documentation effectively in Cursor through prompting, external sources, and internal context",
                                                "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/advanced/working-with-documentation.png?v=1748075460573",
                                                "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/advanced/working-with-documentation.png?v=1748075460573",
                                                "twitter:title": "Cursor – Working with Documentation",
                                                "twitter:description": "How to leverage documentation effectively in Cursor through prompting, external sources, and internal context",
                                                "href": "/guides/advanced/working-with-documentation"
                                            }
                                        ]
                                    },
                                    {"group": "Editor Migration",
                                    "pages": [
                                        {
                                            "title": "VS Code",
                                            "description": "Guide to migrating VS Code settings, extensions, and profiles to Cursor using one-click import or manual methods",
                                            "twitter:title": "Cursor – VS Code",
                                            "twitter:description": "Guide to migrating VS Code settings, extensions, and profiles to Cursor using one-click import or manual methods",
                                            "og:title": "Cursor – VS Code",
                                            "og:description": "Guide to migrating VS Code settings, extensions, and profiles to Cursor using one-click import or manual methods",
                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/migration/vscode.png?v=1745803151494",
                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/migration/vscode.png?v=1745803151494",
                                            "href": "/guides/migration/vscode"
                                        },
                                        {
                                            "title": "JetBrains",
                                            "description": "Guide to migrating from JetBrains IDEs to Cursor: setup extensions, themes, shortcuts, and language-specific tools",
                                            "twitter:title": "Cursor – JetBrains",
                                            "twitter:description": "Guide to migrating from JetBrains IDEs to Cursor: setup extensions, themes, shortcuts, and language-specific tools",
                                            "og:title": "Cursor – JetBrains",
                                            "og:description": "Guide to migrating from JetBrains IDEs to Cursor: setup extensions, themes, shortcuts, and language-specific tools",
                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/migration/jetbrains.png?v=1745803151388",
                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/migration/jetbrains.png?v=1745803151388",
                                            "href": "/guides/migration/jetbrains"
                                        }
                                    ]
                                },
                                {
                                    "group": "Languages \u0026 Frameworks",
                                    "pages": [
                                        {
                                            "title": "Python",
                                            "description": "Comprehensive guide to setting up Python development in Cursor with essential extensions, linting and tools",
                                            "twitter:title": "Cursor – Python",
                                            "twitter:description": "Comprehensive guide to setting up Python development in Cursor with essential extensions, linting and tools",
                                            "og:title": "Cursor – Python",
                                            "og:description": "Comprehensive guide to setting up Python development in Cursor with essential extensions, linting and tools",
                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/languages/python.png?v=1743819392199",
                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/languages/python.png?v=1743819392199",
                                            "href": "/guides/languages/python"
                                        },
                                        {
                                            "title": "JavaScript \u0026 TypeScript",
                                            "description": "Complete guide to JavaScript \u0026 TypeScript development in Cursor, featuring extensions, AI tools, and framework support",
                                            "twitter:title": "Cursor – JavaScript \u0026 TypeScript",
                                            "twitter:description": "Complete guide to JavaScript \u0026 TypeScript development in Cursor, featuring extensions, AI tools, and framework support",
                                            "og:title": "Cursor – JavaScript \u0026 TypeScript",
                                            "og:description": "Complete guide to JavaScript \u0026 TypeScript development in Cursor, featuring extensions, AI tools, and framework support",
                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/languages/javascript.png?v=1743819392329",
                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/languages/javascript.png?v=1743819392329",
                                            "href": "/guides/languages/javascript"
                                        },
                                        {
                                            "title": "iOS \u0026 macOS (Swift)",
                                            "description": "Guide to integrating Cursor with Swift development workflows using Xcode, hot reloading, and Sweetpad tools",
                                            "twitter:title": "Cursor – iOS \u0026 macOS (Swift)",
                                            "twitter:description": "Guide to integrating Cursor with Swift development workflows using Xcode, hot reloading, and Sweetpad tools",
                                            "og:title": "Cursor – iOS \u0026 macOS (Swift)",
                                            "og:description": "Guide to integrating Cursor with Swift development workflows using Xcode, hot reloading, and Sweetpad tools",
                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/languages/swift.png?v=1743819392098",
                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/languages/swift.png?v=1743819392098",
                                            "href": "/guides/languages/swift"
                                        },
                                        {
                                            "title": "Java",
                                            "description": "Complete guide to setting up Java development in Cursor: JDK setup, extensions, debugging, and Maven/Gradle integration",
                                            "twitter:title": "Cursor – Java",
                                            "twitter:description": "Complete guide to setting up Java development in Cursor: JDK setup, extensions, debugging, and Maven/Gradle integration",
                                            "og:title": "Cursor – Java",
                                            "og:description": "Complete guide to setting up Java development in Cursor: JDK setup, extensions, debugging, and Maven/Gradle integration",
                                            "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/languages/java.png?v=1743819392298",
                                            "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/guides/languages/java.png?v=1743819392298",
                                            "href": "/guides/languages/java"
                                        }
                                    ]
                                }
                            ],
                            "global": {
                                "anchors": [
                                    {
                                        "anchor": "Website",
                                        "icon": {
                                            "style": "duotone",
                                            "name": "globe"
                                        },
                                        "href": "https://cursor.com/"
                                    },
                                    {
                                        "anchor": "Forum",
                                        "icon": {
                                            "style": "duotone",
                                            "name": "newspaper"
                                        },
                                        "href": "https://forum.cursor.com/"
                                    },
                                    {
                                        "anchor": "Support",
                                        "icon": {
                                            "style": "duotone",
                                            "name": "headset"
                                        },
                                        "href": "mailto:<EMAIL>"
                                    }
                                ]
                            }
                        }
                    ]
                },
                "pageMetadata": {
                    "title": "Common Issues",
                    "description": "Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs",
                    "twitter:title": "Cursor – Common Issues",
                    "twitter:description": "Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs",
                    "og:title": "Cursor – Common Issues",
                    "og:description": "Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs",
                    "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/common-issues.png?v=1743819390998",
                    "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/common-issues.png?v=1743819390998",
                    "href": "/troubleshooting/common-issues"
                },
                "docsConfig": {
                    "theme": "mint",
                    "$schema": "https://mintlify.com/docs.json",
                    "name": "Cursor",
                    "colors": {
                        "primary": "#0C0C15",
                        "light": "#ffffff",
                        "dark": "#0C0C15"
                    },
                    "logo": {
                        "light": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/logo/app-logo.svg",
                        "dark": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/logo/app-logo.svg"},
                        "favicon": "/images/logo/favicon.svg",
                        "appearance": {
                            "default": "light"
                        },
                        "background": {
                            "color": {
                                "light": "#fff",
                                "dark": "#000000"
                            }},
                            "navbar": {
                                "links": [
                                    {
                                        "label": "Sign in",
                                        "href": "https://cursor.com/settings"
                                    }
                                ],
                                "primary": {
                                    "type": "button",
                                    "label": "Download",
                                    "href": "https://cursor.com"
                                }
                            },
                            "navigation": {
                                "tabs": [
                                    {
                                        "tab": "Documentation",
                                        "icon": "book-open",
                                        "groups": [
                                            {
                                                "group": "Get Started",
                                                "pages": [
                                                    "welcome",
                                                    "get-started/installation",
                                                    "faq"
                                                ]
                                            },
                                            {
                                                "group": "Editor",
                                                "pages": [
                                                    {
                                                        "group": "Tab",
                                                        "pages": [
                                                            "tab/overview",
                                                            "tab/from-gh-copilot",
                                                            "tab/auto-import",
                                                            "tab/advanced-features"
                                                        ]
                                                    },
                                                    {
                                                        "group": "Chat",
                                                        "pages": [
                                                            "chat/overview",
                                                            "chat/agent",
                                                            "chat/ask",
                                                            "chat/manual",
                                                            "chat/custom-modes",
                                                            "chat/tools",
                                                            "chat/apply"
                                                        ]
                                                    },
                                                    {
                                                        "group": "Inline Edit (⌘K)",
                                                        "pages": [
                                                            "cmdk/overview",
                                                            "cmdk/terminal-cmdk"
                                                        ]
                                                    },
                                                    "models",
                                                    "kbd",
                                                    {
                                                        "group": "Features",
                                                        "pages": [
                                                            "more/ai-commit-message",
                                                            "beta/notepads",
                                                            "background-agent"
                                                        ]
                                                    }
                                                ]
                                            },
                                            {
                                                "group": "Context",
                                                "pages": [
                                                    "context/codebase-indexing",
                                                    "context/rules",
                                                    "context/management",
                                                    {
                                                        "group": "@ Symbols",
                                                        "pages": [
                                                            "context/@-symbols/overview",
                                                            "context/@-symbols/@-files",
                                                            "context/@-symbols/@-folders",
                                                            "context/@-symbols/@-code",
                                                            "context/@-symbols/@-docs",
                                                            "context/@-symbols/@-git",
                                                            "context/@-symbols/@-web",
                                                            "context/@-symbols/@-definitions",
                                                            "context/@-symbols/@-link",
                                                            "context/@-symbols/@-lint-errors",
                                                            "context/@-symbols/@-recent-changes",
                                                            "context/@-symbols/@-cursor-rules",
                                                            "context/@-symbols/@-notepads",
                                                            "context/@-symbols/@-past-chats",
                                                            "context/@-symbols/pill-files",
                                                            "context/@-symbols/slash-commands"
                                                        ]
                                                    },
                                                    "context/ignore-files",
                                                    "context/model-context-protocol",
                                                    "context/max-mode"
                                                ]
                                            },
                                            {
                                                "group": "Account",
                                                "pages": [
                                                    "account/plans-and-usage",
                                                    {
                                                        "group": "Business",
                                                        "pages": [
                                                            "account/teams/setup",
                                                            "account/teams/members",
                                                            "account/teams/analytics",
                                                            "account/teams/sso"
                                                        ]
                                                    },
                                                    "account/dashboard",
                                                    "account/billing",
                                                    "account/pricing",
                                                    "account/privacy"
                                                ]
                                            },
                                            {
                                                "group": "Settings",
                                                "pages": [
                                                    "settings/api-keys",
                                                    "settings/beta"
                                                ]
                                            },
                                            {
                                                "group": "Troubleshooting",
                                                "pages": [
                                                    "troubleshooting/common-issues",
                                                    "troubleshooting/troubleshooting-guide",
                                                    "troubleshooting/request-reporting"
                                                ]
                                            }
                                        ],
                                        "global": {
                                            "anchors": [
                                                {
                                                    "anchor": "Website",
                                                    "icon": {
                                                        "style": "duotone",
                                                        "name": "globe"
                                                    },
                                                    "href": "https://cursor.com/"
                                                },
                                                {
                                                    "anchor": "Forum",
                                                    "icon": {
                                                        "style": "duotone",
                                                        "name": "newspaper"
                                                    },
                                                    "href": "https://forum.cursor.com/"
                                                },
                                                {
                                                    "anchor": "Support",
                                                    "icon": {
                                                        "style": "duotone",
                                                        "name": "headset"
                                                    },
                                                    "href": "mailto:<EMAIL>"
                                                }
                                            ]
                                        }
                                    },
                                    {
                                        "tab": "Guides",
                                        "icon": {
                                            "style": "duotone",
                                            "name": "book-open"
                                        },
                                        "groups": [
                                            {
                                                "group": "Core",
                                                "pages": [
                                                    "guides/working-with-context",
                                                    "guides/selecting-models"
                                                ]
                                            },
                                            {
                                                "group": "Tutorials",
                                                "pages": [
                                                    "guides/tutorials/web-development",
                                                    "guides/tutorials/architectural-diagrams"
                                                ]
                                            },
                                            {
                                                "group": "Advanced",
                                                "pages": [
                                                    "guides/advanced/large-codebases",
                                                    "guides/advanced/working-with-documentation"
                                                ]
                                            },
                                            {
                                                "group": "Editor Migration",
                                                "pages": [
                                                    "guides/migration/vscode",
                                                    "guides/migration/jetbrains"
                                                ]
                                            },
                                            {
                                                "group": "Languages \u0026 Frameworks",
                                                "pages": [
                                                    "guides/languages/python",
                                                    "guides/languages/javascript",
                                                    "guides/languages/swift",
                                                    "guides/languages/java"
                                                ]
                                            }
                                        ],
                                        "global": {
                                            "anchors": [
                                                {
                                                    "anchor": "Website",
                                                    "icon": {
                                                        "style": "duotone",
                                                        "name": "globe"
                                                    },
                                                    "href": "https://cursor.com/"
                                                },
                                                {
                                                    "anchor": "Forum",
                                                    "icon": {
                                                        "style": "duotone",
                                                        "name": "newspaper"
                                                    },
                                                    "href": "https://forum.cursor.com/"
                                                },
                                                {
                                                    "anchor": "Support",
                                                    "icon": {
                                                        "style": "duotone",
                                                        "name": "headset"
                                                    },
                                                    "href": "mailto:<EMAIL>"
                                                }]}
                                            }
                                        ]
                                    },
                                    "footer": {
                                        "socials": {
                                            "x": "https://x.com/cursor_ai",
                                            "github": "https://github.com/getcursor/cursor/",
                                            "website": "https://cursor.com"
                                        },
                                        "links": [
                                            {
                                                "header": "Product",
                                                "items": [
                                                    {
                                                        "label": "Pricing",
                                                        "href": "https://www.cursor.com/pricing"
                                                    },
                                                    {
                                                        "label": "Downloads",
                                                        "href": "https://www.cursor.com/downloads"
                                                    },
                                                    {
                                                        "label": "Docs",
                                                        "href": "https://docs.cursor.com"
                                                    },
                                                    {
                                                        "label": "Forum",
                                                        "href": "https://forum.cursor.com"
                                                    }
                                                ]
                                            },
                                            {
                                                "header": "Company",
                                                "items": [
                                                    {
                                                        "label": "Careers",
                                                        "href": "https://anysphere.inc"
                                                    },
                                                    {
                                                        "label": "About",
                                                        "href": "https://anysphere.inc"
                                                    },
                                                    {
                                                        "label": "Security",
                                                        "href": "https://cursor.com/security"
                                                    },
                                                    {
                                                        "label": "Privacy",
                                                        "href": "https://cursor.com/privacy"
                                                    }
                                                ]
                                            },
                                            {
                                                "header": "Resources",
                                                "items": [
                                                    {
                                                        "label": "Terms",
                                                        "href": "https://cursor.com/terms-of-service"
                                                    },
                                                    {
                                                        "label": "Changelog",
                                                        "href": "https://cursor.com/changelog"
                                                    },
                                                    {
                                                        "label": "Twitter",
                                                        "href": "https://x.com/cursor_ai"
                                                    },
                                                    {
                                                        "label": "GitHub",
                                                        "href": "https://github.com/getcursor/cursor"
                                                    }
                                                ]
                                            }
                                        ]
                                    },
                                    "seo": {
                                        "metatags": {
                                            "og:site_name": "Cursor",
                                            "og:title": "Cursor - Build Software Faster",
                                            "og:description": "Built to make you extraordinarily productive, Cursor is the best way to code with AI.",
                                            "og:type": "website",
                                            "og:url": "https://cursor.com",
                                            "og:image": "/images/hero.png",
                                            "og:locale": "en_US",
                                            "og:logo": "/images/logo/app-logo.svg",
                                            "article:publisher": "Anysphere Inc.",
                                            "twitter:title": "Cursor - Build Software Faster",
                                            "twitter:description": "Built to make you extraordinarily productive, Cursor is the best way to code with AI.",
                                            "twitter:url": "https://cursor.com",
                                            "twitter:image": "/images/hero.png",
                                            "twitter:card": "summary_large_image",
                                            "twitter:site": "@cursor_ai",
                                            "og:image:width": "1200",
                                            "og:image:height": "630"
                                        },
                                        "indexing": "navigable"
                                    },
                                    "styling": {
                                        "eyebrows": "breadcrumbs"
                                    },
                                    "redirects": [
                                        {
                                            "source": "/ai-review",
                                            "destination": "/chat/agent"
                                        },
                                        {
                                            "source": "/background-agents",
                                            "destination": "/background-agent"
                                        },
                                        {
                                            "source": "/advanced/api-keys",
                                            "destination": "/settings/api-keys"
                                        },
                                        {
                                            "source": "/advanced/keyboard-shortcuts",
                                            "destination": "/kbd"
                                        },
                                        {
                                            "source": "/advanced/models",
                                            "destination": "/settings/models"
                                        },
                                        {
                                            "source": "/advanced/model-context-protocol",
                                            "destination": "/context/model-context-protocol"
                                        },
                                        {
                                            "source": "/billing/faq",
                                            "destination": "/account/billing"
                                        },
                                        {
                                            "source": "/forum",
                                            "destination": "/resources/forum"
                                        },
                                        {
                                            "source": "/composer/overview",
                                            "destination": "/composer"
                                        },
                                        {
                                            "source": "/get-started/usage",
                                            "destination": "/account/usage"
                                        },
                                        {
                                            "source": "/get-started/migrate-from-vs-code",
                                            "destination": "/guides/migration/vscode"
                                        },
                                        {
                                            "source": "/get-started/what-is-cursor",
                                            "destination": "/welcome"
                                        },
                                        {
                                            "source": "/plans/business/getting-started",
                                            "destination": "/account/teams/setup"
                                        },
                                        {
                                            "source": "/plans/business/roles",
                                            "destination": "/account/teams/members"
                                        },
                                        {
                                            "source": "/plans/business/sso",
                                            "destination": "/account/teams/sso"
                                        },
                                        {
                                            "source": "/plans/business/team-management",
                                            "destination": "/account/teams/members"
                                        },
                                        {
                                            "source": "/privacy/privacy",
                                            "destination": "/account/privacy"
                                        },
                                        {
                                            "source": "/settings/ide/features",
                                            "destination": "/settings/preferences"
                                        },
                                        {
                                            "source": "/settings/ide/models",
                                            "destination": "/settings/models"
                                        },
                                        {
                                            "source": "/settings/ide/overview",
                                            "destination": "/settings/preferences"
                                        },
                                        {
                                            "source": "/features/generate-commit-message",
                                            "destination": "/more/ai-commit-message"
                                        },
                                        {
                                            "source": "/features/beta/notepads",
                                            "destination": "/beta/notepads"
                                        },
                                        {
                                            "source": "/context/@-symbols/basic",
                                            "destination": "/context/@-symbols/overview"
                                        },
                                        {
                                            "source": "/get-started/resources",
                                            "destination": "/resources/forum"
                                        },
                                        {
                                            "source": "/agent",
                                            "destination": "/chat/agent"
                                        },
                                        {
                                            "source": "/composer",
                                            "destination": "/chat/overview"
                                        },
                                        {
                                            "source": "/chat/customize",
                                            "destination": "/chat/overview"
                                        },
                                        {
                                            "source": "/chat/codebase",
                                            "destination": "/context/codebase-indexing"
                                        },
                                        {
                                            "source": "/settings/preferences",
                                            "destination": "/settings/beta"
                                        },
                                        {
                                            "source": "/account/usage",
                                            "destination": "/account/plans-and-usage"
                                        },
                                        {
                                            "source": "/account/plans",
                                            "destination": "/account/plans-and-usage"
                                        },
                                        {
                                            "source": "/account/security",
                                            "destination": "/account/privacy"
                                        },
                                        {
                                            "source": "/settings/models",
                                            "destination": "/models"
                                        },
                                        {
                                            "source": "/context/rules-for-ai",
                                            "destination": "/context/rules"
                                        },
                                        {
                                            "source": "/context/@-symbols/@-summarized-composers",
                                            "destination": "/context/@-symbols/@-past-chats"
                                        },
                                        {
                                            "source": "/",
                                            "destination": "/welcome"
                                        }
                                    ]
                                },
                                "apiReferenceData": {
                                }
                            },
                            "feedback": {
                                "issues": false,
                                "edits": false,
                                "thumbs": true
                            },
                            "favicons": {
                                "icons": [
                                    {
                                        "rel": "apple-touch-icon",
                                        "sizes": "180x180",
                                        "href": "https://mintlify.s3-us-west-1.amazonaws.com/cursor/_generated/favicon/apple-touch-icon.png?v=3",
                                        "type": "image/png"
                                    },
                                    {
                                        "rel": "icon",
                                        "sizes": "32x32",
                                        "href": "https://mintlify.s3-us-west-1.amazonaws.com/cursor/_generated/favicon/favicon-32x32.png?v=3",
                                        "type": "image/png"
                                    },
                                    {
                                        "rel": "icon",
                                        "sizes": "16x16",
                                        "href": "https://mintlify.s3-us-west-1.amazonaws.com/cursor/_generated/favicon/favicon-16x16.png?v=3",
                                        "type": "image/png"
                                    },
                                    {
                                        "rel": "shortcut icon",
                                        "href": "https://mintlify.s3-us-west-1.amazonaws.com/cursor/_generated/favicon/favicon.ico?v=3",
                                        "type": "image/x-icon"
                                    }
                                ],
                                "browserconfig": "https://mintlify.s3-us-west-1.amazonaws.com/cursor/_generated/favicon/browserconfig.xml?v=3"
                            },
                            "subdomain": "docs.cursor.com",
                            "actualSubdomain": "cursor",
                            "internalAnalyticsWriteKey": "phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW",
                            "inkeep": {
                                "integrationApiKey": "987829e194ca5c619c6021c6d5b4a27168db6e4c19aeb0e6"
                            },
                            "trieve": {
                                "datasetId": "88bb09dd-b6e6-411b-8fb4-1ed0b1ccbf16",
                                "chatEnabled": true
                            },
                            "shouldIndex": true,
                            "org": {
                                "plan": "enterprise",
                                "createdAt": "2023-11-09T20:25:04.726Z"
                            },
                            "cssFiles": [
                                {
                                    "_id": "66624db6c1136bf8b85a8833",
                                    "filePath": "global.css",
                                    "subdomain": "cursor",
                                    "__v": 0,
                                    "content": "::-webkit-scrollbar {\n  width: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background: white;\n}\n\n::-webkit-scrollbar-thumb {\n  background-color: #eeee;\n  border-radius: 10px;\n  border: none;\n}\n\nhtml {\n  scrollbar-width: thin;\n  scrollbar-color: #eeee white;\n  scrollbar-gutter: stable;\n}\n\nhtml.dark {\n  scrollbar-width: thin;\n  scrollbar-color: #222 #222;\n  scrollbar-gutter: stable;\n}\n\nhtml.dark ::-webkit-scrollbar-track {\n  background: #222;\n}\n\nhtml.dark ::-webkit-scrollbar-thumb {\n  background-color: #222;\n}\n\nhtml.dark ::-webkit-scrollbar-thumb:hover {\n  background-color: #333;\n}\n\na:has(\u003e code) {\n  border-bottom: none !important;\n}\n\na\u003ecode {\n  cursor: pointer;\n  border-bottom: #0c0c15 1px solid !important;\n}\n\na\u003ecode:hover {\n  border-bottom: #0c0c15 2px solid !important;\n}\n\n#content-area\u003ediv.leading-6.mt-14\u003efooter\u003ediv.sm\\:flex\u003ea {\n  display: none;\n}\n\n* {\n  /* Use system font stack as the default font */\n  font-family: -apple-system, BlinkMacSystemFont, sans-serif;\n}\n\n/* h1,\nh2,\nh3,\nh4,\nh5,\nh6, */\ncode * {\n  font-family: monospace;\n}\n\n#header h1 {\n  font-size: 2.25rem;\n}\n\n/* #content-container h2 {\n  font-size: 2rem;\n}\n\n#content-container h3 {\n  font-size: 1.75rem;\n}\n\n#content-container h4 {\n  font-size: 1.25rem;\n} */\n\n.full-width-table table {\n  display: table;\n}\n\n.full-width-table table th {\n  text-align: left;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.full-width-table table th:nth-child(3),\n.full-width-table table th:nth-child(4),\n.full-width-table table th:nth-child(5) {\n  text-align: center;\n}\n\n@media (max-width: 640px) {\n\n  .full-width-table table th:nth-child(2),\n  .full-width-table table td:nth-child(2) {\n    display: none;\n  }\n}\n\n\n\n:not(.dark) .prose :where(a):not(:where([class~=not-prose], [class~=not-prose] *)):not(h1, h1 *, h2, h2 *, h3, h3 *, h4, h4 *, h5, h5 *, h6, h6 *) {\n  border-bottom: #666666 1px solid;\n}\n\n\n/* img {\n  border-radius: 0.5rem;\n} */\n\n.max-pill {\n  background-color: white;\n  color: #0c0c15;\n  padding: 1px 4px;\n  border-radius: 8px;\n  font-size: 0.6rem;\n  font-weight: 500;\n  display: inline-block;\n  margin-left: 2px;\n  margin-right: 4px;\n  line-height: 1.2;\n  border: 1px solid #0c0c15;\n}\n\n.dark .max-pill {\n  background-color: #0c0c15;\n  color: white;\n  border-color: white;\n}\n\n.footnotes-container {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  grid-auto-flow: row;\n  margin-top: -14px;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid rgb(var(--gray-800)/.5);\n  line-height: 1.6;\n}\n\n@media (max-width: 640px) {\n  .footnotes-container {\n    grid-template-columns: 1fr;\n  }\n}\n\n\n.footnotes-container p {\n  font-size: 0.8rem;\n  opacity: 0.9;\n  margin: 0 0 0 0.35em;\n}\n\n.dark .footnotes-container {\n  border-bottom-color: rgb(var(--gray-800)/.5);\n}\n\n.no-wrap {\n  white-space: nowrap;\n}\n\n.flowchart {\n  margin: 0 auto;\n}"
                                }
                            ],
                            "jsFiles": [
                                {
                                    "_id": "681e7f7d777ecd3987ff9f93",
                                    "filePath": "inkeep.js",
                                    "subdomain": "cursor",
                                    "__v": 0,
                                    "content": "function loadScript(url, callback) {\n    const script = document.createElement(\"script\");\n    script.src = url;\n    script.type = \"text/javascript\";\n    script.onload = callback;\n    document.head.appendChild(script);\n  }\n  \n  const styleOverrides = `\n    .inkeep-widget-vars {\n      --ikp-color-inkeep-expanded-primary-50: #f6f6f6 !important;\n      --ikp-color-inkeep-expanded-primary-100: #e7e7e7 !important;\n      --ikp-color-inkeep-expanded-primary-200: #d1d1d1 !important;\n      --ikp-color-inkeep-expanded-primary-300: #b0b0b0 !important;\n      --ikp-color-inkeep-expanded-primary-400: #888888 !important;\n    }\n    .ikp-chat-button__button {\n      background-color:#0c0c15;\n    }\n    [data-theme='light'] .ikp-ai-chat-input__fieldset {\n      box-shadow: none;\n      border: 1px solid var(--ikp-color-gray-300);\n      background: #f7f7f7;\n    }\n  `;\n  \n  loadScript(\n    \"https://cdn.jsdelivr.net/npm/@inkeep/cxkit-mintlify@0.5/dist/index.js\",\n    () =\u003e {\n      const settings = {\n        baseSettings: {\n          apiKey: \"495cde664ec61905c784440c803e08517a8e53ac46269749\",\n          primaryBrandColor: \"#0A0A0A\",\n          theme: {\n            styles: [\n              {\n                value: styleOverrides,\n                type: \"style\",\n                key: \"cursor-styles\",\n              },\n            ],\n          },\n          transformSource: (source) =\u003e {\n            if (source.contentType === \"documentation\") {\n              return {\n                ...source,\n                tabs: [...(source.tabs || []), \"Docs\"],\n              };\n            }\n            return source;\n          },\n        },\n        aiChatSettings: {\n          chatSubjectName: \"Cursor\",\n          aiAssistantAvatar: \"https://www.cursor.com/apple-touch-icon.png\",\n          getHelpOptions: [\n            {\n              name: \"Support\",\n              icon: {\n                builtIn: \"IoMail\",\n              },\n              action: {\n                type: \"open_link\",\n                url: \"mailto:<EMAIL>\",\n              },\n            },\n            {\n              name: \"Forum\",\n              icon: {\n                builtIn: \"FaDiscourse\",\n              },\n              action: {\n                type: \"open_link\",\n                url: \"https://forum.cursor.com\",\n              },\n            },\n          ],\n          exampleQuestions: [\n            \"How to use my own LLM API key?\",\n            \"How to index my codebase?\",\n            \"How to enable partial accepts?\",\n            \"When does usage reset?\",\n            \"How to migrate from VS Code?\",\n            \"Ignore files?\",\n          ],\n        },\n        searchSettings: {\n          tabs: [ \"Docs\", \"All\", \"GitHub\", \"Forums\"],\n        },\n      };\n  \n      // Initialize the UI components\n      Inkeep.ModalSearchAndChat(settings); // Search Bar\n      Inkeep.ChatButton(settings); // 'Ask AI' button\n    }\n  );\n  "
                                }
                            ],
                            "mdxSourceWithNoJs": {
                                "compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    a: \"a\",\n    code: \"code\",\n    li: \"li\",\n    ol: \"ol\",\n    p: \"p\",\n    strong: \"strong\",\n    ul: \"ul\",\n    ..._provideComponents(),\n    ...props.components\n  }, {Accordion, AccordionGroup, Heading, Note} = _components;\n  if (!Accordion) _missingMdxReference(\"Accordion\", true);\n  if (!AccordionGroup) _missingMdxReference(\"AccordionGroup\", true);\n  if (!Heading) _missingMdxReference(\"Heading\", true);\n  if (!Note) _missingMdxReference(\"Note\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(_components.p, {\n      children: \"While we strive to make Cursor as stable as possible, sometimes issues can arise. Below are some common issues and how to resolve them.\"\n    }), \"\\n\", _jsx(Heading, {\n      level: \"3\",\n      id: \"networking-issues-http%2F2\",\n      isAtRootLevel: \"true\",\n      children: \"Networking Issues (HTTP/2)\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Cursor relies on the HTTP/2 protocol for many of it’s AI features, due to it’s ability to handle streamed responses. If HTTP/2 is not supported by your network, this can cause issues such as failure to index your code, and the inability to use Cursor’s AI features.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This can be the case when on corpoorate networks, using VPNs, or using a proxy like Zscaler.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To resolve this, Cursor now comes with a HTTP/1.1 fallback, which is slower, but will allow you to use Cursor’s AI features. You can enable this yourself in the app settings (not the Cursor settings), by pressing \", _jsx(_components.code, {\n        children: \"CMD/CTRL + ,\"\n      }), \" and then searching for \", _jsx(_components.code, {\n        children: \"HTTP/2\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You should then enable the \", _jsx(_components.code, {\n        children: \"Disable HTTP/2\"\n      }), \" option, which will force Cursor to use HTTP/1.1, and should resolve the issue.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We hope to add automatic detection and fallback in the future!\"\n    }), \"\\n\", _jsx(Heading, {\n      level: \"3\",\n      id: \"resource-issues-cpu%2C-ram%2C-etc\",\n      isAtRootLevel: \"true\",\n      children: \"Resource Issues (CPU, RAM, etc.)\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Some users see high CPU or RAM usage in Cursor, which can cause their machine to slow down, or to show warnings about high RAM usage.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"While Cursor can use a lot of resources when working on large codebases, this is usually not the case for most users, and is more likely to be an issue with Cursor’s extensions or settings.\"\n    }), \"\\n\", _jsx(Note, {\n      children: _jsxs(_components.p, {\n        children: [\"If you are seeing a low RAM warning on \", _jsx(_components.strong, {\n          children: \"MacOS\"\n        }), \", please note that there is a bug for some users that can show wildly incorrect values. If you are seeing this, please open the Activity Monitor and look at the “Memory” tab to see the correct memory usage.\"]\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"If you’re experiencing high CPU or RAM usage in Cursor, here are steps to diagnose and resolve the issue:\"\n    }), \"\\n\", _jsxs(AccordionGroup, {\n      children: [_jsxs(Accordion, {\n        title: \"Check Your Extensions\",\n        children: [_jsx(_components.p, {\n          children: \"While many extensions can be useful, some can significantly impact performance!\"\n        }), _jsxs(_components.p, {\n          children: [\"To test this, you can try to run \", _jsx(_components.code, {\n            children: \"cursor --disable-extensions\"\n          }), \" from the command line to launch Cursor without any extensions enabled. If the performance improves, gradually re-enable extensions one by one to identify the problematic ones.\"]\n        }), _jsxs(_components.p, {\n          children: [\"You can also try to use the Extension Bisect feature, which will help you identify which extension is causing the issue. You can read more about it \", _jsx(_components.a, {\n            href: \"https://code.visualstudio.com/blogs/2021/02/16/extension-bisect#_welcome-extension-bisect\",\n            children: \"here\"\n          }), \", but note that this may only be useful if the issues are immediate and obvious, and not an issue that worsens over time.\"]\n        })]\n      }), _jsxs(Accordion, {\n        title: \"Use the Process Explorer\",\n        children: [_jsxs(_components.p, {\n          children: [\"The \", _jsx(_components.strong, {\n            children: \"Process Explorer\"\n          }), \" is a built in tool in Cursor that allows you to see which processes are consuming resources.\"]\n        }), _jsxs(_components.p, {\n          children: [\"To open it, open the Command Palette (\", _jsx(_components.code, {\n            children: \"Cmd/Ctrl + Shift + P\"\n          }), \") and run the \", _jsx(_components.code, {\n            children: \"Developer: Open Process Explorer\"\n          }), \" command.\"]\n        }), _jsx(_components.p, {\n          children: \"This should open a new window, with a list of all the processes Cursor is running, both as part of it’s own executation, as well as any processes needed to run extensions and any terminals you may have running. This should immediately identify any processes that are consuming a lot of resources.\"\n        }), _jsxs(_components.p, {\n          children: [\"If the process is listed under the \", _jsx(_components.strong, {\n            children: _jsx(_components.code, {\n              children: \"extensionHost\"\n            })\n          }), \" dropdown, this suggests an extension is causing the issue, and you should try to find and disable the problematic extension.\"]\n        }), _jsxs(_components.p, {\n          children: [\"If the process is listended under the \", _jsx(_components.strong, {\n            children: _jsx(_components.code, {\n              children: \"ptyHost\"\n            })\n          }), \" dropdown, this suggests a terminal is consuming a lot of resources. The Process Explorer will show you each terminal that is running, and what command is running within it, so that you can try to kill it, or diagnose it’s high resource usage.\"]\n        }), _jsxs(_components.p, {\n          children: [\"If the usage is from another process, please let us know in the \", _jsx(_components.a, {\n            href: \"https://forum.cursor.com/\",\n            children: \"forum\"\n          }), \" and we’ll be happy to help diagnose the issue.\"]\n        })]\n      }), _jsxs(Accordion, {\n        title: \"Monitor System Resources\",\n        children: [_jsx(_components.p, {\n          children: \"Depending on your operating system, you can use a number of different tools to monitor your system’s resources.\"\n        }), _jsx(_components.p, {\n          children: \"This will help you identify if the issue is Cursor-specific, or if it’s a system-wide issue.\"\n        })]\n      }), _jsx(Accordion, {\n        title: \"Testing a Minimal Installation\",\n        children: _jsx(_components.p, {\n          children: \"While the above steps should help the majority of users, if you are still experiencing issues, you can try testing a minimal installation of Cursor to see if the issue persists.\"\n        })\n      })]\n    }), \"\\n\", _jsx(Heading, {\n      level: \"2\",\n      id: \"general-faqs\",\n      isAtRootLevel: \"true\",\n      children: \"General FAQs\"\n    }), \"\\n\", _jsxs(AccordionGroup, {\n      children: [_jsx(Accordion, {\n        title: \"I see an update on the changelog but Cursor won't update\",\n        children: _jsx(_components.p, {\n          children: \"If the update is very new, it might not have rolled out to you yet. We do staged rollouts, which means we release new updates to a few randomly selected users first before releasing them to everyone. Expect to get the update in a couple days!\"\n        })\n      }), _jsx(Accordion, {\n        title: \"I have issues with my GitHub login in Cursor / How do I log out of GitHub in Cursor?\",\n        children: _jsxs(_components.p, {\n          children: [\"You can try using the \", _jsx(_components.code, {\n            children: \"Sign Out of GitHub\"\n          }), \" command from the command palette \", _jsx(_components.code, {\n            children: \"Ctrl/⌘ + Shift + P\"\n          }), \".\"]\n        })\n      }), _jsx(Accordion, {\n        title: \"I can't use GitHub Codespaces\",\n        children: _jsx(_components.p, {\n          children: \"Unfortunately, we don’t support GitHub Codespaces yet.\"\n        })\n      }), _jsx(Accordion, {\n        title: \"I have errors connecting to Remote SSH\",\n        children: _jsxs(_components.p, {\n          children: [\"Currently, we don’t support SSHing into Mac or Windows machines. If you’re not using a Mac or Windows machine, please report your issue to us in the \", _jsx(_components.a, {\n            href: \"https://forum.cursor.com/\",\n            children: \"forum\"\n          }), \". It would be helpful to include some logs for better assistance.\"]\n        })\n      }), _jsxs(Accordion, {\n        title: \"SSH Connection Problems on Windows\",\n        children: [_jsx(_components.p, {\n          children: \"If you encounter the error “SSH is only supported in Microsoft versions of VS Code”, follow these steps:\"\n        }), _jsxs(_components.ol, {\n          children: [\"\\n\", _jsxs(_components.li, {\n            children: [\"\\n\", _jsx(_components.p, {\n              children: \"Uninstall the current Remote-SSH extension:\"\n            }), \"\\n\", _jsxs(_components.ul, {\n              children: [\"\\n\", _jsxs(_components.li, {\n                children: [\"Open the Extensions view (\", _jsx(_components.code, {\n                  children: \"Ctrl + Shift + X\"\n                }), \")\"]\n              }), \"\\n\", _jsx(_components.li, {\n                children: \"Search for “Remote-SSH”\"\n              }), \"\\n\", _jsx(_components.li, {\n                children: \"Click on the gear icon and select “Uninstall”\"\n              }), \"\\n\"]\n            }), \"\\n\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"\\n\", _jsx(_components.p, {\n              children: \"Install version 0.113 of Remote-SSH:\"\n            }), \"\\n\", _jsxs(_components.ul, {\n              children: [\"\\n\", _jsx(_components.li, {\n                children: \"Go to the Cursor marketplace\"\n              }), \"\\n\", _jsx(_components.li, {\n                children: \"Search for “Remote-SSH”\"\n              }), \"\\n\", _jsx(_components.li, {\n                children: \"Find version 0.113 and install it\"\n              }), \"\\n\"]\n            }), \"\\n\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"\\n\", _jsx(_components.p, {\n              children: \"After installation:\"\n            }), \"\\n\", _jsxs(_components.ul, {\n              children: [\"\\n\", _jsx(_components.li, {\n                children: \"Close all VS Code instances that have active SSH connections\"\n              }), \"\\n\", _jsx(_components.li, {\n                children: \"Restart Cursor completely\"\n              }), \"\\n\", _jsx(_components.li, {\n                children: \"Try connecting via SSH again\"\n              }), \"\\n\"]\n            }), \"\\n\"]\n          }), \"\\n\"]\n        }), _jsx(_components.p, {\n          children: \"If you still experience issues, make sure your SSH configuration is correct and that you have the necessary SSH keys set up properly.\"\n        })]\n      }), _jsx(Accordion, {\n        title: \"Cursor Tab and Cmd K do not work behind my corporate proxy\",\n        children: _jsxs(_components.p, {\n          children: [\"Cursor Tab and Cmd K use HTTP/2 by default, which allows us to use less resources with lower latency. Some corporate proxies (e.g. Zscaler in certain configurations) block HTTP/2. To fix this, you can set \", _jsx(_components.code, {\n            children: \"\\\"cursor.general.disableHttp2\\\": true\"\n          }), \" in the settings (\", _jsx(_components.code, {\n            children: \"Cmd/Ctrl + ,\"\n          }), \" and then search for \", _jsx(_components.code, {\n            children: \"http2\"\n          }), \").\"]\n        })\n      }), _jsx(Accordion, {\n        title: \"I just subscribed to Pro but I'm still on the free plan in the app\",\n        children: _jsx(_components.p, {\n          children: \"Try logging out and logging back in from the Cursor Settings\"\n        })\n      }), _jsxs(Accordion, {\n        title: \"When will my usage reset again?\",\n        children: [_jsxs(_components.p, {\n          children: [\"If you’re subscribed to Pro you can click on \", _jsx(_components.code, {\n            children: \"Manage Subscription\"\n          }), \" from the \", _jsx(_components.a, {\n            href: \"https://cursor.com/settings\",\n            children: \"Dashboard\"\n          }), \" and your plan renewal date will be displayed at the top.\"]\n        }), _jsx(_components.p, {\n          children: \"If you’re a free user you can check when you got the first email from us in your inbox. Your usage will reset every month from that date.\"\n        })]\n      }), _jsxs(Accordion, {\n        title: \"My Chat/Composer history disappeared after an update\",\n        children: [_jsx(_components.p, {\n          children: \"If you notice that your Chat or Composer history has been cleared following an update, this is likely due to low disk space on your system. Cursor may need to clear historical data during updates when disk space is limited. To prevent this from happening:\"\n        }), _jsxs(_components.ol, {\n          children: [\"\\n\", _jsx(_components.li, {\n            children: \"Ensure you have sufficient free disk space before updating\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"Regularly clean up unnecessary files on your system\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"Consider backing up important conversations before updating\"\n          }), \"\\n\"]\n        })]\n      }), _jsx(Accordion, {\n        title: \"How do I uninstall Cursor?\",\n        children: _jsxs(_components.p, {\n          children: [\"You can follow \", _jsx(_components.a, {\n            href: \"https://code.visualstudio.com/docs/setup/uninstall\",\n            children: \"this guide\"\n          }), \" to uninstall Cursor. Replace every occurrence of “VS Code” or “Code” with “Cursor”, and “.vscode” with “.cursor”.\"]\n        })\n      }), _jsx(Accordion, {\n        title: \"How do I delete my account?\",\n        children: _jsxs(_components.p, {\n          children: [\"You can delete your account by clicking on the \", _jsx(_components.code, {\n            children: \"Delete Account\"\n          }), \" button in the \", _jsx(_components.a, {\n            href: \"https://cursor.com/settings\",\n            children: \"Dashboard\"\n          }), \". Note that this will delete your account and all data associated with it.\"]\n        })\n      }), _jsxs(Accordion, {\n        title: \"How do I open Cursor from the command line?\",\n        children: [_jsxs(_components.p, {\n          children: [\"You can open Cursor from the command line by running \", _jsx(_components.code, {\n            children: \"cursor\"\n          }), \" in your terminal. If you’re missing the \", _jsx(_components.code, {\n            children: \"cursor\"\n          }), \" command, you can\"]\n        }), _jsxs(_components.ol, {\n          children: [\"\\n\", _jsxs(_components.li, {\n            children: [\"Open the command palette \", _jsx(_components.code, {\n              children: \"⌘⇧P\"\n            })]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"Type \", _jsx(_components.code, {\n              children: \"install command\"\n            })]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"Select \", _jsx(_components.code, {\n              children: \"Install 'cursor' command\"\n            }), \" (and optionally the \", _jsx(_components.code, {\n              children: \"code\"\n            }), \" command too which will override VS Code’s \", _jsx(_components.code, {\n              children: \"code\"\n            }), \" command)\"]\n          }), \"\\n\"]\n        })]\n      }), _jsx(Accordion, {\n        title: \"Unable to Sign In to Cursor\",\n        children: _jsx(_components.p, {\n          children: \"If you click Sign In on the General tab of Cursor’s Settings tab but are redirected to cursor.com and then return to Cursor still seeing the Sign In button, try disabling your firewall or antivirus software, which may be blocking the sign-in process.\"\n        })\n      })]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsx(MDXLayout, {\n    ...props,\n    children: _jsx(_createMdxContent, {\n      ...props\n    })\n  }) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n",
                                "frontmatter": {
                                },
                                "scope": {
                                    "config": {
                                        "theme": "mint",
                                        "$schema": "https://mintlify.com/docs.json",
                                        "name": "Cursor",
                                        "colors": {
                                            "primary": "#0C0C15",
                                            "light": "#ffffff",
                                            "dark": "#0C0C15"
                                        },
                                        "logo": {
                                            "light": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/logo/app-logo.svg",
                                            "dark": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/logo/app-logo.svg"
                                        },
                                        "favicon": "/images/logo/favicon.svg",
                                        "appearance": {
                                            "default": "light"
                                        },
                                        "background": {
                                            "color": {
                                                "light": "#fff",
                                                "dark": "#000000"
                                            }
                                        },
                                        "navbar": {
                                            "links": [
                                                {
                                                    "label": "Sign in",
                                                    "href": "https://cursor.com/settings"
                                                }
                                            ],
                                            "primary": {
                                                "type": "button",
                                                "label": "Download",
                                                "href": "https://cursor.com"
                                            }
                                        },
                                        "navigation": {
                                            "tabs": [
                                                {
                                                    "tab": "Documentation",
                                                    "icon": "book-open",
                                                    "groups": [
                                                        {
                                                            "group": "Get Started",
                                                            "pages": [
                                                                "welcome",
                                                                "get-started/installation",
                                                                "faq"
                                                            ]
                                                        },
                                                        {
                                                            "group": "Editor",
                                                            "pages": [
                                                                {
                                                                    "group": "Tab",
                                                                    "pages": [
                                                                        "tab/overview",
                                                                        "tab/from-gh-copilot",
                                                                        "tab/auto-import",
                                                                        "tab/advanced-features"
                                                                    ]
                                                                },
                                                                {
                                                                    "group": "Chat",
                                                                    "pages": [
                                                                        "chat/overview",
                                                                        "chat/agent",
                                                                        "chat/ask",
                                                                        "chat/manual",
                                                                        "chat/custom-modes",
                                                                        "chat/tools",
                                                                        "chat/apply"
                                                                    ]
                                                                },
                                                                {
                                                                    "group": "Inline Edit (⌘K)",
                                                                    "pages": [
                                                                        "cmdk/overview",
                                                                        "cmdk/terminal-cmdk"
                                                                    ]
                                                                },
                                                                "models",
                                                                "kbd",
                                                                {
                                                                    "group": "Features",
                                                                    "pages": [
                                                                        "more/ai-commit-message",
                                                                        "beta/notepads",
                                                                        "background-agent"
                                                                    ]
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            "group": "Context",
                                                            "pages": [
                                                                "context/codebase-indexing",
                                                                "context/rules",
                                                                "context/management",
                                                                {
                                                                    "group": "@ Symbols",
                                                                    "pages": [
                                                                        "context/@-symbols/overview",
                                                                        "context/@-symbols/@-files",
                                                                        "context/@-symbols/@-folders",
                                                                        "context/@-symbols/@-code",
                                                                        "context/@-symbols/@-docs",
                                                                        "context/@-symbols/@-git",
                                                                        "context/@-symbols/@-web",
                                                                        "context/@-symbols/@-definitions",
                                                                        "context/@-symbols/@-link",
                                                                        "context/@-symbols/@-lint-errors",
                                                                        "context/@-symbols/@-recent-changes",
                                                                        "context/@-symbols/@-cursor-rules",
                                                                        "context/@-symbols/@-notepads",
                                                                        "context/@-symbols/@-past-chats",
                                                                        "context/@-symbols/pill-files",
                                                                        "context/@-symbols/slash-commands"
                                                                    ]
                                                                },
                                                                "context/ignore-files",
                                                                "context/model-context-protocol",
                                                                "context/max-mode"
                                                            ]
                                                        },
                                                        {
                                                            "group": "Account",
                                                            "pages": [
                                                                "account/plans-and-usage",
                                                                {
                                                                    "group": "Business",
                                                                    "pages": [
                                                                        "account/teams/setup",
                                                                        "account/teams/members",
                                                                        "account/teams/analytics",
                                                                        "account/teams/sso"
                                                                    ]
                                                                },
                                                                "account/dashboard",
                                                                "account/billing",
                                                                "account/pricing",
                                                                "account/privacy"
                                                            ]
                                                        },
                                                        {
                                                            "group": "Settings",
                                                            "pages": [
                                                                "settings/api-keys",
                                                                "settings/beta"
                                                            ]
                                                        },
                                                        {
                                                            "group": "Troubleshooting",
                                                            "pages": [
                                                                "troubleshooting/common-issues",
                                                                "troubleshooting/troubleshooting-guide",
                                                                "troubleshooting/request-reporting"
                                                            ]
                                                        }
                                                    ],
                                                    "global": {
                                                        "anchors": [
                                                            {
                                                                "anchor": "Website",
                                                                "icon": {
                                                                    "style": "duotone",
                                                                    "name": "globe"
                                                                },
                                                                "href": "https://cursor.com/"
                                                            },
                                                            {
                                                                "anchor": "Forum",
                                                                "icon": {
                                                                    "style": "duotone",
                                                                    "name": "newspaper"
                                                                },
                                                                "href": "https://forum.cursor.com/"
                                                            },
                                                            {
                                                                "anchor": "Support",
                                                                "icon": {
                                                                    "style": "duotone",
                                                                    "name": "headset"
                                                                },
                                                                "href": "mailto:<EMAIL>"
                                                            }
                                                        ]
                                                    }
                                                },
                                                {
                                                    "tab": "Guides",
                                                    "icon": {
                                                        "style": "duotone",
                                                        "name": "book-open"
                                                    },
                                                    "groups": [
                                                        {
                                                            "group": "Core",
                                                            "pages": [
                                                                "guides/working-with-context",
                                                                "guides/selecting-models"
                                                            ]
                                                        },
                                                        {
                                                            "group": "Tutorials",
                                                            "pages": [
                                                                "guides/tutorials/web-development",
                                                                "guides/tutorials/architectural-diagrams"
                                                            ]
                                                        },
                                                        {
                                                            "group": "Advanced",
                                                            "pages": [
                                                                "guides/advanced/large-codebases",
                                                                "guides/advanced/working-with-documentation"
                                                            ]
                                                        },
                                                        {
                                                            "group": "Editor Migration",
                                                            "pages": [
                                                                "guides/migration/vscode",
                                                                "guides/migration/jetbrains"
                                                            ]
                                                        },
                                                        {
                                                            "group": "Languages \u0026 Frameworks",
                                                            "pages": [
                                                                "guides/languages/python",
                                                                "guides/languages/javascript",
                                                                "guides/languages/swift",
                                                                "guides/languages/java"
                                                            ]
                                                        }
                                                    ],
                                                    "global": {
                                                        "anchors": [
                                                            {
                                                                "anchor": "Website",
                                                                "icon": {
                                                                    "style": "duotone",
                                                                    "name": "globe"
                                                                },
                                                                "href": "https://cursor.com/"
                                                            },
                                                            {
                                                                "anchor": "Forum",
                                                                "icon": {
                                                                    "style": "duotone",
                                                                    "name": "newspaper"
                                                                },
                                                                "href": "https://forum.cursor.com/"
                                                            },
                                                            {
                                                                "anchor": "Support",
                                                                "icon": {
                                                                    "style": "duotone",
                                                                    "name": "headset"
                                                                },
                                                                "href": "mailto:<EMAIL>"}
                                                            ]
                                                        }
                                                    }
                                                ]
                                            },
                                            "footer": {
                                                "socials": {
                                                    "x": "https://x.com/cursor_ai",
                                                    "github": "https://github.com/getcursor/cursor/",
                                                    "website": "https://cursor.com"
                                                },
                                                "links": [
                                                    {
                                                        "header": "Product",
                                                        "items": [
                                                            {
                                                                "label": "Pricing",
                                                                "href": "https://www.cursor.com/pricing"
                                                            },
                                                            {
                                                                "label": "Downloads",
                                                                "href": "https://www.cursor.com/downloads"
                                                            },
                                                            {
                                                                "label": "Docs",
                                                                "href": "https://docs.cursor.com"
                                                            },
                                                            {
                                                                "label": "Forum",
                                                                "href": "https://forum.cursor.com"
                                                            }
                                                        ]
                                                    },
                                                    {
                                                        "header": "Company",
                                                        "items": [
                                                            {
                                                                "label": "Careers",
                                                                "href": "https://anysphere.inc"
                                                            },
                                                            {
                                                                "label": "About",
                                                                "href": "https://anysphere.inc"
                                                            },
                                                            {
                                                                "label": "Security",
                                                                "href": "https://cursor.com/security"
                                                            },
                                                            {
                                                                "label": "Privacy",
                                                                "href": "https://cursor.com/privacy"
                                                            }
                                                        ]
                                                    },
                                                    {
                                                        "header": "Resources",
                                                        "items": [
                                                            {
                                                                "label": "Terms",
                                                                "href": "https://cursor.com/terms-of-service"
                                                            },
                                                            {
                                                                "label": "Changelog",
                                                                "href": "https://cursor.com/changelog"
                                                            },
                                                            {
                                                                "label": "Twitter",
                                                                "href": "https://x.com/cursor_ai"
                                                            },
                                                            {
                                                                "label": "GitHub",
                                                                "href": "https://github.com/getcursor/cursor"
                                                            }
                                                        ]
                                                    }
                                                ]
                                            },
                                            "seo": {
                                                "metatags": {
                                                    "og:site_name": "Cursor",
                                                    "og:title": "Cursor - Build Software Faster",
                                                    "og:description": "Built to make you extraordinarily productive, Cursor is the best way to code with AI.",
                                                    "og:type": "website",
                                                    "og:url": "https://cursor.com",
                                                    "og:image": "/images/hero.png",
                                                    "og:locale": "en_US",
                                                    "og:logo": "/images/logo/app-logo.svg",
                                                    "article:publisher": "Anysphere Inc.",
                                                    "twitter:title": "Cursor - Build Software Faster",
                                                    "twitter:description": "Built to make you extraordinarily productive, Cursor is the best way to code with AI.",
                                                    "twitter:url": "https://cursor.com",
                                                    "twitter:image": "/images/hero.png",
                                                    "twitter:card": "summary_large_image",
                                                    "twitter:site": "@cursor_ai",
                                                    "og:image:width": "1200",
                                                    "og:image:height": "630"
                                                },
                                                "indexing": "navigable"
                                            },
                                            "styling": {
                                                "eyebrows": "breadcrumbs"
                                            },
                                            "redirects": [
                                                {
                                                    "source": "/ai-review",
                                                    "destination": "/chat/agent"
                                                },
                                                {
                                                    "source": "/background-agents",
                                                    "destination": "/background-agent"
                                                },
                                                {
                                                    "source": "/advanced/api-keys",
                                                    "destination": "/settings/api-keys"
                                                },
                                                {
                                                    "source": "/advanced/keyboard-shortcuts",
                                                    "destination": "/kbd"
                                                },
                                                {
                                                    "source": "/advanced/models",
                                                    "destination": "/settings/models"
                                                },
                                                {
                                                    "source": "/advanced/model-context-protocol",
                                                    "destination": "/context/model-context-protocol"
                                                },
                                                {
                                                    "source": "/billing/faq",
                                                    "destination": "/account/billing"
                                                },
                                                {
                                                    "source": "/forum",
                                                    "destination": "/resources/forum"
                                                },
                                                {
                                                    "source": "/composer/overview",
                                                    "destination": "/composer"
                                                },
                                                {
                                                    "source": "/get-started/usage",
                                                    "destination": "/account/usage"
                                                },
                                                {
                                                    "source": "/get-started/migrate-from-vs-code",
                                                    "destination": "/guides/migration/vscode"
                                                },
                                                {
                                                    "source": "/get-started/what-is-cursor",
                                                    "destination": "/welcome"
                                                },
                                                {
                                                    "source": "/plans/business/getting-started",
                                                    "destination": "/account/teams/setup"
                                                },
                                                {
                                                    "source": "/plans/business/roles",
                                                    "destination": "/account/teams/members"
                                                },
                                                {
                                                    "source": "/plans/business/sso",
                                                    "destination": "/account/teams/sso"
                                                },
                                                {
                                                    "source": "/plans/business/team-management",
                                                    "destination": "/account/teams/members"
                                                },
                                                {
                                                    "source": "/privacy/privacy",
                                                    "destination": "/account/privacy"
                                                },
                                                {
                                                    "source": "/settings/ide/features",
                                                    "destination": "/settings/preferences"
                                                },
                                                {
                                                    "source": "/settings/ide/models",
                                                    "destination": "/settings/models"
                                                },
                                                {
                                                    "source": "/settings/ide/overview",
                                                    "destination": "/settings/preferences"
                                                },
                                                {
                                                    "source": "/features/generate-commit-message",
                                                    "destination": "/more/ai-commit-message"
                                                },
                                                {
                                                    "source": "/features/beta/notepads",
                                                    "destination": "/beta/notepads"
                                                },
                                                {
                                                    "source": "/context/@-symbols/basic",
                                                    "destination": "/context/@-symbols/overview"
                                                },
                                                {
                                                    "source": "/get-started/resources",
                                                    "destination": "/resources/forum"
                                                },
                                                {
                                                    "source": "/agent",
                                                    "destination": "/chat/agent"
                                                },
                                                {
                                                    "source": "/composer",
                                                    "destination": "/chat/overview"
                                                },
                                                {
                                                    "source": "/chat/customize",
                                                    "destination": "/chat/overview"
                                                },
                                                {
                                                    "source": "/chat/codebase",
                                                    "destination": "/context/codebase-indexing"
                                                },
                                                {
                                                    "source": "/settings/preferences",
                                                    "destination": "/settings/beta"
                                                },
                                                {
                                                    "source": "/account/usage",
                                                    "destination": "/account/plans-and-usage"
                                                },
                                                {
                                                    "source": "/account/plans",
                                                    "destination": "/account/plans-and-usage"
                                                },
                                                {
                                                    "source": "/account/security",
                                                    "destination": "/account/privacy"
                                                },
                                                {
                                                    "source": "/settings/models",
                                                    "destination": "/models"
                                                },
                                                {
                                                    "source": "/context/rules-for-ai",
                                                    "destination": "/context/rules"
                                                },
                                                {"source": "/context/@-symbols/@-summarized-composers",
                                                "destination": "/context/@-symbols/@-past-chats"
                                            },
                                            {
                                                "source": "/",
                                                "destination": "/welcome"
                                            }
                                        ]
                                    },
                                    "pageMetadata": {
                                        "title": "Common Issues",
                                        "description": "Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs",
                                        "twitter:title": "Cursor – Common Issues",
                                        "twitter:description": "Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs",
                                        "og:title": "Cursor – Common Issues",
                                        "og:description": "Guide to resolving common Cursor issues including networking, resource usage, SSH connections, and general FAQs",
                                        "og:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/common-issues.png?v=1743819390998",
                                        "twitter:image": "https://mintlify.s3.us-west-1.amazonaws.com/cursor/images/og/troubleshooting/common-issues.png?v=1743819390998",
                                        "href": "/troubleshooting/common-issues"
                                    }
                                }
                            },
                            "entitlements": {
                                "AI_CHAT": {
                                    "status": "ENABLED"
                                },
                                "REMOVE_BRANDING": {
                                    "status": "ENABLED"
                                }
                            },
                            "gitSource": {
                                "type": "github",
                                "owner": "anysphere",
                                "repo": "cursor-docs",
                                "deployBranch": "main",
                                "contentDirectory": "",
                                "isPrivate": false
                            },
                            "banner": null,
                            "buildId": "683751d7b138e0efb84145c4:success"
                        },
                        "__N_SSG": true
                    },
                    "page": "/_sites/[subdomain]/[[...slug]]",
                    "query": {
                        "subdomain": "docs.cursor.com",
                        "slug": [
                            "troubleshooting",
                            "common-issues"
                        ]
                    },
                    "buildId": "xsFVauHHhYjewKg9sNuSJ",
                    "assetPrefix": "/mintlify-assets",
                    "isFallback": false,
                    "isExperimentalCompile": false,
                    "gsp": true,
                    "scriptLoader": [
                    ]
                }</script>
        </html>
        </body></html>
