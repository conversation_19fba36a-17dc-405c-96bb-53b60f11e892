# Cursor Agent Terminal Doesn't Work Well with Powerlevel10k + Oh-My-Zsh

I’m a macOS and Debian user, and my default shell environment is **Oh-My-Zsh** with the **Powerlevel10k** theme. While this setup works flawlessly in my native terminal, I’ve encountered a significant issue when using the **Cursor Agent**.

Whenever the agent tries to execute commands in the terminal (for example, during code explanation or command suggestions), the terminal session does **not automatically detect when a command has finished**. It just keeps waiting indefinitely. This only happens when Powerlevel10k is enabled.

I’ve tried creating a separate terminal profile with a more minimal Zsh setup and pointed to that profile in the global settings of Cursor, but it seems that **Cursor Agent still uses the default system profile**, not the one I explicitly set. The custom profile only works when I manually open the terminal from within Cursor, not when the agent runs commands automatically.

As a result, I’ve had to give up on using Powerlevel10k entirely just to get the agent to work—which is quite painful since I’ve customized my terminal environment heavily and rely on it daily.

Is anyone else experiencing this? Any workarounds or future fixes planned?

Here’s a cleaner solution that keeps Powerlevel10k while fixing Cursor Agent detection.

**Step 1:** Download shell integration

```bash
curl -L https://iterm2.com/shell_integration/zsh -o ~/.iterm2_shell_integration.zsh
```

**Step 2:** Add to `~/.zshrc` (only activates in Cursor Agent):

```bash
if [[ -n $CURSOR_TRACE_ID ]]; then
  PROMPT_EOL_MARK=""
  test -e "${HOME}/.iterm2_shell_integration.zsh" && source "${HOME}/.iterm2_shell_integration.zsh"
  precmd() { print -Pn "\e]133;D;%?\a" }
  preexec() { print -Pn "\e]133;C;\a" }
fi
```

**Step 3:** `source ~/.zshrc` and restart Cursor

This way you keep your beautiful P10k theme in normal terminal use, but Cursor Agent gets the proper command detection signals. The `CURSOR_TRACE_ID` check ensures it only affects Cursor sessions.


