Pre/post processors support adding databases. After selecting and connecting to a database, you can perform CRUD and more on the data tables. The results of these operations can be printed in the console or extracted as variables. These variables can then be used in various scenarios, such as parameters for other endpoint requests, assertions, custom scripts, and interacting with other databases.

## Getting started

Here are the steps to use database operations.

<Steps>
  <Step>
    In the Run tab (Design-first Mode) or the Request tab (Request-first mode), navigate to Post Processors.
      <Tabs>
  <Tab title="Design-first Mode">
<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/352194/image-preview)
</Background>
  </Tab>
  <Tab title="Request-first Mode">
<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/352193/image-preview)
</Background>
    
    </Tab>

</Tabs>
  </Step>
  <Step>
    Hover over "Add PostProcessor" and select "Database operation".
      
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/342780/image-preview" style="width: 640px" />
</p>
  </Step>
  <Step>
   Name the database operation and select a database connection.
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/342782/image-preview" style="width: 640px" />
</p>
  </Step>
  <Step>
Enter the SQL command. The command supports using `{{variables}}` within it.
For example, you can use SQL like this:

```SQL
SELECT * FROM User where username = '{{name}}'
```
  </Step>
  <Step>
You have the option to extract the results of the SELECT query as variables. Set "Extract Result To Variable". JSONPath is supported.

The SELECT statement retrieves an array of results where each row represents an element in the array. For example, using the JSONPath `$[0].uid` would extract the value of the "uid" field from the first row of the results.
  </Step>
  <Step>
Click Send to execute the request. You can view the database operation's results in the Console.
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/342783/image-preview" style="width: 640px" />
</p>
  </Step>
</Steps>

:::tip[]
Apidog supports standard SQL queries but does not accommodate complex SQL operations like stored procedures.
:::

## Database connections

Apidog Free supports connections to the following types of databases:

- MySQL
- SQLServer(Supports versions above [SQL Server 2014](https://sqlserverbuilds.blogspot.com/#sql2014x))
- Oracle
- PostgreSQL

After upgrading to the paid version, Apidog also supports connectivity to the following databases in addition:

- Clickhouse
- MongoDB
- Redis


:::tip[]
Connecting to an Oracle database in Apidog requires installing the [Oracle Client](apidog://link/pages/593551) separately.
:::

To set up a database connection in Apidog, follow the steps:

<Steps>
  <Step>
Navigate to Settings - Database Connections.
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342821/image-preview)
    </Step>
  <Step>
Click on "New" at the top right to create a new database connection entry.
    </Step>
  <Step>
Choose the database type from the available options and provide the necessary connection details for the database, such as host, port, database name, username, and password.
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/342822/image-preview" style="width: 640px" />
</p>
    </Step>
  <Step>
Besides connecting to a database locally with a username and password, you can establish a more secure connection through an SSH tunnel for added protection of the data being transmitted.
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/342823/image-preview" style="width: 640px" />
</p>
  </Step>
  <Step>
Click `Save`,and you'll be able to select this connection in Post processors.
    </Step>
</Steps>

:::tip[]
Apidog values your data security. Database address, port, username, password, and database name are only stored on the client side locally and are not synced to the cloud. Even within the same team, members do not sync their database connection information with each other, and each team member needs to manually set up their database.
:::

:::tip[MySQL Tips]

Currently, the latest mysql modules do not fully support the `caching_sha2_password` encryption method of MySQL8, where `caching_sha2_password` is the default encryption method.

Please use the method that requires you to specify the `mysql_native_password` mode to change the MySQL account password, use another tool to connect to MySQL, and then run the following SQL to change the password of the corresponding account.

```SQL
ALTER USER 'username'@'%' IDENTIFIED WITH mysql_native_password BY '123456'
```

Please replace username and password above manually.
:::

## Using database operations in multiple environments

When working with multiple environments such as testing and production environments, they often have different databases configured. If you are using database operations in your workflows, it implies that these operations need to switch along with the environment changes.

In such scenarios, you can configure multiple database connections under the "Database Connections" settings in Apidog. By setting up database connections for each environment, you can ensure that when switching between environments using the dropdown menu in the top right corner, the database queries will automatically be directed to the corresponding database connection associated with that environment. 
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/342824/image-preview" style="width: 640px" />
</p>

## Using NoSQL databases

For operations on MongoDB, refer to [MongoDB operations](apidog://link/pages/588760).

For operations on Redis, refer to [Redis operations](apidog://link/pages/593555).

## Using database operations in Apidog CLI

Apidog supports executing test scenarios on any platform using the Command Line Interface (CLI). By utilizing the CLI, you can fetch test scenario configurations from the server in real-time and run them efficiently. 

However, since database operations are locally stored, it means that the CLI cannot dynamically access your database configurations.

In such cases, you need to export your database operations as configuration files and place them on the machine where the CLI is being run. Learn more about [Apidog CLI](apidog://link/pages/605134).

## FAQ

**Q: Can I combine multiple SQL queries into a single database operation in Apidog?**

A: Each database operation in Apidog can only execute one SQL query. If you have multiple SQL queries to run, they need to be divided into separate database operations for execution.

MySQL is a widely used relational database management system known for structured data storage, high performance, and strong scalability.

Apidog supports connecting to and interacting with MySQL databases, allowing SQL statements to be executed either before or after an endpoint call.

## Connecting to the Database

<Steps>
  <Step>
  On the endpoint page, click "**Pre/Post Processors**" and select "**Database Operation**".

<Background>
![database-operation.png](https://api.apidog.com/api/v1/projects/544525/resources/356415/image-preview)
</Background>

  </Step>
  <Step>
Click the "**Database Connections**" dropdown and choose "**Manage Database Connection**", then click the "**+ New**" button at the top right corner.

<Background>
![manage-database-connection.png](https://api.apidog.com/api/v1/projects/544525/resources/356416/image-preview)
</Background>

  </Step>
  <Step>
In the popup window, select "**MySQL**" as the database type and fill in the connection details including host, port, user name, password, and database name. You can store these details in variables and test the connection. 
      
<Background>
![database-connection.png](https://api.apidog.com/api/v1/projects/544525/resources/356418/image-preview)
</Background>

  </Step>
</Steps>

:::tip[]
Apidog suggests that you:
1. store database connection info in Apidog Cloud for centralized management. 
2. use **Vault** variables to protect sensitive fields. 
3. avoid storing plaintext credentials in the "**Initial Value**" section of the environment variables.
:::

## Operating the Database

Apidog supports standard SQL queries, but does not support complex operations like stored procedures.

### SELECT

Run a query to fetch data from the database.

**Example:**

```sql
SELECT * FROM users WHERE id = 1;
```

You can also use variables:

```sql
SELECT * FROM users WHERE id = '{{user_id}}';
```

The query will return structured data as an array of objects — even if there's only one result:

```json
[
    {
        "id": 1,
        "name": "jack"
    }
]
```
You can extract fields using [JSONPath](apidog://link/pages/645606) and save them as variables:

- Variable name: `user_id`
- JSONPath: `$[0].id`

To save the entire result set:

- Variable name: `userList`
- JSONPath: `$`

<Background>
![extract-results-to-variable.png](https://api.apidog.com/api/v1/projects/544525/resources/356621/image-preview)
</Background>

### INSERT

Insert new records into the database.

Example:

```sql
INSERT INTO users (id, name, email) VALUES (1, 'Tom', '<EMAIL>');
```

Dynamic data supported:

```sql
INSERT INTO users (id, name) VALUES ('{{user_id}}', '{{user_name}}');
```

### DELETE

Delete records from the database.

Example:

```sql
DELETE FROM users WHERE id = 1;
```

Using a variable:

```sql
DELETE FROM users WHERE id = '{{user_id}}';
```

### UPDATE

Update existing records in the database.

Example:

```sql
UPDATE users SET name = 'Jerry' WHERE id = 1;
```

Supports dynamic update:

```sql
UPDATE users SET name = '{{new_name}}' WHERE id = '{{user_id}}';
```

## Usage in Automated Test

Database operations are supported during automated test, and SQL statements in test steps can use dynamic value expressions and variables.

### Using Dynamic Value Expressions

You can retrieve pre-step data using dynamic values in SQL statements:

```sql
SELECT * FROM products WHERE id = '{{$.1.response.body.products[0].id}}'
```

<Background>

![01-apidog.gif](https://api.apidog.com/api/v1/projects/544525/resources/356618/image-preview)
</Background>


### Using Variables

Extract a variable (e.g., `products_id`) from previous test step:

<Background>
![extract-variable.png](https://api.apidog.com/api/v1/projects/544525/resources/356428/image-preview)
</Background>

Then reference them as `{{variables}}` in SQL for the next test steps: 

```sql
SELECT * FROM products WHERE id = '{{products_id}}'
```

### Using ForEach Loop

You can use **ForEach loops** in test steps to perform batch database operations.

**Example 1: Query by Field in a Loop**

```sql
SELECT * FROM products WHERE id = '{{$.4.element.id}}';
```

<Background>
![Query by Field in a Loop.png](https://api.apidog.com/api/v1/projects/544525/resources/356619/image-preview)
   
</Background>

*Note:* `4` is the ID of the ForEach step — replace it with the correct ID in your workflow.

You can use dynamic values to fetch the expression:

<Background>

![02-apidog.gif](https://api.apidog.com/api/v1/projects/544525/resources/356620/image-preview)
</Background>


**Example 2: Insert Multiple Fields in a Loop**

```sql
INSERT INTO products (id, title) VALUES ('{{$.4.element.id}}', '{{$.4.element.title}}');
```

This is especially helpful when you need to insert multiple records in batch or verify if certain data already exists in the database.

## FAQs

<Accordion title="How to fix authentication errors when connecting to MySQL 8?" defaultOpen>

The latest MySQL module in Apidog does not fully support the `caching_sha2_password`, the default authentication method in MySQL 8.

To work around this, you need to specify the `mysql_native_password` mode to modify the MySQL password: Connect to MySQL using other tools and run the following SQL command:

```SQL
ALTER USER 'username'@'%' IDENTIFIED WITH mysql_native_password BY '123456'
```

Replace `username` and `123456` with your actual username and password.
</Accordion>

:::info[]
Connecting to the MongoDB database is a paid feature. Check [Pricing Details](https://apidog.com/pricing/) to learn more.
:::

MongoDB is a document-oriented database management system belonging to non-relational databases (NoSQL). It aims to provide scalable high-performance data storage solutions for web applications. Unlike relational databases (SQL), MongoDB does not use SQL statements to operate on the database, but uses database commands (Database Commands) or simpler and more user-friendly methods of adding, deleting, querying and modifying data. Further reading: [*MongoDB Documentation*](https://www.mongodb.com/docs/).

## Connect to the database

1. When adding database operations, click `Database Connetions` and select `Manage Database Connection`.

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/342907/image-preview)

2. Click the "New" button in the upper right corner.

![](https://assets.apidog.com/uploads/help/2023/12/01/0c06fd5ccfd2c3eee29f4fda4faad92e.png)

3. Select `MongoDB` database type, then fill in the corresponding connection information.

![](https://assets.apidog.com/uploads/help/2023/12/01/e86e418e35b9811b43e43d4a386e37bb.png)

:::tip
Apidog attaches importance to your data security. The **database address**, **port**, **username**, **password**, **database name** are only stored locally on the client and will not be synced to the cloud. Even within the same team, connection information for databases will not be synced between members. Each team member needs to manually set up the database connection.
:::

## Operate the database

MongoDB stores data in BSON document format. For convenience of most user's usage habits, when modifying MongoDB databases in Apidog, submit JSON format files. The system will automatically map each field to the corresponding BSON data type based on the actual content of the JSON.

Among them, the `_id` field is relatively special. According to MongoDB specifications, each document must have an `_id` field as the primary key, and the default data type of this field is `ObjectId` rather than `String`.

If you need to declare an `_id` field of type `ObjectId`, you can use the conventional string format. If the string content conforms to the `ObjectId` format, Apidog will automatically map it to the `ObjectId` type in BSON.

Assume there is such a BSON document in MongoDB now:

```bson
{
    _id: ObjectId('6569a36afcf5ee79c08d2f94'),
    name: "Apidog"
}
```

So when using Apidog to query the document via `_id`, the JSON value that needs to be entered in the "Query Conditions" is:

```json
{
    "_id": "6569a36afcf5ee79c08d2f94"  
}
```

### Common operations

For common CRUD operations, support via visual API operations. No need to write any JavaScript code, just specify the action in the "Operation Type", then specify the "Collection Name", and then write the corresponding content in JSON in "Query Conditions".

For example, for the query operation mentioned above, you can view the queried document in the console after entering the command and enabling "Console Log".

![](https://assets.apidog.com/uploads/help/2023/12/01/0aa793e9610aa0bfc0ba1e98b81be936.png)

### Advanced commands

If you need more advanced operations, you can also directly run database commands. In "Operation Type", select "Run Database Command" and then enter the database command in JSON format. Note that the database command does not refer to method calls like `db.collection.findOne()`, but JSON data in a specific format.

For example, to query the number of documents in the users collection, you can use the `count` database command:

```json
{
    "count": "users"
}
```

After entering the command, you can view the result in the console.

![](https://assets.apidog.com/uploads/help/2023/12/01/7d56893affe5eeea3a807b891745327e.png)

:::info[]
Connecting to the Redis database is a paid feature. Check [Pricing Details](https://apidog.com/pricing/) for details.
:::

Redis (Remote Dictionary Server) is a **non-relational database (NoSQL)** that provides key-value pair (Key-Value) storage capabilities. Redis keeps data in memory and can ensure fast reading and writing speeds while supporting persistence, which is particularly suitable for applications requiring high performance caching, such as chat rooms, leaderboards, and real-time operating systems.

## Connect to the database

1. Click on "Pre/Post Processors" in the API and select "Database Operation".

![](https://assets.apidog.com/uploads/help/2023/12/01/bfda81ae012bb9d7e86fa6a5ba109136.png)

1. Click on the "Manage Database Connection" dropdown on "Database Connection" and then click the New button in the upper right corner.

![](https://assets.apidog.com/uploads/help/2023/12/01/fc172d1a1d465897c926c8c8d67759f6.png)

3. Select the "Redis" database type and then fill in the corresponding connection information.

![](https://assets.apidog.com/uploads/help/2023/12/01/239b6032dd1450b6dccc6b9360d94de6.png)

:::tip
Apidog attaches importance to your data security. The **database address**, **port**, **username**, **password**, **database name** are only stored locally on the client and will not be synced to the cloud. Even within the same team, connection information for databases will not be synced between members. Each team member needs to manually set up the database connection.
:::

## Operate the database

### Common operations

For common database CRUD operations, Apidog provides a visual API. No coding or Redis commands are needed - just select an operation type in "Operation" and fill in the relevant content in the form to send the request. For example, to retrieve a single element from the database list, refer to the figure below to select the operation type and click Send.

With "Console Log" enabled, the result can be viewed in the console.

![](https://assets.apidog.com/uploads/help/2023/12/01/7c483c982c92177cf56b3beb979a5006.png)

### Advanced commands

If more advanced operations are needed, Apidog also supports directly running Redis advanced commands. Switch to the "Run Redis Command" tab and input the specific Redis command to run. For the querying example above, you can also directly run the equivalent Redis command to get the result:

![](https://assets.apidog.com/uploads/help/2023/12/01/cc31e36bae13fbd2b22b01305a1c762f.png)

Connecting to an Oracle database in Apidog requires installing the Oracle Client separately.

## Windows

1. Visit the [Oracle Download Page](https://www.oracle.com/database/technologies/instant-client/downloads.html), download the compatible macOS version `Instant Client ZIP` file.
2. Extract the ZIP file to a designated directory, such as `C:\oracle\instantclient_19_3`.
3. Add the aforementioned directory to the environment variable `PATH` for detailed instructions.
4. Restart Apidog.

## MacOS

1. Click [here](https://assets.apidog.com/app/static/apidog-oracle-instantclient_19_8-macos-x64.zip) to download the macOS version file.
2. Extract the ZIP file to a designated directory, such as `~/oracle/instantclient_19_8/`.
3. Execute the following command to create a symbolic link from the `libclntsh.dylib` file in the designated directory to `/usr/local/lib`:
   `ln -s ~/oracle/instantclient_19_8/libclntsh.dylib /usr/local/lib`
4. Restart Apidog.

## Linux

Visit the [Oracle Download Page](https://www.oracle.com/database/technologies/instant-client/downloads.html), select the appropriate system version, and download the corresponding Instant Client file. It is recommended to follow the official instructions provided by Oracle for your specific Linux distribution.

