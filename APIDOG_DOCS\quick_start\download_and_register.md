Download & Register
Download
We recommend using the Apidog client, which is a full-featured, integrated API collaboration platform. With the Apidog client, you can:

Send requests

Design APIs

Debug APIs

Test APIs

Use mock data

Publish API documentation

You can choose your system and download the corresponding installation package here:
https://apidog.com/download/

Apidog is compatible with 32-bit and 64-bit Windows, MacOS for both Intel and Mac chips, as well as Linux systems.

Register
After downloading and installing the Apidog client, open it.

screenshot-********-182923.png
You'll need to create an Apidog account. Apidog is a collaborative tool that stores data in the cloud. This means you'll need an internet connection and an account to access your data.

You can sign up with your Google or Github account, or use your email.

In the lower-left corner of the registration screen, you can switch between English and Japanese. If you're on an intranet and need to use a proxy, you can configure that in the lower-left corner as well.

Role and Mode
After you sign up, you'll see Apidog's main interface. If this is your first time logging in, you'll need to select your role on the development team and your preferred work mode. Choose the options that best reflect your situation.

screenshot-********-183435.png
If you're still learning and don't fit into any of the roles, try selecting 'Fullstack developer' and 'API design first'.

screenshot-********-183454.png
Click 'Go Explore' to start using Apidog.

Next Step
Now, you can specify your first endpoint in Apidog.

