# Use chat in NotebookLM

After uploading your sources, you can:

- Ask the model questions about your source material.
- Give the model instructions to perform actions.

NotebookLM uses direct quotes, text, and images straight from your sources as citations to answer your questions and perform actions. These citations help you check the accuracy of the response. You can hover over any citation to get the full quoted text right away. If you select a citation, NotebookLM automatically navigates to the location of the quote, so you can easily view it in context.

In your notebook, you can use the checkbox on each source to include or exclude certain sources the model should use to answer your question.

**Tip:** Chat responses in NotebookLM only use data from your sources. If you explicitly ask the model to do something more creative, like “rewrite the end of my short story,” you may receive, “NotebookLM can’t answer this question” as a chat response. Try to rephrase or ask a different question.

## Use advanced chat settings in NotebookLM

[With Pro capabilities in NotebookLM](https://support.google.com/notebooklm/answer/16206866), you can customize your chat responses.

1. In the “Chat” panel, navigate to Configure Chat ![](https://storage.googleapis.com/support-kms-prod/ajU5sFaysHTQ3vXOolWStr5UPbvv8a8SZU4Y).
2. Select a conversational style.
   - **Default:** Best for general purpose research and brainstorming tasks.
   - **Analyst:** Best for business-oriented strategy and decision-making.
   - **Guide:** Best for sharing your notebook as a group knowledge base or a help center.
   - **Custom:** Best for choosing a specific style ("Respond like a PhD student”) or suggesting a role ("Pretend to be a role-playing game host.").
3. Select a response length.
   - **Default**
   - **Longer**
   - **Shorter**
4. Select **Save**.

**Tips:**

- To pin NotebookLM chat responses to the noteboard for your reference later, select **Save to note**.
  - When you save a response as a note, the original format—including tables and clickable inline citations—gets saved.
- To clear chat history, select Refresh ![](https://storage.googleapis.com/support-kms-prod/k1xWxrfawIN9OOez2PhIJDfZSVFeMAIzl7nm) ![and then](https://lh3.googleusercontent.com/3_l97rr0GvhSP2XV5OoCkV2ZDTIisAOczrSdzNCBxhIKWrjXjHucxNwocghoUa39gw=w36-h36) **Continue**.
- In chat, to generate a note, select **Add a note**. You can also access features like **Audio Overview** and **Mind Maps** in chat.
