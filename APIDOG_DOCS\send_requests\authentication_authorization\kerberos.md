Kerberos
Kerberos is a network authentication protocol initially developed by the Massachusetts Institute of Technology (MIT) and widely used in many modern computing systems, especially in enterprise environments. Kerberos utilizes symmetric encryption and a trusted third party, known as the Key Distribution Center (KDC), to perform its functions.

When using Windows Authentication in IIS, Kerberos will be used preferentially.

Kerberos Authentication

Prerequisites
To use Apidog's Kerberos authentication feature, please ensure that your computer meets the following conditions:

For Windows: Successfully joined to a domain or possesses a valid Kerberos credential.

For macOS: Possesses a valid Kerberos credential.

For Linux: Possesses a valid Kerberos credential.

Additionally, this feature is only supported in the Apidog client version and is not available in the web version.

Configuration
The Kerberos authentication parameters are as follows:

SPN

The format is HTTP/<EMAIL>. For specific details, please consult the provider of the endpoint.

