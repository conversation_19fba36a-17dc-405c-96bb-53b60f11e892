# Documentation as Code: why you need it and how to get started

## What is Documentation as Code (DaC)?

Documentation as Code (DaC) is a revolutionary methodology in which technical writers and developers create and maintain documentation using the same processes and tools as software code development. By adopting this approach, DaC promotes the creation of comprehensive, up-to-date, and accurate documents, by embracing version control, automated testing, and continuous delivery mechanisms typically used in coding. As a result, documentation remains synchronized with code updates, improving clarity, and reducing discrepancies. Owing to these benefits, DaC is swiftly gaining traction among software teams worldwide, marking a significant evolution in documentation practices.

Using a DaC approach reduces manual work, allowing documentation writers to focus on creating accurate, usable information. It also helps ensure all your documentation shares a consistent structure and language. Another aspect of the DaC process is the ability to automatically test for formatting and style errors and ensure a new version of the documentation is ready for deployment.

This is part of a series of articles about [code documentation](https://swimm.io/learn/code-documentation/code-documentation-benefits-challenges-and-tips-for-success).

## Benefits of Documentation as Code

DaC methods provide the following advantages.

### Keeping documentation and code in sync

When a page in the documentation is outdated, it can confuse new employees, remote workers, or end users, impacting productivity. If you keep documentation in a separate system, development teams often forget about it – in the worst case, there is no clear source of truth for documentation.

Disorganized documentation can increase technical debt. Keeping documentation next to code can help keep it up to date. Developers can update the docs when they change the code – the local availability of documentation also allows developers to update and search sections themselves, rather than rely on a technical writer. However, this still requires ongoing vigilance by developers to update the documentation. Documentation as code solutions like Swimm can help reduce this burden by updating documentation automatically. Learn more below.

### Using the same tools to review documentation and code

Most organizations manage code changes with pull request workflows – developers submit changes for review before publishing to the code base. The code reviews aim to promote collaboration while standardizing workflows and maintaining code quality.

With DaC, you use the same system to review documentation. If the team fails to update the documentation when making a change, the code review should catch it. You could use documentation previews for all pull requests to help reviewers check the documentation’s accuracy.

### Writer-developer collaboration

Dedicated technical writers should use the same methods and tools as the developers to ensure collaboration. Writers and developers must work together on complex projects to keep documentation in sync and allow writers to review changes by developers. A shared workflow helps reduce friction and keep everyone on the same page.

### Automated documentation checks

Publishing documentation as part of the CI/CD pipeline lets you automate documentation checks. These may include identifying broken links, style errors, or formatting issues.

### Documentation versioning

Documentation versioning is important if your project has multiple software versions that are used in parallel – it is critical to clarify which version the documentation relates to. Most documentation tools have versioning features, but it is still time-consuming to keep documentation in sync. With DaC, you branch the documentation when you branch the code, so versioning is done automatically.

### Tips from the expert

![Omer Rosenbaum](https://swimm.io/wp-content/webp-express/webp-images/uploads/2024/09/Omer-Rosenbaum-253x300.png.webp)

Omer Rosenbaum

CTO & Co-founder at Swimm

1.

Integrate documentation updates into code reviews Make documentation a part of every code review to ensure it reflects all changes and additions in the codebase.

2.

Set up automated linting for doc consistency Use tools like Vale or custom CI scripts to check for style, grammar, and terminology consistency in documentation files.

3.

Use branching for versioned docs Branch documentation updates with feature branches, so any changes align with specific releases or updates.

## Docs as Code workflow

Here is an outline of the Docs as Code approach in practice, including the tools and processes for implementing DaC in your organization. You can implement this workflow as part of your developer experience strategy.

### 1. Plain text authoring

The Integrated Development Environment (IDE) is developers’ most widely used tool, including for documentation. Development teams should write all documentation in plain text for easy reading and editing in the IDE. It is common to use simple languages like Markdown to delineate formatting (learn more below).

There are three key advantages to the plain text approach:

- A unified tool and language allow developers to find code easily and avoid distracting context switches.
- Plain text is compatible with every IDE and easy for humans to read. Most operating systems support plain-text files, and many open source tools allow non-technical individuals to work with them.
- Plain-text documentation helps focus on technical content without distractions like layout considerations. Formatting should be basic, with features such as headings, tables, text highlighting, hypertext links, bullets, and numbers.

### 2. Source control

Source control is essential for keeping track of changes, including who made them and when. Development teams should be familiar with a version control system (VCS) that monitors application code, so you can use the same VCS for writing and storing documentation. You can store documentation in a dedicated repository or distribute it together with the relevant source code.

Git-based versioning platforms like GitHub and GitLab encourage collaboration between developers and writers, by showing the changes made to every file, with a dedicated space for discussing and explaining the changes. Teams can make granular decisions to accept or reject changes to the documentation.

Another benefit of a versioning tool is the ability to work asynchronously with multiple contributors to one file. Branches allow individual team members to create their file versions and merge changes without impacting other contributors or the original file. A versioning platform also provides an interface that facilitates conflict reporting and resolution.

### 3. Publishing

After writing, versioning, and saving the documentation, you can publish it – ideally in a plain-text format. Creating static web pages with a Static Site Generator (SSG) makes it easier to convert plain-text files to HTML. SSGs let you enhance your brand using CSS stylesheets and add dynamic JS sections. Everything is rendered on the server side.

With an SSG, you can decouple content from the final graphics. Developers only handle technical content, and another team can handle the visuals. This approach saves developers time because they don’t need to address formatting. It also makes editing easier because the style applies to all pages.

SSGs usually offer features to enhance your content’s usability without requiring extra intervention by developers. These may include search bars, navigation menus, a homepage, a contents page, and documentation displays for previous versions.

### 4. Automation

Another advantage of DaC is that it supports automation the same way you might automate a CI/CD pipeline for source code. Continuous integration practices let you build automated tests to check content whenever someone makes a Pull or Merge request. These tests identify formal and technical errors in the documentation (e.g., invalid links, typos, incorrect punctuation).

You can then leverage continuous delivery (CD) to automate the detection and publishing of changes in the main branch – this saves time when publishing new content. Combining CI and CD automation helps speed up the overall documentation process and reduce errors.

## DaC and Markdown

DaC writing usually involves using Markdown or a similar text-based language. This type of language can be useful for short and simple documents or documentation open to developer contributions. However, Markdown or an equivalent language is not enough for longer, more complex documents or documentation that need to follow a specific pattern. One example is a schema that gives you granular control over the document structure.

Complex documentation often requires stronger link handling, smarter search and replace capabilities, and tighter CMS integration. Most documentation-writing features and tools are not easily available in Markdown, static site generators, or standard Markdown editors.

A Help Authoring Tool (HAT) or documentation tool suite can better enforce standards templates (e.g., DITA, DocBook) and style guidelines (e.g., cross-reference formats, layouts). Many tools include or integrate a content management system (CMS) to provide more robust content reuse and chunking capabilities.

There is a tradeoff between keeping documentation simple, and enabling simple plaintext authoring with markdown, and using a HAT to enable richer documentation, but requiring a more complex workflow.

## Documentation as Code with Swimm

While DaC is great in keeping documentation next to the code, and part of the development flow, it does not guarantee that documentation will remain up to date as the code changes. Any team practicing DaC will tell you that while this method is superior to keeping docs on an external system like Notion and Confluence in terms of freshness, many documents still become stale over time.

Also, keeping docs as part of your code means you can search it with your regular code search tools. However, textual search is still limiting, and team members are often not sure if there’s documentation that can help them solve a particular challenge, and how to find it, and may not even search for the documentation to begin with.

Swimm is a Continuous Documentation platform based on Documentation as Code. Swimm documentation is saved as regular Markdown files within the code. However, Swimm has introduced three novel capabilities to this methodology:

- **Swimm can identify and sync documentation automatically** as relevant code changes. This is done by integrating a Swimm app into CI.
- **Swimm IDE plugins prompt devs about docs** next to relevant parts of their code, so they can find them when they need them.
- **Swimm enables remote editing and indexing** using a Workspace for all collaborators. This way, even if docs are saved in different repositories, they can be searched, indexed by tags, edited remotely and more – features that are unavailable in traditional DaC.
