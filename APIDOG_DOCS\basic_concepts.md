This article introduces some core concepts in Apidog. Many of these concepts may differ from similar products (such as Postman). Understanding these definitions and differences will help you better comprehend Apidog's workflow.

## Design-first Mode & Request-first Mode

Apidog's APIs module features two modes that can be switched at the bottom left corner of the interface: `Design-first Mode` and `Request-first Mode`.
<Background>
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/342735/image-preview" style="width: 640px" />
</p>
</Background>
Both modes provide similar functionalities but with different interfaces, catering to different team workflows. 

**Design-first Mode** is Apidog's recommended mode, suitable for teams following the API-Design First approach. In this mode, teams spec the API first and then proceed with development and testing based on the API spec. 

On the other hand, **Request-first Mode** is ideal for teams that do not initially define the API specifications. These teams typically focus on backend development, finalize the code, and then produce the API spec for testing and client-side work to begin. 

If you need to call APIs developed by someone else but you don't have the documentation, you should also use Request-first Mode.

:::tip[]
Learn more about [Design-first Mode & Request-first Mode](apidog://link/pages/541775).
:::


## Endpoint

Apidog is an API-first product, meaning everything starts with defining APIs, and Endpoints are the core elements in APIs.

In Apidog's main interface, Endpoints are basic elements, grouped together in a directory form. For each Endpoint, you can [modify its definition](apidog://link/pages/533932), preview it, send requests based on this endpoint spec, or save requests as [endpoint cases](apidog://link/pages/541771).

This structure is very different from Postman and is more like an extension based on OAS - an API spec that can directly debug and save requests.

In Postman, the basic element is a request, and requests are essentially separate from API specs. This means that when the API spec changes, all requests and scripts need to be rewritten.

In Apidog, all endpoint cases (corresponding to Postman's requests) are based on API specs. When API specs change, endpoint cases naturally change, and all test scenarios and CI/CD based on this can be automatically or manually updated, which is very suitable for development teams to maintain and update APIs.

## Request

In Apidog, you can also [create Requests](apidog://link/pages/626721). Requests don't need to be based on endpoint specs, and their definition is the same as Postman requests. You can also [parse a successful request into an endpoint spec](apidog://link/pages/629856).

## Test scenario

When you need to send a batch of requests together (similar to running a Postman Collection), you'll need to use a Test scenario.

A test scenario includes a series of requests. These requests can be [imported into the test scenario from endpoint specs or endpoint cases](apidog://link/pages/599311), and can be [automatically or manually updated as the API spec changes](apidog://link/pages/603709).

Test scenarios also support [logic components](apidog://link/pages/599419) such as If, for, forEach, etc. You can [pass data between requests](apidog://link/pages/601617), [dynamically generate request parameters](apidog://link/pages/541766), and more.
Based on test scenarios, you can also [view test reports](apidog://link/pages/603898), [run performance tests](apidog://link/pages/603638), [manage test data](apidog://link/pages/602987), [integrate CI/CD](apidog://link/pages/609698), and more.

## Environment

[Environments](apidog://link/pages/577823) in Apidog are similar to environments in Postman, containing many variables. You can use different values of the same environment variable when switching environments.

However, Apidog's environments also include another important concept: [services](apidog://link/pages/584758). Each service corresponds to a prefix URL. Because Endpoints defined in OAS all start with /, they can be sent to different services. In Apidog, you don't need to write `{{Base_url}}` at the beginning of requests, storing services in the form of variables. You just need to switch to the corresponding environment, and all requests will be sent directly to the corresponding service in the environment.

## Project

A collection of endpoints, requests, schemas, environments, test scenarios, etc., forms a project. In Apidog, Project is the basic unit of collaboration.

A Project corresponds to an OAS definition. You can also [import an OAS as a project](apidog://link/pages/633036) or [export an OAS from a project](apidog://link/pages/635117).
Compared to Apidog, an Apidog Project roughly corresponds to a Postman Collection, while an Apidog Team corresponds to a Postman Workspace.