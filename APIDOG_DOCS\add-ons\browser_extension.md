## Browser Extension Limitations and Solutions

Browser security policies impose certain restrictions on extensions, limiting their functionality in specific scenarios. Below are the key limitations and their workarounds.

### Restrictions in Browser Extensions

1. **Forbidden Request Header**

Some headers, such as `<PERSON><PERSON>`、`Host`、`Origin`、`Content-Length` are automatically blocked by browsers. ([View the full list here.](https://developer.mozilla.org/en-US/docs/Glossary/Forbidden_request_header)).

2. **Cookie Limitations**  
  - Cross-origin requests will be blocked from carrying cookies unless CORS credentials support is configured.
  - Browser extensions cannot directly read or manipulate cookies (including automatically attaching cookies to requests).

3. **Special Request Types**  

`GET`/`HEAD`methods cannot include a request body, and local code/database calls are restricted.

### **Alternative Solutions**

✅ Use [Apidog Desktop Client](https://apidog.com/download)(No restrictions)
✅ Enable [Cloud Agent](apidog://link/pages/835152)(Bypasses browser limitations)


## Installation Guide

### Install from Chrome Web Store

<Steps>
  <Step>
    Visit the [Chrome Web Store page](https://chromewebstore.google.com/detail/apidog-browser-extension/dmhljjnonlhapikmelaefohecogokhio)
  </Step>
  <Step>
    Click `Add to Chrome`
  </Step>
</Steps>

## Permission Configuration (Required in Some Cases)

If a request fails, you may need to manually allow the extension to access specific permissions:
 
1. Go to `chrome://extensions` and find the Apidog extension.
2. Click "Details" and under the "Permissions", add:

```
http://apidog.com/*
http://app.apidog.com/*
https://apidog.com/*
https://app.apidog.com/*
```

<Background>
![apidog-browser-extension-permission-settings.png](https://api.apidog.com/api/v1/projects/544525/resources/352872/image-preview)
</Background>

## FAQs

<Accordion title="Why can't certain headers be sent?" defaultOpen>
Browsers automatically block certain headers(e.g., Cookie). To bypass this, use the [Apidog Desktop Client](https://apidog.com/download) or configure the [Cloud Agent](apidog://link/pages/835152).
</Accordion>


<Accordion title="Why does a cross-origin request return 403/Forbidden?" defaultOpen={false}>
1. Check if the server explicitly rejected the request (e.g., authentication failure, IP restrictions).
2. Configure [CORS Proxy](apidog://link/pages/780225)
3. Verify if the backend has CORS headers configured, for example:
```http
Access-Control-Allow-Origin: https://your-domain.com
Access-Control-Allow-Credentials: true
```
</Accordion>

<Accordion title="Why is the GET request body removed?？" defaultOpen={false}>
Browser standards prohibit `GET` and `HEAD` requests from containing a body. Use `POST` instead or switch to the desktop client for debugging.
</Accordion>

<Accordion title="Need to call local code/database？" defaultOpen={false}>
Browser sandboxing does not support this. You need to use the [Apidog Desktop Client](https://apidog.com/download).
</Accordion>


## Learn More


<Card title="CORS Proxy" href="apidog://link/pages/780225">
</Card> 

<Card title="Request proxy in Apidog web" href="apidog://link/pages/835152">
</Card>

<Card title="Request proxy in shared docs" href="apidog://link/pages/835153">
</Card>

<Card title="Request proxy in Apidog client" href="apidog://link/pages/835154">
</Card>

## Browser Extension Limitations and Solutions

Browser security policies impose certain restrictions on extensions, limiting their functionality in specific scenarios. Below are the key limitations and their workarounds.

### Restrictions in Browser Extensions

1. **Forbidden Request Header**

Some headers, such as `Cookie`、`Host`、`Origin`、`Content-Length` are automatically blocked by browsers. ([View the full list here.](https://developer.mozilla.org/en-US/docs/Glossary/Forbidden_request_header)).

2. **Cookie Limitations**  
  - Cross-origin requests will be blocked from carrying cookies unless CORS credentials support is configured.
  - Browser extensions cannot directly read or manipulate cookies (including automatically attaching cookies to requests).

3. **Special Request Types**  

`GET`/`HEAD`methods cannot include a request body, and local code/database calls are restricted.

### **Alternative Solutions**

✅ Use [Apidog Desktop Client](https://apidog.com/download)(No restrictions)
✅ Enable [Cloud Agent](apidog://link/pages/835152)(Bypasses browser limitations)

## Installation Guide

    Visit the [Apidog browser extension page on Microsoft Edge](https://microsoftedge.microsoft.com/addons/detail/apidog-browser-extension/baclgjhlijboldpfeeanjjlkldmeeinn?hl=en-US&gl=JP) to directly `Get` the extension.

<Background>
![get-apidog-extension-microsoft-edge.png](https://api.apidog.com/api/v1/projects/544525/resources/352878/image-preview)
</Background>

## Permission Settings

Some browser versions require manual permission adjustments, otherwise requests may fail.

<Background>
![apidog-edge-extension-permission-settings.png](https://api.apidog.com/api/v1/projects/544525/resources/352882/image-preview)
</Background>


## FAQs

<Accordion title="Why can't I install successfully?" defaultOpen>
Check if the extension is compatible with your browser version. Try updating Microsoft Edge to resolve the issue.
</Accordion>

<Accordion title="Why can't certain headers be sent?" defaultOpen={false}>
Browsers automatically block certain headers(e.g., Cookie). To bypass this, use the [Apidog Desktop Client](https://apidog.com/download) or configure the [Cloud Agent](apidog://link/pages/835152).
</Accordion>

<Accordion title="Why does a cross-origin request return 403/Forbidden?" defaultOpen={false}>
1. Check if the server explicitly rejected the request (e.g., authentication failure, IP restrictions).
2. Configure [CORS Proxy](apidog://link/pages/780225)
3. Verify if the backend has CORS headers configured, for example:
```http
Access-Control-Allow-Origin: https://your-domain.com
Access-Control-Allow-Credentials: true
```
</Accordion>

<Accordion title="Why is the GET request body removed?？" defaultOpen={false}>
Browser standards prohibit `GET` and `HEAD` requests from containing a body. Use `POST` instead or switch to the desktop client for debugging.
</Accordion>

<Accordion title="Need to call local code/database？" defaultOpen={false}>
Browser sandboxing does not support this. You need to use the [Apidog Desktop Client](https://apidog.com/download).
</Accordion>

## Learn More

<Card title="CORS Proxy" href="apidog://link/pages/780225">
</Card> 

<Card title="Request proxy in Apidog web" href="apidog://link/pages/835152">
</Card>

<Card title="Request proxy in shared docs" href="apidog://link/pages/835153">
</Card>

<Card title="Request proxy in Apidog client" href="apidog://link/pages/835154">
</Card>