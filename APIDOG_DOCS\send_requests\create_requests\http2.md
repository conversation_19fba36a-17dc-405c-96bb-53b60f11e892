HTTP/2
HTTP/2 is the latest version of the Hypertext Transfer Protocol (HTTP). It is a major update to the HTTP/1.1 protocol that offers better performance, security, and features.

Turn On Compatibility Switch
You can request HTTP/2 APIs directly within an HTTP project, but you need to ensure that the HTTP/2 compatibility switch is enabled in "Settings" → "Feature Settings" → "Advanced Settings."


After enabling the compatibility switch, when requesting HTTP/2 APIs, <PERSON><PERSON><PERSON> will automatically upgrade the request protocol to HTTP/2.

HTTP/2 sample API: https://http2.pro/api/v1


More Settings
You can manually switch relevant settings in "Settings" → "Advanced Settings." It is recommended to use <PERSON><PERSON><PERSON>'s default configuration to ensure compatibility.

After enabling the compatibility switch, <PERSON><PERSON><PERSON> will automatically upgrade the request protocol to HTTP/2 while requesting HTTP/2 APIs.

Complete configuration options:

HTTPS

HTTP/2 ALPN: Default option, <PERSON><PERSON><PERSON> will attempt to establish a connection using the HTTP/2 protocol and send requests using the HTTP/2 protocol. If the API does not support it, it will automatically fall back to HTTP/1.1.

HTTP/1.1: Use the original HTTP connection method and do not use the HTTP/2 protocol.

HTTP

HTTP/1.1: Default option, establish a connection using the HTTP/1.1 protocol and send requests using the HTTP/1.1 protocol.

HTTP/2 Prior Knowledge: Establish a h2c connection. If the API does not support the HTTP/2 protocol, the connection will fail directly without automatically back to HTTP/1.1.

