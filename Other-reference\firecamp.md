<br/><br/>

<p align="center">
<a href="https://firecamp.dev">
  <img src="https://github.com/firecamp-dev/firecamp/blob/main/.github/logo.svg" alt="Firecamp Logo" width="70">
</a>
</p>

<h3 align="center"><b>Firecamp</b></h3>
<h4 align="center"><b>Open Source Postman Alternative</b></h4>

<p align="center">
   <a href="https://discord.gg/8hRaqhK"><img alt="Discord online members" src="https://badgen.net/discord/members/8hRaqhK?color=5865F2&label=Discord&style=for-the-badge" /></a>
   <a href="https://github.com/firecamp-dev/firecamp/stargazers"><img src="https://img.shields.io/github/stars/firecamp-dev/firecamp" alt="GitHub Stars"></a>
   <a href="https://github.com/firecamp-dev/firecamp/pulse"><img src="https://img.shields.io/github/commit-activity/y/firecamp-dev/firecamp" alt="Commits-per-month"></a>
</p>

<br/>

# Firecamp

Firecamp is a dx-first API development platform that helps developers design, develop, test, and document their APIs effortlessly. With a user-friendly interface and a range of powerful features, it streamlines the API development workflow and enhances collaboration among team members.

- 🌈  best-in-class developer experience inspired by vscode dx. <br/>
- 📡 multi-protocol testing capabilities with Rest, GraphQL, Websocket, and SocketIO APIs. <br/>
- 👐 collaborate on API collections across the workspace and team <br/>
- ⛺ build APIs faster without switching between tools and apps. documentation, cli, ci/cd under one roof <br/>
  

👉 Web: https://firecamp.dev

## Download Firecamp Desktop Application

1. [Firecamp for MacOS Intel](https://fcamp.co/mac)
2. [Firecamp for  MacOS Silicon](https://fcamp.co/mac-silicon)
3. [Firecamp for Windows](https://fcamp.co/win-x64)
4. [Firecamp for Linux AppImage](https://fcamp.co/linux-appImage)

<br/>


<p align="center">
  <a href="https://firecamp.dev">
  <img alt ="firecamp-cover" src="https://github.com/firecamp-dev/firecamp/assets/5078921/1ef25fd3-bf97-4bd0-b440-7c1f4ef3eb22"/>
  </a>
</p>

## 🚀 Getting started with Firecamp
To get started with Firecamp, follow these steps:

1. Sign in to Firecamp at https://firecamp.dev
2. Follow the [Getting Started](https://firecamp.io/docs) guide from the documentation.
3. Start developing, and testing your APIs using Firecamp.

Read the Firecamp doc for in-depth walk-throughs on functionality to understand the various features and capabilities of Firecamp. 


## 🔬 Features

Firecamp offers you a broad range of features that will help you build APIs faster than ever.

📂 [Collection](https://firecamp.io/docs/platform/collection): 
Manage Collection of APIs to collaborate within the team <br/>

⛳ [API Playgrounds](https://firecamp.io/docs/rest/introduction): 
Get instant playgrounds for your APIs. Rest, GraphQL, and more <br/>

♻️ [Team Collaboration](https://firecamp.io/docs/collaboration/getting-started): 
Collaborate with your team at a centralized shared workspace <br/>

🔐 [Authentication](https://firecamp.io/docs/platform/authentication): 
Test and Debug your auth endpoints with a range of supported auths <br/>

📠 [Scripts](https://firecamp.io/docs/platform/scripts): 
Interpret the API with pre-request and tests scripts <br/>

🔩 [Dynamic Variables](https://firecamp.io/docs/platform/environment): 
Set values in variables to reuse in the whole platform dynamically <br/>

📡 [Firecamp Web](https://firecamp.dev): 
Get quick access to the web platform <br/>


## 🏕️ API Playgrounds
There is a dedicated GUI playground for each API protocol you would be using for your stack.

1. [**Rest Playground**](https://firecamp.io/docs/rest/introduction) 
  It's the lightweight, IntelliSense, and next-generation testing client to give the most delightful collaborative experience while building APIs in a team. 

2. [**GraphQL Playground**](https://firecamp.io/docs/graphql/introduction) 
  Perform GraphQL operations with a seamless experience. Prepare, Share, and Export your Query Collection collaboratively with your team.

3. [**WebSocket Playground**](https://firecamp.io/docs/websocket/introduction)  
  Debug bidirectional connection visually with WebSocket GUI client more precisely now. The only GUI client to test, debug, and visualize real-time or event-driven messages collaboratively. 
 
4. [**Socket.io Playground**](https://firecamp.io/docs/socket-io/introduction) 
  Monitor each emitter’s and listener’s events visually over a bi-directional SocketIO connection. It enables you to collaborate with the team over a SocketIO event-driven API build.
 
## 🛣️  Roadmap

🛰️ Self-hosted: Host Firecamp on your own server

🔁 CLI & CI/CD: Run API Collection Tests in the terminal or set in CI/CD pipeline <br/>

➿  API Test Runner:  Run the API Collection visually within the platform </br>

📄  API Documentation:  Publish beautiful API documentation for your team and community </br>

⚛️ Artificial Intelligence: Cutting-edge AI-powered capabilities </br>

⚓ SSL: Use custom SSL certificates to test out the SSL-secured API endpoints </br>

📝  Proxy: Setup proxy while running requests from Firecamp </br>

ℹ️ History: Seamless history tracking


## 🏆 Community and Support

For community support, you can join the Firecamp community on [Discord](https://discord.gg/8hRaqhK). You can also check out the [Firecamp Blog](https://firecamp.io/blog) for the latest updates, tutorials, and articles.

Here are helpful links for Firecamp
- [Documentation](https://firecamp.io/docs)
- [Discord community](https://discord.gg/8hRaqhK)
- [Twitter updates](https://twitter.com/FirecampDev)

If you encounter any bugs, or issues, or have suggestions, please open an issue on the [Firecamp GitHub repository](https://github.com/firecamp-dev/Firecamp). We appreciate your contributions to making Firecamp even better.

## 💭 Philosophy
The decentralization of tools, processes, and people creates friction in API development workflow and frustration across the team. Developers are constantly switching between tools, searching for information, and wasting time due to inefficient tools.
We believe that the existing solution lacks both world-class experience and the simplicity needed to build APIs faster. This leads to a decrease in developer productivity, slow release times, and poor team collaboration.
We are bringing the VS code philosophy to Firecamp to solve this critical problem. Our mission is to build the most extensible, minimal, and best developer experience API platform where developers feel most productive while building APIs.

## 🏄  What's cool about this?

Firecamp is a Multi-protocol API platform, which offers end-to-end testing and development of Rest, GraphQL, WebSocket, and many other APIs.
We support major API specifications and protocols, which allows for seamless API testing, API documentation, and smoother collaboration between backend, frontend, and mobile teams.


## ✍️ Contribution

Here is the [guide for contributions](https://github.com/firecamp-dev/firecamp/blob/main/CONTRIBUTING.md) to help you begin your journey in contributing and our [list of open issues](https://github.com/firecamp-dev/firecamp/issues) for more information.

We appreciate all your contributions to Firecamp 🤗

Here are a few ways:

- Star this repo.
- Create issues every time you feel something is missing or goes wrong.
- Upvote issues with 👍 reaction so we know what the demand for a particular issue is to prioritize it within the roadmap.

## 💎 All Thanks To Our Contributors

<a href="https://github.com/firecamp-dev/firecamp/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=firecamp-dev/firecamp" />
</a>
