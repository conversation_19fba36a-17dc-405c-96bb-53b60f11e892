There are three methods that People can use Google VEO 3 for free:

## Method 1: Free Access via Student Promo

 University students can enjoy the upgraded Gemini experience for free for 15 months where they can get access to Veo 3 for free. There's a student promo going on right now that gives you 15 months of access. it gives you access to video generation through vo3 today. l'm going to walk you through every single step to get this for yourself the first thing we need is a student email. head over to [the site]([https://tempemail.id](https://tempemail.id)) first you'll need to complete a quick verification to prove you're human then enter any information you want for your username let's create it now grab that email address you just generated we're going to head over to the Google Al platform and sign up using that email paste in the email go through the sign up process and just like that we're subscribed we now have access to all of these pro features including what we came for— Vo3.

Here is a little tip for those who are not students, they may get a temporary education email at https://tempemail.id and get verify for student eligibility, which can help with the student verification to get the Veo 3 for free. And if you are from contries not eligiable for the free plan, use VPN and get yourself on the avaiable country.

A reference of what features are in Student free plan:

Original price US$19.99/month, current price~~US$19.99/month~~ Only US$0 until June 2026

Must re-verify student status before the end of August 2025

Using Google AI to make learning easier

Analyze entire textbooks up to 1,500 pages with Gemini Pro

Use Veo 3 (limited access) to create high-quality video clips

Convert your course materials into engaging podcasts in NotebookLM Pro and enjoy higher limits

Use Gemini in Gmail, Google Docs, and more to polish your emails and resumes

Be the first to experience new features of Google AI

2 TB of storage and other Google One Premium benefits




This offer expires June 30, 2025 and is only available in Brazil, Indonesia, Japan, the United Kingdom, and the United States for verified students 18+. You must provide a valid payment method. Age limits, system requirements, and other [restrictions](https://support.google.com/googleone/answer/15639789) may apply , and some languages ​​may not be supported.



## Method 2: For those who are not students or don't have an education email, they can subscribe to Google AI plan for free for one month to enjoy Gemini Pro and other benefits

A reference of what features are in Student free plan:

by Original price US$19.99~~$19.99~~US$0 for 1 month, then US$19.99/month

Get more access to Google's most powerful models

Use Veo 3 (limited access) to create high-quality video clips

Unlock premium features (including Deep Research)

Decipher large books and reports, uploading files up to 1,500 pages

Experience our most powerful AI model yet

This Google One subscription also offers

**Flow:** Get access to AI film-making tools like Veo 3 (limited access) for cinematic scenes and stories

**Whisk:** Higher limits when converting images to videos using Veo 2

**NotebookLM:** Our research and writing assistant quota has been increased by 5 times

Gemini in Gmail, Google Docs, Google Videos, and more

**2 TB** of total storage space

Other Premium Benefits

Explore options

Compare various [Google AI solutions](https://one.google.com/ai?g1_last_touchpoint=61) and find the one that suits you



## Method 3: Google Cloud $300 Free Trial




Google Cloud offers a 300-dollar free trial for new users, which can be used to access multiple Google AI tools including Veo 3 through Vertex AI. During the trial period, the $300 credit can be used for API calls and free video generation.

Quickstart: Generate text using the Vertex AI Gemini API

## Prerequisites

Completing this quickstart requires you to:

- Set up a Google Cloud project and enable the Vertex AI API
- On your local machine:
  - Install, initialize, and authenticate with the Google Cloud CLI
  - Install the SDK for your language

### Set up a Google Cloud project

Set up your Google Cloud project and enable the Vertex AI API.

1. Sign in to your Google Cloud account. If you're new to Google Cloud, [create an account](https://console.cloud.google.com/freetrial) to evaluate how our products perform in real-world scenarios. New customers also get $300 in free credits to run, test, and deploy workloads.
2. In the Google Cloud console, on the project selector page, select or create a Google Cloud project.
3. [Make sure that billing is enabled for your Google Cloud project](https://cloud.google.com/billing/docs/how-to/verify-billing-enabled#confirm_billing_is_enabled_on_a_project).
4. Enable the Vertex AI API.

### Set up the Google Cloud CLI

On your local machine, set up and authenticate with the Google Cloud CLI. If you are familiar with the Gemini API in Google AI Studio, note that the Vertex AI Gemini API uses Identity and Access Management instead of API keys to manage access.

1. [Install and initialize](https://cloud.google.com/sdk/docs/install) the Google Cloud CLI.

2. If you previously installed the gcloud CLI, ensure your `gcloud` components are updated by running this command.
   
   gcloud components update

3. To authenticate with the gcloud CLI, generate a local Application Default Credentials (ADC) file by running this command. The web flow launched by the command is used to provide your user credentials.
   
   gcloud auth application-default login
   
   For more information, see [Set up Application Default Credentials](https://cloud.google.com/docs/authentication/provide-credentials-adc).

### Set up the SDK for your programming language

On your local machine, click one of the following tabs to install the SDK for your programming language. (Use Python as the example)

Gen AI SDK for Python:

Install and update the Gen AI SDK for Python by running this command.

```
pip install --upgrade google-genai
```

## Send a prompt to the Vertex AI Gemini API

Use the following code to send a prompt to the Vertex AI Gemini API. This sample returns a [list of possible names](https://cloud.google.com/vertex-ai/generative-ai/docs/prompt-gallery/samples/ideation_creative_naming_49) for a specialty flower store.

You can run the code from the command line, by using an IDE, or by including the code in your application.

#### Install

pip install --upgrade google-genai

To learn more, see the [SDK reference documentation](https://googleapis.github.io/python-genai/).

Set environment variables to use the Gen AI SDK with Vertex AI:

```
# Replace the `GOOGLE_CLOUD_PROJECT` and `GOOGLE_CLOUD_LOCATION` values
# with appropriate values for your project.
export GOOGLE_CLOUD_PROJECT=GOOGLE_CLOUD_PROJECT
export GOOGLE_CLOUD_LOCATION=global
export GOOGLE_GENAI_USE_VERTEXAI=True
```

```
from google import genai
from google.genai.types import HttpOptions

client = genai.Client(http_options=HttpOptions(api_version="v1"))
response = client.models.generate_content(
    model="gemini-2.0-flash-001",
    contents="How does AI work?",
)
print(response.text)
# Example response:
# Okay, let's break down how AI works. It's a broad field, so I'll focus on the ...
#
# Here's a simplified overview:
# ... 
```


