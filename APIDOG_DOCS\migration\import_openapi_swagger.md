Import OpenAPI (Swagger) spec
Apidog supports importingjson oryaml files inOpenAPI 3 andSwagger-2 data formats.

Ifsummary,operationId andpath parameters are all written within the same Swagger file, then during the import, the endpoint name will be set according to the priority ofsummary > operationId > path.

Manual Import
Open theSettings and click onImport Data to select either file import or URL import.

Manual Import - File Import
You can either drag and drop yourJSON orYAML files into the area shown below, or simply click the area to browse and select the desired file from your system’s file manager.

manual-import-openapi-specification-upload.png
Manual Import - URL Import
When using URL import, please provide the direct URL to theJSON orYAML data file, not the Base URL of the Swagger UI.

swagger
manual-import-openapi-specification-url.png
Import - Advanced Settings
The imported OpenAPI/Swagger specifications include only endpoints, schemas, and environments.

importing-openapi-specification-01.png
When importing a file, you can set up the following options:

1.
Forder: Support importing files into specific folders.

importing-openapi-specification-02.png
2.
Overwrite: When two files have the same Method and Path, the newer file will overwrite the older file.

importing-openapi-specification-03.png
3.
Merge: When two files have the same Method and Path, any modified information from the old file, such as Mock rules, parameter descriptions, request examples, and response examples, will be retained.

4.
Overwrite Selected Fields: When two files have the same Method and Path, you can choose to overwrite specific fields. Other unselected fields will retain the existing data in Apidog.

Overwrite Selected Fields

5.
Ignore: When two files have the same Method and Path, the new file will not be imported.

6.
Keep Both: When two files have the same Method and Path, the new file will be imported, and the old file will not be deleted.

7.
Import Cases: When this option is enabled, the test cases under the selected endpoints will be automatically selected by default. You can also manually choose the corresponding test cases in the import preview.

Schemaneed to be configured separately, including the selection of overwrite modes and the import folders.

importing-openapi-specification-04.png
To align the structure of your existing endpoints in Apidog with the folder structure of an imported Swagger file, simply enable theAnd update endpoint folder option. This will automatically adjust the current folder structure to match the imported file.

importing-openapi-specification-05.png
Scheduled Import
Open theSettings panel, go toImport Data, and click onScheduled. You can select multiple data sources here and synchronize them to a specific folders at regular intervals.

TIP
TheImport Dataoption is only visible and accessible to project admins.

automatic-import.png


