Managing Projects
Create a project
To customize a project, the team owner/administrator can edit the project name or icon in the "Basic Settings" section under "Settings".



Project overview
In an existing project, you can view key information and statistics on theOverviewpage underAPIs.

Endpoint case/Test scenario stats
This section displays all the endpoints of the current project and provides statistics on testing coverage. It shows the percentage of endpoints that have associated test steps, as well as the percentage that have been tested within test scenarios. These statistics are valuable for evaluating the thoroughness of the API testing and their overall reliability.

test-coverage-data.png
Core data:

Coverage of endpoint case: The number of endpoints with associated cases / Total number of endpoints. This is mainly used to check if individual endpoints have corresponding cases.

Average cases per endpoint: The total number of cases / Total number of endpoints. This is mainly used to assess the overall completeness of endpoint cases.

Endpoints without cases: This shows the number of endpoints that have no cases.

Coverage of test scenario: The number of endpoints covered in test scenarios / Total number of endpoints. This is mainly used to assess the extent to which endpoints are covered by automated tests.

Endpoints not covered by any test scenario: This shows the number of endpoints that are not yet covered by automated tests.

TIP
Data calculation is asynchronous, so there may be a delay. If discrepancies occur, refresh after a moment to confirm.

Edit the project
The team owner/administrator enters the created project and changes the project name or project icon in " Settings" > "Basic Settings".



Clone the project
To clone a project from current team to another team, click "Settings" > "Basic Settings" > "Clone Project".



Move the project
To move a project to another team, click "Settings" > "Basic Settings" > "Move Project".



Delete the project
To delete a project completely, go to "Settings" > "Basic Settings" > "Delete".



You must be cautious - projects deleted will not be retrievable.

