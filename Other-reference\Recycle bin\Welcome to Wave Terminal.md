# Welcome to Wave Terminal

Wave is an [open-source](https://github.com/wavetermdev/waveterm) terminal that combines traditional terminal features with graphical capabilities like file previews, web browsing, and AI assistance. It runs on MacOS, Linux, and Windows.

Modern development involves constantly switching between terminals and browsers - checking documentation, previewing files, monitoring systems, and using AI tools. Wave brings these graphical tools directly into the terminal, letting you control them from the command line. This means you can stay in your terminal workflow while still having access to the visual interfaces you need.

Check out [Getting Started](https://docs.waveterm.dev/gettingstarted) for installation instructions.

![Wave Screenshot](https://docs.waveterm.dev/assets/images/wave-screenshot-993da899ca60c328d188d6f441a594ea.webp)

[

Customization

Set up tabs and terminals to match your workflow needs.

](https://docs.waveterm.dev/customization)[

Key Bindings

Boost efficiency with keyboard shortcuts for faster navigation.

](https://docs.waveterm.dev/keybindings)[

Layout

Organize your workspace using our layout system.

](https://docs.waveterm.dev/layout)[

Remote Connections

Quickly SSH or connect to remote machines in one step.

](https://docs.waveterm.dev/connections)[

Widgets

Explore built-in tools to extend your terminal’s functionality.

](https://docs.waveterm.dev/widgets)[

wsh Command

Control Wave and launch widgets directly from the command line.

](https://docs.waveterm.dev/wsh)

info

If you have a question, please feel free to ask us in [Discord](https://discord.gg/XfvZ334gwU). If you'd like to file a bug/enchancement, please use [Github Issues](https://github.com/wavetermdev/waveterm/issues). These docs are also open-source and we do accept PRs for docs [here](https://github.com/wavetermdev/waveterm/blob/main/docs). You can click the "Edit this page" link at the bottom of the page to get taken directly to the editor page for that document in GitHub.

## Roadmap

Wave is constantly improving! Our roadmap will be continuously updated with our goals for each release. You can find it [here](https://github.com/wavetermdev/waveterm/blob/main/ROADMAP.md).

Want to provide input to our future releases? Connect with us on [Discord](https://discord.gg/XfvZ334gwU) or open a [Feature Request](https://github.com/wavetermdev/waveterm/issues/new/choose)!

## Links

- **Homepage** [https://waveterm.dev](https://waveterm.dev/)
- **Download** [Download — Wave Terminal](https://waveterm.dev/download)
- **Discord** https://discord.gg/XfvZ334gwU
- **GitHub** https://github.com/wavetermdev/waveterm/

## Looking for WaveLegacy documentation?

WaveLegacy docs can be found at [legacydocs.waveterm.dev](https://legacydocs.waveterm.dev/).



# Getting Started

Wave Terminal is a modern terminal that includes graphical capabilities like web browsing, file previews, and AI assistance alongside traditional terminal features. This guide will help you get started.

## Installation

macOSLinuxWindows

### Platform requirements

- Supported architectures: x64
- Supported OS version: Windows 10 1809 or later, Windows 11

note

ARM64 is planned, but is currently blocked by upstream dependencies (see [Windows ARM Support](https://github.com/wavetermdev/waveterm/issues/928)).

### Package managers

#### Windows Package Manager

```
winget install CommandLine.Wave
```

#### Chocolatey

```
choco install wave
```

You can also download installers directly from our [Downloads page](https://www.waveterm.dev/download).

## Core Concepts

### Tabs and Blocks

- **Tabs**: Like browser tabs, these help organize your work. Create new tabs with 
  
  AltT
  
  .

- **Blocks**: The building blocks of Wave. Each block can be a terminal, web browser, file preview, AI chat, or other widget.

- **Layout**: Blocks can be dragged, dropped, and resized to create your ideal layout.

### Key Features

1. **Terminal Features**
   
   - Works with common shells (bash, zsh, fish)
   - Supports standard terminal features (readline, control sequences, etc)
   - Includes the `wsh` command for interacting with Wave's GUI features
   - GPU accelerated (on most platforms)

2. **Graphical Widgets**
   
   - Preview files (images, video, markdown, code with syntax highlighting)
   - Browse web pages
   - Ask questions and get AI help directly from the terminal (set up multiple AI models)
   - Basic system monitoring graphs

3. **Remote Connections**
   
   - Easy SSH connections with the connection button 
   - WSL integration on Windows
   - Consistent experience across local and remote sessions

## Quick Start Guide

1. **Open Your First New Tab**
   
   - New Wave tabs start with a single terminal block
   
   - Use it just like your regular terminal
   
   - Create additional terminal blocks with 
     
     AltN

2. **Try Some Basic Commands**
   
   ```
   # View a file or directory
   wsh view ~/Documents
   
   # Open a webpage
   wsh web open github.com
   
   # Get AI assistance
   wsh ai "how do I find large files in my current directory?"
   ```

3. **Customize Your Layout**
   
   - Drag block headers to rearrange them
   - Hover between blocks to resize them
   - Right-click tab headers for background options
   - Right-click block headers for block-specific options

4. **Connect to Remote Machines**
   
   - Click the  button
   - Enter `username@hostname` for SSH connections
   - Or select a WSL distribution on Windows

## Next Steps

- Explore [Key Bindings](https://docs.waveterm.dev/keybindings) to work more efficiently
- Learn about [Tab Layouts](https://docs.waveterm.dev/layout) to organize your workspace
- Set up [Custom Widgets](https://docs.waveterm.dev/customwidgets) for quick access to your tools
- Configure [AI Presets](https://docs.waveterm.dev/ai-presets) to use your preferred AI models
- Check out [Configuration](https://docs.waveterm.dev/config) for detailed customization options

## Getting Help

- Join our [Discord community](https://discord.gg/XfvZ334gwU) for help and discussions
- Report issues on [GitHub](https://github.com/wavetermdev/waveterm/issues)
- Check our [FAQ](https://docs.waveterm.dev/faq) for common questions

# Key Bindings

Here's the set of default keybindings available in Wave. It is split into sections. Some keybindings are always active. Others are only active for certain types of blocks.

Note that these are the MacOS keybindings (they use "Cmd"). For Windows and Linux, replace "Cmd" with "Alt" (note that "Ctrl" is "Ctrl" on both Mac, Windows, and Linux).

Chords are shown with a + between the keys. You have 2 seconds to hit the 2nd chord key after typing the first key. Hitting Escape after an initial chord key will always be a no-op.

## Global Keybindings

macOSLinuxWindows

| Key                      | Function                                                                                                                                               |
| ------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------ |
| AltT                     | Open a new tab                                                                                                                                         |
| AltN                     | Open a new block (defaults to a terminal block with the same connection and working directory). Switch to launcher using `app:defaultnewblock` setting |
| AltD                     | Split horizontally, open a new block to the right                                                                                                      |
| Alt⇧D                    | Split vertically, open a new block below                                                                                                               |
| Ctrl⇧S<br><br>+<br><br>↑ | Split vertically, open a new block above                                                                                                               |
| Ctrl⇧S<br><br>+<br><br>↓ | Split vertically, open a new block below                                                                                                               |
| Ctrl⇧S<br><br>+<br><br>← | Split horizontally, open a new block to the left                                                                                                       |
| Ctrl⇧S<br><br>+<br><br>→ | Split horizontally, open a new block to the right                                                                                                      |
| Alt⇧N                    | Open a new window                                                                                                                                      |
| AltW                     | Close the current block                                                                                                                                |
| Alt⇧W                    | Close the current tab                                                                                                                                  |
| AltM                     | Magnify / Un-Magnify the current block                                                                                                                 |
| AltG                     | Open the "connection" switcher                                                                                                                         |
| AltI                     | Refocus the current block (useful if the block has lost input focus)                                                                                   |
| Ctrl⇧                    | Show block numbers                                                                                                                                     |
| Ctrl⇧1-9                 | Switch to block number                                                                                                                                 |
| Ctrl⇧↑→↓←                | Move left, right, up, down between blocks                                                                                                              |
| Ctrl⇧K                   | Replace the current block with a launcher block                                                                                                        |
| Alt1-9                   | Switch to tab number                                                                                                                                   |
| Alt[                     | Switch tab left                                                                                                                                        |
| Alt]                     | Switch tab right                                                                                                                                       |
| AltCtrl1-9               | Switch to workspace number                                                                                                                             |
| Alt⇧R                    | Refresh the UI                                                                                                                                         |
| Ctrl⇧I                   | Toggle terminal multi-input mode                                                                                                                       |

## File Preview Keybindings

| Key                   | Function                                                                                           |
| --------------------- | -------------------------------------------------------------------------------------------------- |
| [text]                | Any regular character (e.g. "a", "b") will filter the file list                                    |
| Esc                   | Clears the filter                                                                                  |
| ↑<br><br> / <br><br>↓ | Change file selection up/down                                                                      |
| Enter                 | Open the currently selected file/directory                                                         |
| Alt↑                  | Move "up" a directory (parent directory)                                                           |
| Alt←                  | Back, move to the previously selected file/directory                                               |
| Alt→                  | Forward (opposite of back)                                                                         |
| AltO                  | Open a new file (accepts relative paths to the current directory)                                  |
| AltS                  | When file editor is open, save file                                                                |
| AltE                  | For files that can be previewed or edited (markdown, CSVs), switches between preview and edit mode |
| AltR                  | When file editor is open, revert changes                                                           |

## Web Keybindings

| Key  | Function                                                      |
| ---- | ------------------------------------------------------------- |
| AltL | Focus the URL input bar                                       |
| Esc  | When the URL input bar is focused, will focus the web content |
| AltR | Reload webpage                                                |
| Alt← | Back                                                          |
| Alt→ | Forward                                                       |
| AltF | Find in webpage                                               |
| AltO | Open a bookmark                                               |

## WaveAI Keybindings

| Key  | Function      |
| ---- | ------------- |
| AltL | Clear AI Chat |

## Terminal Keybindings

| Key    | Function         |
| ------ | ---------------- |
| Ctrl⇧C | Copy             |
| Ctrl⇧V | Paste            |
| AltK   | Clear Terminal   |
| AltF   | Find in Terminal |

## Customizeable Systemwide Global Hotkey

Wave allows setting a custom global hotkey to focus your most recent window from anywhere in your computer. For more information on this, see [the config docs](https://docs.waveterm.dev/config#customizable-systemwide-global-hotkey).

# Workspaces

Workspaces are a powerful way to organize your workflows into separate environments, which you can tailor and optimize.

## Workspace Switcher

![Workspace switcher screenshot](https://docs.waveterm.dev/assets/images/workspace-switcher-3f5c63d8e175a38a1090f205d8180f86.png#right)

The primary mechanism to interact with workspaces is via the Workspace Switcher, located to the left of the tab bar.

This is where you can create a new workspace, edit how a workspace entry appears, and delete a workspace.

The Workspace Switcher button changes to display the icon and color of the active workspace. If the current workspace is not saved, it will display the  icon. Clicking the button will open the Workspace Switcher.

The Switcher contains a list of all saved workspaces for your installation, each with a customizable icon, icon color, and name.

The active workspace for the current window will have a  next to it. Any workspace that is currently open in another window will have the  icon next to it.

Hovering over a workspace in the switcher will display a  icon, which will open an editor pane when clicked, in which you can change the workspace name, icon, and icon color. You can also delete a workspace from this pane.

## Creating a new workspace

Every new window is initialized with a blank workspace containing a single tab with a single terminal block inside it. There are three ways to create a new workspace:

1. Create a new window, either via `File` app menu or using the [keybinding](https://docs.waveterm.dev/keybindings#global-keybindings). This will create a new window and a new workspace within that.
2. Create a new workspace via the `Workspace` app menu. This will create a new workspace and switch the current window to that workspace.
3. If you are on a saved workspace, you can click the "Create new workspace" button at the bottom of the Workspace Switcher. This will create a new workspace and switch the current window to that workspace.

## Saving a workspace

info

A new workspace is ephemeral. When a window closes, its workspace, along with all its tabs, is deleted unless the workspace is saved.

The exception to this rule is the last window will be preserved when closed and will be reopened next time you open the app, regardless of whether the workspace is saved.

To preserve a new workspace, you must save it. This can be acheived by clicking the "Save workspace" button at the bottom of the Workspace Switcher.

If you instead see "Create new workspace" at the bottom of the Workspace Switcher, you are already in a saved workspace. You can also confirm this by checking the wording at the top of the Workspace Switcher. For an unsaved workspace, you will see "Open workspace"; for a saved workspace, you will see "Switch workspaces". You can also confirm this because the icon for the Workspace Switcher button will be .

Once a workspace is saved, you will see a new entry in the Workspace Switcher list for your saved workspace. It will be named `New Workspace (<random string>)`. To make the most of your workspace, is recommended to change this name, and the icon and icon color, to something more memorable or meaningful.

## Switching workspaces

There are two ways to switch workspaces:

1. From an open window, you can open the Workspace Switcher and click on a workspace from the list.
2. From the Workspace app menu, click on a workspace from the list.

If the workspace is already open in another window (it has the  next to it if you are in the Workspace Switcher), that window will take focus.

If the workspace is not open, your current window will switch to it. If your current workspace is unsaved, you will be prompted whether you want to open the new workspace in a new window or whether you want to open it in the current window. **If you choose the latter option, the current workspace and its contents will be deleted.**

The Workspace Switcher button will update with the colored icon for your new active workspace.

## Edit a workspace

info

The tabs, layouts, and terminal and AI histories of a [saved workspace](https://docs.waveterm.dev/workspaces#saving-a-workspace) are persisted automatically, however if you have unsaved file changes in an editor or a webpage, your progress will be lost when you close the window.

To update the name, icon, and icon color of a workspace, hover over the workspace in the Workspace Switcher and click the  button that appears. This will open an editor pane, where you can make your changes. They are persisted and updated automatically.

# Connections

Wave allows users to connect to various machines and unify them together in a way that preserves the unique behavior of each. At the moment, this extends to SSH remote connections, local WSL connections, and AWS S3 buckets.

## Access a Connection in a Block

The easiest way to access connections is to click the  icon. From there, you can type one of the following to depending on the connection you want:

For SSH Connections:

- `[user]@[host]`
- `[host]`
- `[user]@[host]:[port]`

For WSL Connections:

- `wsl://<distribution name>`

For AWS S3 Connections:

- `aws:[profile]`

Alternatively, if the connection already exists in the dropdown list, you can either click it or navigate to it with arrow keys and press enter to connect.

![a dropdown showing a list of connections that already exist](https://docs.waveterm.dev/assets/images/connection-dropdown-1ef30ec5e9fbaaa1a11dfac4ce39036b.png)

## Different Types of Connections

As there are several different types of connections, not all of the types have access to the same features. For instance, AWS S3 connections can only be used in preview widgets (directory, image viewer, code editor, etc.). Meanwhile, SSH and WSL connections can always work in terminal widgets, and if `wsh` shell extensions are installed, they can also work in preview widgets and the sysinfo widget.

As such, certain features will not be available for certain types of connections. As an example, AWS S3 connections cannot run startup scripts as they are not capable of running scripts.

## What are wsh Shell Extensions?

`wsh` is a small program that helps manage waveterm regardless of which machine you are currently connected to. It is always included on your host machine, but you also have the option to install it when connecting to SSH and WSL Connections. If it is installed on the connection, it is installed at `~/.waveterm/bin/wsh`. Then, when wave connects to your connection (and only when wave connects to your connection), the following happens:

- `~/.waveterm/bin` is added to your `PATH` for that individual session. This allows the user to use the `wsh` command without providing the complete path.
- Several environment variables are injected into the session to make certain tasks with `wsh` easier. These are [listed below](https://docs.waveterm.dev/connections#additional-environment-variables).
- The user-defined environment variables in the `cmd:env` entry of`connections.json` are injected into the session.
- The user-defined initialization scripts located in `connections.json` are run. For more information on these scripts, see the section below.

If this fails for some reason, Wave will attempt to run without `wsh`. You will see this indicated by a small  icon in the block header. For more info on what `wsh` is capable of, see [wsh command](https://docs.waveterm.dev/wsh). And if you wish to view the source code of `wsh`, you can find it [here](https://github.com/wavetermdev/waveterm/tree/main/cmd/wsh).

With `wsh` installed, you have the ability to view certain widgets from the remote machine as if it were your host, for instance the `files` and `sysinfo` widgets. In addition, `wsh` can be used to influence the widgets across various machines. As a simple example, you can close a widget on the host machine by using the `wsh` command in a terminal window on a remote machine. For more information on what you can accomplish with `wsh`, take a look [here](https://docs.waveterm.dev/wsh).

### Additional Environment Variables

As mentioned above, `wsh` injects a few environment variables in remote sessions for the user's convenience. These are listed below:

| Variable Name        | Description                                                                   |
| -------------------- | ----------------------------------------------------------------------------- |
| TERM_PROGRAM         | Set to `waveterm` in wave.                                                    |
| WAVETERM             | This is set to 1 in wave.                                                     |
| WAVETERM_BLOCKID     | The id of the block containing your current terminal widget.                  |
| WAVETERM_CLIENTID    | The id of the RPC Client being used by your current terminal widget.          |
| WAVETERM_CONN        | The name of the remote connection being used by your current terminal widget. |
| WAVETERM_TABID       | The id of the tab containing your current terminal widget.                    |
| WAVETERM_VERSION     | The current semver version of wave.                                           |
| WAVETERM_WORKSPACEID | The id of thw workspace containing your current terminal widget.              |

# Initialization Scripts

Wave provides you with options for running initialization scripts on your remote machines when connecting to them. These are defined in `connections.json` and can take either the form of the path of a script or a short script written directly in the file. If multiple scripts are defined, the most specific one relevant to the current shell is applied. The keywords for the scripts are:

| Script Keyword      | Shells Where Applied |
| ------------------- | -------------------- |
| cmd:initscript      | all shells           |
| cmd:initscript.sh   | bash and zsh         |
| cmd:initscript.bash | bash                 |
| cmd:initscript.zsh  | zsh                  |
| cmd:initscript.pwsh | pwsh                 |
| cmd:initscript.fish | fish                 |

## Add a New Connection to the Dropdown

The SSH values that are loaded into the dropdown by default are obtained by parsing the internal `config/connections.json` file in addition to your `~/.ssh/config` and `/etc/ssh/ssh_config` files. Adding a new connection can be added in a couple ways:

- adding a new `Host` to one of your ssh config files, typically the `~/.ssh/config` file
- adding a new entry in the internal `config/connections.json` file
- manually typing your connection into the connection box (if this successfully connects, the connection will be added to the internal `config/connections.json` file)
- use `wsh ssh [user]@[host]` in your terminal (if this successfully connects, the connection will be added to the internal `config/connections.json` file)

WSL connections are added by searching the installed WSL distributions as they appear in the Windows Registry. They also exist in the `config/connections.json` file similarly to SSH connections.

AWS S3 Connections are added by parsing the `~/.aws/config` file. Unlike the SSH and WSL connections, these are not stored in the `config/connections.json` file.

## SSH Config Parsing

At the moment, we are capable of parsing any SSH config file that does not contain the `Match` keyword. This keyword is incompatible with a library we are using, but we are hoping to fix that soon. While all other valid keywords are parsed, we only support the functionality of a small subset of them at the moment:

| Keyword                      | Description                                                                                                                                                                                                                                                                                                                                                                                                     |
| ---------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Host                         | The pattern to match when attempting to connect via `[user]@[host]`. We list hosts that do not contain any wildcards characters (`*`, `?`, or `!`). Even if a host pattern contains wildcards, it will still be parsed when determining the values associated with the keys as usual.                                                                                                                           |
| User                         | The user of the SSH remote connection. This will default to the current user on the local machine if not specified.                                                                                                                                                                                                                                                                                             |
| HostName                     | The real host name of the machine to log into. An IP address can be used if desired. This will default to the Host if not specified.                                                                                                                                                                                                                                                                            |
| Port                         | The port to connect to the remote on. `22` is the default if not specified.                                                                                                                                                                                                                                                                                                                                     |
| IdentityFile                 | This can be specified more than once per host. It gives the path to a private identity file (id_rsa, id_ed25519, id_ecdsa, etc.) that is used to authenticate the connection. Each will be tried in order, and they can be encrypted with a passphrase if desired. If no value is set, the default is to try in order: ~/.ssh/id_rsa, ~/.ssh/id_ecdsa, ~/.ssh/id_ecdsa_sk, ~/.ssh/id_ed25519_sk, ~/.ssh/id_dsa. |
| BatchMode                    | If set to true, user interaction via password, challenge/response, and publickey passphrase authentication will be disabled. It is set to false by default.                                                                                                                                                                                                                                                     |
| PubkeyAuthentication         | (partial) This is used to specify if pubkey authentication should be attempted. It is partially implementented as the `unbound` and `host-bound` values simply work the same as the `yes` value. The default is `yes`.                                                                                                                                                                                          |
| PasswordAuthentication       | This is used to specify if password authentication should be attempted. The default is `yes`.                                                                                                                                                                                                                                                                                                                   |
| KbdInteractiveAuthentication | This is used to specify if keyboard-interactive authentication should be attempted. The default is `yes`.                                                                                                                                                                                                                                                                                                       |
| PreferredAuthentications     | (partial) Specifies the order the client should attempt to authenticate in. It is partially implemented as it does not support `gssapi-with-mic` or `hostbased` authentication. The default is `publickey,keyboard-interactive,password`                                                                                                                                                                        |
| AddKeysToAgent               | (partial) This option will automatically add keys and their corresponding passphrase to your running ssh agent if it is enabled. It is partially supported as it can only accept `yes` and `no` as valid inputs. Other inputs such as `confirm` or a time interval will behave the same as `no`. The default value is `no`.                                                                                     |
| IdentityAgent                | Specifies the Unix Domain Socket used to communicate with the SSH Agent. This is used to overwrite the SSH_AUTH_SOCK identity agent.                                                                                                                                                                                                                                                                            |
| IdentitiesOnly               | Specifies that only the specified authentication identity files should be used. This is either the default files or the ones specified with the IdentityFile keyword. It can accept `yes` or `no`. The default value is `no`.                                                                                                                                                                                   |
| ProxyJump                    | Specifies one or more jump proxies in a comma separated list. Each will be visited sequentially using TCP forwarding before connecting to the desired connection (also using TCP forwarding). It can be set to `none` to disable the feature.                                                                                                                                                                   |
| UserKnownHostsFile           | Provides the location of one or more user host key database files for recording trusted remote connections. The filenames are entered in the same string and separated by whitespace. The default value is `"~/.ssh/known_hosts ~/.ssh/known_hosts2"`.                                                                                                                                                          |
| GlobalKnownHostsFile         | Provides the location of one or more global host key database files for recording trusted remote connections. The filenames are entered in the same string and separated by whitespace. The default value is `"/etc/ssh/ssh_known_hosts /etc/ssh/ssh_known_hosts2"`.                                                                                                                                            |

### Example SSH Config Host

For a quick example, a host in your config file may look like:

```
Host myhost   User username   HostName *************   IdentityFile ~/.ssh/id_rsa   AddKeysToAgent yes
```

You would then be able to access this connection with `myhost` or `username@myhost`. And if you wanted to manually specify a port such as port 2222, you could do that by either adding `Port 2222` to the config file or connecting to `username@myhost:2222`.

## Internal SSH Configuration

In addition to the regular ssh config file, wave also has its own config file to manage separate variables. These include

| Keyword                          | Description                                                                                                                                                                                                                                                                                                                                                                                       |
| -------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| conn:wshenabled                  | This boolean allows `wsh` to be used for your connection, if it is set to `false`, `wsh` will never be used for that connection. It defaults to `true`.                                                                                                                                                                                                                                           |
| conn:askbeforewshinstall         | This boolean is used to prompt the user before installing wsh. If it is set to false, `wsh` will automatically be installed instead without prompting. It defaults to `true`.                                                                                                                                                                                                                     |
| conn:wshpath                     | A string indicating the path to the `wsh` executable on the connection. It defaults to `"~/.waveterm/bin/wsh"`.                                                                                                                                                                                                                                                                                   |
| conn:shellpath                   | A string indicating the path to the shell executable on the connection. If not set, the output of `$SHELL` on the connection will be used.                                                                                                                                                                                                                                                        |
| conn:ignoresshconfig             | This boolean allows wave to ignore the `~/.ssh/config` file for resolving keywords for this connection. The regular defaults will be used, but all changes to those must be specified in the `connections.json` file instead. This defaults to false.                                                                                                                                             |
| display:hidden                   | This boolean hides the connection from the dropdown list. It defaults to `false`                                                                                                                                                                                                                                                                                                                  |
| display:order                    | This float determines the order of connections in the connection dropdown. It defaults to `0`.                                                                                                                                                                                                                                                                                                    |
| term:fontsize                    | This int can be used to override the terminal font size for blocks using this connection. The block metadata takes priority over this setting. It defaults to null which means the global setting will be used instead.                                                                                                                                                                           |
| term:fontfamily                  | This string can be used to specify a terminal font family for blocks using this connection. The block metadata takes priority over this setting. It defaults to null which means the global setting will be used instead.                                                                                                                                                                         |
| term:theme                       | This string can be used to specify a terminal theme for blocks using this connection. The block metadata takes priority over this setting. It defaults to null which means the global setting will be used instead.                                                                                                                                                                               |
| cmd:env                          | A json object with key value pairs of environment variables and the value they should be set to for this remote. This only works if `wsh` is enabled.                                                                                                                                                                                                                                             |
| cmd:initscript                   | A script or a path to a script that runs when initializing this connection with any shell. This only works if `wsh` is enabled.                                                                                                                                                                                                                                                                   |
| cmd:initscript.sh                | A script or a path to a script that runs when initializing this connection with POSIX shells like `bash` or `zsh`. This only works if `wsh` is enabled.                                                                                                                                                                                                                                           |
| cmd:initscript.bash              | A script or a path to a script that runs when initializing this connection with the `bash` shell. This only works if `wsh` is enabled.                                                                                                                                                                                                                                                            |
| cmd:initscript.zsh               | A script or a path to a script that runs when initializing this connection with the `zsh` shell. This only works if `wsh` is enabled.                                                                                                                                                                                                                                                             |
| cmd:initscript.pwsh              | A script or a path to a script that runs when initializing this connection with the `pwsh` shell. This only works if `wsh` is enabled.                                                                                                                                                                                                                                                            |
| cmd:initscript.fish              | A script or a path to a script that runs when initializing this connection with the `fish` shell. This only works if `wsh` is enabled.                                                                                                                                                                                                                                                            |
| ssh:user                         | A string that indicates the username of the connection. Can be used to override the value in `~/.ssh/config` or to set it if the ssh config is being ignored.                                                                                                                                                                                                                                     |
| ssh:hostname                     | A string representing the internal hostname of the connection. Can be used to override the value in `~/.ssh/config` or to set it if the ssh config is being ignored.                                                                                                                                                                                                                              |
| ssh:port                         | A string to indicate the numerical port to connect on. Can be used to override the value in `~/.ssh/config` or to set it if the ssh config is being ignored.                                                                                                                                                                                                                                      |
| ssh:identityfile                 | A list of strings containing the paths to identity files that will be used. If a `wsh ssh` command using the `-i` flag is successful, the identity file will automatically be added here. These are used before the `~/.ssh/config` values.                                                                                                                                                       |
| ssh:identitiesonly               | A boolean indicating if only the specified identity files should be used. This means only the files set with the `ssh:identityfile` flag or the defaults. Can be used to override the value in `~/.ssh/config` or to set it if the ssh config is being ignored.                                                                                                                                   |
| ssh:batchmode                    | A boolean indicating if password and passphrase prompts should be skipped. Can be used to override the value in `~/.ssh/config` or to set it if the ssh config is being ignored.                                                                                                                                                                                                                  |
| ssh:pubkeyauthentication         | A boolean indicating if public key authentication is enabled. Can be used to override the value in `~/.ssh/config` or to set it if the ssh config is being ignored.                                                                                                                                                                                                                               |
| ssh:passwordauthentication       | A boolean indicating if password authentication is enabled. Can be used to override the value in `~/.ssh/config` or to set it if the ssh config is being ignored.                                                                                                                                                                                                                                 |
| ssh:kbdinteractiveauthentication | A boolean indicating if keyboard interactive authentication is enabled. Can be used to override the value in `~/.ssh/config` or to set it if the ssh config is being ignored.                                                                                                                                                                                                                     |
| ssh:preferredauthentications     | A list of strings indicating an ordering of different types of authentications. Each authentication type will be tried in order. This supports `"publickey"`, `"keyboard-interactive"`, and `"password"` as valid types. Other types of authentication are not handled and will be skipped. Can be used to override the value in `~/.ssh/config` or to set it if the ssh config is being ignored. |
| ssh:addkeystoagent               | A boolean indicating if the keys used for a connection should be added to the ssh agent. Can be used to override the value in `~/.ssh/config` or to set it if the ssh config is being ignored.                                                                                                                                                                                                    |
| ssh:identityagent                | A string giving the path to the unix domain socket of the identity agent. Can be used to overwrite the value in `~/.ssh/config` or to set it if the ssh config is being ignored.                                                                                                                                                                                                                  |
| ssh:proxyjump                    | A list of strings specifying the names of hosts that must be successively visited with tcp forwarding to establish a connection. Can be used to overwrite the value in `~/.ssh/config` or to set it if the ssh config is being ignored.                                                                                                                                                           |
| ssh:userknownhostsfile           | A list containing the paths of any user host key database files used to keep track of authorized connections. Can be used to overwrite the value in `~/.ssh/config` or to set it if the ssh config is being ignored.                                                                                                                                                                              |
| ssh:globalknownhostsfile         | A list containing the paths of any global host key database files used to keep track of authorized connections. Can be used to overwrite the value in `~/.ssh/config` or to set it if the ssh config is being ignored.                                                                                                                                                                            |

### Example Internal Configurations

Here are a couple examples of things you can do using the internal configuration file `connections.json`:

#### Hiding a Connection

Suppose you have a connection named `github.com` in your `~/.ssh/config` file that shows up as `**************` in the connections dropdown. While it does belong in the config file for authentication reasons, it makes no sense to be in the dropdown since it doesn't involve connecting to a remote environment. In that case, you can hide it as in the example below:

```
{
    <... other connections go here ...>,
    "**************" : {
        "display:hidden": true
    },
    <... other connections go here ...>
}
```

#### Moving a Connection

Suppose you have a connection named `rarelyused` that shows up as `myusername@rarelyused:9999` in the connections dropdown. Since it's so rarely used, you would prefer to move it later in the list. In that case, you can move it as in the example below:

```
{
    <... other connections go here ...>,
    "myusername@rarelyused:9999" : {
        "display:order": 100
    },
    <... other connections go here ...>
}
```

#### Theming a Connection

Suppose you have a connection named `myhost` that shows up as `myusername@myhost` in the connections dropdown. You use this connection a lot, but you keep getting it mixed up with your local connections. In this case, you can use the internal configuration file to style it differently. For example:

```
{
    <... other connections go here ...>,
    "myusername@myhost" : {
        "term:theme": "warmyellow",
        "term:fontsize": 16,
        "term:fontfamily": "menlo"
    },
    <... other connections go here ...>
}
```

This style, font size, and font family will then only apply to the widgets that are using this connection.

### Entirely Defined Internally

Suppose you want to set up a connection but have no desire to learn the syntax of `~/.ssh/config`. In this case, you can entirely define the connection in your `connections.json` file. For example:

```
{
    <... other connections go here ...>,
    "myusername@myhost" : {
        "ssh:hostname": "*********",
        "ssh:identityfile": ["~/.ssh/myidentityfile"],
        "ssh:identitiesonly": true,
        "ssh:addkeystoagent": true
    },
    <... other connections go here ...>
}
```

This will create a connection without that connection needing to be in the `~/.ssh/config` file. A couple additional options are set as well as an example of how that can be done.

### Disabling wsh for a Connection

While Wave provides an option disable `wsh` when first connecting to a remote, there are cases where you may wish to disable it afterward. The easiest way to do this is by editing the `connections.json` file. Suppose the connection shows up in the dropdown as `root@wshless`. Then you can disable it manually with the following line:

```
{
    <... other connections go here ...>,
    "root@wshless" : {
        "conn:enablewsh": false,
    },
    <... other connections go here ...>
}
```

Note that this same line gets added to your `connections.json` file automatically when you choose to disable `wsh` in gui when initially connecting.

## Managing Connections with the CLI

The `wsh` command gives some commands specifically for interacting with the connections. You can view these [here](https://docs.waveterm.dev/wsh-reference#conn).

## Troubleshooting Connections

### Log Files

If there are issues with connections, the easiest first step is to enable debugging in a terminal widget that is trying to connect. To do this, click the  button and hover over the **`Debug Connection`** item. From there you can select two log levels, `Info` and `Verbose`. After this, debug info will print out to the terminal during the connection.

If this is not sufficient, it is also possible to view the full log file. To do this, you can run the command `wsh wavepath log` to get the location of a log file.

### Known Limitations

In the case that there is an error setting up `wsh`, your connection will still launch without `wsh`. However, depending on the debug info, there are a few things that can cause this.

#### Shell Type

Wave is capable of injecting `wsh` in the following shells:

- bash
- zsh
- pwsh (powershell)
- fish

If the shell is different than those, it is possible the `wsh` command will not work by default. The easiest way to fix this at the moment is the switch the shell type. This can be done by setting the `conn:shellpath` value with a path to one of the above shells in the `connections.json` file for the connection you are trying to use. Alternatively, you can use the `chsh` command to change the shell in that connection, but this will also take effect outside of wave. Once this is done, restart wave for the changes to take effect.

#### AllowTcpForwarding in sshd

Some systems have sshd configured to disable TCP forwarding by default. This can be found on the connection in the `/etc/ssh/sshd_config` file. In that file, search for the line containing `AllowTcpForwarding`. If this is set to `no`, it is likely the reason `wsh` will not work on your connection. In order to get `wsh` working, set the value for `AllowTcpForwarding` to either `yes` or `local` (they both provide different levels of permission but both work in this case). Then, restart the `sshd` service with whichever method your remote machine provides. Once that is done, restart wave, so it can reconnect with this change.

# Customization

## Tab Themes

![Tab Context Menu](https://docs.waveterm.dev/assets/images/tab-context-menu-5bf421fce677bdce6c3c1f8f18c71b4c.png#right)

Right click on any tab to bring up a menu which allows you to rename the tab and select different backgrounds.

It is also possible to create your own themes using custom colors, gradients, images and more by editing your presets.json config file. To see how Wave's built in tab themes are defined, you can check out our [default presets file](https://github.com/wavetermdev/waveterm/blob/main/pkg/wconfig/defaultconfig/presets.json).

## Terminal Customization

#### Terminal Theme

![Terminal Context Menu](https://docs.waveterm.dev/assets/images/terminal-context-menu-0cf372b1e9ab93beb77af4912c144f3e.png#right)

Right click in the header area of any terminal block to bring up a menu which allows you to set a terminal theme for that terminal.

You can set the default theme for all terminals (which haven't had their theme manually overridden) by editing your settings.json file and adding the key `term:theme` and setting it to the appropriate key. The keys can be found in the [default termthemes.json file](https://github.com/wavetermdev/waveterm/blob/main/pkg/wconfig/defaultconfig/termthemes.json).

If you add your own termthemes.json file in the config directory, you can also add your own custom terminal themes (just follow the same format).

You can set the key `tab:preset` in your [Wave Config File](https://docs.waveterm.dev/config) to apply a theme to all new tabs.

#### Font Size

From the same context menu you can also change the font-size of the terminal. To change the default font size across all of your (non-overridden) terminals, you can set the config key `term:fontsize` to the size you want. e.g. `{ "term:fontsize": 14}`.

#### Font Family

There is no UI to edit your default terminal font family. But, it *can* be overridden. In your settings.json file you can add the key `term:fontfamily` and set it to a font that is *installed* on your local system. If type a font that is not installed, or use a non-monospace font, your terminal will look terrible (don't do that 🙂), delete the key to return to using the default.

## Widgets Sidebar

![Terminal Context Menu](https://docs.waveterm.dev/assets/images/custom-widgets-c1135ae2e1d0a1274f7831725bc43ef0.png#right)

See [Custom Widgets](https://docs.waveterm.dev/customwidgets) for detailed documentation around changing what appears in your right widget sidebar.

Using widgets.json, you'll be able to remove any default widgets and add widgets of your own. You can fully customize the icons, colors, text, and defaults (like directories, webpages, AI model, remote connection, commands, etc.) of your custom widgets.

You can also suppress the help widgets in the bottom right by setting the config key `widget:showhelp` to `false`.

## Tab Backgrounds

Wave supports powerful custom backgrounds for your tabs using images, patterns, gradients, and colors. The quickest way to set an image background is using the `wsh setbg` command:

```
# Set an image background with 50% opacity (default)
wsh setbg ~/pictures/background.jpg

# Set a color background (use quotes to prevent # being interpreted as a shell comment)
wsh setbg "#ff0000"          # hex color
wsh setbg forestgreen        # CSS color name

# Adjust opacity
wsh setbg --opacity 0.3 ~/pictures/light-pattern.png
wsh setbg --opacity 0.7      # change only opacity of current background

# Image positioning options
wsh setbg --tile ~/pictures/texture.png          # create tiled pattern
wsh setbg --center ~/pictures/logo.png           # center without scaling
wsh setbg --center --size 200px ~/pictures/logo.png  # center with specific size (px, %, auto)

# Remove background
wsh setbg --clear
```

You can use any JPEG, PNG, GIF, WebP, or SVG image as your background. The `--center` option is particularly useful for logos or icons where you want to maintain the original size.

To preview the metadata for any background without applying it, use the `--print` flag:

```
wsh setbg --print "#ff0000"
```

For more advanced customization options including gradients, colors, and saving your own background presets, check out our [Background Configuration](https://docs.waveterm.dev/presets#background-configurations) documentation.

## Presets

For more advanced customization, to set up multiple AI models, and your own tab backgrounds, check out our [Presets Documentation](https://docs.waveterm.dev/presets).

# Tabs

Tabs are collections of [Widgets](https://docs.waveterm.dev/widgets) that can be arranged into tiled dashboards. You can create as many tabs as you want within a given workspace to help organize your workflows.

## Tab Bar

The tab bar is located at the top of the window and shows all tabs within a given workspace. You can click on a tab to switch to it. When switching tabs, any commands in the previous tab will continue running and any unsaved work will be persisted until you return to it. If you close the window or switch workspaces within the same window, that work will be lost.

macOSLinuxWindows

### Creating a new tab

You can create a new tab by clicking the  button to the right of the tabs in the tab bar, or by pressing 

AltT

 on the keyboard. This will also focus you to the new tab.

### Closing a tab

You can close a tab by hovering over it and clicking the  button that appears, or by pressing 

Alt⇧W

 on the keyboard. You can also close a tab by [closing all the blocks](https://docs.waveterm.dev/tabs#delete-a-block) within it.

Closing a block is a destructive action that will stop any running processes and discard any unsaved work. This cannot be undone.

### Rearranging tabs

You can rearrange tabs by dragging them around within the tab bar.

### Switching tabs

You can switch to an existing tab by clicking on it in the tab bar. You can also use the following shortcuts:

| Key    | Function             |
| ------ | -------------------- |
| Alt1-9 | Switch to tab number |
| Alt[   | Switch tab left      |
| Alt]   | Switch tab right     |

### Pinning a tab

Pinning a tab makes it harder to close accidentally. You can pin a tab by right-clicking on it and selecting "Pin Tab" from the context menu that appears. You can also pin a tab by dragging it to a lesser index than an existing pinned tab. When a tab is pinned, the  button for the tab will be replaced with a  button. Clicking this button will unpin the tab. You can also unpin a tab by dragging it to an index higher than an existing unpinned tab.

## Tab Layout System

The tabs are comprised of tiled blocks. The contents of each block is a single widget. You can move blocks around and arrange them into layouts that best-suit your workflow. You can also magnify blocks to focus on a specific widget.

![screenshot showing a block being dragged over another block, with the placeholder depicting a out-of-line before outer drop](https://docs.waveterm.dev/assets/images/drag-edge-38a84993f776937ddabd3c0cb4238782.png)

### Layout system under the hood

info

**Definitions**

- Layout tree: the in-memory representation of a tab layout, comprised of nodes
- Node: An entry in the layout tree, either a single block (a leaf) or an ordered list of nodes. Defines a tiling direction (row or column) and a unitless size
- Block: The contents of a leaf in the layout tree, defines what contents is displayed at the given layout location

Our layout system emulates the [CSS Flexbox](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_flexible_box_layout/Basic_concepts_of_flexbox) system, comprising of a tree of columns and rows. Under the hood, the layout is represented as an n-tree, where each node in the tree is either a single block, or a list of nodes. Each level in the tree alternates the direction in which it tiles (level 1 tiles as a row, level 2 as a column, level 3 as a row, etc.).

### Layout actions

macOSLinuxWindows

#### Add a new block

You can add new blocks by selecting a widget from the right sidebar.

Starting at the topmost level of the tree, since the first level tiles as a row, new blocks will be added to the right side of existing blocks. Once there are 5 blocks across, new blocks will begin being added below existing blocks, starting from the right side and working to the left. As a new block gets added below an existing one, the node containing the existing block is converted from a single-block node to a list node and the existing block definition is moved one level deeper in the tree as the first element of the node list. New blocks will always be added to the last-available node in the deepest level, where available is defined as having less than five children. We don't set a limit on the number of blocks in a tab, but you may experience degraded performance past around 25 blocks.

While we define a 5-child limit for each node in the tree when automatically placing new blocks, there is no actual limit to the number of children a node can hold. After the block is placed, you are free to move it wherever in the layout

#### Delete a block

You can delete blocks by clicking the  button in the top-right corner of the block, by right-clicking on the block header and selecting "Close Block" from the context menu, or by running the [`wsh deleteblock` command](https://docs.waveterm.dev/wsh-reference#deleteblock). Alternatively, the currently focused block/widget can be closed by pressing 

AltW

When you delete a block, the layout tree will be automatically adjusted to minimize the tree depth.

#### Move a block

You can move blocks by clicking on the block header and dragging the block around the tab. You will see placeholders appear to show where the block will land when you drop it.

There are 7 different drop targets for any given block. A block is divided into quadrants along its diagonals. If the block is tiling as a row (left-to-right), dropping a block into the left or right quadrant will place the dropped block in the same level as the targeted block. This can be considered dropping the block inline. If you drop the block out of line (in quadrants corresponding to opposite tiling direction), the block will either be placed one level above or one level below the targeted block. Dropping the block towards the outside will place it in the same level as the target block's parent, while dropping it towards the center of the block will create a new level, where both the target block and the dropped block will be moved. The middle fifth of the block is reserved for the swap action. Dropping a block here will cause the target block and the dropped block to swap positions in the layout.

##### Possible block movements

note

All block movements except for Swap will cause the rest of the layout to shift to accommodate the block's new displacement.

![screenshot showing a block being dragged over another block, with the placeholder depicting a swap movement](https://docs.waveterm.dev/assets/images/drag-swap-8b19564c119b3d5620372fa4318e2eb9.png) ![annotated example showing the drop targets within a block](https://docs.waveterm.dev/assets/images/block-drag-example-a708d55a8fae9ecd63aad6c02d81ff38.jpg)

1. Inline before: Drops the block under the same node as the target block, placing it before the target in the same tiling direction
2. Inline after: Drops the block under the same node as the target block, placing it after the target in the same tiling direction
3. Out-of-line before outer: Drops the block before the target block's parent node in the opposite tiling direction
4. Out-of-line before inner: Segments the target block, creating a new node in the tree. Places the dropped block before the target block in the opposite tiling direction.
5. Out-of-line after inner: Segments the target block, creating a new node in the tree. Places the dropped block after the target block in the opposite tiling direction.
6. Out-of-line after outer: Drops the block after the target block's parent node in the opposite tiling direction
7. Swap: Swaps the position of the dropped block and the targeted block in the layout, preserving the rest of the layout

#### Resize a block

![screenshot showing the line that appears when the cursor hovers over the margin of a block, indicating which blocks
will be resized by dragging the margin](https://docs.waveterm.dev/assets/images/node-resize-c1fe445a3405596d28d0caf42bc63361.png)

You do not directly resize a block. Rather, you resize the nodes containing the blocks. If you hover your mouse over the margin of a block, you will see the cursor change to  or  to indicate the direction the node can be resized. You will also see a line appear after 500ms to show you how many blocks will be resized by moving that margin. Clicking and dragging on this margin will cause the block(s) to get resized.

Node sizes are unitless values. The ratio of all node sizes at a given tree level determines the displacement of each node. If you move a block and its node is deleted, the other nodes at the given tree level will adjust their sizes to account for the new size ratio.

### Magnify a block

You can magnify a block by clicking the  button or by pressing 

AltM

 on the keyboard. You can then un-magnify a block by clicking the  button or by pressing 

AltM

 again.

### Change the gap size between blocks

The gap between blocks defaults to 3px, but this value can be changed by modifying the `window:tilegapsize` configuration value. See [Configuration](https://docs.waveterm.dev/config) for more information on how to change configuration values.



# Widgets

Every individual Component is contained in its own widget. These can be added, removed, moved and resized. Each widget has its own header which can be right clicked to reveal more operations you can do with that widget.

macOSLinuxWindows

### How to Add a Widget

Adding a widget can be done using the widget bar on the right hand side of the window. This will add a widget of the selected type to the current tab.

### How to Close a Widget

Widgets can be closed by clicking the  button on the right side of the header. Alternatively, the currently focused widget can be closed by pressing 

AltW

### How to Navigate Widgets

At most, it is possible to have one widget be focused. Depending on the type of widget, this allows you to directly interact with the content in that widget. A focused widget is always outlined with a distinct border. A widget may be focused by clicking on it. Alternatively, you can change the focused widget by pressing 

Ctrl⇧↑→↓←

 (Ctrl + Shift + Arrow Keys) to navigate relative to the currently selected widget.

### How to Magnify Widgets

Magnifying a widget will pop the widget out in front of everything else. You can magnify using the header icon, or with 

AltM

.

### How to Reorganize Widgets

By dragging and dropping their headers, widgets can be moved to different locations in the layout. This effectively allows you to reorganize your screen however you see fit. When dragging, you will see a preview of the widget that is being dragged. When the widget is over a valid drop point, the area where it would be moved to will turn green. Releasing the click will place the widget there and reflow the other widgets around it. If you see a green box cover half of two different widgets, the drop will place the widget between the two. If you see the green box cover half of one widget at the edge of the screen, the widget will be placed between that widget and the edge of the screen. If you see the green box cover one widget entirely, the two widgets will swap locations.

See [Tab Layout System](https://docs.waveterm.dev/layout#move-a-block) for more information.

### How to Resize Widgets

Hovering the mouse between two widgets changes your cursor to  or ; and reveals a green line dividing the widgets. By dragging and dropping this green line, you are able to resize the widgets adjacent to it.

See [Tab Layout System](https://docs.waveterm.dev/layout#resize-a-block) for more information.

## Types of Widgets

### Term

The usual terminal you know and love. We add a few plugins via the `wsh` command that you can read more about further below.

### Preview

Preview is the generic type of widget used for viewing files. This can take many different forms based on the type of file being viewed. You can use `wsh view [path]` from any Wave terminal window to open a preview widget with the contents of the specified path (e.g. `wsh view .` or `wsh view ~/myimage.jpg`).

#### Directory

When looking at a directory, preview will show a file viewer much like MacOS' *Finder* application or Windows' *File Explorer* application. This variant is slightly more geared toward software development with the focus on seeing what is shown by the `ls -alh` command.

##### View a New File

The simplest way to view a new file is to double click its row in the file viewer. Alternatively, while the widget is focused, you can use the 

↑

 and 

↓

 arrow keys to select a row and press enter to preview the associated file.

##### Copy a File

If you have two directory widgets open, you can copy a file or a directory between them. To do this, simply drag the file or directory from one directory preview widget to another that is opened to where you would like it dropped. This even works for copying files and directories across connections.

##### View the Parent Directory

In the directory view, this is as simple as opening the `..` file as if it were a regular file. This can be done with the method above. You can also use the keyboard shortcut 

Alt↑

.

##### Navigate Back and Forward

When looking at a file, you can navigate back by clicking the back button in the widget header or the keyboard shortcut 

Alt←

. You can always navigate back and forward using 

Alt←

 and 

Alt→

.

##### Filter the List of Files

While the widget is focused, you can filter by filename by typing a substring of the filename you're looking for. To clear the filter, you can click the  on the filter dropdown or press 

Esc

.

##### Sort by a File Column

To sort a file by a specific column, click on the header for that column. If you click the header again, it will reverse the sort order.

##### Hide and Show Hidden Files

At the right of the widget header, there is an  button. Clicking this button hides and shows hidden files.

##### Refresh the Directory

At the right of the widget header, there is a refresh button . Clicking this button refreshes the directory contents.

##### Navigate to Common Directories

At the left of the widget header, there is a file icon . Clicking and holding on this icon opens a menu where you can select a common folder to navigate to. The available options are *Home*, *Desktop*, *Downloads*, and *Root*.

##### Open a New Terminal in the Current Directory

If you right click the header of the widget (alternatively, click the gear icon ), one of the menu items listed is **Open Terminal in New Widget**. This will create a new terminal widget at your current directory.

##### Open a New Terminal in a Child Directory

If you want to open a terminal for a child directory instead, you can right click on that file's row to get the **Open Terminal in New Widget** option. Clicking this will open a terminal at that directory. Note that this option is only available for children that are directories.

##### Open a New Preview for a Child

To open a new Preview Widget for a Child, you can right click on that file's row and select the **Open Preview in New Widget** option.

##### Quick Look (MacOS only)

On a MacOS host, it is possible to use the Quick Look feature from the directory preview. To do this, select the file you wish to view and press 

Space

. This will open a preview of your file in a separate window. This preview can then be closed by pressing 

Space

 again. This currently supports the filetypes that can be accessed by the `qlmanage` command.

#### Markdown

Opening a markdown file will bring up a view of the rendered markdown. These files cannot be edited in the preview at this time.

#### Images/Media

Opening a picture will bring up the image of that picture. Opening a video will bring up a player that lets you watch the video.

### Codeedit

Opening most text files will open Codeedit to either view or edit the file. It is technically part of the Preview widget, but it is important enough to be singled out. After opening a Codeedit widget, it is often useful to magnify it (

AltM

) to get a larger view. You can then use the hotkeys below to switch to edit mode, make your edits, save, and then use 

AltW

 to close the widget (all without using the mouse!).

#### Switch to Edit Mode

To switch to edit mode, click the edit button to the right of the header. This lets you edit the file contents with a regular Monaco editor. You can also switch to edit mode by pressing 

AltE

.

#### Save an Edit

Once an edit has been made in **edit mode**, click the save button to the right of the header to save the contents. You can also save by pressing 

AltS

.

#### Exit Edit Mode Without Saving

To exit **edit mode** without saving, click the cancel button to the right of the header. You can also exit without saving by pressing 

AltR

.

# Presets

Wave's preset system allows you to save and apply multiple configuration settings at once. Presets can be used in two different scenarios:

- AI models: Configure different AI providers and models (see [AI Presets](https://docs.waveterm.dev/ai-presets))
- Tab backgrounds: Apply visual styles to your tabs

## Managing Presets

You can store presets in two locations:

- `~/.config/waveterm/presets.json`: Main presets file
- `~/.config/waveterm/presets/`: Directory for organizing presets into separate files

All presets are aggregated regardless of which file they're in, so you can use the `presets` directory to organize them (e.g., `presets/bg.json`, `presets/ai.json`).

info

You can easily edit your presets using the built-in editor:

```
wsh editconfig presets.json        # Edit main presets file
wsh editconfig presets/ai.json     # Edit AI presets
```

## File Format

Presets follow this format:

```
{
  "<preset-type>@<preset-key>": {
    "display:name": "<Preset name>",
    "display:order": "<number>", // optional
    "<overridden-config-key-1>": "<overridden-config-value-1>"
    ...
  }
}
```

The `preset-type` determines where the preset appears in Wave's interface:

- `ai`: Appears in the models dropdown in the "Wave AI" widget header (see [AI Presets](https://docs.waveterm.dev/ai-presets))
- `bg`: Appears in the "Backgrounds" submenu when right-clicking a tab

### Common Keys

| Key Name      | Type   | Function                                  |
| ------------- | ------ | ----------------------------------------- |
| display:name  | string | Name shown in the UI menu (required)      |
| display:order | float  | Controls the order in the menu (optional) |

info

When a preset is applied, it overrides the default configuration values for that tab or block. Using `bg:*` or `ai:*` will clear any previously overridden values, setting them back to defaults. It's recommended to include these keys in your presets to ensure a clean slate.

## AI Presets

For configuring AI providers and models, see our dedicated [AI Presets](https://docs.waveterm.dev/ai-presets) documentation. It covers setting up presets for:

- Local LLMs via Ollama
- Azure OpenAI
- Anthropic's Claude
- Perplexity
- And more

## Background Presets

Wave's background system harnesses the full power of CSS backgrounds, letting you create rich visual effects through the "background" attribute. You can apply solid colors, gradients (both linear and radial), images, and even blend multiple elements together.

### Configuration Keys

| Key Name             | Type   | Function                                                                                                |
| -------------------- | ------ | ------------------------------------------------------------------------------------------------------- |
| bg:*                 | bool   | Reset all existing bg keys (recommended to prevent any existing background settings from carrying over) |
| bg                   | string | CSS `background` attribute for the tab (supports colors, gradients images, etc.)                        |
| bg:opacity           | float  | The opacity of the background (defaults to 0.5)                                                         |
| bg:blendmode         | string | The [blend mode](https://developer.mozilla.org/en-US/docs/Web/CSS/blend-mode) of the background         |
| bg:bordercolor       | string | The color of the border when a block is not active (rarely used)                                        |
| bg:activebordercolor | string | The color of the border when a block is active                                                          |

### Examples

#### Simple solid color:

```
{
  "bg@blue": {
    "display:name": "Blue",
    "bg:*": true,
    "bg": "blue",
    "bg:opacity": 0.3,
    "bg:activebordercolor": "rgba(0, 0, 255, 1.0)"
  }
}
```

#### Complex gradient:

```
{
  "bg@duskhorizon": {
    "display:name": "Dusk Horizon",
    "bg:*": true,
    "bg": "linear-gradient(0deg, rgba(128,0,0,1) 0%, rgba(204,85,0,0.7) 20%, rgba(255,140,0,0.6) 45%, rgba(160,90,160,0.5) 65%, rgba(60,60,120,1) 100%), radial-gradient(circle at 30% 30%, rgba(255,255,255,0.1), transparent 60%), radial-gradient(circle at 70% 70%, rgba(255,255,255,0.05), transparent 70%)",
    "bg:opacity": 0.9,
    "bg:blendmode": "overlay"
  }
}
```

#### Background image:

```
{
  "bg@ocean": {
    "display:name": "Ocean Scene",
    "bg:*": true,
    "bg": "url('/path/to/ocean.jpg') center/cover no-repeat",
    "bg:opacity": 0.2
  }
}
```

info

Background images support both URLs and local file paths. For better reliability, we recommend using local files. Local paths must be absolute or start with `~` (e.g., `~/Downloads/background.png`). We support common web formats: PNG, JPEG/JPG, WebP, GIF, and SVG.

tip

The `setbg` command can help generate background preset JSON:

```
# Preview a solid color preset
wsh setbg --print "#ff0000"
{
  "bg:*": true,
  "bg": "#ff0000",
  "bg:opacity": 0.5
}

# Preview a centered image preset
wsh setbg --print --center --opacity 0.3 ~/logo.png
{
  "bg:*": true,
  "bg": "url('/absolute/path/to/logo.png') no-repeat center/auto",
  "bg:opacity": 0.3
}
```

Just add the required `display:name` field to complete your preset!



# AI Presets

![AI Presets Menu](https://docs.waveterm.dev/assets/images/ai-presets-049858075b5b6805b80b53a1bdf4f763.png#right)

Wave's AI widget can be configured to work with various AI providers and models through presets. Presets allow you to define multiple AI configurations and easily switch between them using the dropdown menu in the AI widget.

## How AI Presets Work

AI presets are defined in `~/.config/waveterm/presets/ai.json`. You can easily edit this file using:

```
wsh editconfig presets/ai.json
```

Each preset defines a complete set of configuration values for the AI widget. When you select a preset from the dropdown menu, those configuration values are applied to the widget. If no preset is selected, the widget uses the default values from `settings.json`.

Here's a basic example using Claude:

```
{
  "ai@claude-sonnet": {
    "display:name": "Claude 3 Sonnet",
    "display:order": 1,
    "ai:*": true,
    "ai:apitype": "anthropic",
    "ai:model": "claude-3-5-sonnet-latest",
    "ai:apitoken": "<your anthropic API key>"
  }
}
```

To make a preset your default, add this single line to your `settings.json`:

```
{
  "ai:preset": "ai@claude-sonnet"
}
```

info

You can quickly set your default preset using the `setconfig` command:

```
wsh setconfig ai:preset=ai@claude-sonnet
```

This is easier than editing settings.json directly!

## Provider-Specific Configurations

### Anthropic (Claude)

To use Claude models, create a preset like this:

```
{
  "ai@claude-sonnet": {
    "display:name": "Claude 3 Sonnet",
    "display:order": 1,
    "ai:*": true,
    "ai:apitype": "anthropic",
    "ai:model": "claude-3-5-sonnet-latest",
    "ai:apitoken": "<your anthropic API key>"
  }
}
```

### Local LLMs (Ollama)

To connect to a local Ollama instance:

```
{
  "ai@ollama-llama": {
    "display:name": "Ollama - Llama2",
    "display:order": 2,
    "ai:*": true,
    "ai:baseurl": "http://localhost:11434/v1",
    "ai:name": "llama2",
    "ai:model": "llama2",
    "ai:apitoken": "ollama"
  }
}
```

Note: The `ai:apitoken` is required but can be any value as Ollama ignores it. See [Ollama OpenAI compatibility docs](https://github.com/ollama/ollama/blob/main/docs/openai.md) for more details.

### Azure OpenAI

To connect to Azure AI services:

```
{
  "ai@azure-gpt4": {
    "display:name": "Azure GPT-4",
    "display:order": 3,
    "ai:*": true,
    "ai:apitype": "azure",
    "ai:baseurl": "<your Azure AI base URL>",
    "ai:model": "<your model deployment name>",
    "ai:apitoken": "<your Azure API key>"
  }
}
```

Note: Do not include query parameters or `api-version` in the `ai:baseurl`. The `ai:model` should be your model deployment name in Azure.

### Perplexity

To use Perplexity's models:

```
{
  "ai@perplexity-sonar": {
    "display:name": "Perplexity Sonar",
    "display:order": 4,
    "ai:*": true,
    "ai:apitype": "perplexity",
    "ai:model": "llama-3.1-sonar-small-128k-online",
    "ai:apitoken": "<your perplexity API key>"
  }
}
```

### Google (Gemini)

To use Google's Gemini models from [Google AI Studio](https://aistudio.google.com/):

```
{
  "ai@gemini-2.0": {
    "display:name": "Gemini 2.0",
    "display:order": 5,
    "ai:*": true,
    "ai:apitype": "google",
    "ai:model": "gemini-2.0-flash-exp",
    "ai:apitoken": "<your Google AI API key>"
  }
}
```

## Multiple Presets Example

You can define multiple presets in your `ai.json` file:

```
{
  "ai@claude-sonnet": {
    "display:name": "Claude 3 Sonnet",
    "display:order": 1,
    "ai:*": true,
    "ai:apitype": "anthropic",
    "ai:model": "claude-3-5-sonnet-latest",
    "ai:apitoken": "<your anthropic API key>"
  },
  "ai@ollama-llama": {
    "display:name": "Ollama - Llama2",
    "display:order": 2,
    "ai:*": true,
    "ai:baseurl": "http://localhost:11434/v1",
    "ai:name": "llama2",
    "ai:model": "llama2",
    "ai:apitoken": "ollama"
  },
  "ai@perplexity-sonar": {
    "display:name": "Perplexity Sonar",
    "display:order": 3,
    "ai:*": true,
    "ai:apitype": "perplexity",
    "ai:model": "llama-3.1-sonar-small-128k-online",
    "ai:apitoken": "<your perplexity API key>"
  }
}
```

The `display:order` value determines the order in which presets appear in the dropdown menu.

Remember to set your default preset in `settings.json`:

```
{
  "ai:preset": "ai@claude-sonnet"
}


```

# wsh overview

The `wsh` command provides Wave Terminal's core command line interface, allowing users to interact with both terminal and graphical elements from the command line. This guide covers the basics of using `wsh` and its key features.

See the [wsh reference](https://docs.waveterm.dev/wsh-reference) for a list of all wsh commands and their arguments.

## Overview

At its core, `wsh` enables seamless interaction between your terminal commands and Wave's graphical blocks. It allows you to:

- Control graphical widgets directly from the command line
- Share data between terminal sessions and GUI components
- Manage your workspace programmatically
- Connect remote and local environments
- Send CLI output and files directly to AI conversations
- Run terminal commands in separate, isolated blocks

## Key Concepts

### Interacting with Blocks

`wsh` provides direct interaction with Wave's graphical blocks through the command line. For example:

```
# Open a file in the editor
wsh edit config.json

# Get the current file path from a preview block
wsh getmeta -b 2 file

# Send output to an AI assistant (the "-" reads from stdin)
ls -la | wsh ai - "what are the largest files here?"
```

### Persistent State

`wsh` can maintain state across terminal sessions through its variable and file storage system:

```
# Store a variable that persists across sessions
wsh setvar API_KEY=abc123

# Store globally
wsh setvar DEPLOY_ENV=prod
# Or store in the current workspace
wsh setvar -b workspace DEPLOY_ENV=staging

# Use stored variables in commands
curl -H "Authorization: $(wsh getvar API_KEY)" https://api.example.com

# Store a file that can be accessed from any block
echo "data" | wsh file write wavefile://global/config.txt

# Append logs from multiple terminals
echo "Terminal 1 log" | wsh file append wavefile://workspace/logs.txt
```

### Block Management

Every visual element in Wave is a block, and `wsh` gives you complete control over them (hold Ctrl+Shift to see block numbers):

```
# Create a new block showing a webpage
wsh web open github.com

# Do a web search in a new block
wsh web open "wave terminal"

# Run a command in a new block and auto-close when done
wsh run -x -- npm test

# Get information about the current block
wsh getmeta
```

## Common Workflows

Here are some common ways to use `wsh`:

### Development Workflow

```
# Open directory or markdown files
wsh view .
wsh view README.md

# add a -m to open the block in "magnified" mode
wsh view -m README.md

# Start development server in a new block (-m will magnify the block on startup)
wsh run -m -- npm run dev

# Open documentation in a web block
wsh web open http://localhost:3000
```

### Remote Development

```
# Connect to remote server with optional key
wsh ssh -i ~/.ssh/mykey.pem dev@server

# Edit remote files
wsh edit /etc/nginx/nginx.conf

# Monitor remote logs
wsh run -- tail -f /var/log/app.log

# Share variables between sessions
wsh setvar -b tab SHARED_ENV=staging
```

### AI-Assisted Development

```
# Get AI help with code (uses "-" to read from stdin)
git diff | wsh ai - "review these changes"

# Get help with a file
wsh ai -f .zshrc "help me add ~/bin to my path"

# Debug issues (uses "-" to read from stdin)
dmesg | wsh ai - "help me understand these errors"
```

## Tips & Features

1. **Working with Blocks**
   
   - Use block numbers (1-9) to target specific blocks within a tab (hold Ctrl+Shift to see block numbers)
   - Can get full block ids by right click a block's header and selecting "Copy Block Id" (useful for scripting)
   - Use references like "this", "tab", "workspace", or "global" for different scopes

2. **Data Storage**
   
   - Use `wsh setvar/getvar` for configuration and secrets
   - Store file data using `wsh file`, which can be easily referenced in all terminals (local and remote)
   - Use appropriate storage scopes (block, tab, workspace, global)

3. **Command Execution**
   
   - Use `wsh run` to execute commands in new blocks
   - Send command output and files quickly to AI blocks with `wsh ai`

## Scripting with wsh

wsh commands can be combined in scripts to automate common tasks. Here's an example that sets up a development environment and uses `wsh notify` to monitor a long-running build:

```
#!/bin/bash
# Setup development environment
wsh run -- docker-compose up -d
wsh web open localhost:8080
wsh view ./src
wsh run -- npm run test:watch

# Get notified when long-running tasks complete using wsh notify
npm run build && wsh notify "Build complete" || wsh notify "Build failed"
```

## Getting Help

You can get help on available commands by running `wsh` with no arguments, or get detailed help for a specific command using `wsh [command] -h`.

For a complete reference of all `wsh` functionality, see the [WSH Command Reference](https://docs.waveterm.dev/wsh-reference).



# wsh command

The `wsh` command is always available from Wave blocks. It is a powerful tool for interacting with Wave blocks and can bridge data between your CLI and the widget GUIs.

This is the detailed wsh reference documention. For an overview of `wsh` functionality, please see our [wsh command docs](https://docs.waveterm.dev/wsh).

---

## view

You can open a preview block with the contents of any file or directory by running:

```
wsh view [path]
```

You can use this command to easily preview images, markdown files, and directories. For code/text files this will open a codeedit block which you can use to quickly edit the file using Wave's embedded graphical editor.

---

## edit

```
wsh edit [path]
```

This will open up codeedit for the specified file. This is useful for quickly editing files on a local or remote machine in our graphical editor. This command will wait until the file is closed before exiting (unlike `view`) so you can set your `$EDITOR` to `wsh editor` for a seamless experience. You can combine this with a `-m` flag to open the editor in magnified mode.

---

## getmeta

You can view the metadata of any block or tab by running:

```
# get the metadata for the current terminal block
wsh getmeta

# get the metadata for block num 2 (see block numbers by holidng down Ctrl+Shift)
wsh getmeta -b 2

# get the metadata for a blockid (get block ids by right clicking any block header "Copy Block Id")
wsh getmeta -b [blockid]

# get the metadata for a tab
wsh getmeta -b tab

# dump a single metadata key
wsh getmeta [-b [blockid]] [key]

# dump a set of keys with a certain prefix
wsh getmeta -b tab "bg:*"

# dump a set of keys with prefix (and include the 'clear' key)
wsh getmeta -b tab --clear-prefix "bg:*"
```

This is especially useful for preview and web blocks as you can see the file or url that they are pointing to and use that in your CLI scripts.

blockid format:

- `this` -- the current block (this is also the default)

- `tab` -- the id of the current tab

- `d6ff4966-231a-4074-b78a-20acc7226b41` -- a full blockid is a UUID

- `a67f55a3` -- blockids may be truncated to the first 8 characters

- `5` -- if a number less than 100 is given, it is a block number. blocks are numbered sequentially in the current tab from the top-left to bottom-right. holding 
  
  Ctrl⇧
  
   will show a block number overlay.

---

## setmeta

You can update any metadata key value pair for blocks (and tabs) by using the setmeta command. The setmeta command takes the same `-b` arguments as getmeta.

```
wsh setmeta -b [blockid] [key]=[value]
wsh setmeta -b [blockid] file=~/myfile.txt
wsh setmeta -b [blockid] url=https://waveterm.dev/

# set the metadata for the current tab using the given json file
wsh setmeta -b tab --json [jsonfile]

# set the metadata for the current tab using a json file read from stdin
wsh setmeta -b tab --json
```

You can get block and tab ids by right clicking on the appropriate block and selecting "Copy BlockId" (or use the block number via Ctrl:Shift). When you update the metadata for a preview or web block you'll see the changes reflected instantly in the block.

Other useful metadata values to override block titles, icons, colors, themes, etc.

Here's a complex command that will copy the background (bg:* keys) from one tab to the current tab:

```
wsh getmeta -b [other-tab-id] "bg:*" --clear-prefix | wsh setmeta -b tab --json -
```

---

## ai

Send messages to new or existing AI blocks directly from the CLI. `-f` passes a file. note that there is a maximum size of 10k for messages and files, so use a tail/grep to cut down file sizes before passing. The `-f` option works great for small files though like shell scripts or `.zshrc` etc. You can use "-" to read input from stdin.

By default the messages get sent to the first AI block (by blocknum). If no AI block exists, then a new one will be created. Use `-n` to force creation of a new AI block. Use `-b` to target a specific AI block.

```
wsh ai "how do i write an ls command that sorts files in reverse size order"
wsh ai -f <(tail -n 20 "my.log") -- "any idea what these error messages mean"
wsh ai -f README.md "help me update this readme file"

# creates a new AI block
wsh ai -n "tell me a story"

# targets block number 5
wsh ai -b 5 "tell me more"

# read from stdin and also supply a message
tail -n 50 mylog.log | wsh ai - "can you tell me what this error means?"
```

---

## editconfig

You can easily open up any of Wave's config files using this command.

```
wsh editconfig [config-file-name]

# opens the default settings.json file
wsh editconfig

# opens presets.json
wsh editconfig presets.json

# opens widgets.json
wsh editconfig widgets.json

# opens ai presets
wsh editconfig presets/ai.json
```

---

## setbg

The `setbg` command allows you to set a background image or color for the current tab with various customization options.

```
wsh setbg [--opacity value] [--tile|--center] [--size value] (image-path|"#color"|color-name)
```

You can set a background using:

- An image file (displayed as cover, tiled, or centered)
- A hex color (must be quoted like "#ff0000")
- A CSS color name (like "blue" or "forestgreen")

Flags:

- `--opacity value` - set the background opacity (0.0-1.0, default 0.5)
- `--tile` - tile the background image instead of using cover mode
- `--center` - center the image without scaling (good for logos)
- `--size` - size for centered images (px, %, or auto)
- `--clear` - remove the background
- `--print` - show the metadata without applying it

Supported image formats: JPEG, PNG, GIF, WebP, and SVG.

Examples:

```
# Set an image background with default settings
wsh setbg ~/pictures/background.jpg

# Set a background with custom opacity
wsh setbg --opacity 0.3 ~/pictures/light-pattern.png

# Set a tiled background
wsh setbg --tile --opacity 0.2 ~/pictures/texture.png

# Center an image (good for logos)
wsh setbg --center ~/pictures/logo.png
wsh setbg --center --size 200px ~/pictures/logo.png

# Set color backgrounds
wsh setbg "#ff0000"          # hex color (requires quotes)
wsh setbg forestgreen        # CSS color name

# Change just the opacity of current background
wsh setbg --opacity 0.7

# Remove background
wsh setbg --clear

# Preview the metadata
wsh setbg --print "#ff0000"
```

The command validates that:

- Color values are valid hex codes or CSS color names
- Image paths point to accessible, supported image files
- The opacity value is between 0.0 and 1.0
- The center and tile options are not used together

tip

Use `--print` to preview the metadata for any background configuration without applying it. You can then copy this JSON representation to use as a [Background Preset](https://docs.waveterm.dev/presets#background-configurations)

---

## run

The `run` command creates a new terminal command block and executes a specified command within it. The command can be provided either as arguments after `--` or using the `-c` flag. Unless the `-x` or `-X` flags are passed, commands can be re-executed by pressing `Enter` once the command has finished running.

```
# Run a command specified after --
wsh run -- ls -la

# Run a command using -c flag
wsh run -c "ls -la"

# Run with working directory specified
wsh run --cwd /path/to/dir -- ./script.sh

# Run in magnified mode
wsh run -m -- make build

# Run and auto-close on successful completion
wsh run -x -- npm test

# Run and auto-close regardless of exit status
wsh run -X -- ./long-running-task.sh
```

The command inherits the current environment variables and working directory by default.

Flags:

- `-m, --magnified` - open the block in magnified mode
- `-c, --command string` - run a command string in *shell*
- `-x, --exit` - close block if command exits successfully (stays open if there was an error)
- `-X, --forceexit` - close block when command exits, regardless of exit status
- `--delay int` - if using -x/-X, delay in milliseconds before closing block (default 2000)
- `-p, --paused` - create block in paused state
- `-a, --append` - append output on command restart instead of clearing
- `--cwd string` - set working directory for command

Examples:

```
# Run a build command in magnified mode
wsh run -m -- npm run build

# Execute a script and auto-close after success
wsh run -x -- ./backup-script.sh

# Run a command in a specific directory
wsh run --cwd ./project -- make test

# Run a shell command and force close after completion
wsh run -X -c "find . -name '*.log' -delete"

# Start a command in paused state
wsh run -p -- ./server --dev

# Run with custom close delay
wsh run -x --delay 5000 -- ./deployment.sh
```

When using the `-x` or `-X` flags, the block will automatically close after the command completes. The `-x` flag only closes on successful completion (exit code 0), while `-X` closes regardless of exit status. The `--delay` flag controls how long to wait before closing (default 2000ms).

The `-p` flag creates the block in a paused state, allowing you to review the command before execution.

tip

You can use either `--` followed by your command and arguments, or the `-c` flag with a quoted command string. The `--` method is preferred when you want to preserve argument handling, while `-c` is useful for shell commands with pipes or redirections.

---

## deleteblock

```
wsh deleteblock -b [blockid]
```

This will delete the block with the specified id.

---

## ssh

```
wsh ssh [user@host]
```

This will use Wave's internal ssh implementation to connect to the specified remote machine. The `-i` flag can be used to specify a path to an identity file.

---

## wsl

```
wsh wsl [-d <distribution-name>]
```

This will connect to a WSL distribution on the local machine. It will use the default if no distribution is provided.

---

## web

You can search for a given url using:

```
wsh web open [url]
```

Alternatively, you can search with the configured search engine using:

```
wsh web open [search-query]
```

Both of these commands will open a new web block with the desired page.

---

## notify

The `notify` command creates a desktop notification from Wave Terminal.

```
wsh notify [message] [-t title] [-s]
```

This allows you to trigger desktop notifications from scripts or commands. The notification will appear using your system's native notification system. It works on remote machines as well as your local machine.

Flags:

- `-t, --title string` - set the notification title (default "Wsh Notify")
- `-s, --silent` - disable the notification sound

Examples:

```
# Basic notification
wsh notify "Build completed successfully"

# Notification with custom title
wsh notify -t "Deployment Status" "Production deployment finished"

# Silent notification
wsh notify -s "Background task completed"
```

This is particularly useful for long-running commands where you want to be notified of completion or status changes.

---

## conn

This has several subcommands which all perform various features related to connections.

### status

```
wsh conn status
```

This command gives the status of all connections made since waveterm started.

### reinstall

For ssh connections,

```
wsh conn reinstall [user@host]
```

For wsl connections,

```
wsh conn reinstall [wsl://<distribution-name>]
```

This command reinstalls the Wave Shell Extensions on the specified connection.

### disconnect

For ssh connections,

```
wsh conn disconnect [user@host]
```

For wsl connections,

```
wsh conn disconnect [wsl://<distribution name>]
```

This command completely disconnects the specified connection. This will apply to all blocks where the connection is being used

### connect

For ssh connections,

```
wsh conn connect [user@host]
```

For wsl connections,

```
wsh conn connect [wsl://<distribution-name>]
```

This command connects to the specified connection but does not create a block for it.

### ensure

For ssh connections,

```
wsh conn ensure [user@host]
```

For wsl connections,

```
wsh conn ensure [wsl://<distribution-name>]
```

This command connects to the specified connection if it isn't already connected.

---

## setconfig

```
wsh setconfig [<config-name>=<config-value>]
```

This allows setting various options in the `config/settings.json` file. It will check to be sure a valid config option was provided.

---

## file

The `file` command provides a set of subcommands for managing files across different storage systems, such as `wavefile`, `wsh` remote servers, and S3.

note

Wave Terminal is capable of managing files from remote SSH hosts, S3-compatible systems, and the internal Wave filesystem. Files are addressed via URIs, which vary depending on the storage system. If no scheme is specified, the file will be treated as a local connection.

URI format: `[profile]:[uri-scheme]://[connection]/[path]`

Supported URI schemes:

- `wsh` - Used to access files on remote hosts over SSH via the WSH helper. Allows for file streaming to Wave and other remotes.
  
  Profiles are optional for WSH URIs, provided that you have configured the remote host in your "connections.json" or "~/.ssh/config" file.
  
  If a profile is provided, it must be defined in "profiles.json" in the Wave configuration directory.
  
  Format: `wsh://[remote]/[path]`
  
  Shorthands can be used for the current remote and your local computer: `[path]` a relative or absolute path on the current remote `//[remote]/[path]` a path on a remote `/~/[path]` a path relative to the home directory on your local computer

- `s3` - Used to access files on S3-compatible systems. Requires S3 credentials to be set up, either in the AWS CLI configuration files, or in "profiles.json" in the Wave configuration directory.
  
  If no profile is provided, the default from your AWS CLI configuration will be used. Profiles from the AWS CLI must be prefixed with "aws:".
  
  Format:
  
  - `s3://[bucket]/[path]`
  - `aws:[profile]:s3://[bucket]/[path]`
  - `[profile]:s3://[bucket]/[path]`

- `wavefile` - Used to retrieve blockfiles from the internal Wave filesystem.
  
  Format: `wavefile://[zoneid]/[path]`
  
  Wave file locations can be:
  
  - `wavefile://block/...` - stored in the current block ("this" is also an alias for "block")
  - `wavefile://tab/...` - stored in the current tab
  - `wavefile://workspace/...` - stored in the current workspace ("ws" is also an alias for "workspace")
  - `wavefile://client/...` - stored globally for the client ("global" is also an alias for "client")
  - `wavefile://temp/...` - stored globally, but removed on startup/shutdown
  - `wavefile://[uuid]/...` - an entity id (can be a block, tab, workspace, etc.)

### cat

```
wsh file cat [file-uri]
```

Display the contents of a file. For example:

```
wsh file cat wavefile://block/config.txt
wsh file cat wavefile://client/settings.json
```

### write

```
wsh file write [file-uri]
```

Write data from stdin to a file. The maximum file size is 10MB. For example:

```
echo "hello" | wsh file write wavefile://block/greeting.txt
cat config.json | wsh file write //ec2-user@remote01/~/config.json
```

### append

```
wsh file append [file-uri]
```

Append data from stdin to a file, respecting a 10MB total file size limit. This is useful for log files or accumulating data. For example:

```
tail -f app.log | wsh file append wavefile://block/logs.txt
echo "new line" | wsh file append wavefile://client/notes.txt
```

### rm

```
wsh file rm [flag] [file-uri]
```

Remove a file. For example:

```
wsh file rm wsh://user@ec2/home/<USER>/config.txt
wsh file rm wavefile://client/settings.json
```

Flags:

- `-r, --recursive` - recursively deletes directory entries

### info

```
wsh file info [file-uri]
```

Display information about a file including size, creation time, modification time, and metadata. For example:

```
wsh file info wsh://user@ec2/home/<USER>/config.txtwsh file info wavefile://client/settings.json
```

### cp

```
wsh file cp [flags] [source-uri] [destination-uri]
```

Copy files between different storage systems. For example:

```
# Copy a wave file into your local filesystem
wsh file cp wavefile://block/config.txt ./local-config.txt

# Copy a local file into the wave filesystem
wsh file cp ./local-config.txt wavefile://block/config.txt

# Copy a remote file into the wave filesystem
wsh file cp wsh://user@ec2/home/<USER>/config.txt wavefile://client/config.txt

# Recursively copy a directory between two remote computers
wsh file cp wsh://user@ec2-1/home/<USER>/.config wsh://user@ec2-2/home/<USER>/.config -r
```

Flags:

- `-r, --recursive` - copies all files in a directory recursively
- `-f, --force` - overwrites any conflicts when copying
- `-m, --merge` - does not clear existing directory entries when copying a directory, instead merging its contents with the destination's

### mv

```
wsh file mv [flags] [source-uri] [destination-uri]
```

Move files between different storage systems. The source file will be deleted once the operation completes successfully. For example:

```
# Move a wave file into your local filesystem
wsh file mv wavefile://block/config.txt ./local-config.txt

# Move a local file into the wave filesystem
wsh file mv ./local-config.txt wavefile://block/config.txt

# Move a remote file into the wave filesystem
wsh file mv wsh://user@ec2/home/<USER>/config.txt wavefile://client/config.txt

# Recursively move a directory between two remote computers
wsh file mv wsh://user@ec2-1/home/<USER>/.config wsh://user@ec2-2/home/<USER>/.config -r
```

Flags:

- `-r, --recursive` - moves all files in a directory recursively
- `-f, --force` - overwrites any conflicts when moving

### ls

```
wsh file ls [flags] [file-uri]
```

List files in a directory. By default, lists files in the current directory for the current terminal session.

Examples:

```
wsh file ls wsh://user@ec2/home/<USER>/
wsh file ls wavefile://client/configs/
```

Flags:

- `-l, --long` - use long listing format showing size, timestamps, and metadata
- `-r, --recursive` - list subdirectories recursively
- `-1, --one` - list one file per line
- `-f, --files` - list only files (no directories)

When output is piped to another command, automatically switches to one-file-per-line format:

```
# Easy to process with grep, awk, etc.
wsh file ls wavefile://client/ | grep ".json$"
```

---

## launch

The `wsh launch` command allows you to open pre-configured widgets directly from your terminal.

```
wsh launch [flags] widget-id
```

The command will search for the specified widget ID in both user-defined widgets and default widgets, then create a new block using the widget's configuration.

Flags:

- `-m, --magnify` - open the widget in magnified mode, overriding the widget's default magnification setting

Examples:

```
# Launch a widget with its default settings
wsh launch my-custom-widget

# Launch a widget in magnified mode
wsh launch -m system-monitor
```

The widget's configuration determines the initial block settings, including the view type, metadata, and default magnification state. The `-m` flag can be used to override the widget's default magnification setting.

tip

Widget configurations can be customized in your `widgets.json` configuration file, which you can edit using `wsh editconfig widgets.json`

---

## getvar/setvar

Wave Terminal provides commands for managing persistent variables at different scopes (block, tab, workspace, or client-wide).

### setvar

```
wsh setvar [flags] KEY=VALUE...
```

Set one or more variables. By default, variables are set at the client (global) level. Use `-l` for block-local variables.

Examples:

```
# Set a single variable
wsh setvar API_KEY=abc123

# Set multiple variables at once
wsh setvar HOST=localhost PORT=8080 DEBUG=true

# Set a block-local variable
wsh setvar -l BLOCK_SPECIFIC=value

# Remove variables
wsh setvar -r API_KEY PORT
```

Flags:

- `-l, --local` - set variables local to the current block
- `-r, --remove` - remove the specified variables instead of setting them
- `--varfile string` - use a different variable file (default "var")
- `-b [blockid]` - used to set a specific zone (block, tab, workspace, client, or UUID)

### getvar

```
wsh getvar [flags] [key]
```

Get the value of a variable. Returns exit code 0 if the variable exists, 1 if it doesn't. This allows for shell scripting like:

```
# Check if a variable exists
if wsh getvar API_KEY >/dev/null; then
    echo "API key is set"
fi

# Use a variable in a command
curl -H "Authorization: $(wsh getvar API_KEY)" https://api.example.com

# Get a block-local variable
wsh getvar -l BLOCK_SPECIFIC

# List all variables
wsh getvar --all

# List all variables with null terminators (for scripting)
wsh getvar --all -0
```

Flags:

- `-l, --local` - get variables local to the current block
- `--all` - list all variables
- `-0, --null` - use null terminators in output instead of newlines
- `--varfile string` - use a different variable file (default "var")

Variables can be accessed at different scopes using the `-b` flag:

```
# Get/set at block level
wsh getvar -b block MYVAR
wsh setvar -b block MYVAR=value

# Get/set at tab level
wsh getvar -b tab MYVAR
wsh setvar -b tab MYVAR=value

# Get/set at workspace level
wsh getvar -b workspace MYVAR
wsh setvar -b workspace MYVAR=value

# Get/set at client (global) level
wsh getvar -b client MYVAR
wsh setvar -b client MYVAR=value
```

Variables set with these commands persist across sessions and can be used to store configuration values, secrets, or any other string data that needs to be accessible across blocks or tabs.

## wavepath

The `wavepath` command lets you get the paths to various Wave Terminal directories and files, including configuration, data storage, and logs.

```
wsh wavepath {config|data|log}
```

This command returns the full path to the requested Wave Terminal system directory or file. It's useful for accessing Wave's configuration files, data storage, or checking logs.

Flags:

- `-o, --open` - open the path in a new block
- `-O, --open-external` - open the path in the default external application
- `-t, --tail` - show the last ~100 lines of the log file (only valid for log path)

Examples:

```
# Get path to config directory
wsh wavepath config

# Get path to data directory
wsh wavepath data

# Get path to log file
wsh wavepath log

# Open log file in a new block
wsh wavepath -o log

# Open config directory in system file explorer
wsh wavepath -O config

# View recent log entries
wsh wavepath -t log
```

The command will show you the full path to:

- `config` - Where Wave Terminal stores its configuration files
- `data` - Where Wave Terminal stores its persistent data
- `log` - The main Wave Terminal log file

tip

Use the `-t` flag with the log path to quickly view recent log entries without having to open the full file. This is particularly useful for troubleshooting.
