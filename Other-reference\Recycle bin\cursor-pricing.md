Plans & Usage
Guide to Cursor usage tiers, request types, quotas, and billing options for models and team accounts

To view your current usage, you can visit the dashboard at cursor.com/dashboard

Understanding Usage
​
Request-Based System
Cursor uses a simple request-based system for all models. Each model costs a certain number of requests, which you can find on the models page.

Pro and Business plans include 500 requests per month. When you run out of included requests, you have two options:

Enable usage-based pricing to continue using models at their standard request costs
Continue using models with slow requests, which may experience delays as paying users are prioritized
​
Included Requests
Every paid subscription includes 500 requests per month. The specific number depends on your plan as shown in the plan comparison above.

​
Additional Usage Options
​
Usage-based Pricing
You may opt in to usage-based pricing for requests beyond your plan’s included quota from your dashboard.

Usage-based pricing is only available with a paid subscription.

From the dashboard, you can configure a spend limit in USD to ensure you never exceed your budget. You can also enable or disable usage-based pricing at any time.

We will bill for additional requests when you’ve made requests totaling $20, or on the 2nd or 3rd day of the month, whichever comes first.

Single invoice

375 requests ($15) will be billed at the beginning of the next month since the total value is under $20


Multiple invoices

1150 requests ($46) will be billed 3 times:


1. When first batch of 500 requests has been made ($20)

2. When second batch of 500 requests has been made (also $20)

3. Beginning of next month (remaining $6)

For team accounts, administrators can restrict usage-based pricing settings to admin-only access.

Cost per request for each model can be found on the models page.

​
Max Mode
For more demanding tasks, you can use Max mode which charges based on token usage rather than requests. This is particularly useful for tasks requiring larger context windows or more complex reasoning. Learn more about Max mode pricing in our Max mode documentation.

​
FAQ
​
When do my requests reset?
Your requests reset on a fixed monthly date based on when you first set up your plan. If you purchase additional requests through usage-based pricing, the reset date remains unchanged. For instance, if your plan started on the 23rd, your requests will always reset on the 23rd of each month.

​
What does “500 requests” mean for teams?
Each user gets their own quota of 500 requests per month. These requests are not pooled across the team - every team member gets their own fresh 500 requests when their personal monthly cycle resets.

​
How do slow requests work?
When you run out of fast requests, the system automatically switches to using slow requests. Slow requests include a waiting period before they are processed.

The waiting time varies based on your slow request usage. If you’ve used many slow requests in a month, you may experience longer delays as we balance system load. Wait times can range from a few seconds to a minute or more depending on usage patterns.

​
Why use slow requests?
Slow requests provide a free way to continue using premium models without needing to upgrade your plan or opt into usage-based pricing immediately. They’re available to all Pro and Business subscribers, allowing you to keep working within your plan limits even after exhausting your fast requests.

​
How can I avoid waiting in the queue?
The simplest way to avoid queue times is to enable usage-based pricing through your dashboard. This ensures immediate access to fast requests, and you’ll only pay for requests that exceed your plan’s included quota. You can set a spending limit to control costs.

To stay within your fast request limit, regularly check your usage on the dashboard.


