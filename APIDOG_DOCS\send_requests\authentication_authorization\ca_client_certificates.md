CA and client certificates
You can add and manage certificates in Apidog to enable authentication when sending requests.

To connect to an API that uses Mutual TLS (mTLS), you need to add a client certificate to Apidog. Mutual TLS is an authentication method that requires both the client and the server to confirm their identity with a certificate. Once the identity of both parties is confirmed, an encrypted connection is established.

You can also add a custom CA certificate to Apidog. If an endpoint uses a certificate that's registered with an internal certificate registry, requests sent from Apidog will fail with SSL Error: Self signed certificate. Adding a custom CA certificate will enable you to send requests to the endpoint without needing to turn off SSL verification.

Managing certificates
In the Apidog settings, you can view installed certificates, add a new certificate, or remove a certificate.

1
Select the settings icon  in the top right.
2
Select the Certificates tab.

Adding CA certificates
To avoid "self signed certificate" errors when sending requests, add your custom CA certificate to Apidog.

1
Turn on the toggle next to CA Certificates.
2
Select the PEM file for your CA certificate. (The PEM file can contain multiple CA certificates.)

Adding Client certificates
To send requests to an API that uses mutual TLS authentication, add your client certificate to Apidog.

1
Select Add Certificate.
2
Enter the Host domain for the certificate (don't include the protocol).
The Host field supports pattern matching. If you enter *.example.com, the same client certificate will be used for all example.com subdomains.
3
(Optional) Enter a custom port number to associate with the domain. If you don't specify a port, <PERSON>pidog uses the default HTTPS port (443).
4
Select the CRT file and the Key file for your certificate OR select the PFX file for your certificate.
5
If you used a Passphrase when generating the client certificate, enter it in the box. Otherwise, leave the box blank.
6
Select Add.

Each client certificate is specific to a domain. To send requests to more domains, add the appropriate certificate for each domain. Don't add more than one certificate for the same domain. If you add more than one certificate for a domain, Apidog will use the last certificate added.

Editing a certificate
You can't edit a certificate after adding it. To make changes, first remove the certificate, then generate a new certificate and add it to Apidog.

Removing a certificate
Remove a certificate if you no longer need it to send requests from Apidog.

To remove a CA certificate, select the remove icon Close icon next to the certificate.

To remove a client certificate, select the delete icon Delete icon next to the certificate.

Using a certificate
After adding a client certificate, you don't have to perform any extra steps to use the certificate in Apidog. When you make an HTTPS request to a configured domain, Apidog automatically sends the client certificate with the request. The certificate is sent using OpenSSL handling, and Apidog doesn't change the certificate.

Apidog won't send the certificate if you make an HTTP request.

