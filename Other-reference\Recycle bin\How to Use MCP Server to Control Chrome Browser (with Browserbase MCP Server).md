# Pro Tip

**Looking for the ultimate all-in-one API development platform? Try [Apidog](https://apidog.com/) to streamline your API design, testing, and collaboration—all in one place!**

# Mastering Chrome Automation with Browserbase MCP Server: A Fresh Guide

Ever imagined your AI seamlessly browsing the web on your behalf? With the **Browserbase MCP Server**, that vision is now a reality. This innovative tool empowers AI models—like <PERSON>, GPT, and Gemini—to interact with Chrome browsers, automating everything from navigation to data extraction. Whether you’re a developer, data enthusiast, or just exploring AI’s potential, this guide will help you harness the Browserbase MCP Server for browser automation. Let’s dive in!

## Introducing the Browserbase MCP Server

The **Browserbase MCP Server** leverages the Model Context Protocol (MCP) to bridge AI models and web browsers. This means your AI can:

- Visit any website
- Click elements and complete forms
- Scrape data from web pages
- Capture screenshots
- Execute custom JavaScript
- Manage multiple browser sessions simultaneously

It’s like giving your AI a virtual set of hands for the web. Plus, it’s compatible with leading AI models including OpenAI’s GPT, <PERSON><PERSON><PERSON>’s <PERSON>, and Google’s <PERSON>.

![browserbase mcp server](https://assets.apidog.com/blog-next/2025/07/cover-mcp.png)

## Why Choose Browserbase MCP Server?

Here’s why this tool stands out for AI-driven browser automation:

- **Cloud-First Automation**: Run browser tasks remotely—no local setup required.
- **Supports Multiple AI Models**: Integrate with your preferred LLM.
- **Concurrent Sessions**: Automate several browser tasks at once.
- **Visual Intelligence**: Take and analyze screenshots.
- **Effortless Integration**: Works smoothly with clients like Claude Desktop and Cursor.

Whether you’re automating workflows or building AI-powered apps, Browserbase MCP Server unlocks new possibilities.

## Getting Started: Installation & Setup

You can use Browserbase MCP Server via a hosted service, npm, or a local installation. Here’s how to get started with each method.

### Prerequisites

Before you begin, make sure you have:

- **An MCP-compatible AI client** (e.g., [Claude Desktop](https://www.anthropic.com/claude) or [Cursor](https://cursor.sh/))
- **A Browserbase API key** from [Browserbase](https://www.browserbase.com/)

![browserbase official website](https://assets.apidog.com/blog-next/2025/07/Screenshot-2025-07-22-202459.png)

#### For Local Installations (Optional)

If you want to run the server locally, you’ll also need:

- **Git** for cloning repositories
- **pnpm** from [pnpm.io](https://pnpm.io/)
- **Node.js** from [nodejs.org](https://nodejs.org/)
- **An API key for your chosen AI model** (e.g., [Gemini](https://ai.google.dev/gemini-api))

### Option 1: Use the Hosted Browserbase MCP Server (Quickest)

Skip the setup and use the hosted service:

1. **Get Your API Keys & MCP URL**: Visit [smithery.ai](https://smithery.ai/) for your credentials.

![smithery ai](https://assets.apidog.com/blog-next/2025/07/smithery.jpg)

2. **Configure Your AI Client**: Add the provided MCP URL to your client’s settings. For Claude Desktop, update `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "browserbase": {
      "url": "https://your-smithery-url.com"
    }
  }
}
```

Or, for the `mcp-remote` CLI:

```json
{
  "mcpServers": {
    "browserbase": {
      "command": "npx",
      "args": ["mcp-remote", "https://your-smithery-url.com"]
    }
  }
}
```

3. **Restart Your Client**: Relaunch your AI client to activate the new settings.

You’re now ready to automate Chrome via the Browserbase MCP Server!

### Option 2: Run Browserbase MCP Server via NPM (Recommended)

For maximum compatibility, use npm:

1. **Update Your MCP Config**: Add the following to your config file:

```json
{
  "mcpServers": {
    "browserbase": {
      "command": "npx",
      "args": ["@browserbasehq/mcp"],
      "env": {
        "BROWSERBASE_API_KEY": "",
        "BROWSERBASE_PROJECT_ID": "",
        "GEMINI_API_KEY": ""
      }
    }
  }
}
```

2. **Insert Your API Keys**: Fill in your actual credentials.

### Option 3: Local Installation for Full Control

Prefer hands-on control? Here’s how to run the server locally:

1. **Clone the Repo**:

```bash
git clone https://github.com/browserbase/mcp-server-browserbase.git
cd mcp-server-browserbase
```

2. **Install & Build**:

```bash
pnpm install && pnpm build
```

3. **Start the Server**:
   - For STDIO (local process), configure your MCP client:

```json
{
  "mcpServers": {
    "browserbase": {
      "command": "node",
      "args": ["path/to/mcp-server-browserbase/cli.js"],
      "env": {
        "BROWSERBASE_API_KEY": "your_api_key",
        "BROWSERBASE_PROJECT_ID": "your_project_id",
        "GEMINI_API_KEY": "your_gemini_key"
      }
    }
  }
}
```

   - For HTTP transport, start the server:

```bash
node cli.js --port 3000
```

   - Then, point your client to `http://localhost:3000`.

4. **Add Your API Keys**: Replace placeholders with your real keys.

5. **Restart Your Client**: Relaunch to connect to the local server.

### Server Customization

Browserbase MCP Server supports several flags for advanced use:

- `--proxies`: Enable privacy proxies
- `--advancedStealth`: Activate stealth mode (requires Scale Plan)
- `--browserWidth` & `--browserHeight`: Set window size
- `--modelName`: Specify a different AI model

Add these to the `args` array in your config, e.g.:

```json
"args": ["path/to/cli.js", "--proxies", "--browserWidth=1920"]
```

## Using Browserbase MCP Server to Automate Chrome

With setup complete, here’s how to control Chrome:

### 1. Connect Your AI Client

Ensure your client (like Claude Desktop or Cursor) is linked to the Browserbase MCP Server using one of the above methods.

### 2. Send Commands

Issue natural language instructions in your AI client, such as:

- "Go to [https://example.com](https://example.com/)"
- "Click the 'Sign Up' button"
- "Fill the email field with '[<EMAIL>](mailto:<EMAIL>)' and submit"
- "Take a screenshot of the homepage"
- "Extract all product titles from this page"

The AI will translate your requests into browser actions via the Browserbase MCP Server.

### 3. Review Results

Depending on your command, you’ll receive:

- Action confirmations (e.g., "Navigated to [https://example.com](https://example.com/)")
- Extracted data (like product lists)
- Screenshots (displayed or saved)

For example, after requesting a screenshot, you might see:

> "Screenshot captured and saved as 'homepage.png'"

## Example: Automating a Google Search

Let’s walk through a practical test:

1. **Open Your AI Client** (e.g., Claude Desktop)
2. **Command Example**:

> "Go to [https://google.com](https://google.com/), search for 'Browserbase MCP Server', and click the first result"

3. **Watch the Automation**: If running locally with HTTP, you’ll see Chrome perform the actions.
4. **Check the Output**: Your AI client should confirm the steps and may extract page content.

![Install claude](https://assets.apidog.com/blog-next/2025/07/download_cluade-2.png)

## Troubleshooting

- **Server won’t start?** Double-check your API keys and ensure `pnpm build` succeeded.
- **Client not connecting?** Review your MCP config and restart the client.
- **Actions failing?** Some sites may need advanced stealth or proxies.
- **Model issues?** Confirm your model API key and capabilities.

## Wrapping Up

You’re now equipped to automate Chrome with AI using the Browserbase MCP Server! Whether you opt for the hosted, npm, or local approach, this tool brings powerful web automation to your AI projects. Ready to take your automation further? Start integrating Browserbase MCP Server into your daily workflows or next big idea.
