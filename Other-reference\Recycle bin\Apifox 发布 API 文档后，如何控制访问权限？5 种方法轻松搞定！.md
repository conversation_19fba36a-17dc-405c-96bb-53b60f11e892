# After Publishing API Docs in Apidog, How Do You Control Access? 5 Easy Methods!

So, you've finished your API docs in Apidog and you're ready to share them with the world—or maybe just your team. But wait: **who should actually be able to see these docs?**

- Should they be public for everyone?
- Only for your internal team?
- Or maybe just for a specific partner, but not the whole internet?

Managing separate docs for every scenario is a headache. Thankfully, Apidog gives you flexible access control options so you can share your docs exactly how you want. Here's a quick, developer-friendly guide to each method:

---

## 1. Public Release: Open to All

**Best for:** Public APIs, open products, or when you want maximum exposure.

- Anyone with the link can view your docs—no restrictions.
- Want even more visibility? Publish to the [API Hub](https://apifox.com/apihub) to reach more developers.

---

## 2. Password Protection: Simple and Quick

**Best for:** Temporary sharing with external users or quick team collaboration.

- Set a password—only those who know it can access the docs.
- Super easy to set up. Change the password or take the docs offline when you're done.
- **Heads up:** Passwords can be shared, so it's not the most secure option for sensitive info.

---

## 3. IP Whitelist: Lock It Down by Network

**Best for:** Company intranets, office networks, or partners with fixed locations.

- Only users from specified IP addresses or ranges can access the docs.
- Acts like a firewall—if the IP doesn't match, access is denied.
- Also works for team access control in Apidog (requires Business/Pro edition).

---

## 4. Email Whitelist: Control by Email Verification

**Best for:** Teams or partners who work remotely or change locations often.

- Add corporate emails to the whitelist—users verify via email to access docs.
- Supports wildcards (e.g., anyone with @apidog.com can get in).
- Easy to manage: add or remove emails as team members join or leave.
- Even if the link is shared, only authorized emails can access.

---

## 5. Custom Login Page: Use Your Own User System

**Best for:** Enterprises with complex permission needs or existing user systems.

- Redirects users to your own login page for authentication.
- After login, your server issues a JWT token; Apidog checks the token before granting access.
- Gives you full control over permissions and user management.
- [See the docs for setup details.](https://docs.apifox.com/5701992m0#%E8%87%AA%E5%AE%9A%E4%B9%89%E7%99%BB%E5%BD%95%E9%A1%B5)

---

## How to Choose?

Pick the method that fits your scenario:

- **Public Release:** For open APIs and maximum reach
- **Password Protection:** For quick, temporary sharing
- **IP Whitelist:** For secure, fixed-location access
- **Email Whitelist:** For flexible, user-based control
- **Custom Login:** For advanced, enterprise-level needs

**Pro Tip:** You can mix and match! For example, use IP whitelist for internal staff, email whitelist for partners, and password protection for demos. Apidog lets you publish multiple doc sites, each with its own access rules.

From your second doc site onward, you can select which endpoints to share and pair each with the right access control. That way, every audience sees only what they need—giving you both access and content control for truly fine-grained permissions.
