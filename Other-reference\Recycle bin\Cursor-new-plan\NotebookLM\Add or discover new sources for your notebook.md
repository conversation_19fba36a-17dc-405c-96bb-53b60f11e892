# Add or discover new sources for your notebook

A source is a static copy of the source document you import or upload to the app. When you use NotebookLM, the model uses the sources you upload to answer your questions or complete your requests.

## Understand source types & limitations

NotebookLM supports these source types:

- Audio files
- Copy and pasted text
- Google Docs
- Google Slides
- Text, Markdown and PDF files
- Web URLs
- YouTube URLs of public videos
- Images (.png, .jpg, .jpeg), up to 10MB

Each source can contain up to 500,000 words or up to 200MB for uploaded files. You can include up to 50 sources.

**Tips:**

- Avoid uploading documents you don’t have rights to.
- You can copy and paste text to create a new source and add or edit the title upon creation.
- To chat with a specific set of sources in your Notebook, select them individually in the “Source” panel.
- When uploading multiple web URLs, separate links by a space or a new line.

## Summarize a source

NotebookLM offers 2 modes for summarizing sources. You can:

- Ask for a summary of specific topics from your source directly in chat.
- Find an auto-generated summary of the entire source in the Source Guide. In the left hand side source viewer, select "To open a source.”

To get more focused summaries in the chat, ask specific questions about the information you're looking for. When multiple sources are selected, mentioning source names in your query helps NotebookLM narrow its search. For example, instead of "Summarize this source," try "What are the key findings about dog training in the 'Dog Training 101' document?"

## Add source

Computer AndroidiPhone & iPad

1. On your computer, open [NotebookLM](https://notebooklm.google.com/).
2. Select Add ![](https://storage.googleapis.com/support-kms-prod/tKbhvylWdt3SXsLkNu9x93PCC8oxy8yqG5tc) button.
   - To discover a source: Select Discover ![](https://storage.googleapis.com/support-kms-prod/q2HfqB416xlGXOszGrDpuftsJnikpdq12rnN) button.
3. Select all the sources you want to include in your notebook.

### Import through Google Drive

- NotebookLM can’t delete or edit your original files in Drive. When you import Google Docs or Google Slides, the app makes a copy of the original file. NotebookLM may reformat the content to analyze and understand the information.
- NotebookLM doesn’t keep track of changes to the original doc automatically. You have to manually re-sync the imported Google Docs and Slides in the source viewer.
- You will only see the ”Click to sync with Google Drive” button in the “Sources” panel, if the original file has been updated since the last time you viewed the current source.
  - Select **Click to sync with Google Drive** to update your source.
- **Limitations:**
  - **Click to sync with Drive** in NotebookLM is only displayed if you have write access to the original Google Drive file.
  - Other types of sources need to be manually deleted and uploaded again. NotebookLM only keeps a static copy of the file at upload time.
  - NotebookLM does not import footnotes from Google Docs.
  - While NotebookLM can understand your tabs in Google Docs, content within sub-tabs don't get imported.

### Import through Web URL

- Only the text content of the given HTML webpage is scraped for use as a source. Images, embedded videos, or nested webpages are not imported. Paywalled webpages aren't supported.
- PDFs uploaded through URLs are treated as PDF sources and support both text and images.

### Import through YouTube URL

- Only public YouTube videos with captions, either user-uploaded or auto-generated, are supported.
- Only the text transcript of the video is imported as a source.
- Videos uploaded less than 72 hours prior may not be available to import.
- Videos without speech aren't supported.
- If a video is deleted or made private, sources are auto-deleted from your notebook within 30 days.
- There is no limit for the length of the video unless the caption file contains over 500,000 words.
- Your import can fail for a number of reasons; the most common are:
  - The YouTube link is invalid.
  - The video is potentially unsafe.
  - The content doesn't have a captions file.
  - The video language is not currently supported.

### Import a local audio file

- The audio file is transcribed at the time of import and its text is saved to use as a new source.
- Supported audio file types include MP3 and WAV, among others.
- Audios with no speech aren't supported.

Languages supported for audio import

**Tip:** If the source content is too short, NotebookLM references the entire document without citing individual text from your source.

## How to use Discover Sources

The Discover Sources feature in NotebookLM allows you to easily discover and import relevant sources from the web directly into your notebook. Discover Sources helps you more conveniently begin new notebooks and build a comprehensive collection of materials.

The Discover Sources button is in the Sources panel next to “Add source.”

1. **Initiate a source search:** In the Sources panel, select **Discover Sources**.
2. **Prompt the Search:**
   - Enter a search query in the “Describe something you’d like to learn about” prompt box.
   - To randomly bring in something new and exciting, select **I’m feeling curious**.
3. **Search and review results:** The most relevant search results are presented in a list and include:
   - The title
   - A brief description on how the source relates to your original query
   - A link to open the full webpage in a new window
4. **Select sources and import:** Select one or multiple sources from the search results to import into your notebook.
5. **Give feedback:** If you think a response generated in NotebookLM is offensive or unsafe, select the thumbs down button and choose **Offensive/unsafe** as the reason. To improve the safety of the model, the report is reviewed and we take appropriate actions.
