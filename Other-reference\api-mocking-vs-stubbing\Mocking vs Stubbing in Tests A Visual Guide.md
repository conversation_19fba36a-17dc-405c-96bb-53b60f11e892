# **🧪 Mocking vs. Stubbing in Tests: A Visual Guide**

![](https://miro.medium.com/v2/resize:fit:945/1*JMrLC6m6Y4OlidUfFJVUEg.png)

If you’ve ever found yourself in the middle of a test suite wondering whether you need a mock or a stub — you’re not alone. These two concepts often get thrown around in conversations about unit testing, but their differences can feel subtle, especially when you’re in the weeds of writing fast, reliable, and maintainable tests.

Let’s break down the difference between mocking and stubbing in a clear, visual, and practical way — with examples, analogies, and guidance on when to use which.

**🎭 What Are Mocks and Stubs, Really?**

At their core, both mocks and stubs are types of **test doubles** — replacements for real objects in your code. They allow you to isolate the unit you’re testing and control or observe its interactions with external dependencies.

But they serve different purposes.

**🧱 Stubs: “Just give me the data.”**

A **stub** is like a stand-in actor who only says their line when cued — nothing more. It provides **predefined responses** to method calls made during the test. You use stubs when your test doesn’t care how a method is called, just that it returns something so the test can continue.

*📦 Think of a stub as a fake shipping API that always says: “Your package will arrive tomorrow.”*

**Example (in JavaScript using sinon):**

const stub = sinon.stub(shippingService, 'getETA').returns('Tomorrow');

You use a stub when your test needs to **focus on logic that depends on an external value**, not on how that value was retrieved.

**🕵️ Mocks: “Tell me how they behave.”**

A **mock**, on the other hand, is a bit more nosy. It doesn’t just answer questions — it **remembers** how it was used. It can verify whether it was called, how many times, with which arguments, and more.

*🎤 Think of a mock as a microphone at a press conference — it records everything you say and how you said it.*

**Example:**

const mock = sinon.mock(paymentService);  
mock.expects('chargeCard').once().withArgs('1234-5678', 100);

You use a mock when you want to **verify interactions** — whether a method was called, how many times, and with what parameters.

**🤔 When to Use What?**

Here’s a simple heuristic:

• **Use a stub** when your code needs a canned response from a collaborator so it can execute correctly.

• **Use a mock** when your test is asserting that a collaborator was **used correctly**.

**Example: Testing an Email Notification**

Let’s say you’re testing a UserService that sends an email after creating a user.

• Use a **stub** for the email service if you don’t care whether the email was sent — maybe you’re just testing the database write.

• Use a **mock** if you want to **assert** that the email function was indeed called after user creation.

**🎨 Visual Breakdown**

Here’s a simple flow:

                   +-----------------+  
                   |  UserService    |  
                   +-----------------+  
                          |  
                +---------------------+  
                | Needs email service |  
                +---------------------+  
                          |  
    ---------------------------------------------  
    |                                           |  

[Stub: returns 'OK']               [Mock: asserts 'sendEmail() was called']

In a **stub setup**, the test moves on regardless of whether sendEmail was called.

In a **mock setup**, the test **fails** if sendEmail wasn’t called with the expected args.

**🔥 Common Gotchas**

• **Mocks can be overused**: Not everything needs behavioral verification. Over-mocking makes tests brittle.

• **Mocks and stubs aren’t mutually exclusive**: You might stub one dependency and mock another in the same test.

• **Watch for false positives**: If you stub something and forget to verify behavior where it’s needed, your test may pass but be incomplete.

**✨ Real-World Analogy: Restaurant Scenario**

Let’s say you run a restaurant and want to test your kitchen’s behavior.

• **Stub**: You simulate a food delivery and assume it always arrives in 10 minutes, no matter what.

• **Mock**: You track whether the delivery service **actually came** and **when** they came, to evaluate if they’re reliable.

**✅ Final Takeaways**

• **Stubs** are passive: they help your code keep moving by returning expected data.

• **Mocks** are active: they assert that specific interactions occurred.

• Prefer **stubs** for functional tests and **mocks** for behavioral tests.

• When in doubt, **start with stubs** — they’re simpler and safer. Reach for mocks only when interaction matters.

**👩‍💻 Your Turn**

Next time you write a test, pause and ask:

> *“Am I checking what the code* ***does****, or just what it* ***needs****?”*

That small distinction will guide you toward better test doubles — and cleaner, more meaningful tests.
