Import from apiDoc
Apidog supports importing or api_data.js files in api_data.json apiDoc.Please refer to Project Address for details on text format.

Export data
1.
Install the node environment locally, use 0.29.0 version, go to the directory where the source code apidoc is located, and run npx apidoc@0.29.0 -i src -o dist on the command.

2.
src is the source code file of the API, which is the file generated after the apidoc command is compiled. api_data.js and dist are the files that needed to be imported into Apidog.


Import data
Open the Project "Settings" in Apidog and click "Import Data", select the file and upload, supports api_data.js and api_data.json file.


