# Getting Started with Kiro AWS on Windows

## Getting Started with Kiro AWS (Windows)

Kiro AWS is a lightweight tool designed to streamline AWS workflows with a friendly desktop interface. In this guide, we’ll walk you through the installation process on **Windows** and get you up and running.

---

### [](https://dev.to/jajera/getting-started-with-kiro-aws-on-windows-56db#step-1-download-kiro-aws)Step 1: Download Kiro AWS

Download the latest version of Kiro AWS directly from:

[Downloads - <PERSON><PERSON>](https://kiro.dev/downloads)

You will be presented with a download page where you can choose the installer that matches your operating system:

- **Download for Mac (Apple Silicon)**
- **Download for Mac (Intel)**
- **Download for Windows**
- **Download for Linux (Debian/Ubuntu)**
- **Download for Linux (Universal)**

[![Kiro Downloads Page](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Ff95f83oo4fhlb99y1xqn.png)](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Ff95f83oo4fhlb99y1xqn.png)

Click the appropriate button to begin the download.

---

### [](https://dev.to/jajera/getting-started-with-kiro-aws-on-windows-56db#step-2-accept-the-license-agreement)Step 2: Accept the License Agreement

Once the installer launches, you’ll see the License Agreement screen.

[![License Agreement](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Fajzmo9cimtjcx2cx02mz.png)](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Fajzmo9cimtjcx2cx02mz.png)

Kiro AWS is licensed to you under the [AWS Customer Agreement](https://aws.amazon.com/agreement/) and the [AWS Intellectual Property License](https://aws.amazon.com/legal/aws-ip-license-terms/). To proceed, accept the agreement and click **Next**.

---

### [](https://dev.to/jajera/getting-started-with-kiro-aws-on-windows-56db#step-3-choose-installation-location)Step 3: Choose Installation Location

Next, choose where Kiro should be installed on your system. By default, it installs under your user directory:  

```
C:\Users\<USER>\AppData\Local\Programs\Kiro
```

[![Installation Location](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2F7v8z3de82rwhmidovcun.png)](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2F7v8z3de82rwhmidovcun.png)

You can click **Browse** to select a different folder or accept the default location and click **Next** to continue.

---

### [](https://dev.to/jajera/getting-started-with-kiro-aws-on-windows-56db#step-4-select-start-menu-folder)Step 4: Select Start Menu Folder

Now choose where the Start Menu shortcuts should be placed. The default folder name is `Kiro`.

[![Start Menu Folder](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Fuq4qrm5yhpyytca7hu3x.png)](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Fuq4qrm5yhpyytca7hu3x.png)

You can:

- Accept the default folder and click **Next**, or
- Check "Don't create a Start Menu folder" if you prefer not to create a shortcut

---

### [](https://dev.to/jajera/getting-started-with-kiro-aws-on-windows-56db#step-5-select-additional-tasks)Step 5: Select Additional Tasks

Choose any additional tasks you want Setup to perform during installation:

- [ ] Create a desktop icon
- [ ] Add "Open with Kiro" to the Windows Explorer file context menu
- [ ] Add "Open with Kiro" to the Windows Explorer directory context menu
- [x] Register Kiro as an editor for supported file types
- [x] Add to PATH (requires shell restart)

[![Additional Tasks](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2F3s8xx5jm6y2wqs7z2qs0.png)](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2F3s8xx5jm6y2wqs7z2qs0.png)

The last two options are selected by default, which is helpful for using Kiro from the terminal or as a default editor.

Click **Next** to continue.

---

### [](https://dev.to/jajera/getting-started-with-kiro-aws-on-windows-56db#step-6-confirm-installation-settings)Step 6: Confirm Installation Settings

Before the actual installation begins, you'll see a summary of your chosen configuration:

- **Destination location**: where Kiro will be installed
- **Start Menu folder**: the folder name for shortcuts
- **Additional tasks**: any extra options selected

[![Ready to Install](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2F0mkhieduj3xiwluvm0ah.png)](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2F0mkhieduj3xiwluvm0ah.png)

If everything looks correct, click **Install** to begin the installation. You can also click **Back** to review or change any previous steps.

---

### [](https://dev.to/jajera/getting-started-with-kiro-aws-on-windows-56db#step-7-installing-kiro)Step 7: Installing Kiro

The installer will now begin extracting files and installing Kiro on your system. This step may take a few moments.

[![Installing](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Fs5drf22swi3g3objavfk.png)](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Fs5drf22swi3g3objavfk.png)

Once the progress bar completes, Kiro will be fully installed and ready for launch.

---

### [](https://dev.to/jajera/getting-started-with-kiro-aws-on-windows-56db#step-8-finish-installation)Step 8: Finish Installation

When the installation is complete, you’ll see the final screen of the setup wizard.

[![Setup Complete](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Fyxf2q9i6mx3nkxt2d8q5.png)](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Fyxf2q9i6mx3nkxt2d8q5.png)

Click **Finish** to exit the Setup Wizard. By default, the **Launch Kiro** option is checked, so Kiro will open immediately after closing the installer.

---

### [](https://dev.to/jajera/getting-started-with-kiro-aws-on-windows-56db#step-9-sign-in-to-kiro)Step 9: Sign In to Kiro

Once Kiro launches for the first time, you’ll be prompted to sign in. You can choose from several authentication methods:

- **Google**
- **GitHub**
- **AWS Builder ID**
- **Organization Identity**

[![Sign In](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Fz6zxno6aphwhduqbja3m.png)](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Fz6zxno6aphwhduqbja3m.png)

Select the method that best suits your workflow to get started.

---

### [](https://dev.to/jajera/getting-started-with-kiro-aws-on-windows-56db#step-10-import-vs-code-configuration-optional)Step 10: Import VS Code Configuration (Optional)

Kiro offers a convenient option to migrate your VS Code setup, including:

- Extensions (only those available on Open VSX)
- Keybindings
- Editor settings

You can choose to:

- **Import from VS Code** to keep your familiar setup
- **Skip** this step and proceed with a fresh Kiro environment

[![Import Configuration](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Fysna864jp6jbkxmnvro9.png)](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Fysna864jp6jbkxmnvro9.png)

Kiro loads the extensions in the background so you can continue onboarding without delay.

---

### [](https://dev.to/jajera/getting-started-with-kiro-aws-on-windows-56db#step-11-choose-your-theme)Step 11: Choose Your Theme

Personalize your Kiro experience by selecting a theme. You can choose between:

- **Kiro Dark**: A sleek, low-light theme great for long coding sessions
- **Kiro Light**: A clean, bright theme suitable for well-lit environments

[![Choose Theme](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Ff07f4nfx7x52j8yr3wa1.png)](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Ff07f4nfx7x52j8yr3wa1.png)

Once you've selected your preferred theme, click **Continue** to proceed.

---

### [](https://dev.to/jajera/getting-started-with-kiro-aws-on-windows-56db#step-12-set-up-shell-integration)Step 12: Set Up Shell Integration

To complete the onboarding process, Kiro integrates with your system shell. This allows you to launch projects or files directly from the terminal using the `kiro` command.

[![Set Up Shell](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Fg2bpr0wvno3ywnt5ovfb.png)](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Fg2bpr0wvno3ywnt5ovfb.png)

You'll see a message confirming that the shell setup is complete. Click **Done** to finish the onboarding.

---

### [](https://dev.to/jajera/getting-started-with-kiro-aws-on-windows-56db#step-13-welcome-to-kiro)Step 13: Welcome to Kiro

After onboarding is complete, you’ll land on Kiro’s home screen.

Here you can:

- **Open a project**: Load a local workspace to start coding immediately
- **Reconnect to a recent project**: If you've used Kiro before
- **Connect to a remote workspace**: For cloud development environments

[![Welcome Screen](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Fcemlyt5irsve131shqiy.png)](https://media2.dev.to/dynamic/image/width=800%2Cheight=%2Cfit=scale-down%2Cgravity=auto%2Cformat=auto/https%3A%2F%2Fdev-to-uploads.s3.amazonaws.com%2Fuploads%2Farticles%2Fcemlyt5irsve131shqiy.png)

This is your new development hub — you’re now fully set up with Kiro AWS!
