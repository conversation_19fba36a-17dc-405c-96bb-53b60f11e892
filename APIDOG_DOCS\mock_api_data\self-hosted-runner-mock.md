Runner Mock is designed for use in intranet or private network where a Mock service needs to be provided. When a request is made, it’s sent to the server where the Runner is deployed. The Runner then generates and returns the mock response.

It is suggested that team or project admins follow these steps after [deploying the Runner](apidog://link/pages/755233):

## 1. Configure the Server Host

Go to "Team Resources -> General Runner", and enter the `IP` or `domain` of the server where the Runner is located in the `Server Host` of the deployed runner. 

<Tabs>
  <Tab title="Example 1">
   
    ```js
    https://runner.example.com:443
    ```
  </Tab>
  <Tab title="Example 2">

    ```js
    http://127.0.0.1:80
    ```
  </Tab>

</Tabs>

<Background>
![configure the server host.png](https://api.apidog.com/api/v1/projects/544525/resources/355210/image-preview)
</Background>

## 2. Verify the Runner Mock Environment

Navigate to the project’s "Environment Management" page and check whether the Runner Mock environment is listed. If it’s been properly configured, it will show up in the environment list.

<Background>
![verify the Runner mock environment.png](https://api.apidog.com/api/v1/projects/544525/resources/355217/image-preview)
</Background>

## 3. Use the Runner Mock Environment

When making an endpoint request, select the newly created Runner Mock environment. The request will be sent to the Runner server, which will return the Mock response.

<Background>
![use the Runner mock environment.png](https://api.apidog.com/api/v1/projects/544525/resources/355220/image-preview)
</Background>

## FAQ

<Accordion title="Does Runner Mock support HTTPS? How can I enable it?" defaultOpen>

Yes, Runner Mock **can be accessed via HTTPS**, but note the following:

- The **Runner itself does not include built-in HTTPS certificate support** or automatic certificate provisioning.
- As long as your domain is already configured with HTTPS and is accessible (for example, via a reverse proxy like Nginx with a certificate), you can use it directly.

✅ You only need to enter `https://your-domain:port` in the “Server Host” field to access the Runner Mock service over HTTPS, without any additional configuration on the Runner itself. For example:

 ```js
https://runner.example.com:443
```
    

<Frame>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/353876/image-preview)
</Frame>

</Accordion>


