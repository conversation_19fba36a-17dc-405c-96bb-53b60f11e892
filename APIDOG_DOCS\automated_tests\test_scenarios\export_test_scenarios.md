Test scenarios can be exported in `Apidog CLI`, `Postman`, and `J<PERSON>eter` data files, allowing them to run in local environments or external continuous integration pipelines. This integration facilitates seamless alignment with existing testing workflows within your team.

## Exporting configuration files

Click the `...` button on the right side of the test scenario and select the desired export format.


<Background>

![CleanShot 2025-05-07 at <EMAIL>](https://api.apidog.com/api/v1/projects/544525/resources/354511/image-preview)
</Background>


For detailed instructions, please refer to the [Installing and running Apidog CLI](apidog://link/pages/605135).