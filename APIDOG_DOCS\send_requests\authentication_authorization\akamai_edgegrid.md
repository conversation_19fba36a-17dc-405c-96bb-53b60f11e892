Akamai EdgeGrid
Akamai EdgeGrid is an HTTP request-based authentication protocol developed by Akamai that uses a signature algorithm to generate signatures, ensuring the integrity and authenticity of requests and preventing them from being tampered with or forged. For more information about Akamai EdgeGrid, please visit the official documentation.



Basic Settings
The basic authentication parameters for Akamai EdgeGrid are as follows:

Access Token

Used to identify the access token for the current request.

Client Token

Used to identify the client token for the current request.

Client Secret

Used to identify the client secret for the current request.

Advanced Settings
If you need to add more custom options, click the "Advanced" button to fill them in. If left blank, they will be automatically generated.

Nonce

A random string generated by the client.

Timestamp

A timestamp used to prevent requests outside the time window.

Base URL

Used to identify the API target address for the current request.

Headers to Sign

Select the request headers to be signed.



