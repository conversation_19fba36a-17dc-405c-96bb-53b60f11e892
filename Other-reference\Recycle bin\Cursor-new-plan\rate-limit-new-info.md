# [New info about rate limits!](https://forum.cursor.com/t/new-info-about-rate-limits/105392)



So, what exactly is this ‘compute usage’? The article mentions models, message length, file size, and conversation content. But how are these quantified into a unit? For instance, what is the compute unit for Sonnet? How many units does a 1000-word message equate to? There’s absolutely no explanation of a concrete quantitative standard!

Regarding the ‘slow to refill’ for burst limits, for how long exactly is it slow? This significantly impacts ‘Burst rate limits,’ and we need a specific timeframe.

And ‘refill fully every few hours’ – what does ‘a few hours’ precisely mean? Is it 2 hours? 6 hours? Or 12 hours?

Furthermore, there are no specific thresholds provided to define what constitutes ‘hitting a limit.’ For example, what is the upper limit in compute units for a ‘local rate limit’? What about the ‘Burst rate limit’?

This is completely a black box operation! Vague explanations like these only confuse consumers who are not familiar with the product!



So, what exactly is this ‘compute usage’? The article mentions models, message length, file size, and conversation content. But how are these quantified into a unit? For instance, what is the compute unit for Sonnet? How many units does a 1000-word message equate to? There’s absolutely no explanation of a concrete quantitative standard!

Regarding the ‘slow to refill’ for burst limits, for how long exactly is it slow? This significantly impacts ‘Burst rate limits,’ and we need a specific timeframe.

And ‘refill fully every few hours’ – what does ‘a few hours’ precisely mean? Is it 2 hours? 6 hours? Or 12 hours?

Furthermore, there are no specific thresholds provided to define what constitutes ‘hitting a limit.’ For example, what is the upper limit in compute units for a ‘local rate limit’? What about the ‘Burst rate limit’?

This is completely a black box operation! Vague explanations like these only confuse consumers who are not familiar with the product!



The Pro Plan can’t do this. I don’t like being rate-limited if I don’t use all 500 requests. I tested opting out and then opting in again, but the button was disabled



![Ảnh chụp màn hình 2025-06-19 095057](https://us1.discourse-cdn.com/cursor1/original/3X/4/3/43b38cc33c356bdbd64bfb6ecf512fa61800a4a8.png)
