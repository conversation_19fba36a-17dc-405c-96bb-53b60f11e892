Currently, most APIs are differentiated based on the `method and path`. However, some development projects (such as some e-commerce API documents) use a fixed URL for the API request and differentiate between APIs using parameters in the Query / Header.

After version 2.2.9, <PERSON><PERSON><PERSON> added the `endpoint unique Identification` feature, which supports `OperationId`, `Query parameters`, `Body parameters`, and `Header parameters` as parameters to differentiate between APIs.

## 1. Setting endpoint unique identification

`Endpoint unique ID` is defined as a set of the `directory` level. When you need to set an API as a unique identification, you need to set it in its `parent directory`. Click on the directory and choose the unique identification parameter according to your needs, and after clicking save it will take effect on all APIs under that directory.


<Background>

![CleanShot 2024-11-27 at <EMAIL>](https://api.apidog.com/api/v1/projects/544525/resources/348231/image-preview)
</Background>

For this example we will choose Query parameter and write 'OperationiId' inside param name.

## 2. Fill in the corresponding Parameter Value for the Unique Identifier

After setting the endpoint unique Identification for the directory, click on an API under that directory, click the `operationid` tab, and in both the basic information and request parameters at the bottom of the API, there is an icon of `K`, which represents the parameter for the `endpoint unique ID`. 


<Background>

![CleanShot 2024-11-27 at <EMAIL>](https://api.apidog.com/api/v1/projects/544525/resources/348232/image-preview)
</Background>



You can enter the corresponding value under the corresponding parameter as the value for the endpoint unique Identification.  

## 3. Import

If you use parameters in Query/Header to distinguish between APIs in your project and import an OpenFormatted file into Apidog, the following page will appear.

The rule for matching APIs during import is subject to the settings of the target directory. If the setting of the endpoint unique Identification in the target directory does not meet the requirements, you can modify it in the import settings. After modification, it will take effect directly on the target directory

As an example we will import this directory and create endpoint unique ID for it with `Query Param` and `Param Name` for it called  'action'.


<Background>

![CleanShot 2024-11-27 at <EMAIL>](https://api.apidog.com/api/v1/projects/544525/resources/348236/image-preview)
</Background>


Remember if your directory  alredy has unqiue ID,the new import can't overwrite it.


<Background>

![CleanShot 2024-11-27 at <EMAIL>](https://api.apidog.com/api/v1/projects/544525/resources/348233/image-preview)
    
    
![CleanShot 2024-11-27 at <EMAIL>](https://api.apidog.com/api/v1/projects/544525/resources/348237/image-preview)

</Background>




:::tip[]

1. Users who have used the `Fixed Value` in Query parameters need not worry because this function will still be retained. However, when importing, the `Fixed Value` is judged based on the URL, so it is recommended that users who have used the `Fixed Value` use the endpoint unique Identification.

2. The endpoint unique Identification supports setting multiple parameters.

3. If only a subdirectory in your directory is set as the `endpoint unique Identification`, when importing Swagger and updating all directories, please avoid importing all projects to the root directory for updating. It is recommended to import APIs set as `endpoint unique Identification` separately into that special.
   :::

## 4. Mock Data

Starting from version 2.2.24, if the API has set the `unique identifier` as `Body Parameter` or `Header Parameter`, you need to send the `path + parameter name and value of the unique identifier` to get the corresponding Mock Data.



:::tip[]

1. When accessing Mock Data during development, frontend developers also need to send the `path + parameter name and value of the unique identifier` if the API has set the `unique identifier` as `Body Parameter` or `Header Parameter`.
2. For projects that have a `unique identifier` for APIs, the API documentation needs to be standardized to avoid cases where APIs have the same URL but do not have a `unique identifier` set. This is to avoid the failure of obtaining Mock Data correctly.
   :::