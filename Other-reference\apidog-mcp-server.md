![NPM Version](https://img.shields.io/npm/v/apidog-mcp-server)

The **Apidog MCP Server** allows you to use your API specification as a data source for AI-powered IDEs like **Cursor**. This means Agentic AI can directly access and work with your API specification, speeding up development and making your workflow more efficient.

<Video src="https://www.youtube.com/watch?v=lw046MO5POY"></Video>


With Apidog MCP Server, developers can leverage the AI assistant to:

- Generate or modify code based on API specification.
- Search through API specification content.
- And much more! Let your imagination and your team's creativity guide you. 😜

## 🎯 How Apidog MCP Server Works?

Once the Apidog MCP Server is set up, it automatically reads and caches all API specification data on your local machine. The AI can then retrieve and utilize this data seamlessly.

Simply instruct the AI on what you’d like to achieve with the API specification. Here are some examples:

1. **Generate Code**: "Use MCP to fetch the API specification and generate Java records for the 'Product' schema and related schemas".
2. **Update DTOs**: "Based on the API specification, add the new fields to the 'Product' DTO".
3. **Add Comments**: "Add comments for each field in the 'Product' class based on the API specification".
4. **Create MVC Code**: "Generate all the MVC code related to the endpoint '/users' according to the API specification".

**Note:** API documentations are cached locally. If data in Apidog changes, ask the AI to refresh to ensure it reads the latest updates.

## 🚀 Installation Guide

### **Prerequisites**

- **Node.js** (version 18 or higher, preferably the latest LTS version).
- An IDE that supports MCP, such as:
  - **Cursor**
  - **VS Code + Cline plugin**

### Choose Configuration Method Based on Different Data Source

Apidog MCP Server supports connecting AI to three different data sources. Select the corresponding configuration method based on your needs:

<AccordionGroup>
  <Accordion title="Use Apidog Project as the Data Source" defaultOpen>

**Use Case:** 
- Use AI to access API specification within your Apidog team

**Features:** 
- Apidog personal API access token required


For **detailed setup**, see: [Connecting API Specification within Apidog Project to AI via Apidog MCP Server](apidog://link/pages/901476)
  </Accordion>
      
   <Accordion title="Use Online API Documentation Published by Apidog as the Data Source">

**Use Case:**
- Use AI to read API documentation published via Apidog by others
- Allow external developers (API consumers) to access your team's public online API documentation via AI

**Features:**
- No Apidog personal API access token required
- Only supports publicly accessible API documentation (documentation with passwords or allowlist settings are not supported.)
- For non-public API specification within your team, refer to [Conntect API Specificatio within Apidog Project to AI via Apidog MCP Server](apidog://link/pages/901476)
       
For **detailed setup**, see: [Connecting Online API Documentation Published by Apidog to AI via Apidog MCP Server](apidog://link/pages/901468)
  </Accordion>
      
  <Accordion title="Use Swagger/OpenAPI Files as the Data Source">

**Use Case:**
- Use AI to read local or online Swagger/OpenAPI files

**Features:**
- Independent of Apidog projects or online API documentations
- No Apidog personal API access token required 

For **detailed setup**, see: [Connecting OpenAPI Files to AI via Apidog MCP Server](apidog://link/pages/901477) 
  </Accordion>
</AccordionGroup>

## Apidog MCP Server Configuration for On-Premise Deployment

For users of the on-premise deployment, regardless of which configuration method you choose above, you must include your on-premise server's API address in the configuration: "--apidog-api-base-url=`<API address of the on-premise server, starting with http:// or https://>`"

:::tip[]
**Note**: Ensure that your can access www.npm.com properly.
:::

## ❓ Help and Support

The **Apidog MCP Server** is currently in beta. We’d love to hear your feedback and suggestions! Join our [Discord](https://discord.com/invite/ZBxrzyXfbJ) or [Slack](https://join.slack.com/t/apidogcommunity/shared_invite/zt-2sgz1kxxe-QvLy_LfE6piCYzw_c~kunQ) community for support and updates.

# Conntect API Specification within Apidog Project to AI via Apidog MCP Server

Apidog MCP Server enables AI to connect and utilize API specifications within your Apidog project.

## Configuring the MCP Client

### Prerequisites

- Node.js environment installed (version ≥ 18, latest LTS recommended)
- Any IDE that supports MCP:
  - Cursor
  - VSCode + Cline extension
  - Others

### Obtaining API Access Token and Project ID

<Steps>
  <Step title="Generate API Access Token">
   - Open Apidog, hover over your profile picture (top-right), and select `Account Settings` → `API Access Token`
   - [Create a new API access token](apidog://link/pages/640857) and replace `<access-token>` in the configuration below
      
    <Background>  
![creating-new-api-access-token.png](https://api.apidog.com/api/v1/projects/544525/resources/352790/image-preview)
    </Background>
 
  </Step>
  <Step title="Get Apidog Project ID">
   - Open your target project in Apidog
   - Click "Project Settings" in the left sidebar, then copy the Project ID from "Basic Settings"
   - Replace `<project-id>` in the configuration below 
      
    <Background>
![apidog-project-id.png](https://api.apidog.com/api/v1/projects/544525/resources/352791/image-preview)
    </Background>

  </Step>
</Steps>

### Configuring MCP in Cursor

<Steps>
  <Step title="Edit MCP Config File">
Open Cursor editor, click the settings icon (top-right), select "MCP" from the left menu, then click "+ Add new global MCP server".
      
<Background>
![mcp-server-setting-cursor.png](https://api.apidog.com/api/v1/projects/544525/resources/352759/image-preview)
</Background>

  </Step>
  <Step title="Add Configuration">
Paste the following configuration in the opened `mcp.json`. Remember to replace `<access-token>` and `<project-id>` with your own:
      
    <Tabs>
      <Tab title="macOS / Linux">
        ```json
        {
          "mcpServers": {
            "API specification": {
              "command": "npx",
              "args": [
                "-y",
                "apidog-mcp-server@latest",
                "--project=<project-id>"
              ],
              "env": {
                "APIDOG_ACCESS_TOKEN": "<access-token>"
              }
            }
          }
        }
        ```
      </Tab>
      <Tab title="Windows">
        ```json
        {
          "mcpServers": {
            "API specification": {
              "command": "cmd",
              "args": [
                "/c",
                "npx",
                "-y",
                "apidog-mcp-server@latest",
                "--project=<project-id>"
              ],
              "env": {
                "APIDOG_ACCESS_TOKEN": "<access-token>"
              }
            }
          }
        }
        ```
      </Tab>
    </Tabs>  
  </Step>
  <Step title="Verify Configuration">
    Test the connection by asking the AI (in Agent mode):

```
Please fetch API specification via MCP and tell me how many endpoints exist in the project
```   
Successful connection is confirmed when AI returns your Apidog project's API information.

<Background>
![connecting-apidog-project-with-idea.png](https://api.apidog.com/api/v1/projects/544525/resources/352792/image-preview)
</Background>
  </Step>

</Steps>    
    
## Important Notes

- Replace `<access-token>` and `<project-id>` with your personal Apidog API access token and project ID.
- If you need to work with API specification from several projects, simply add multiple MCP Server configurations to the configuration file. Each project should have its own unique `<project-id>`. For clarity, name each MCP Server following the format **"xxx API Specification"**.
- If your team syncs the MCP configuration file to a code repository, it is recommended to remove the line `"APIDOG_ACCESS_TOKEN": "<access-token>"` and instead, configure the `APIDOG_ACCESS_TOKEN` as an environment variable on each member's machine to prevent token leakage.
- For users of the on-premise deployment, please include your on-premise server's API address in the IDE MCP configuration: "--apidog-api-base-url=`<API address of the on-premise server, starting with http:// or https://>`". Additionally, ensure network access to `www.npmjs.com` properly.
      
    ```json
    {
      "mcpServers": {
        "API specification": {
          "command": "npx",
          "args": [
            "-y",
            "apidog-mcp-server@latest",
            "--project=<project-id>",
            // Required for on-premise deployment:
            "--apidog-api-base-url=<API address of the on-premise server, starting with http:// or https://>"
          ],
          "env": {
            "APIDOG_ACCESS_TOKEN": "<access-token>"
          }
        }
      }
    }
    ```
  
## Connecting Other Data Resources to AI


<Card title="Conntect Online API Documentation Published by Apidog to AI via Apidog MCP Server" href="apidog://link/pages/901468">
</Card>

<Card title="Conntect OpenAPI Files to AI via Apidog MCP Server" href="apidog://link/pages/901477">
</Card>
      
      
## FAQs


<Accordion title="Windows Configuration Issues" defaultOpen>

If standard configuration fails on Windows, use this instead:

```json
{
  "mcpServers": {
    "API specification": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "apidog-mcp-server@latest",
        "--project=<project-id>"
      ],
      "env": {
        "APIDOG_ACCESS_TOKEN": "<access-token>"
      }
    }
  }
}
```
</Accordion>


<Accordion title="Node.js Version Problems" defaultOpen={false}>

If you see Node.js version errors, ensure you’re using v18 or higher. Check with:

```bash
node -v
```
</Accordion>

<Accordion title="How to let AI reads the latest data from the updated API specification?" defaultOpen={false}>
AI caches API specification locally. If the API specifications are updated, make sure to tell the AI to refresh the API specification data to ensure that the latest version is used when generating code.

For example：

```
Please reload API specification and add the new fields in Pet DTO
```
</Accordion>

# Conntect Online API Documentation Published by Apidog to AI via Apidog MCP Server
Apidog MCP Server enables AI to connect and utilize online API documentation published by Apidog.

![Apidog MCP Server](https://assets.apidog.com/uploads/help/2025/03/26/1562f2ed8710ec754897595552c1b84c.gif)

This configuration method only supports publicly published online documentation and does not support documentation with [password or whitelist settings](https://docs.apidog.com/publish-docs-sites-631325m0#docs-site-visibility-setting). For non-public documentations, it is recommended to use the project ID and personal API access token to read Apidog project data. For more details, refer to: [Connecting API Documentation within Apidog Project to AI via Apidog](apidog://link/pages/901476).

## Enabling MCP for Online Documentation

>  Apidog version should be ≥ 2.7.2.

<Steps>
  <Step title="Enable MCP Service">
Navigate to Apidog project, then go to `Share Docs` → `Publish Docs Sites` → `AI Features` to enable MCP service.
      
<Background>
![enabling-mcp-service.png](https://api.apidog.com/api/v1/projects/544525/resources/352755/image-preview)
</Background>

  </Step>
  <Step title="Get Configuration File">
After enabling, the `Vibe Coding(via MCP)` button will appear when accessing online documentation.
      
<Background>
![vibe-coding-via-mcp-button-api-documentation.png](https://api.apidog.com/api/v1/projects/544525/resources/352756/image-preview)
</Background>

Clicking the button will display the configuration guide and the MCP config file, which automatically includes your documentation's `site-id`. Simply copy this configuration for IDE integration.
      
<Background>
![vibe-coding-mcp-configuration-guide.png](https://api.apidog.com/api/v1/projects/544525/resources/352757/image-preview)
</Background>
  </Step>
</Steps>


## Configuring MCP Client

### Prerequisites

- Node.js environment installed (version ≥ 18, latest LTS recommended)
- Any IDE that supports MCP:
  - Cursor
  - VSCode + Cline extension
  - Others
- Copied MCP JSON configuration from Apidog online documentations


### Configuring MCP in Cursor


<Steps>
  <Step title="Edit MCP Config File">
Open Cursor editor, click the settings icon (top-right), select "MCP" from the left menu, then click "+ Add new global MCP server".
      
<Background>
![mcp-server-setting-cursor.png](https://api.apidog.com/api/v1/projects/544525/resources/352759/image-preview)
</Background>

  </Step>
  <Step title="Add Configuration">
Paste the MCP JSON configuration copied from online documentations into the opened `mcp.json` file:
      
<Tabs>
  <Tab title="macOS / Linux">
    ```json
    {
      "mcpServers": {
        "apidog-site-123456": {
          "command": "npx",
          "args": [
            "-y",
            "apidog-mcp-server@latest",
            "--site-id=123456"
          ]
        }
      }
    }
    ```
  </Tab>
  <Tab title="Windows">
    ```json
    {
      "mcpServers": {
        "apidog-site-123456": {
          "command": "cmd",
          "args": [
            "/c",
            "npx",
            "-y",
            "apidog-mcp-server@latest",
            "--site-id=123456"
          ]
        }
      }
    }
    ```
  </Tab>
</Tabs>
      
  </Step>
  <Step title="Verify Configuration">
Test the connection by asking the AI (in Agent mode), for example:

```plain
Please fetch API documentation via MCP and tell me how many endpoints exist in the project.
```
      
If the AI returns correct API information, the connection is successful.
      
<Background>
![apidog-mcp-server-in-cursor.png](https://api.apidog.com/api/v1/projects/544525/resources/352763/image-preview)
</Background>
  </Step>
</Steps>

## Important Notes

- If you need to work with different API documentations, simply add multiple MCP Server configurations to the configuration file. Each API documentation should have its own unique `<site-id>`.
- For users of the on-premise deployment, please include your on-premise server's API address in the IDE MCP configuration: "--apidog-api-base-url=`<API address of the on-premise server, starting with http:// or https://>`" Additionally, ensure network access to `www.npmjs.com` properly.
      
    ```json
     {
      "mcpServers": {
        "apidog-site-123456": {
          "command": "npx",
          "args": [
            "-y",
            "apidog-mcp-server@latest",
            "--site-id=123456",
            // Required for on-premise deployment:
            "--apidog-api-base-url=<API address of the on-premise server>"
          ]
        }
      }
    }
    ```

## Connecting Other Data Resources to AI

<Card title="Conntect API Specification within Apidog Project to AI via Apidog MCP Server" href="apidog://link/pages/901476"> 
</Card>

<Card title="Conntect OpenAPI Files to AI via Apidog MCP Server
" href="apidog://link/pages/901477">
</Card>

## FAQs


<Accordion title="Windows Configuration Issues" defaultOpen>

If standard configuration fails on Windows, use this instead:

```json
{
  "mcpServers": {
    "apidog-site-123456": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "apidog-mcp-server@latest",
        "--site-id=123456"  // Replace with your Site ID
      ]
    }
  }
}
```
</Accordion>



<Accordion title="Node.js Version Problems" defaultOpen={false}>

If receiving Node.js version errors, ensure you have Node.js v18 or higher version. You can check the version with the following command:

```bash
node -v
```
</Accordion>


<Accordion title="How to let AI reads the latest data from the updated API documentation?" defaultOpen={false}>
AI caches API documentation locally. If the API documentations are updated, make sure to tell the AI to refresh the API documentation data to ensure that the latest version is used when generating code.

For example：

```
Please reload API documentation and add the new fields in Pet DTO
```
</Accordion>

    
# Conntect OpenAPI Files to AI via Apidog MCP Server
In addition to the API specification within Apidog project, online API documentation published via Apidog, Apidog MCP Server also has the ability to directly read Swagger or OpenAPI Specification (OAS) files.

## Configuring the MCP Client

### Prerequisites

- Node.js environment installed (version ≥ 18, latest LTS recommended)
- Any IDE that supports MCP:
  - Cursor
  - VSCode + Cline extension
  - Others

### Common Configuration Steps

<Steps>
  <Step title="Prepare OpenAPI File">
   - Ensure you have a URL or a local path to a Swagger/OpenAPI file
   - Supported formats: OpenAPI files in JSON or YAML 
  </Step>
  <Step title="Configure MCP in IDE">    
    Add this JSON configuration to your IDE's MCP config file:
      
     <Tabs>
      <Tab title="macOS / Linux">
        ```json
        {
          "mcpServers": {
            "API specification": {
              "command": "npx",
              "args": [
                "-y",
                "apidog-mcp-server@latest",
                "--oas=<oas-url-or-path>"
              ]
            }
          }
        }
        ```
      </Tab>
      <Tab title="Windows">
        ```json
        {
          "mcpServers": {
            "API specification": {
              "command": "cmd",
              "args": [
                "/c",
                "npx",
                "-y",
                "apidog-mcp-server@latest",
                "--oas=<oas-url-or-path>"
              ]
            }
          }
        }
        ```
      </Tab>
    </Tabs>  

   Where `<oas-url-or-path>` can be:  
    - A remote URL (e.g.,`https://petstore.swagger.io/v2/swagger.json`)
    - A local file path (e.g.,`~/data/petstore/swagger.json`)

  </Step>
</Steps>

### Configuring MCP in Cursor


<Steps>
  <Step title="Edit MCP Config File">
Open Cursor editor, click the settings icon (top-right), select "MCP" from the left menu, then click "+ Add new global MCP server".
      
<Background>
![mcp-server-setting-cursor.png](https://api.apidog.com/api/v1/projects/544525/resources/352759/image-preview)
</Background>
  </Step>
    
  <Step title="Add Configuration"> 
In the opened `mcp.json` file, add the following configuration (remember to replace `https://petstore.swagger.io/v2/swagger.json` with your actual OpenAPI file path or URL):
      
     <Tabs>
      <Tab title="macOS / Linux">
        ```json
        {
          "mcpServers": {
            "API specification": {
              "command": "npx",
              "args": [
                "-y",
                "apidog-mcp-server@latest",
                "--oas=https://petstore.swagger.io/v2/swagger.json"
              ]
            }
          }
        }
        ```
      </Tab>
      <Tab title="Windows">
        ```json
        {
          "mcpServers": {
            "API specification": {
              "command": "cmd",
              "args": [
                "/c",
                "npx",
                "-y",
                "apidog-mcp-server@latest",
                "--oas=https://petstore.swagger.io/v2/swagger.json"
              ]
            }
          }
        }
        ```
      </Tab>
    </Tabs>  
      
  </Step>
  <Step title="Verify Configuration">
  Test the connection by asking the AI (in Agent mode):

```plain
Please fetch API documentation via MCP and tell me how many endpoints exist in the project
```
If the AI returns correct API information in your OpenAPI file, the connection is successful.      
      
<Background>
![connect-ai-to-open-api-file-using-apidog-mcp-server.png](https://api.apidog.com/api/v1/projects/544525/resources/352793/image-preview)
</Background>

  </Step>
</Steps>

## Important Notes
         
- If you need to use multiple OpenAPI specification files, you can add multiple MCP Servers in the configuration file, each with a different `--oas` parameter.
- For local file paths, ensure the path is correct and the file exists. For URLs, make sure they are publicly accessible and return a valid OpenAPI specification file.
- If your OpenAPI file is large or contains complex data structures, the MCP server may take longer to process it.
- For users of the on-premise deployment, please include your on-premise server's API address in the IDE MCP configuration: "--apidog-api-base-url=`<API address of the on-premise server, starting with http:// or https://>`" Additionally, ensure network access to `www.npmjs.com` properly.
         
    ```json
    {
      "mcpServers": {
        "API specification": {
          "command": "npx",
          "args": [
            "-y",
            "apidog-mcp-server@latest",
            "--oas=<oas-url-or-path>",
            // Required for on-premise deployment:
            "--apifox-api-base-url=<API address of the on-premise server, starting with http:// or https://>"
          ]
        }
      }
    }
    ```

## Connecting Other Data Resources to AI

<Card title="Conntect API Specification within Apidog Project to AI via Apidog MCP Server" href="apidog://link/pages/901476">
</Card>
    
<Card title="Conntect Online API Documentation Published by Apidog to AI via Apidog MCP Server" href="apidog://link/pages/901468">
</Card>
    
      
## FAQs


<Accordion title="Windows Configuration Issues" defaultOpen>

If standard configuration fails on Windows, use this instead:

```json
{
  "mcpServers": {
    "API specification": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "apidog-mcp-server@latest",
        "--oas=<oas-url-or-path>"
      ]
    }
  }
}
```
</Accordion>

<Accordion title="Node.js Version Problems" defaultOpen={false}>

If you see Node.js version errors, ensure you’re using v18 or higher. Check with:

```bash
node -v
```
</Accordion>
