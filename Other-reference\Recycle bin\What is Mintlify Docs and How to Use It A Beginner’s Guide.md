# Mintlify Docs 101: What It Is and How to Get Productive Fast

> Pro Tip: Building or documenting APIs as you write? Use Apidog—the all‑in‑one platform for API design, testing, mocking, and docs—to turn ideas into production‑ready APIs. Try it at https://apidog.com/

Are you ready to ship clean, modern documentation without wrestling with tooling? Meet **Mintlify**—an AI‑native docs platform that makes publishing, organizing, and scaling documentation as simple as editing Markdown. With 28K+ GitHub stars and customers like Stripe, **Mintlify** blends interactive components, AI features, and first‑class developer ergonomics to help you go from draft to deployed in minutes. In this beginner‑friendly walkthrough, you’ll install **Mintlify**, preview locally, map a custom domain, enable AI ingestion, wire up an MCP server, add translations, and incorporate OpenAPI for API docs. Let’s dive in.

## Meet Mintlify: A Modern Docs Platform Built for Teams

[**Mintlify**](https://mintlify.com/) helps you build beautiful, fast, and SEO‑friendly documentation sites with minimal effort. It renders Markdown/MDX into responsive pages, integrates with GitHub, and includes AI‑powered upgrades for search, content generation, and translations.

Why teams pick Mintlify:

- WYSIWYG web editor for quick edits
- Interactive MDX components (tabs, code groups, API playgrounds)
- AI ingestion for smarter, semantic search
- Straightforward customization via `docs.json`
- Git‑based workflow and CI/CD friendly

![mintlify.com](https://assets.apidog.com/blog-next/2025/05/Screenshot-2025-05-27-001222.png)

## Quick Start: Install and Run Mintlify Locally

Getting set up takes just a few minutes. You’ll need Node.js, the Mintlify CLI, and a GitHub account for versioning.

1) Prerequisites

- Node.js 16.6+ from https://nodejs.org/ (check with `node -v`)
- GitHub account: https://github.com/
- VS Code (optional): https://code.visualstudio.com/

2) Clone the Starter Kit

```bash
git clone https://github.com/mintlify/starter-kit
cd starter-kit
```

The project includes a `docs` directory with MDX pages and a `docs.json` settings file.

3) Install the CLI

```bash
npm install -g mintlify-cli
```

Confirm with `mintlify-cli --version`.

4) Start the local preview

```bash
mintlify-cli dev
```

Open `http://127.0.0.1:3000`. If port 3000 is taken, Mintlify will try 3001, or you can set a custom port with `--port` (for example: `mintlify-cli dev --port=3333`).

5) Make your first edit

Open the project in VS Code (`code .`), then update `docs/introduction.mdx` (for instance, change the title). Save to see hot‑reloaded changes. Tweak brand settings in `docs.json`, such as theme, colors, and name:

```json
{
  "name": "My Docs",
  "theme": "prism",
  "colors": { "primary": "#3498db" }
}
```

Check https://mintlify.com/docs for all global options.

## Add a Custom Domain (Professional URLs in Minutes)

You can host docs at `docs.yourcompany.com` instead of a `mintlify.app` subdomain.

1) Open Dashboard settings

- Go to the **Mintlify** Dashboard at https://mintlify.com/
- Navigate to Settings → Custom Domain

![custom domain](https://assets.apidog.com/blog-next/2025/05/add-custom-domain-dark.png)

2) Configure DNS

- Enter your domain (for example, `docs.yourcompany.com`)
- Add a CNAME record: `docs` → `cname.vercel-dns.com`
- For Cloudflare, enable “full strict” HTTPS
- If using Vercel, add the TXT verification record shown in the dashboard

![cname](https://assets.apidog.com/blog-next/2025/05/mintlify-dns.png)

3) Wait for propagation

DNS updates can take up to 48 hours, but usually apply much faster. Visit the domain to confirm it’s live.

## Supercharge Search with AI Ingestion

Give users smarter results with AI‑powered indexing and semantic search.

1) Enable AI ingestion

- In the **Mintlify** Dashboard, go to Products → AI Chat
- Turn on AI ingestion to index your content automatically
- Mintlify exposes an `/llms.txt` for efficient LLM indexing

![ai chat](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-04-050413.png)

2) Try AI search

- In your published docs, open AI Chat or use the search bar
- Ask natural questions like “How do I authenticate with the API?” to see relevant sections pulled together

3) Use AI to smooth rough edges

- In the web editor, enable AI suggestions (Settings → Editor) to catch MDX formatting issues and minor fixes

## Connect an MCP Server (Let Tools Query Your Docs)

The Model Context Protocol (MCP) lets AI apps interact with your documentation and APIs through a standard interface. **Mintlify** can generate an MCP server so external tools can search, navigate, and retrieve your content.

1) Install the MCP CLI

```bash
npm i @mintlify/mcp
```

You can also find a tailored command in Dashboard → Products → MCP Server.

2) Configure the server

- In Dashboard → API Keys, copy your External Admin Key and Chat API Key
- Run the install/initialize command shown in the dashboard
- Toggle on OpenAPI access if you also want endpoint visibility (requires an OpenAPI spec)

3) Start and test with an MCP client

- The CLI prints a start command (for example, `npm --prefix ~/.mcp/mintlify start`)
- Try a client like Windsurf and query: “Search my docs for API authentication.”

### Local install example (npx)

```bash
npx @mintlify/mcp add mintlify
```

- Press Enter to skip bearer token if prompted
- Select your installed MCP client (for example, Claude Desktop or Windsurf)
- The server is added to the selected client

![install mintlify locally](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-04-041046.png)

Example output:

```bash
node C:\User\Me.mcp\mintlify\src\index.js
```

- Run the command, open your MCP client’s tools panel, and confirm the server is active
- Or in **Mintlify** Dashboard → MCP Server, copy the “add server” command for your target client

![mcp command](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-04-050413-1.png)

Sample prompt in an MCP client:

```text
Write a help doc about this feature based on this PRD, in the style and tone of AppleDB docs.
```

Mintlify‑formatted prompt:

```text
Write this in markdown using Mintlify components like anchors.
```

Paste output into Mintlify’s web editor, switch to visual mode, and preview.

![run in mcp client](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-04-051035.png)

## Translate Your Docs for Global Audiences

Reach more readers by enabling AI‑powered translations.

1) Turn on translations

- In Dashboard → Settings → Translations, add a locale (for example, `es`)
- Choose which version to translate—updates sync automatically

![mintlify translation](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-04-044932.png)

2) Fine‑tune language output

- Edit translated MDX files in the locale folder (for example, `docs/es/`)
- Fixed UI strings (“Was this page helpful?”) adjust to the target language

3) Verify in the UI

- Use the locale or version switcher (configured in `docs.json`) to preview translations

![switch between languages](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-04-045149.png)

Confirm titles, headings, and snippets display correctly for each language.

![view all languages](https://assets.apidog.com/blog-next/2025/05/Screenshot-2025-05-27-002032.png)

## Power Tips: Use Mintlify with VS Code

Prefer writing close to your code? The **Mintlify** VS Code extension streamlines authoring.

1) Install the extension

- Open the Extensions Marketplace in VS Code and search “Mintlify” → Install

![vs code mintlify extension](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-04-045405.png)

2) Generate or refine docs

- Select code, click the Mintlify icon, and choose Generate Docs (or press `Ctrl + .`)
- The extension drafts documentation you can edit before committing

Sample code:

```javascript
test("@e2e wait for api response", async ({ page }) => {
  const sidemenuPage = new SideMenuPage(page);
  await sidemenuPage.interceptApiLink.click();
  const response = await page.waitForResponse((response) =>
    response.url().includes("/comments")
  );
  expect(response.status()).toBe(200);
  await page.getByText("CREATE post").isVisible();
});
```

Generated description:

```text
The code block is a test case that checks if the API response is received successfully.
```

![use mintlify in vs code](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-04-045524-1.png)

## Explore More Mintlify Superpowers

Level up your docs with these built‑ins:

- Web editor: Type `/` for components like tabs and callouts (Dashboard → Editor)
- Themes and styling: Tweak `docs.json` (for example, `theme: "prism"`, custom colors, logos)
- GitHub integration: Use the **Mintlify** GitHub App for auto‑deploys
- Analytics: See popular pages and drop‑offs in Dashboard → Analytics
- MDX components: Accordions, code groups, Mermaid diagrams, and more at https://mintlify.com/docs

## Final Thoughts: From Great Docs to Great APIs

Mintlify removes the busywork from documentation so teams can focus on clarity and impact. With a friendly MDX workflow, scalable structure, and AI enhancements for search and translations, you can ship docs your users will love—fast.

And when your documentation includes APIs, pair Mintlify with **Apidog** to complete the loop:

- Design OpenAPI contracts and generate mocks early
- Validate responses, add visual assertions, and automate tests
- Publish interactive, always‑up‑to‑date API documentation
- Collaborate across product, backend, frontend, and QA in one hub

Start your docs with **Mintlify**, and deliver reliable APIs with **Apidog**. That combination helps teams move quickly without sacrificing quality. Happy documenting!
