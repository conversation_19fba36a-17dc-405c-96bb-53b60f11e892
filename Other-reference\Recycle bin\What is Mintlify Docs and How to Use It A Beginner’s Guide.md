# What is Mintlify Docs and How to Use It: A Beginner’s Guide

Are you struggling to create sleek, user-friendly documentation that doesn’t feel like a chore? Say hello to **Mintlify**, the AI-native platform that makes building stunning docs as easy as writing a blog post. With 28K+ GitHub stars and clients like <PERSON><PERSON>, **Mintlify** offers interactive features, AI tools, and seamless integrations to level up your docs game. In this beginner’s guide, I’ll walk you through installing **Mintlify**, setting up a custom domain, leveraging AI ingestion, integrating an MCP server, adding translations, and using OpenAPI for API docs. Whether you’re a developer or a team lead, you’ll be shipping pro-level docs in no time. Ready to make documentation fun? Let’s dive in!



## What is Mintlify? Your Docs Superpower

[**Mintlify**](https://mintlify.com/) is a modern documentation platform that helps developers create beautiful, interactive, and SEO-friendly docs with minimal fuss. Built for teams and open-source projects, it renders Markdown and MDX files into responsive pages, supports GitHub integration, and packs AI-driven features like code generation and translations. Key perks include:

- **WYSIWYG Editor**: Edit docs in a browser-based interface.
- **Interactive Components**: Add tabs, code snippets, and API playgrounds.
- **AI Optimization**: Enhance search and accessibility with AI ingestion.
- **Customizable**: Tweak themes, fonts, and navigation via a `docs.json` file.

With a drag-and-drop workflow and 40% higher user conversion rates, **Mintlify** is perfect for startups or public projects. Let’s set it up

![mintlify.com](https://assets.apidog.com/blog-next/2025/05/Screenshot-2025-05-27-001222.png)

## Installing and Setting Up Mintlify

Getting started with **Mintlify** is a breeze, and you’ll need Node.js, the **Mintlify** CLI, and a GitHub account. Here’s how to kick things off, based on the official guide.

**1. Install Prerequisites**:

- **Node.js**: Download version 16.6 or higher from [https://nodejs.org/](https://nodejs.org/). Verify with `node -v`.
- **GitHub Account**: Sign up at [github.com](https://github.com/) if you don’t already.
- **VS Code**: Optional but recommended for editing. Grab it from [code.visualstudio.com](https://code.visualstudio.com/).

**2. Clone the Starter Kit**:

- In your terminal, clone the **Mintlify** [Starter Kit](https://github.com/mintlify/starter):

```bash
git clone https://github.com/mintlify/starter-kit
cd starter-kit
```

- This creates a project with a `docs` folder containing MDX files and a `docs.json` file for settings.

**3. Install Mintlify CLI**:

- Install the CLI globally:

```bash
npm install -g mintlify-cli
```

- Verify with `mintlify-cli --version` (e.g., `mintlify-cli version 3.1.25`).

**4. Run the Local Preview**:

- In the `starter` folder, start the development server:

```bash
mintlify-cli dev
```

- This opens a preview at `http://127.0.0.1:3000`. If port 3000 is busy, it tries 3001 or use `--port 3333` to customize (e.g., `mintlify-cli dev --port=3333`).

**5. Edit Your Docs**:

- Open the project in VS Code (`code .`).
- Navigate to `docs/introduction.mdx` and change the title” field field to “Welcome to My Docs!” Save, and the preview updates instantly.
- Explore `docs.json` to tweak fonts, colors, or enable dark mode. For example:

```json
{
  "name": "My Docs",
  "theme": "prism",
  "colors": { "primary": "#3498db" }
}
```

- Check [Introduction - Mintlify](https://mintlify.com/docs) for global settings like icons or logos.

I set this up in under 10 minutes and had a live site with my tweaks—super smooth!

## Setting Up a Custom Domain for Mintlify

Want your docs to live at `docs.yourcompany.com` instead of a `mintlify.app` subdomain? **Mintlify** makes custom domains easy.

**1. Access Settings**:

- Log in to your **Mintlify** Dashboard at [mintlify.com](https://mintlify.com/).
- Go to **Settings** > **Custom Domain**.

![custom domain](https://assets.apidog.com/blog-next/2025/05/add-custom-domain-dark.png)

**2. Add Your Domain**:

- Enter your domain (e.g., `docs.yourcompany.com`).
- Follow the DNS instructions for your provider (e.g., Namecheap, GoDaddy):
- Add a **CNAME** record: `docs` → `cname.vercel-dns.com`.
- For Cloudflare, enable “full strict” HTTPS security.
- If using Vercel, add a TXT verification record (shown in the dashboard).

![cname](https://assets.apidog.com/blog-next/2025/05/mintlify-dns.png)

**3. Wait for Propagation**:

- DNS changes take up to 48 hours but often resolve faster. Check your domain in a browser to confirm.

I set up a custom domain for a test site, and it was live in a few hours—professional vibes all the way!

## Leveraging AI Ingestion with Mintlify

**Mintlify**’s AI ingestion makes your docs smarter and more accessible, letting users search content via AI-powered queries.

**1. Enable AI Ingestion**:

- In the **Mintlify** Dashboard, go to **Products** > **AI Chat**.
- Toggle on AI ingestion. This indexes your docs for AI search without extra setup.
- **Mintlify** automatically generates an `/llms.txt` sitemap for LLMs to index your content efficiently.

![ai chat](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-04-050413.png)

**2. Test AI Search**:

- In your live docs, use the search bar or AI Chat (if enabled via `docs.json`).
- Ask: “How do I authenticate with the API?” The AI pulls relevant sections, like your authentication page.

**3. Enhance with AI Fixes**:

- In the web editor, AI suggests syntax fixes for MDX errors (e.g., malformed components). Enable this in **Settings** > **Editor**.

I tried the AI search on a sample site, and it found my API docs instantly—way faster than manual browsing!

## Integrating an MCP Server with Mintlify

The Model Context Protocol (MCP) lets AI apps interact with your docs or APIs. **Mintlify** generates an MCP server to bridge your content with LLMs.

**1. Install the MCP CLI**:

- Run:

```bash
npm i @mintlify/mcp
```

- In the **Mintlify** Dashboard, go to **Products** > **MCP Server** to get your unique install command.

**2. Configure the MCP Server**:

- Find your **External Admin Key** and **Chat API Key** in **Settings** > **API Keys**.
- Run the CLI command from the dashboard, entering the keys when prompted.
- Toggle **OpenAPI Access** to include API endpoints (requires an OpenAPI spec).

**3. Use the MCP Server**:

- The CLI outputs a command to start your server (e.g., `npm --prefix ~/.mcp/mintlify start`).
- Test with an MCP client like Windsurf, querying: “Search my docs for API authentication.” The server returns structured results.

### Installing Mintlify MCP Server Locally

To run the Mintlify MCP server on your machine:  

- Run:

```bash
npx @mintlify/mcp add mintlify
```

- Skip the bearer token prompt by pressing Enter.
- Select your MCP client (e.g., Claude Desktop, Windsurf) that’s installed locally.
- The server installs and configures for your chosen client.

![install mintlify locally](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-04-041046.png)

**Example:** I used Windsurf, and the terminal gave me:

```bash
node C:\User\Me.mcp\mintlify\src\index.js
```

- Run this command, click the tools icon in your client, and verify the server is available.
- Alternatively, in the Mintlify Dashboard, go to **MCP Server** to find a command to add the server to your MCP client (e.g., Apple DB).

![mcp command](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-04-050413-1.png)

**Sample Prompt** (in Windsurf):    

```text
Write a help doc about this feature based on this PRD, in the style and tone of AppleDB docs.
```

Or, for Mintlify-compatible format:  

```text
Write this in markdown using Mintlify components like anchors.
```

- Copy the output from your mcp client (e.g windsurf or claude desktop), paste it into Mintlify’s web editor, switch to visual mode, and preview!

![run in mcp client](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-04-051035.png)

I set up an MCP server, and it let my AI assistant query my docs like a pro—futuristic stuff!

## Adding Translations to Mintlify Docs

Want your docs in multiple languages? **Mintlify**’s AI-powered translations make localization a snap.

**1. Enable Translations**:

- In the **Mintlify** Dashboard, go to **Settings** > **Translations**.
- Add a locale (e.g., `es` for Spanish) and select a version to translate.
- **Mintlify** auto-translates your docs, syncing updates with the main version.

![mintlify translation](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-04-044932.png)

**2. Customize Translations**:

- Edit translated MDX files in your repo’s locale folder (e.g., `docs/es/`).
- Fixed content like “Was this page helpful?” adapts to the locale automatically.

**3. Test the Translation**:

- In your live docs, switch locales via the version dropdown (configured in `docs.json`).

![switch between languages](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-04-045149.png)

- Check that titles and content reflect the target language (e.g., “¡Bienvenidos!” for Spanish).

I added Spanish translations, and my docs were global-ready in minutes—huge win for accessibility!

![view all languages](https://assets.apidog.com/blog-next/2025/05/Screenshot-2025-05-27-002032.png)

## Using Mintlify with VS Code

Documenting code in VS Code? Mintlify’s extension makes it a snap!

**1. Install the Mintlify Extension**:

- Open VS Code’s Extension Marketplace.
- Search for “Mintlify” and click **Install**.

![vs code mintlify extension](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-04-045405.png)

**2. Generate Documentation**:

- Highlight the code you want to document.
- Click the Mintlify icon in the sidebar and select **Generate Docs**, or use the shortcut `Ctrl + .`.
- Mintlify writes documentation automatically.

**Sample Code**:

```javascript
test("@e2e wait for api response", async ({ page }) => {
  const sidemenuPage = new SideMenuPage(page);
  await sidemenuPage.interceptApiLink.click();
  const response = await page.waitForResponse((response) =>
    response.url().includes("/comments")
  );
  expect(response.status()).toBe(200);
  await page.getByText("CREATE post").isVisible();
});
```

**Generated Documentation:**

```text
The code block is a test case that checks if the API response is received successfully.
```

![use mintlify in vs code](https://assets.apidog.com/blog-next/2025/06/Screenshot-2025-06-04-045524-1.png)

I documented a test script, and Mintlify nailed the description—effortless!

## Exploring Mintlify’s Features

**Mintlify** is packed with tools to make your docs shine. Here’s how to level up:

- **Web Editor**: Edit docs in the browser via **Dashboard** > **Editor**. Type `/` for components like tabs or callouts.
- **Themes and Styling**: Customize colors, fonts, or logos in `docs.json`. Try themes like `prism` or `default`.
- **GitHub Integration**: Install the **Mintlify** GitHub App to auto-deploy changes. Check the dashboard for setup.
- **Analytics**: Track user engagement in **Dashboard** > **Analytics** to see popular pages or drop-offs.
- **Components**: Add accordions, code groups, or Mermaid diagrams via MDX. See [Introduction - Mintlify](https://mintlify.com/docs) for examples.

I tweaked my theme and added tabs, making my docs look like a pro design in under an hour!

## Wrapping Up: Your Mintlify Journey Begins

Great job—you’ve unlocked the power of **Mintlify** to create stunning, AI-ready documentation! From installing the CLI to setting up custom domains, AI ingestion, MCP servers, translations, and OpenAPI playgrounds, you’re ready to build docs that wow users. Experiment with new components, add more languages, or document your APIs with APIdog! Happy documenting!
