Navigating Apidog
The main interface of Apidog can be broadly divided into the following parts:

Header

Sidebar

Workbench

Directory tree

Tabs

Environment selector

Footer

image.png
This layout provides a structured and user-friendly interface for working with various aspects of API development and testing within the Apidog platform.

Header
The header includes the following main functions:

image.png
 Home: Takes you to your personal homepage, displaying the teams you've joined, including team members, team projects, team Plan, organizations you belong to, API hub, and more.

Project Tabs: Each tab represents a project. Click to switch between projects. Clicking the button to the left of the project name opens the project in a separate window.

Upgrade/plan: Shows the current Plan being used by your Team.

 Refresh: Reloads the data of the current project; reloading will close all currently open content tabs.

 Settings: Local settings, where you can set Appearance, proxy, and other settings related only to the current client and not to the logged-in user.

 Notifications: Displays alerts from team activities that require your attention.

Avatar: Account settings, where you can set basic account information, Access tokens, etc.

Sidebar


The Sidebar contains the following features:

Project Icon: Click to jump to Teams and projects.

APIs: Apidog's core interface where you can define APIs, debug APIs, send requests, create Schemas, write Markdown, and more.

Tests: If you need to send requests in batch (similar to running a Postman Collection) or orchestrate data relationships and sending logic between requests, you can create a test scenario in Tests. Here, you can also view test reports, run performance tests, manage test data, integrate CI/CD, and more.

Share docs: Once APIs are defined, you can share them with other colleagues in this module, or officially publish them as API documentation. You can define the documentation interface, customize domain names, and more here.

History: All requests that have been sent can be viewed and resent in the History section.

Settings: All settings related to the current project are here, including basic settings, function settings, notification settings, project resources, and project data import/export.

 Invite: Invite other users to join the current project.

Workbench
Directory tree


The directory tree includes the following functional modules from top to bottom:

 Branches and Versions: Default is the main branch, you can switch to other branches, create new branches, or manage branches.

 Search and Filter: You can search and filter endpoints. Note that currently, you can only search for endpoint names, not folder names or case names.

 Create New: You can create various types of elements, such as endpoints, requests, schemas, Markdown, etc.

Overview: An overall view of the project

Endpoints: Endpoints and endpoint cases organized in a folder structure; Markdown and Websocket, etc., can also be mixed in.

Schemas: Schemas organized in a folder structure

Components: Reusable components. Currently only supports response components.

Requests: API requests organized in a folder structure, similar to Postman's Collection.

 Trash: Deleted endpoints, schemas, components, requests, etc., will enter the trash and be automatically deleted after 30 days.

Tabs
Clicking any element in the directory tree will open a Tab.

Tabs include the following types:

Endpoint

Schema

Component

Markdown

Folder

Request

WebSocket

Overview

Trash

New

When you single-click to open a Tab, you'll notice that the Tab title is in italic font. In this italic state, if you click another element in the directory tree, the old tab will be overwritten. This is suitable for browsing scenarios.

If you make any changes to the Tab content, the Tab title will change to normal font. In the normal font state, clicking another directory tree element will open a new tab, which is suitable for editing scenarios.

If you want the opened tab not to be overwritten by other tabs, you can also use double-click to open the tab. This will directly put the tab into the normal font state.

There are two buttons on the far right of the Tabs:

 New: You can create various types of elements

 More: You can close all tabs, or close the current tab, or close all tabs except the current one.

Footer
image.png
From left to right, the footer includes:

 Collapse/Expand: Can collapse or expand the directory tree.

 Online: Indicates online status. If offline, there will be issues with team synchronization.

 Agent: A feature exclusive to Apidog Web, allows you to select which Agent will send out the requests for Apidog Web.

 Cookies: Cookie manager, can store current cookies

 Community: Links to the Apidog Community, including Discord, Slack, Teams, X, and Email.

 Appearance: Can change Apidog's background color, theme color, and font size.

 Help: Opens the help documentation

