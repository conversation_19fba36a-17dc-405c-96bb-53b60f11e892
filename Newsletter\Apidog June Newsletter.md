Email Subject: Apidog's June Updates: AI Superpowers for API Docs, GitHub Sync & More! 🚀

Hello Apidog Users,
<PERSON> is here, and so are some sizzling new updates from Apidog! This June, we’ve doubled down on AI integration to make your workflows smarter and introduced powerful new ways to organize and automate your projects. Let's jump in and see what's new!

🤖 AI-Powered Schema Descriptions & Mock: You can now use AI to modify field descriptions, generate mock data, and more, directly within your API schemas. This feature helps you save time and effort, ensuring your API documentation is accurate and complete. Learn more.
[Image]
https://assets.apidog.com/uploads/help/2025/06/20/13ghre-26.gif
🔑 Effortless AI Model Integration: To power up Apidog's new AI capabilities, you can now configure API keys for your preferred AI models. Securely manage your keys and unlock AI assistance across the platform. Learn more.
[Image]
https://assets.apidog.com/uploads/help/2025/06/20/13x91p-00.gif
📢 Spoiler Alert: More AI-powered tools are on the way—built to support every step of your workflow, from designing and documenting to testing and beyond. Stay tuned!
🗄️ MCP Configuration Directly within Apidog Desktop: You can now easily copy an endpoint's OpenAPI Spec data and MCP Server configurations directly within Apidog Desktop, allowing for smooth integration with AI coding tools like Cursor. This makes it easier than ever to let AI automatically read and process your API documentation.
[Image]
https://assets.apidog.com/uploads/help/2025/06/20/141yek-al.gif
⬆️ Automated GitHub Spec Backups: Keep your API specs perfectly in sync! Apidog now supports automatically backing up your OpenAPI/Swagger specifications for each project module directly to a designated GitHub repository, ensuring your work is always secure and version-controlled. 
[Image]
https://assets.apidog.com/uploads/help/2025/06/20/14754e-o1.gif
🎨 Organize Projects with Modules: Tame large projects with our new Modules feature! You can now structure your project into logical modules, where each module corresponds to its own OpenAPI spec file, making complex API projects much easier to manage. 
👁️ Improved Documentation with SEO Settings & Custom Login: Online documentation now includes SEO settings, such as global metadata, robots.txt, and sitemap.xml configurations. Plus, we’ve added the ability to configure custom login for your online documentation—perfect for private projects. Learn more.
🪙 Billing Manager Role: We’ve introduced a new Billing Manager role for organizations and teams. This role can manage billing without occupying a user seat—perfect for finance or procurement staff.
🛰️ Advanced gRPC Debugging with Pre/Post Processors: gRPC projects now support pre/post processors during endpoint debugging, enabling more flexibility with operations like assertions, variable extraction, database operations, and custom scripts. This feature helps streamline your testing process, making it more powerful and customizable.
📧 Improved Email Access Control for Documentation: When setting up an email allowlist for documentation access, you can now customize your SMTP server and email templates for full control over the messaging experience.
🛡️ Custom JWT Authorization Headers: When configuring JWT Authorization, you can now customize the name of the Authorization Header.
🌐 More Control over Custom Domain: If you’ve configured a custom domain for your online documentation, you can now deactivate the default Apidog-provided domain, giving you full branding control.
🧩 Project Layouts: You can now switch between grid and list layouts for team projects, with more sorting options to help you stay organized.
🌿Import UI Optimizations: We've refreshed the UI for project imports and improved how the base URL is displayed in the address bar.

🌟 Looking Ahead
We’re just getting warmed up for the summer! Our team is already working on the next batch of powerful features to make your API development journey even more delightful. Stay tuned!

💬 Join the Conversation!
Your feedback shapes Apidog! Have ideas or questions? Join our Discord or Slack communities to connect with fellow developers, share tips, and get the latest Apidog news.

P.S. Explore the full details of all these updates in the Apidog Changelog! 🚀

Happy API Building!
Regards,
The Apidog Team