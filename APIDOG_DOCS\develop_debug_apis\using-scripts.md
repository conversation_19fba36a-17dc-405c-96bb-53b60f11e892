Apidog contains a Javascript-based scripting engine. You can use scripts (JavaScript code snippets) to add dynamic behavior to API requests or collection tests.

What can be achieved with scripts:

1. Test (assert) the correctness of the results returned by the request (postprocessor script).
2. Modify API request parameters dynamically, such as adding API signature parameters, etc (preprocessor script).
3. Pass data between API requests (using scripts to manipulate variables).
4. Directly call programs written in other languages. We support java(.jar), python, PHP, JS, BeanShell, go, shell, Ruby, Lua, etc.

:::tip[]

Apidog script syntax is compatible with Postman script syntax. Postman scripts can be seamlessly migrated to Apidog.

See all pm syntax supported [here](apidog://link/pages/593586).

:::


:::highlight purple

For those unfamiliar with JavaScript, writing pm scripts can be challenging. To address this, we recommend using the **[Apidog Script Generator](https://app.anakin.ai/apps/21857)** to simplify script writing. Simply describe the script you want in natural language, and the generator will create a runnable script for you.

:::

## Usage

Scripts can be added in the following two stages:

1. Use preprocessor scripts before sending the request to the server.
2. Use the postprocessor script (assertion) after receiving the response.

![](https://assets.apidog.com/uploads/help/2023/07/12/ce9ebf6278b2bf4a3a3c67b1d969af89.png)

## Debugging Scripts

You can write debugging scripts in preprocessor and postprocessor so you can log output information through console.log("message") in the console.

```js
pm.console.log("anything")
```
## Examples

1. [Assertion scripts](apidog://link/pages/593739)
2. [Using variables in scripts](apidog://link/pages/597443)
3. [Using scripts to modify request messages](apidog://link/pages/597445)
4. [Other examples](apidog://link/pages/597448)

## FAQ

**Q: Does Apidog support `pm.nextRequest()`?**

A: Apidog does not have a "Run collection" functionality, so it cannot use `pm.nextRequest()`. The corresponding method is to create a **test scenario** in the Tests module, and then add an **if** condition, so that you can make different requests under different conditions.

A pre processor script is a code snippet that is executed before the request is sent. This is useful when you want to include a timestamp in the request header or send a random alphanumeric string in the URL parameters.

## Example

You can use the value returned to set the environment variable in order to include the current timestamp in the request header.

![](https://assets.apidog.com/uploads/help/2023/07/12/515f43fd4664f2284337c4b1503b09e0.png)

Set the value of the parameter timestamp as `{{timestamp}}`. When the request is sent, the preprocessor script will be executed, the value of the environment variable timestamp will be set and replaced with `the current timestamp`.

![](https://assets.apidog.com/uploads/help/2023/07/12/46879417c23c56a63d1ed1ede57c429c.png)

:::tip TIP
  The corresponding environment must be selected when setting an environment variable.
  The preprocessor script is written in JavaScript and has exactly the same syntax as the postprocessor script. However, there is no `pm.response` object.
:::

Postprocessor script is a code snippet that is executed after the request has been sent. It is mainly used to assert whether the result returned by the request is correct or not, write the result data returned by the request to environment variables, etc.

## Examples

Assert whether the result returned by the request is correct:

```js
// pm.response.to.have Example
pm.test("Return status code 200", function() {
  pm.response.to.have.status(200);
});

// pm.expect() Example
pm.test("The current environment is the production environment", function() {
  pm.expect(pm.environment.get("env")).to.equal("production");
});

// response assertions Example
pm.test("No error in return result", function() {
  pm.response.to.not.be.error;
  pm.response.to.have.jsonBody("");
  pm.response.to.not.have.jsonBody("error");
});

// pm.response.to.be* Example
pm.test("No error in return result", function() {
  // assert that the status code is 200
  pm.response.to.be.ok; // info, success, redirection, clientError,  serverError, are other variants
  // assert that the response has a valid JSON body
  pm.response.to.be.withBody;
  pm.response.to.be.json; // this assertion also checks if a body  exists, so the above check is not needed
});
```

Write the returned data to environment variables:

```js
// Get return data in JSON format
var jsonData = pm.response.json();

// Write the value of  jsonData.token into an environment variable
pm.environment.set("token", jsonData.token);
```


:::highlight purple
Learn more about [Assertion scripts](apidog://link/pages/593739).
:::
Public scripts are designed to re-use scripts and avoid repetitions of the same scripts in different places.

You can place the scripts with the same methods and classes in the public scripts and reference the script directly in your API requests.

## Usage

### Manage public scripts

Go to settings -> public script to manage your public scripts.


<Background>

![CleanShot 2025-06-11 at <EMAIL>](https://api.apidog.com/api/v1/projects/544525/resources/356626/image-preview)
</Background>


### Reference public scripts

You can directly reference public scripts in the preprocessor and postprocessor in API Execution Page or API Use Case page.


<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/356627/image-preview)
</Background>


:::tip[]

- The public scripts are executed before all the other scripts.
- The execution of multiple public scripts occurs in the same order as their addition.

:::

## Referencing public scripts in other scripts

Scripts can call each other:

- When normal scripts need to call variables or methods in public scripts, you should not use APIs of setting types such as `pm.sendRequest` or `pm.environments.set`cause they will fail. We recommend writing pure functions and returning them.
- Public scripts can call each other.
- Postprocessor scripts can call preprocessor scripts.

All scripts are executed within their own scope to prevent variable conflicts between them (wrapped by closures). When local variables or local methods are declared with `var`, `let`, `const`, or `function` other scripts cannot call those variables and methods. You must convert a variable or method to a global variable or global method if you want other scripts to use it.

Variable Example:

```js
// Declare a local variable, which cannot be used by other scripts.
var my_var = "hello"；
```

Recommend:

```js
// Declare a global variable, which can be used by other scripts.
my_var = "hello";
```

Function Example:

```js
// Declare a local function, which cannot be used by other scripts.
function my_fun(name) {
  console.log("hello" + name);
}
```

Recommend:

```js
// Declare a global function, which can be used by other scripts.
my_fun = function(name) {
  console.log("hello" + name);
};
```

:::tip[]

- You need to make sure there are no conflicts in global variable or global method naming between scripts.
- For API use cases, you need to add public scripts in preprocessor or postprocessor scripts in order to use it.
- You need to TIP to the order of calls when referencing scripts. Only scripts that are executed later can make calls to scripts that were executed before.

:::


## Practical Example

<Steps>
  <Step title="Example: Declare a Simple Global Function">
    Define a global function within a public script so that it can be accessed by other scripts.
    ```javascript
    my_fun = function (name) {
      return "Hello, " + name + "!";
    };
    ```


<Background>

![CleanShot 2025-06-11 at <EMAIL>](https://api.apidog.com/api/v1/projects/544525/resources/356628/image-preview)
</Background>


  </Step>

  <Step title="Call the Public Script">
    In the **Pre Processors** or **Post Processors** tab of an endpoint, first add the public script. Then, add a custom script and call the function declared in the public script.


```javascript
// Call the global function
const result = my_fun("Apidog");

// Write an assertion to validate the return value
pm.test("The my_fun function should return the correct greeting", function () {
  pm.expect(result).to.eql("Hello, Apidog!");
});
```

<Background>

![CleanShot 2025-06-11 at <EMAIL>](https://api.apidog.com/api/v1/projects/544525/resources/356629/image-preview)
</Background>

:::tip[]
Note: The public script must be added first in order for its methods to be accessible in subsequent custom scripts.
:::


  </Step>
</Steps>



### pm

```js
pm: Object;
```

pm objects contain information related to running API or test collections. You can use it in your request and response data. You can get or set on environment and global variables.

```js
pm.info:Object
```


- `pm.info.eventName:String`: the type of scripts that is currently running (preprocessor script or postprocessor script).
- `pm.info.iteration:Number`: the number of the current iteration(only valid in test collections).
- `pm.info.iterationCount:Number`: the number of total iterations (only valid in test collections).
- `pm.info.requestName:String`: the name of the current API running.
- `pm.info.requestId:String`: the ID of the current API running.

### pm.sendRequest

`pm.sendRequest:Function`

`pm.sendRequest` is used to send HTTP/HTTPS requests asynchronously in scripts.

- This method accepts a request parameter compatible with collection SDK- and a callback function parameter. The callback has 2 arguments. The first is an error and the second is a response compatible with the collection SDK. View more information in the [Collection SDK documentation](http://www.postmanlabs.com/postman-collection/Request.html#~definition).
- You can use it in both preprocessor and postprocessor scripts.

```js
// GET example
pm.sendRequest("https://postman-echo.com/get", function(err, res) {
  if (err) {
    console.log(err);
  } else {
    pm.environment.set("variable_key", "new_value");
  }
});

// Request Parameters Example
const echoPostRequest = {
  url: "https://postman-echo.com/post",
  method: "POST",
  header: {
    headername1: "value1",
    headername2: "value2",
  },
  // body: x-www-form-urlencoded
  body: {
    mode: "urlencoded",
    urlencoded: [
      { key: "account", value: "apidog" },
      { key: "password", value: "123456" },
    ],
  },
  /*
  // body: form-data 
  body: {
    mode: 'formdata',    
    formdata: [  
      { key: 'account', value: 'apidog' },
      { key: 'password', value: '123456' }
    ]
  }
  
  // body: json
  header: {
    "Content-Type": "application/json", // Add Content-Type in the header
  },
  body: {
    mode: 'raw',
    raw: JSON.stringify({ account: 'apidog', password:'123456' }), // serialized the JSON string
  }
  
  // body: raw or JSON 
  body: {
    mode: 'raw',
    raw: 'body contents',
  }
  */
};
pm.sendRequest(echoPostRequest, function(err, res) {
  console.log(err ? err : res.json());
});

// Assert on the return result
pm.sendRequest("https://postman-echo.com/get", function(err, res) {
  if (err) {
    console.log(err);
  }
  pm.test("response should be okay to process", function() {
    pm.expect(err).to.equal(null);
    pm.expect(res).to.have.property("code", 200);
    pm.expect(res).to.have.property("status", "OK");
  });
});
```

For more references, please visit:

- [Request JSON ](http://www.postmanlabs.com/postman-collection/Request.html#~definition)structure
- [Response ](http://www.postmanlabs.com/postman-collection/Response.html)structure

### pm.variables

`pm.variables:`view[ Variable SDK](https://www.postmanlabs.com/postman-collection/Variable.html) documentation here.

Local variables.The priority of different variables is as follows: 

`Local Variables` > `Environment Variables` > `Global Variables Shared within Project` > `Global Variables Shared within Team`.

- `pm.variables.has(variableName:String):function → Boolean`: Check whether a temporary variable exists.
- `pm.variables.get(variableName:String):function → *`: get a temporary variable.
- `pm.variables.set(variableName:String, variableValue:String):function → void`: set a temporary variable.
- `pm.variables.replaceIn(variableName:String):function`: Replaces "dynamic variables" within a string (e.g., {{variable_name}}) with actual values. Example:
```
// Define a string containing a dynamic variable
let stringWithVariable = "Hello, {{username}}";

// Use the replaceIn method to replace the {{username}} placeholder
let realValueString = pm.variables.replaceIn(stringWithVariable);

// Output the replaced value
console.log(realValueString); // Output: "Hello, john_doe"
```
- `pm.variables.replaceInAsync(variableName:String):function`: Replaces "dynamic value expressions" within a string (e.g., {{$person.fullName}}) with actual values. This method returns a Promise, so you need to use await when calling it. Example:
```
// Define a string containing a dynamic value expression
let stringWithVariable = "Hello, {{$person.fullName}}";

// Use the replaceInAsync method to replace the {{$person.fullName}}
let realValueString = await pm.variables.replaceInAsync(stringWithVariable);
```  
- `pm.variables.toObject():function → Object`: get all local variables as objects.

### pm.iterationData

`pm.iterationData:`

Test Data Variables

We currently do not support setting test data variables directly in scripts, since test data is managed separately. However, you can access the variables in scripts as shown below:

- `pm.iterationData.has(variableName:String):function → Boolean`: Check whether a test variable exists.
- `pm.iterationData.get(variableName:String):function → *`: get a test variable.
- `pm.iterationData.replaceIn(variableName:String):function`: replace dynamic variables in a string with their actual values, for example, `{{variable_name}}`.
- `pm.iterationData.toObject():function → Object`: get all local variables as objects.

### pm.environment

- `pm.environment.name:String`: the environment name.
- `pm.environment.has(variableName:String):function → Boolean`:Check whether an environment variable exists.
- `pm.environment.get(variableName:String):function → *`: get an environment variable.
- `pm.environment.set(variableName:String, variableValue:String):function`:set an environment variable.
- `pm.environment.replaceIn(variableName:String):function`: replace dynamic variables in a string with their actual values, for example, `{{variable_name}}`.
- `pm.environment.toObject():function → Object`: get all local variables as objects.
- `pm.environment.unset(variableName:String):function`: unset an environment variable.
- `pm.environment.clear():function`: clear all environment variables under the current environment.

TIP the operations mentioned above only read and write current values; they do not read or write remote values.

### pm.globals

- `pm.globals.has(variableName:String):function → Boolean`: Check whether a global variable exists.

- `pm.globals.get(variableName:String，variableScope:String):function → *`: get a global variable. Use 'PROJECT' (default) or 'TEAM' to specify the variable's scope.

- `pm.globals.set(variableName:String，variableValue:String， variableScope:String):function`: set a global variable. Use 'PROJECT' (default) or 'TEAM' to specify the variable's scope.

- `pm.globals.replaceIn(variableName:String):function`: replace dynamic variables in a string with their actual values, for example, `{{variable_name}}`.

  > In order to get the value of a request parameter that contains a variable in preprocessor scripts, use `pm.globals.replaceIn` to replace the variable with the real value.

- `pm.globals.toObject():function → Object`: get all global variables as objects.

- `pm.globals.unset(variableName:String，variableScope:String):function`: unset a global variable. Use 'PROJECT' (default) or 'TEAM' to specify the variable's scope.

- `pm.globals.clear():function`: clear all global variables under the current environment.

:::tip 
1. All operations above affect only`current values`, not`initial values`.
2. When using set with the 'TEAM' scope, it will only update the current value of an existing team variable. If the team variable doesn't exist, it will not be created. Instead, the variable will be treated as a local variable.
:::

### pm.request

`pm.request`: view [Request SDK](https://www.postmanlabs.com/postman-collection/Request.html) documentation here.

`request` is the API request object. In the preprocessor script, it is the request that will be sent. In the postprocessor script, it is the request that has already been sent.

`request` includes the following information:

- `pm.request.url`:[Url](http://www.postmanlabs.com/postman-collection/Url.html): the URL of the current request.
- `pm.request.getBaseUrl()`: Retrieves <code>BASE URL</code> selected by the current runtime environment. This feature is supported after version 2.1.39.
- `pm.request.headers`:[HeaderList](http://www.postmanlabs.com/postman-collection/HeaderList.html): the header list of the current request.
  - `pm.request.method`:String: the method of the current request, such as GET, POST, etc.
- `pm.request.body`: [RequestBody](http://www.postmanlabs.com/postman-collection/RequestBody.html): the body of the current request.
- `pm.request.headers.add({ key: headerName:String, value: headerValue:String}):function`: Add a header with a key, headerName, in the current request.
- `pm.request.headers.remove(headerName:String):function`: Delete a header with a key, headerName, in the current request.
- `pm.request.headers.upsert({ key: headerName:String, value: headerValue:String}):function`: Upsert a header with a key, headerName, in the current request. If the key already exists, it will be modified.

> The following API can only be used in`postprocessor scripts`.

### pm.response

`pm.response`: view [Response SDK documentation ](https://www.postmanlabs.com/postman-collection/Response.html) here.

Use `pm.response` to access return response information in postprocessor scripts.

pm.response includes the following information:

- `pm.response.code:Number`
- `pm.response.status:String`
- `pm.response.headers`:[HeaderList](http://www.postmanlabs.com/postman-collection/HeaderList.html)
- `pm.response.responseTime:Number`
- `pm.response.responseSize:Number`
- `pm.response.text():Function → String`
- `pm.response.json():Function → Object`

### pm.cookies

pm.cookies: view [CookieList SDK documentation](https://www.postmanlabs.com/postman-collection/CookieList.html) here.

Cookies is the list of cookies under the domain name of the current request.

- `pm.cookies.has(cookieName:String):Function → Boolean`
  Check whether the cookie value of a cookieName exists.
- `pm.cookies.get(cookieName:String):Function → String`
  Get cookie value from cookieName.
- `pm.cookies.toObject:Function → Object`
  Get all cookies under the current domain as an object.
- `pm.cookies.jar().clear(pm.request.url)`
  Clear all cookies.

:::tip TIP
  pm.cookies is the cookie returned after the API request, not the cookie sent by the API request.
:::

### pm.test

```js
pm.test(testName:String, specFunction:Function):Function
```

This function is used to assert whether a result meets expectations.

The example below can be used to determine whether a response is correct.

```js
pm.test("response should be okay to process", function() {
  pm.response.to.not.be.error;
  pm.response.to.have.jsonBody("");
  pm.response.to.not.have.jsonBody("error");
});
```

You can run an async test using `done`(an optional parameter) in a callback function.

```js
pm.test("async test", function(done) {
  setTimeout(() => {
    pm.expect(pm.response.code).to.equal(200);
    done();
  }, 1500);
});
```

- `pm.test.index():Function → Number`
  Get the total number tests from a specific location.

### pm.expect

```js
pm.expect(assertion:*):Function → Assertion
```

`pm.expect` is an assertion method. View ChaiJS expects BDD library documentation here.

This method is designed to assert data in response or variables. For more pm.expect examples, please visit[ Assertion library examples](https://learning.postman.com/docs/writing-scripts/script-references/test-examples/).

### pm.response.to.have.\*

- `pm.response.to.have.status(code:Number)`
- `pm.response.to.have.status(reason:String)`
- `pm.response.to.have.header(key:String)`
- `pm.response.to.have.header(key:String, optionalValue:String)`
- `pm.response.to.have.body()`
- `pm.response.to.have.body(optionalValue:String)`
- `pm.response.to.have.body(optionalValue:RegExp)`
- `pm.response.to.have.jsonBody()`
- `pm.response.to.have.jsonBody(optionalExpectEqual:Object)`
- `pm.response.to.have.jsonBody(optionalExpectPath:String)`
- `pm.response.to.have.jsonBody(optionalExpectPath:String, optionalValue:*)`
- `pm.response.to.have.jsonSchema(schema:Object)`
- `pm.response.to.have.jsonSchema(schema:Object, ajvOptions:Object)`

### pm.response.to.be.\*

You can use the built-in`pm.response.to.be`for quick assertions.

- `pm.response.to.be.info`
  Check whether the status code is 1XX.
- `pm.response.to.be.success`
  Check whether the status code is 2XX.
- `pm.response.to.be.redirection`
  Check whether the status code is 3XX.
- `pm.response.to.be.clientError`
  Check whether the status code is 4XX.
- `pm.response.to.be.serverError`
  Check whether the status code is 5XX.
- `pm.response.to.be.error`
  Check whether the status code is 4XX or 5XX.
- `pm.response.to.be.ok`
  Check whether the status code is 200.
- `pm.response.to.be.accepted`
  Check whether the status code is 202.
- `pm.response.to.be.badRequest`
  Check whether the status code is 400.
- `pm.response.to.be.unauthorized`
  Check whether the status code is 401.
- `pm.response.to.be.forbidden`
  Check whether the status code is 403.
- `pm.response.to.be.notFound`
  Check whether the status code is 404.
- `pm.response.to.be.rateLimited`
  Check whether the status code is 429.

  **External programs** are code files saved under the "External Programs Directory", which can be java program archive files (.jar packages), or code source files of other programs. It supports files with extensions like:

- `java` (.jar)
- `python` (.py)
- `php` (.php)
- `js` (.js)
- `BeanShell` (.bsh)
- `go` (.go)
- `shell` (.sh)
- `ruby` (.rb)
- `lua` (.lua)

:::tip[]

External programs run outside the "sandbox environment", with access to operate other programs, files, and data on your computer, which poses some security risks. Users should verify the security of called programs.
:::

## Calling an external program

You can quickly open the "External Program Directory" in the following way:

![](https://assets.apidog.com/uploads/help/2023/11/14/2bc81154adafd56e118cda73f9fae1be.png)

When calling an external program, Apidog will start a subprocess and run the specified external program in it with command line execution. Finally the standard output (stdout) of the subprocess will be returned as the result of the call. In the whole calling process, the core logic is implemented by users in the external program, while Apidog mainly does the following 3 steps:

1. Combine the command string based on the provided parameters
2. Execute the command 
3. Return the result

Among them, the first step is key to understand the calling principles. Apidog uses the formula "command prefix + program path + parameter list" to concatenate the command. 

The "command prefix" is inferred from the file extension of the program file. The "program path" and "parameter list" are both provided by users while calling.

For example: `pm.execute('com.apidog.Base64EncodeDemo.jar', ['abc','bcd'])`, the actual executed command is `java -jar "com.apidog.Base64EncodeDemo.jar" "abc" "bcd"`.

![](https://assets.apidog.com/uploads/help/2023/11/15/99add6b5bfb3fb6f84bc361edeaa0f93.jpg)

The mapping between program file extensions and command prefixes:

| Language   | Command Prefix         | File Extension |
| ---------- | ---------------------- | -------------- |
| Java       | `java -jar`            | `.jar`         |
| Python     | `python`               | `.py`          |
| PHP        | `php`                  | `.php`         |
| JavaScript | `node`                 | `.js`          |
| BeanShell  | `java bsh.Interpreter` | `.bsh`         |
| Go         | `go run`               | `.go`          |
| Shell      | `sh`                   | `.sh`          |
| Ruby       | `ruby`                 | `.rb`          |
| Lua        | `lua`                  | `.lua`         |
| Rust       | `cargo run`            | `.rs`          |
| Python     | `python`               | `.py`          |

## API

### pm.executeAsync

- `filePath` _string_ External program path 
- `args` _string[]_ Parameters. [When calling specified methods in a jar package](#call-specified-methods-in-jar-packages), `JSON.stringify` will be used for conversion. Except for that, non-_string_ types will be implicitly converted to _string_.
- `options` _Object_
  - `command` _string_ The execution command of the external program, the first part of "command prefix" is the execution command. Optional, default value is inferred automatically (see "command prefix" table above), can be customized to any program.
  - `cwd` _string_ Working directory of the subprocess. Optional, default is "External Programs Directory".
  - `env` _Record&lt;string, string&gt;_ Environment variables of the subprocess. Optional, default is `{}`.
  - `windowsEncoding` _string_ Encoding used on Windows system. Optional, default is `"cp936"`.
  - `className` _string_ Specify the class name to call in the jar package, e.g. `"com.apidog.Utils"`. Optional, see [Call specified methods in jar packages](#call-specified-methods-in-jar-packages) for details.
  - `method` _string_ Specify the method name to call in the jar package, e.g. `"add"`. Optional (required if `className` has value), see [Call specified methods in jar packages](#call-specified-methods-in-jar-packages) for details.
  - `paramTypes` _string[]_ Specify the parameter types of the method to call in the jar package, e.g. `["int", "int"]`. Optional, default is inferred automatically based on parameters, see [Call specified methods in jar packages](#call-specified-methods-in-jar-packages) for details.
- Return: _Promise&lt;string&gt;_

:::tip[Usage of `command` parameter]

By default Apidog uses `python` to execute `.py` files. If `python3` is already installed on the computer, `command` can be specified as `python3`.

```javascript
pm.executeAsync('./demo.py', [], { command: 'python3' }).then(res => {
   console.log('result: ', res);
});
```

:::

### pm.execute

:::tip[]
It is recommended to use [`pm.executeAsync`](#pm-executeasync) instead, See [Code migration instructions](#migrate-from-pm-execute-to-pm-executeasync) for details.
:::

`pm.execute(filePath, args, options)`

- `filePath` _string_ External program path
- `args` _string[]_ Parameters. [When calling specified methods in a jar package](#call-specified-methods-in-jar-packages), `JSON.stringify` will be used for conversion. Except for that, non-_string_ types will be implicitly converted to _string_.
- `options` _Object_
  - `windowsEncoding` _string_ Encoding used on Windows system. Optional, default is `"cp936"`.
  - `className` _string_ Specify the class name to call in the jar package, e.g. `"com.apidog.Utils"`. Optional, see [Call specified methods in jar packages](#call-specified-methods-in-jar-packages) for details.
  - `method` _string_ Specify the method name to call in the jar package, e.g. `"add"`. Optional (required if `className` has value), see [Call specified methods in jar packages](#call-specified-methods-in-jar-packages) for details.
  - `paramTypes` _string[]_ Specify the parameter types of the method to call in the jar package, e.g. `["int", "int"]`. Optional, default is inferred automatically based on parameters, see [Call specified methods in jar packages](#call-specified-methods-in-jar-packages) for details. 
- Return: _string_

## Execution and Logs 

When executing a program, the executed command will be printed in the console (for reference only). If the result does not meet expectations, you can copy the command and paste it in `Shell/CMD` to debug.

The console will also print the "standard output (stdout)" and "standard error output (stderr)" of the executed process. The stdout content (excluding the trailing newline character) will be the final result of the execution. 

:::tip
For historical reasons, `pm.execute` treats execution as failed when there is content in stderr. This causes some programs to fail when outputting warnings or error messages. `pm.executeAsync` changes to use the **exit code** of the process to determine if the execution failed.  
:::

![](https://assets.apidog.com/uploads/help/2023/11/15/a069941be4890dbc6ca04cc498506ed9.png)

## Input and output of external programs

### Parameters

Since the specified external program runs with command line execution, it can only get the passed in parameters through command line arguments.

For example, in script `pm.executeAsync('add.js', [2, 3])`, the actual executed command is `node add.js 2 3`. To get the parameters in the external script add.js: 

```js
let a = parseInt(process.argv[1]); // 2  
let b = parseInt(process.argv[2]); // 3
```

:::tip 

1. Different programming languages have different ways to get command line arguments, please refer to corresponding language docs.
2. The type of command line arguments is always _string_, need to convert based on actual types.
   :::

### Return value

As mentioned above, Apidog uses the stdout content as the result of program execution. So printing content to stdout can return results.

For example, in script `const result = await pm.executeAsync('add.js', [2, 3])`, the result can be returned by:

```js 
console.log(parseInt(process.argv[1]) + parseInt(process.argv[2]));
```

:::tip[]

1. Different programming languages have different ways to print to stdout, refer to corresponding language docs.  
2. The return type is _string_, need to convert based on actual types.
3. The trailing newline character of the result will be trimmed.
4. [When calling specified methods in jar packages](#call-specified-methods-in-jar-packages), the return value of the called method will be used as the final return value.
   :::

### Throwing errors

Throwing errors can fail the current task and stop execution. For example: 

```js
throw Error("Execution failed"); 
```

:::tip[]

1. Different programming languages have different ways to throw errors, refer to corresponding docs.
2. In JavaScript, `console.error('Error')` only prints to stderr instead of throwing an error. Consider this while using other languages too.
   :::

### Debug information 

Since `pm.executeAsync` uses exit code instead of stderr to determine success, stderr can be used to print debug information without affecting execution.

For example:

```js
console.warn("debug info");
console.error("error info");
```

:::tip

1. Only `pm.executeAsync` supports this way of printing debug info.  
2. Different programming languages have different ways to print to stderr, refer to corresponding docs.
   :::

## Migrate from pm.execute to pm.executeAsync

Since the return value of `pm.executeAsync` is _Promise_ type, `execute` cannot be directly changed to `executeAsync`. But you can use `async`/`await` to migrate with minimal changes.

:::tip
Apidog version 2.3.24 or later(CLI version 1.2.38 or later) supports top-level await.
:::

Steps:

1. Change `execute` to `executeAsync`
2. Add `await` before function call


```js
// Before
const result = pm.execute("add.js", [3, 4]); 
pm.environment.set("result", result);
```

```js 
const result = await pm.executeAsync("add.js", [3, 4]);
pm.environment.set("result", result);
```

## Call specified methods in .jar packages

:::tip
This feature requires Apidog version to be 2.1.39 or later. It only supports calling jars with reflection, not jars like Spring Boot using internal runtime reflection.
:::

By default, calling a jar will invoke the main method in the Main class. If `options.className` is specified, it will override the default behavior and call the specified method in the jar instead.

Calling specified methods in jars is different from other external programs. Apidog will use a built-in executor to find the method in the jar by reflection and call it. If the called method has a return value, it will be used as the final return value after converting to string. Otherwise, it works the same as other calls, using stdout content as return value.

For example:

```js
await pm.executeAsync('./scripts/jar-1.0-SNAPSHOT.jar', ['hello', 'world'], {
    className: 'com.apidog.Test', 
    method: 'combine',
    paramTypes: ['String', 'String']
})
```

The actually executed command is: 

```bash
java -jar "<app-dist>/assets/JarExecuter-1.1.0-jar-with-dependencies.jar" ./scripts/jar-1.0-SNAPSHOT.jar "com.apidog.Test.combine(String,String)" "\"hello\"" "\"world\""
```

Where `<app-dist>/assets/JarExecuter-1.1.0-jar-with-dependencies.jar` is the built-in executor, responsible for finding the method `com.apidog.Test.combine(String,String)` in the user program `./scripts/jar-1.0-SNAPSHOT.jar` through reflection, and calling it with parameters (JSON string) `"hello"` and `"world"`.

:::tip
`paramTypes` is optional. If not specified, types will be inferred automatically based on parameters. Integers are inferred as `"int"`, floats as `"double"`, booleans as `"boolean"`, strings as `"String"`, arrays are inferred based on the first element, e.g. `[3]` is inferred as `"int[]"`, `[3.14]` as `"double[]"`, etc.
If the inferred types do not match the actual parameter types of the called method, `paramTypes` needs to be specified manually.
Supported values in `paramTypes` array: `"Number"`、`"int"`、`"Integer"`、`"long"`、`"Long"`、`"short"`、`"Short"`、`"float"`、`"Float"`、`"double"`、`"Double"`、`"boolean"`、`"Boolean"`、`"String"`、`"Number[]"`、`"int[]"`、`"Integer[]"`、`"long[]"`、`"Long[]"`、`"short[]"`、`"Short[]"`、`"float[]"`、`"Float[]"`、`"double[]"`、`"Double[]"`、`"boolean[]"`、`"Boolean[]"`、`"String[]"`

So the `paramTypes` in the example above can be omitted:

```js 
await pm.executeAsync('./scripts/jar-1.0-SNAPSHOT.jar', ['hello', 'world'], {
    className: 'com.apidog.Test',
    method: 'combine' 
})
```

:::

## Examples

### 1. PHP program

   Script:

   ```js
const param1 = { a: 1, b: 2 }
const resultString = await pm.executeAsync('test.php', [JSON.stringify(param1)])
const result = JSON.parse(resultString)
console.log('Result:', result) // Result: { a: 2, b: 4 }
   ```

   test.php:

   ```php
<?php
$param = json_decode($argv[1]);
$result = [];
foreach($param as $key=>$value)
{
    $result[$key] = $value * 2;
}
echo json_encode($result);
   ```

### 2. Jar program

   Script:

   ```js
const result = await pm.executeAsync('com.apidog.utils.jar', [3, 5], {
   className: 'com.apidog.utils.Utils',  
   method: 'add',
   paramTypes: ['Integer', 'Integer']
})
console.log('Result:', result) // Result: 8
   ```

   com.apidog.utils.jar:

   ```java
   package com.apidog.utils;

   public class Utils {
       public Integer add(Integer a, Integer b) {
           return a + b;
       }
   };
   ```

## Common issues

### 1. Some programs require project config files and will error if missing

**Rust and Go: **

Rust:  

```
could not find `Cargo.toml` in `<...>/ExternalPrograms` or any parent directory
```

Go: 

```
go.mod file not found in current directory or any parent directory; see 'go help modules'
```

Solution: Use [pm.executeAsync](#pm-executeasync) and specify `cwd`.

### 2. MacOS has built-in Python 3 but no Python 2

Use [pm.executeAsync](#pm-executeasync) and set `command` to `"python3"`.

### 3. Command xxx not found

Install corresponding program and add necessary directories to system PATH. See [docs](/reference/install-java) for Java installation.

### 4. Calling external scripts prints garbled on some Windows systems 

Set `windowsEncoding` to `'utf-8'`

```js 
var result = pm.execute(`hello.go`, [], { windowsEncoding: 'utf-8' })
```

Apidog supports the utilization of both built-in and non-built-in JavaScript class libraries within scripts.

1. Built-In JS Class Libraries

You can use the built-in JS library in Apidog using `require`.

```js
var cryptoJs = require("crypto-js");
console.log(cryptoJs.SHA256("Message"));
```

2. Non-Built-In JS Class Libraries

You can introduce numerous other libraries that are not built-in but have been made available on npm dynamically using the `$$.liveRequire` function. Only pure js libraries are supported, preferably libraries with the word browser written to support browser-side operation. Libraries containing language extensions such as C/C++ are not supported for loading and will run out of time or exceptions.

:::tip TIP

You need to download JS libraries from the network for non-built-in libraries. Therefore, you must be connected to the Internet. There will be a performance loss to downloading libraries on the run. Therefore, we recommend using built-in JS libraries first.
:::

```js
// Below is an example of using a non-built-in JS class library.

// Get a single npm library: camelcase
$$.liveRequire("camelcase", (camelCase) => {
  camelCase("foo-bar"); // => 'fooBar'
});

//Get a multiple npm libraries: camelcase
$$.liveRequire(["camelcase", "md5"], (camelCase, md5) => {
  camelCase("foo-bar"); // => 'fooBar'
  md5("message"); // => '78e731027d8fd50ed642340b7c9a63b3'
});
```

### Built-in Library List

- Encode and Decode
  - [atob](https://www.npmjs.com/package/atob)(v2.1.2): Base64 decode.
  - [btoa](https://www.npmjs.com/package/btoa)(v1.2.1): Base64 encode.
  - [crypto-js](https://www.npmjs.com/package/crypto-js)(v3.1.9-1): An Encoding / decoding library, including the common encoding and decoding methods (Base64, MD5, SHA, HMAC, AES, etc.).
    - You can only require the entire module, not a submodule of the class library. View the documentation here for more details.
  - [jsrsasign](https://www.npmjs.com/package/jsrsasign)(10.3.0): RSA encryption / decryption. Only Apidog version 1.4.5 or later is supported.
- Assertion
  - [chai](http://chaijs.com/) (v4.2.0): BDD / TDD assertion library.
- Tools
  - [postman-collection](http://www.postmanlabs.com/postman-collection/)(v3.4.0): Postman Collection library.
  - [cheerio](https://cheerio.js.org/)(v0.22.0): a subset of jQuery.
  - [lodash](https://lodash.com/) (v4.17.11): JS Utilities Library.
  - [moment](http://momentjs.com/docs/)(v2.22.2): Date libraries (not including locales).
  - [uuid](https://www.npmjs.com/package/uuid): generate UUID.
  - [xml2js](https://www.npmjs.com/package/xml2js)(v0.4.19): convert XML into JSON.
  - [csv-parse/lib/sync](https://csv.js.org/parse/api/sync/)( v1.2.4): parse CSV.
- JSONSchema Validators
  - [tv4](https://github.com/geraintluff/tv4)(v1.3.0):JSONSchema validator.
  - [ajv](https://www.npmjs.com/package/ajv)(v6.6.2):JSONSchema validator.
- Built-in NodeJS modules
  - [path](https://nodejs.org/api/path.html)
  - [assert](https://nodejs.org/api/assert.html)
  - [buffer](https://nodejs.org/api/buffer.html)
  - [util](https://nodejs.org/api/util.html)
  - [url](https://nodejs.org/api/url.html)
  - [punycode](https://nodejs.org/api/punycode.html)
  - [querystring](https://nodejs.org/api/querystring.html)
  - [string-decoder](https://nodejs.org/api/string_decoder.html)
  - [stream](https://nodejs.org/api/stream.html)
  - [timers](https://nodejs.org/api/timers.html)
  - [events](https://nodejs.org/api/events.html)

### Usage


Common examples of using built-in libraries for encryption, decryption, encoding, and decoding data are provided below.

### SHA256 Encryption

```js
// SHA256 Encryption with Base64 Output
// Define the message to be encrypted
const message = "Hello, World!";

// Encrypt using the SHA256 algorithm
const hash = CryptoJS.SHA256(message);

// Output the encrypted result as Base64
const base64Encoded = CryptoJS.enc.Base64.stringify(hash);

// Print the result
console.log("SHA256: " + base64Encoded);
```

### HMAC-SHA256 Encryption

```js
// HMAC-SHA256 Encryption with Base64 Output
// Define the message and the secret key
const message = "Hello, World!";
const secretKey = "MySecretKey";

// Encrypt using the HMAC-SHA256 algorithm
const hash = CryptoJS.HmacSHA256(message, secretKey);

// Output the encrypted result as Base64
const base64Encoded = CryptoJS.enc.Base64.stringify(hash);

// Print the result
console.log("HMAC-SHA256: " + base64Encoded);
```

### Base64 Encoding

```js
// Define the message to be encoded
const message = "Hello,ApiDog!";

// Encode the message using CryptoJS for Base64
const wordArray = CryptoJS.enc.Utf8.parse(message);
const base64Encoded = CryptoJS.enc.Base64.stringify(wordArray);

// Print the encoded result
console.log("Base64: " + base64Encoded);
```

### Base64 Decoding

**String Decoding:**

```js
// Base64 encoded string (typically extracted from response data)
let encodedData = {
    "data": "SGVsbG8sQXBpRG9nIQ=="
};

// Decode the Base64 encoded data
let decodedData = CryptoJS.enc.Base64.parse(encodedData.data).toString(CryptoJS.enc.Utf8);

// Print the decoded result
console.log(decodedData); // "Hello,ApiDog!"
```

**JSON Decoding:**

You can set the decoded JSON data as the response body using the `pm.response.setBody()` method.

```js
// Import CryptoJS library
const CryptoJS = require("crypto-js");

// Get the Base64 encoded string from the response
let encodedData = pm.response.text();

// Decode the Base64 encoded data
let decodedData = CryptoJS.enc.Base64.parse(encodedData).toString(CryptoJS.enc.Utf8);

// Parse the decoded JSON string
let jsonData = JSON.parse(decodedData);

// Set the parsed JSON data as the response body
pm.response.setBody(jsonData);

// Print the result
console.log(jsonData);
```

### AES Encryption

```js
// Import CryptoJS library
const CryptoJS = require("crypto-js");

// Assume this is the value of the `password` field we want to encrypt, obtained from environment variables
const password = pm.environment.get("password");

// Use secure key and IV (for demonstration purposes, ensure to protect these sensitive details in real applications)
const key = CryptoJS.enc.Utf8.parse('mySecretKey12345'); // Ensure it's 16/24/32 bytes
const iv = CryptoJS.enc.Utf8.parse('myIVmyIVmyIVmyIV'); // Ensure it's 16 bytes

// AES encryption
const encrypted = CryptoJS.AES.encrypt(password, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
}).toString();

// Set the encrypted password as a new variable to be used in the request body
pm.environment.set("encryptedPassword", encrypted);
```

### AES Decryption

Assuming an AES encrypted ciphertext with ECB mode and Pkcs7 padding, the AES decryption script example is as follows:

```js
// Import CryptoJS library
const CryptoJS = require('crypto-js');

// Base64 encoded AES encrypted ciphertext (typically extracted from response data)
const ciphertext = "qvK7eVXu94S1dy4sLC+jrQ==";

// Decryption key, ensure it's 16/24/32 bytes (typically read from environment variables)
const key = CryptoJS.enc.Utf8.parse('1234567891234567');

// AES decryption
const decryptedBytes = CryptoJS.AES.decrypt(ciphertext, key, {
    mode: CryptoJS.mode.ECB, // Decryption mode
    padding: CryptoJS.pad.Pkcs7 // Padding scheme
});

// Convert the decrypted byte array to a UTF-8 string
const originalText = decryptedBytes.toString(CryptoJS.enc.Utf8);

// Print the decrypted text
console.log(originalText); // "Hello,Apidog!"
```

### RSA Encryption

```js
// Import jsrsasign library
const jsrsasign = require('jsrsasign');

// Define the public key (typically read from environment variables)
const publicKey = `
-----BEGIN PUBLIC KEY-----
...Public Key...
-----END PUBLIC KEY-----
`;

// Encrypt using the public key
const plaintext = "Hello,Apidog!";
const pubKeyObj = jsrsasign.KEYUTIL.getKey(publicKey);

const encryptedHex = jsrsasign.KJUR.crypto.Cipher.encrypt(plaintext, pubKeyObj);
console.log("Encrypted Ciphertext:", encryptedHex);
```

### RSA Decryption

```js
// Import jsrsasign library
const jsrsasign = require('jsrsasign');

// Define the private key (typically read from environment variables)
const privateKeyPEM = `
-----BEGIN PRIVATE KEY-----
...Private Key...
-----END PRIVATE KEY-----
`;

// Define the ciphertext (typically extracted from response data)
const ciphertext = '...';

// Decrypt
const prvKeyObj = jsrsasign.KEYUTIL.getKey(privateKeyPEM);
const decrypted = jsrsasign.KJUR.crypto.Cipher.decrypt(ciphertext, prvKeyObj);
console.log(decrypted);
```

Below is a complete example of simple RSA encryption and decryption (Note: The `jsrsasign` version is 10.3.0; syntax may not be compatible with other versions). This example can be run in a Node.js environment and adapted for encryption or decryption operations in Apidog:

```js
const rsa = require('jsrsasign');

// Generate RSA key pair
const keypair = rsa.KEYUTIL.generateKeypair("RSA", 2048);
const publicKey = rsa.KEYUTIL.getPEM(keypair.pubKeyObj);
const privateKey = rsa.KEYUTIL.getPEM(keypair.prvKeyObj, "PKCS8PRV");

console.log("Public Key:", publicKey);
console.log("Private Key:", privateKey);

// Encrypt with public key
const plaintext = "Hello,Apidog!";
const pubKeyObj = rsa.KEYUTIL.getKey(publicKey);

const encryptedHex = rsa.KJUR.crypto.Cipher.encrypt(plaintext, pubKeyObj);
console.log("Encrypted Ciphertext:", encryptedHex);

// Decrypt with private key
const prvKeyObj = rsa.KEYUTIL.getKey(privateKey);

const decrypted = rsa.KJUR.crypto.Cipher.decrypt(encryptedHex, prvKeyObj);
console.log("Decrypted Plaintext:", decrypted);
```


<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/357476/image-preview)
</Background>



:::tip TIP

When using a built-in library, you can only require the entire module; submodules cannot be required individually.

```json
// A correct example.
var cryptoJs = require("crypto-js");
console.log(cryptoJs.SHA256("Message"));

// A wrong example.
var SHA256 = require("crypto-js/sha256");
console.log(SHA256("Message"));
```

:::


Apidog offers a programmable approach to visualize response data, allowing you to present response information in an easily understandable manner. This will enable team members to gain a clearer understanding of the current API documentation.

![](https://assets.apidog.com/uploads/help/2023/07/27/06605f110095a2ae2864dcd896d827b0.jpg)

## 1. Adding custom script

In Apidog, you can add custom scripts in pre-operation or post-operation.

![](https://assets.apidog.com/uploads/help/2023/07/27/51c5fec22ac88f22a6b687bdc888044d.jpg)

## 2. Invocation method

In the custom script, use the `pm.visualizer.set()` function to display the visualized result in the Visualize tab of the response body.

![](https://assets.apidog.com/uploads/help/2023/07/27/1b9886ee7c830a28c5b8a11044ad2325.jpg)

## Code example

In the post-operation, you can use a custom script to extract links from the response and generate base64 images.

```javascript
// Translated to English, keeping the format:

// Encapsulate the API response data into the required structure
var resp = {
    response: pm.response.json()
}

// HTML template string
var template = `<html><img src="{{response.data}}" /></html>`;

// Set visualizer data by passing the template and the parsing object.
pm.visualizer.set(template, resp);
```

## pm.visualizer.set()

This method accepts 3 parameters.

1. The `template` parameter (required):

   `template` is a required parameter, The first parameter is the HTML template string received by [Handlebars](https://handlebarsjs.com/) , which will eventually be rendered under `<body>`.You can write `<link>` in `template` to load external css stylesheets, or use `<script>` to load third-party libraries.

2. The `data` parameter (optional):

   Select the parameter `data` to receive an object that is used to replace the template string variable of [Handlebars](https://handlebarsjs.com/).

      ```JavaScript
   const template = `<div>{{name}}</div>`;
   pm.visualizer.set(template, {
       name: 'Apidog'
   })
   // The rendered result is <div>Apidog</div>
      ```

3. The `options` parameter (optional):

   It is the same as the options parameter received by the `options` method. It is used to configure how Handlebars compiles the template string passed in the first parameter.


## pm.getData(cb: (err, data) => void)

The parameter received by this method is a function that allows you to get the data of the second parameter you passed in the template string passed in `pm.visualizer.set()`.

1. `error` error message

2. The data passed in by `data` through the second parameter `pm.visualizer.set()`

```JavaScript
const template = `
    <div>{{name}}</div>
    <script>
        pm.getData(function(err, data){
            // Do the corresponding operation according to the incoming data in the callback function
            console.log(data.name)
            // Apidog
        })
    </script>
`
pm.visualizer.set(template, {
    name: 'Apidog'
})
```

:::tip[]
You cannot call `Worker` and `indexedDB` in the `window` object in the template string.
:::

Assertion scripts are code snippets that verify if conditions or assumptions in software are true, commonly used in testing to automate checks for expected behavior. They raise errors if assertions fail, ensuring software correctness.

## Examples

Assert whether the result returned by the request is correct:

```js
// pm.response.to.have Example
pm.test("Return status code 200", function () {
  pm.response.to.have.status(200);
});

// pm.expect() Example
pm.test("The current environment is the production environment", function () {
  pm.expect(pm.environment.get("env")).to.equal("production");
});

// response assertions Example
pm.test("No error in return result", function () {
  pm.response.to.not.be.error;
  pm.response.to.have.jsonBody("");
  pm.response.to.not.have.jsonBody("error");
});

// pm.response.to.be* Example
pm.test("No error in return result", function () {
  // assert that the status code is 200
  pm.response.to.be.ok; // info, success, redirection, clientError,  serverError, are other variants
  // assert that the response has a valid JSON body
  pm.response.to.be.withBody;
  pm.response.to.be.json; // this assertion also checks if a body  exists, so the above check is not needed
});
```

Write the returned data to environment variables:

```js
// Get return data in JSON format
var jsonData = pm.response.json();

// Write the value of  jsonData.token into an environment variable
pm.environment.set("token", jsonData.token);
```

Check whether the response body includes a given string.

```js
pm.test("Body matches string", function () {
  pm.expect(pm.response.text()).to.include("string_you_want_to_search");
});
```
## Syntax reference

### Check whether the response body is a given string

```js
pm.test("Body is correct", function () {
  pm.response.to.have.body("response_body_string");
});
```

### Check the json output value

```js
pm.test("Your test name", function () {
  var jsonData = pm.response.json();
  pm.expect(jsonData.value).to.eql(100);
});
```

### Check whether the header is set as Content-Type

```js
pm.test("Content-Type header is present", function () {
  pm.response.to.have.header("Content-Type");
});
```

### Check whether the request response time is less than 200 milliseconds

```js
pm.test("Response time is less than 200ms", function () {
  pm.expect(pm.response.responseTime).to.be.below(200);
});
```

### Check whether the HTTP status code is 200

```js
pm.test("Status code is 200", function () {
  pm.response.to.have.status(200);
});
```

### Check whether the HTTP status code name contains a string

```js
pm.test("Status code name has string", function () {
  pm.response.to.have.status("Created");
});
```

### Check whether the POST request status code is correct.

```js
pm.test("Successful POST request", function () {
  pm.expect(pm.response.code).to.be.oneOf([201, 202]);
});
```

## How to Use the Assertion Libraries

Apidog has built-in ChaiJS as an assertion library. Below is an example of commonly used assertion test scripts. View the documentation, ChaiJS expect BDD library, for more examples.

### Assert that the target string contains another string

```js
pm.test("Assert that the target string contains another string", function () {
  pm.expect("foobar").to.have.string("bar");
});
```

### Assert that the target is strictly equal to (===) a value

```js
const TEN = 10;
pm.test("Check whether number is equal to 10", function () {
  pm.expect(TEN).to.equal(10);
});
```

If the deep flag is on, the assert target is the value.

```js
pm.test("The assert target is the value", function () {
  pm.expect(data1).to.deep.equal(data2);
});
```

When you turn on the deep flag to use equal and property assertions, the flag will allow subsequent assertions to compare key-value pairs of objects recursively instead of comparing the objects themselves.

### Assert that the depth is equal to a value, equivalent to deep.equal(value)

```js
pm.test("Check response value", function () {
  var jsonData = pm.response.json();
  pm.expect(jsonData.value).to.eql(100);
});
```

### Assert the current environment

```js
pm.test("Check whether environment is production", function () {
  pm.expect(pm.environment.get("env")).to.equal("production");
});
```

### Assert a data structure

```js
pm.test("Check whether target is string", function () {
  pm.expect("Postman").to.be.a("string");
});
pm.test("Check whether target is an object", function () {
  pm.expect({ a: 1 }).to.be.an("object");
});
pm.test("Check whether target is undefined", function () {
  pm.expect(undefined).to.be.an("undefined");
});
```

:::tip TIP

1. We recommend to use the .a method to check the data type of the template before making other assertions.
2. Data types are case-sensitive.

:::


### Check if array is empty

```js
pm.test("Check whether array is empty", function () {
  pm.expect([]).to.be.empty;
});
pm.test("Check whether string is empty", function () {
  pm.expect("").to.be.empty;
});
```

### Check if an array is empty after using .a method

```js
pm.test("Check whether array is empty", function () {
  pm.expect([]).to.be.an("array").that.is.empty;
});
```

### Assert the key value of the target object

```js
pm.test("Check whether object contains all provided keys", function () {
  pm.expect({ a: 1, b: 2 }).to.have.all.keys("a", "b");
});
pm.test("Checking if object contains any ONE of the keys", function () {
  pm.expect({ a: 1, b: 2 }).to.have.any.keys("a", "b");
});
pm.test(
  "Check whether object contains any NONE of the provided keys",
  function () {
    pm.expect({ a: 1, b: 2 }).to.not.have.any.keys("c", "d");
  }
);
```

### Assert whether the target object contains the specified attribute

```js
pm.test("Check whether object contains the property", function () {
  pm.expect({ a: 1 }).to.have.property("a");
});
```

:::tip TIP

1. The target object must be an object, set, array, or map.
2. If .keys is not preceded by .all or .any, it defaults to .all.
3. Since only some data types of the target object can be used with the .keys method, we recommend asserting the data type with the .a method first.

:::

```js
pm.test("Check whether object contains all the keys", function () {
  pm.expect({ a: 1, b: 2 }).to.be.an("object").that.has.all.keys("a", "b");
});
```

### Assert the length of the target object

```js
pm.test("Check the length of the target", function () {
  pm.expect("foo").to.have.lengthOf(3);
});
pm.test("Check the size of the target", function () {
  pm.expect([1, 2, 3]).to.have.lengthOf(2);
});
```

### Assert the members of the target object (members)

```js
pm.test(
  "Check whether the target has same members as the array set",
  function () {
    pm.expect([1, 2, 3]).to.have.members([2, 1, 3]);
  }
);
```

:::tip TIP

1. By default, .members uses a strict comparison.
2. The order of the members does not affect the result.
   :::

### Asserts that the target object contains the specified item

```js
pm.test(
  "Check whether the target array includes the number provided",
  function () {
    pm.expect([1, 2, 3]).to.include(2);
  }
);
pm.test(
  "Check whether the target object includes the properties provided",
  function () {
    pm.expect({ a: 1, b: 2, c: 3 }).to.include({ a: 1, b: 2 });
  }
);
```

We recommend using `.a` method to determine the data type before using `.include`.

Example:

```js
pm.test(
  "Check whether the target is an array that includes the number specified",
  function () {
    pm.expect([1, 2, 3]).to.be.an("array").that.includes(2);
  }
);
```

## Environment variables

```js
// Set an environment variable.
pm.environment.set("variable_key", "variable_value");

// Get an environment variable.
var variable_key = pm.environment.get("variable_key");

// Unset an environment variable.
pm.environment.unset("variable_key");
```

### Write an object/array into an environment variable

Environment variables only take strings. You need to use `JSON.stringfy` to convert an object or an array into a string.

```js
var array = [1, 2, 3, 4];
pm.environment.set("array", JSON.stringify(array));

var obj = { a: [1, 2, 3, 4], b: { c: "val" } };
pm.environment.set("obj", JSON.stringify(obj));
```

You need to use JSON.parse to convert it back when reading it.

```js
try {
  var array = JSON.parse(pm.environment.get("array"));
  var obj = JSON.parse(pm.environment.get("obj"));
} catch (e) {
  // handle an exception
}
```

## Global variables

```js
// Set a global variable
pm.globals.set("variable_key", "variable_value");

// Get a global variable
var variable_key = pm.globals.get("variable_key");

// Unset a global variable
pm.globals.unset("variable_key");
```

## Local variables

```js
// Set a temporary variable.
pm.variables.set("variable_key", "variable_value");

// Get a temporary variable.
var variable_key = pm.variables.get("variable_key");

// Unset a temporary variable.
pm.variables.unset("variable_key");
```

You can use `pm.request` to read or modify request messages.

- You can use scripts to get all request parameters, but you can only use scripts to modify header and query parameters.
- Modifying header and query parameters are only valid in preprocessor scripts, not in postprocessor scripts.
- Using scripts to get API parameters: if the parameter contains a variable, it will not be replaced with the corresponding value. You need to use the pm.variables.replaceIn to get the actual value.

```json
// pm.variables.replaceIn: handling variables in parameters
var body = pm.variables.replaceIn(pm.request.body.raw);
var jsonData = JSON.parse(body);
```

:::tip
In Apidog pre processors, there is a Built-In preprocessor named `Variable Substitution`, which replaces all variables (including dynamic variables) in the request with the actual value.
- Scripts like set variable need to be placed before Variable Substitution so that the variable can be set successfully.
- Scripts like API signature need to be dragged after Variable Substitution to get actual values of the parameters.
:::

## URL related information

```js
// Get an url object.
var urlObj = pm.request.url;

// Get the full url of the API request
var url = urlObj.toString();

// Get protocols (http or https).
var protocol = urlObj.protocol;

// Get the port.
var port = urlObj.port;
```

## Header parameters

Get header parameters

```js
// Get header parameters.
var headers = pm.request.headers;

// Get the value of the field1 key from the header.
var field1 = headers.get("field1");

// Get all header parameters as key-value objects.
var headersObject = headers.toObject();

// Iterate over the entire header.
headers.each((item) => {
  console.log(item.key); // log the item key
  console.log(item.value); // log the item value
});
```

Modifying Header Parameters

```js
// Get header parameters.
var headers = pm.request.headers;

// Add a header parameter.
headers.add({
  key: "field1",
  value: "value1",
});

// Modify a header parameter (add a header parameter if it does not exist).
headers.upsert({
  key: "field2",
  value: "value2",
});
```

## Query parameters

Get query parameters

```js
// Get query parameters.
var queryParams = pm.request.url.query;

//  Get the value of the field1 key from the header.
var field1 = queryParams.get("field1");

// Get all header parameters as key-value objects.
var quertParamsObject = queryParams.toObject();

// Iterate over the entire header.
queryParams.each((item) => {
  console.log(item.key); // log the item key
  console.log(item.value); // log the item value
});
```

Modifying Query Parameters

```js
// Get a query parameter.
var queryParams = pm.request.url.query;

// Add a query parameter.
queryParams.add({
  key: "field1",
  value: "value1",
});

// Modify a query parameter (add a query parameter if it does not exist).
queryParams.upsert({
  key: "field2",
  value: "value2",
});
```

## Body parameters

Body parameters come from `pm.request.body` which is a RequestBody instance.

View more details [here](http://www.postmanlabs.com/postman-collection/RequestBody.html).

:::tip TIP

- We recommend referencing variables in the Body to modify body data. You can modify the value of the corresponding variable in preprocessor scripts.
- We support direct modification of body parameters in Apidog version 1.4.16 or later. See example of how to use it below:

```json
var body = pm.request.body.toJSON();
console.log("body object", body);

var bodyStr = body.raw;
console.log("body string", bodyStr);

var bodyJSON = JSON.parse(bodyStr);
bodyJSON.id = 100;
pm.request.body.update(JSON.stringify(bodyJSON, null, 2));
console.log("Modified body", pm.request.body.toJSON());
```

:::

### 1. Body Type: form-data

Get form-data info.

```js
// When body is form-data, use pm.request.body.formdata to get request parameters.
var formData = pm.request.body.formdata;

// Get the value of the field1 key from the header.
var field1 = formData.get("field1");
console.log(field1); // log field 1

// Get all header parameters as key-value objects.
var formdataObject = formData.toObject();
console.log(formdataObject); // log formdataObject

// Iterate over form-data.
formData.each((item) => {
  console.log(item.key); //  log the item key
  console.log(item.value); // log the item value
});
```

### 2. Body Type: x-www-form-urlencode

Get x-www-form-urlencode info.

```js
// When body is x-www-form-urlencode, use pm.request.body.urlencoded to get request parameters.
var formData = pm.request.body.urlencoded;

// Get the value of the field1 key from the header.
var field1 = formData.get("field1");

// Get all header parameters as key-value objects.
var formdataObject = formData.toObject();

// Iterate over form-data.
formData.each((item) => {
  console.log(item.key); //  log the item key
  console.log(item.value); // log the item value
});
```

### 3. Body Type: JSON

Get JSON info.

```js
// When body type is json, use pm.request.body.raw to get request parameters.
try {
  var jsonData = JSON.parse(pm.request.body.raw);
  console.log(jsonData); // log JSON data.
} catch (e) {
  console.log(e);
}
```

### 4. Body Type: raw

Get raw info.

```js
// When body type is raw, use pm.request.body.raw to get request parameters.

var raw = pm.request.body.raw;
console.log(raw); // log raw data.
```

## Send API request

```js
pm.sendRequest("https://www.api.com/get", function(err, response) {
  console.log(response.json());
});
```

View more details for [pm.sendRequest](/pre-post-processors-and-scripts/scripts/pm-api) here.

## Encode/Decode

### Decode base64 Data

```js
var cryptoJs = require("crypto-js");

//  base64Content is a value that has been encoded with base64.
var rawContent = base64Content.slice(
  "data:application/octet-stream;base64,".length
);

// CryptoJS is an object that is embedded in the scripting engine.
// You can use it directly. View documentation here: https://www.npmjs.com/package/crypto-js
var intermediate = cryptoJs.enc.Base64.parse(base64content);
pm.test("Contents are valid", function() {
  pm.expect(cryptoJs.enc.Utf8.stringify(intermediate)).to.be.true; // a check for non-emptiness
});
```

You can use the built-in JS library to implement encryption and decryption algorithms.

### Convert XML to JSON

```js
var jsonObject = xml2Json(responseBody);
```
