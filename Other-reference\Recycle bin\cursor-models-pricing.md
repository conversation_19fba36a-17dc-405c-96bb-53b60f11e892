Models & Pricing
Available models in Cursor and their pricing

Cursor is offering a wide range of models, including the latest state-of-the-art models.

​
Pricing
All model usage is counted and billed in requests. Cursor offers two modes of usage:

Normal
Requests per model/message

Ideal for everyday coding tasks, recommended for most users.

Max
Requests per 1M tokens (MTok)

Best for complex reasoning, hard bugs, and agentic tasks.

​
Request
A request represents a single message sent to the model, which includes your message, any relevant context from your codebase, and the model’s response.

One request is $0.04

​ 
Free requests
Free requests automatically activate when you run out of normal requests. They’re processed at a lower priority, so you may wait a bit longer compared to fast requests—but they still cost nothing.

Free requests are not available for Max mode.
​
Normal mode
In normal mode, each message costs a fixed number of requests based solely on the model you’re using, regardless of context. We optimize context management without it affecting your request count.

For example, let’s look at a conversation using Claude 3.5 Sonnet, where each message costs 1 request:

Role	Message	Cost per message
User	Create a plan for this change (using a more expensive model)	1
Cursor	I’ll analyze the requirements and create a detailed implementation plan…	0
User	Implement the changes with TypeScript and add error handling	1
Cursor	Here’s the implementation with type safety and error handling…	0
Total		2 requests
​
Max Mode
In Max mode, pricing is calculated based on tokens, with Cursor charging the model provider’s API price plus a 20% margin. This includes all tokens from your messages, code files, folders, tool calls, and any other context provided to the model.

We use the same tokenizers as the model providers (e.g. OpenAI’s tokenizer for GPT models, Anthropic’s for Claude models) to ensure accurate token counting. You can see an example using OpenAI’s tokenizer demo.

Here’s an example of how pricing works in Max mode:

Role	Message	Tokens	Note	Cost per message
User	Create a plan for this change (using a more expensive model)	135k	No cached input tokens	2.7 requests
Cursor	I’ll analyze the requirements and create a detailed implementation plan…	82k		1.23 requests
User	Implement the changes with TypeScript and add error handling	135k	Most of input tokens are cached	2.7 requests
Cursor	Here’s the implementation with type safety and error handling…	82k		1.23 requests
Total		434k		7.86 requests
​
Models
Overview
Pricing
MAX Mode

Name	Context	Capabilities

Anthropic
Claude 3.5 Sonnet
75k	


M

Anthropic
Claude 3.7 Sonnet
120k	


M

Anthropic
Claude 4 Opus


M

Anthropic
Claude 4 Sonnet
120k	


M

Google
Gemini 2.5 Flash
128k	



Google
Gemini 2.5 Pro
120k	


M

OpenAI
GPT 4.1
128k	

M

OpenAI
GPT-4o
60k	


M

xAI
Grok 3 Beta
60k	


M

xAI
Grok 3 Mini Beta
60k	

M

OpenAI
o3


M

OpenAI
o4-mini
128k	


M
Show more models
​
Auto-select
Enabling Auto-select configures Cursor to select the premium model best fit for the immediate task and with the highest reliability based on current demand. This feature can detect degraded output performance and automatically switch models to resolve it.

Recommended for most users

​
Capabilities
​
Thinking
Enabling Thinking limits the list of models to reasoning models which think through problems step-by-step and have deeper capacity to examine their own reasoning and correct errors.

These models often perform better on complex reasoning tasks, though they may require more time to generate their responses.

​
Agentic
Agentic models can be used with Chat’s Agent mode. These models are highly capable at making tool calls and perform best with Agent.

Submitting an Agent prompt with up to 25 tool calls consumes one request. If your request extends beyond 25 tool calls, Cursor will ask if you’d like to continue which will consume a second request.

​
Max Mode
Some models support Max Mode, which is designed for the most complex and challenging tasks. Learn more about Max Mode.

​
Context windows
A context window is the maximum span of tokens (text and code) an LLM can consider at once, including both the input prompt and output generated by the model.

Each chat in Cursor maintains its own context window. The more prompts, attached files, and responses included in a session, the larger the context window grows.

Cursor actively optimizes the context window as the chat session progresses, intelligently pruning non-essential content while preserving critical code and conversation elements.

For best results, it’s recommended you take a purpose-based approach to chat management, starting a new session for each unique task.

​
Hosting
Models are hosted on US-based infrastructure by the model’s provider, a trusted partner or Cursor.

When Privacy Mode is enabled from Settings, neither Cursor nor the model providers will store your data, with all data deleted after each request is processed. For further details see our Privacy, Privacy Policy, and Security pages.

​
FAQ
​
What is a request?
A request is the message you send to the model.

​
What is a token?
A token is the smallest unit of text that can be processed by a model.



Max Mode
Experience enhanced AI capabilities with Max Mode in Cursor

Max Mode gives you access to the full capabilities of Cursor’s advanced AI models. It’s designed for moments when you need additional processing power and deeper analysis.

​
Comparison
The main difference between normal mode and Max mode is context behaviour where Max mode is optimized to work through as much context as possible. In practice, this means:

Larger context windows
Up to 200 tool calls (without asking for continuation)
Read file tool can read up to 750 lines
​
Context Window Comparison
Here’s what different context window sizes can handle in practice, with examples from real-world codebases:

Tokens	Scale	Real-World Examples	What Fits
10,000	Small	Single utility libraries	A utility like Underscore.js, or a few React components
60,000	Medium	Utility collections	Most of a medium-sized library like Lodash
120,000	Large	Full libraries	Complete utility libraries or core parts of larger frameworks
200,000	Extra Large	Web frameworks	Full frameworks like Express, or runtime libraries like Tokio
1,000,000	Massive	Framework cores	Core of major frameworks like Django (without tests)
Real-world token usage varies based on code style, comments, and tokenization. For example, Google’s documentation suggests that 1M tokens typically handles ~30,000-50,000 lines of code at 80 characters per line in practice.

​
Pricing
Max mode pricing is calculated based on tokens, with Cursor charging the model provider’s API price plus a 20% margin. This includes all tokens from your messages, code files, folders, tool calls, and any other context provided to the model. For detailed pricing, see models

Token usage from your account dashboard.

​
Token types
Model providers offer pricing in different tiers, depending on capabilities for caching etc. Here’s a breakdown of the various token types and what they mean. You can see an example of how tokens are determined using OpenAI’s tokenizer (note that this is just for demonstration - we use different tokenization under the hood).

Type	Description	Example	Cost
Input	Tokens the model is seeing for the first time,	New messages	Expensive
Input (Cached)	Tokens the model has seen before and have decided to cache for future user	Previous messages in a chat	Cheap
Output	The returned tokens by the model	Model response, mostly code	Expensive
​
How to use Max mode
To use Max mode,

Open model picker
Toggle Max mode
Select a compatible model from the dropdown
​
FAQ
​
When should I use Max mode?
Max Mode works particularly well for the hardest problems where you need the model to think and reason the most. Normal mode is still recommended for most tasks.

​
Why don’t all models have Max mode?
Max mode is intended for the large, context intensive operations. Models with context windows of 100k tokens or less see diminishing returns with Max mode