If you maintain your API spec outside of Apidog but want to use Apidog for API debugging, testing, and documentation, you can use the Scheduled import feature.

## Create a scheduled import

If your API spec is in OpenAPI, ApiDoc, or Apidog format URL, you can use scheduled import.

<Steps>
  <Step>
Go to **Settings** - **Import data** - **Scheduled import**
    </Step>
  <Step>
Click "**New**" to create a data source.
  </Step>
  <Step>
Fill in the necessary information, such as import frequency, URL of the data source, etc.
<Background>
      ![image.png](https://api.apidog.com/api/v1/projects/544525/resources/344171/image-preview)
</Background>
  </Step>
  <Step>
**Save**. The scheduled import will run according to the import frequency you set.
    </Step>
</Steps>

## How it works

Scheduled import works like this: By default, scheduled import is initiated from your **local client**. First, your Apidog client accesses the data source, retrieves the data, and then updates it to the Apidog project.

This means:

1. Scheduled import will only execute **when the project is open in the client**. Specifically, only if you have write permission for a project, have opened this project, and the time since the last scheduled import execution has exceeded the interval set in the import frequency, then the scheduled import will execute.
2. Scheduled import can import internal network data into Apidog, as long as your client can normally access your data source address.

Therefore, if a project hasn't been opened for a long time, the scheduled import won't take effect. It will only update the next time it's opened.

:::tip[]
If you open this project in [Apidog web](https://app.apidog.com), it will also trigger the scheduled import. However, there might be a slight difference here: the web client may be limited by the browser's network environment and might not be able to access the internal network.
:::

To solve the above problem, Apidog provides a second mechanism for scheduled import: the import can be initiated from a self-deployed runner instead of the local client.

If you have deployed a runner on your server, the runner will automatically import data from the source at fixed intervals, without needing to consider whether the client is open or not.

:::highlight purple
Learn more about [Self-hosted Runner](apidog://link/pages/616389).
:::

## Import into sprint Branches

You can schedule data imports to a sprint branch. By default, the main branch is selected. If you switch to a different sprint branch, data will be imported into that branch at regular intervals according to the set rules. [Learn more about importing data into sprint branches.](https://docs.apidog.com/design-api-in-a-branch-616423m0#import-oas-into-sprint-branch)

<Background>
![import-into-sprint-branches.png](https://api.apidog.com/api/v1/projects/544525/resources/348704/image-preview)
</Background>

## Scheduled import options

**Import frequency**: How often to trigger an import. If the time since the last import exceeds this duration, the import will be triggered.

**Source format**: Supports OpenAPI, ApiDoc, or Apidog formats.

**Source name**: Name it yourself, used to distinguish different data sources.

**Source URL**: The JSON/Yaml file URL of the data source, such as `https://petstore.swagger.io/v2/swagger.json`.

**Runs on**: Where to initiate the scheduled import from. The default is local client, you can choose runner after deploying a runner.

**Basic auth**: Used to access encrypted URLs.

**Advanced settings**: See [Import options](apidog://link/pages/633930).

:::tip[]
Apidog supports creating multiple data sources within a single project, each synchronizing and importing into different folders.
:::