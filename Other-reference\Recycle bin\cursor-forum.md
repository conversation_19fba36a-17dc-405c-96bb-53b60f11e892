There are users confusing about Cursor pricing discussing in Cursor Forum:

Questions:
For the new max mode pricing as shown below what exactly does this mean in terms of pricing? It looks like it means it just uses requests rather than pay per use but that raises a few immediate questions.

What happens when a user runs out of fast requests does max mode stop working? Does it become usage based pricing? Does it just use slow requests? Is there a way to opt into usage based pricing earlier to avoid burning through fast requests in 20-50 max requests?

I understand this pricing model is meant to be simpler but I feel there is information lacking on implementation details. I haven’t updated to the new version yet as I want to avoid accidentally burning through all of my fast credits immediately.

Any clarification would be greatly appreciated!

P.S. I know the usage based pricing is cost+20% but if the actual price per model could be listed somewhere under usage based pricing that’d be very helpful.

https://us1.discourse-cdn.com/flex020/uploads/cursor1/original/3X/8/a/8a01f442074a335869615c5f384ecc41d0fe7076.png

Answers:
Yep, T1000 is spot on about the token caching! For anyone wanting to keep costs predictable, staying in non-MAX mode is probably the way to go

Quick breakdown from our docs (Cursor – Models & Pricing):

Normal mode: Fixed cost per message, super predictable
MAX mode: Token-based pricing, can get expensive if sending lots of context
The token caching in MAX mode is pretty neat - when the model sees the same content again (like in follow-up messages), it only charges about 10% of the original token cost. But yeah, if you’re working with a tight request budget, sticking to normal mode is your best bet

To explicitly answer @TechyHercules’s questions:

- What happens when a user runs out of fast requests does max mode stop working? Does it become usage based pricing? Does it just use slow requests?
If a user uses all of their fast requests, they will no longer have access to MAX unless they enable usage-based pricing. Their is no slow pool for MAX mode requests.
- Is there a way to opt into usage based pricing earlier to avoid burning through fast requests in 20-50 max requests?
Unfortunately not, however, your end-of-month bill would be the same hypothetically.

Well, it does mention price per request above that in the same page.

image
image
745×382 9.17 KB
The info about usage-based pricing is in the Plans & Usage help page.

It does not prevent you from running out of requests. You’ll still burn through your requests in 20-50 max prompts. It allows you to pay for additional requests when you run out instead of switching to free, slow requests. It doesn’t say anything about enabling it before you run out of requests(just that you have to enable it when you run out to keep using fast requests), but you can turn it on or off at any time.

The new pricing method is quite expensive in terms of request usage. I lost hundreds of requests for very minimal coding effort — something that wasn’t an issue with previous versions.
Considering this, it’s becoming unaffordable, and competitors are gaining an edge because of it.
I hope Cursor will reconsider its approach to usage-based costing.

This is only if you enable Max mode. Disable it and it’ll cost much less, only as much as it used to.

Agreed, with non-MAX mode the pricing is very predictable and with the right prompts you can do a lot of coding with 500 requests.

For MAX Mode the pricing depends on tokens, so sending too much context unnecessarily would also consume tokens quickly. Some models have caching tokens (cheaper than first submission) for prompts they already received when you continue in chat.

Obviously staying out of MAX mode is going to make costs predictable, but that does not address the OP’s (or my) question – for those times when MAX mode is necessary, how can we understand the costs of the various models?

Also I see the only MAX model is sonnet - gemini went away with the last update?

Could you check again the models page? Gemini is clearly listed in Max mode.

Not sure if that helps but once you use it, you can see in the new dashboard, how many requests/tokens each actual model call consumed that was triggered from a single max mode prompt. It lists many lines with each stating costs of e.g. 0.2 requests or 2.4 and when hovering you see the tokens.

During usage of max mode, I’ve had some cases where it would go into 30 requests or so and from what I’ve seen I suspect that the amount of tool calls also play a role because cursor internally then re-requests the model (with hopefully many cached tokens) after every single one like list me the files in that directory, show me installed packages et cetera.
I think over time we will figure out prompts that will try to avoid the most expensive usages (unless we are willing to pay for it).

Just a follow-up question I have on the matter. For anyone that is just regularly using any of the max models, whether you are paying out of pocket or your employer is paying it, how much do you generally rack up in spending per day or per month?

I have had a nice experience with rare calls to the max models with the previous pricing model, but I am not sure how quickly the expense builds up primarily using a max model with the price changes. Especially since you can’t opt into usage-based pricing for a max model specifically since it now burns through fast requests first. I’d imagine it would force you into usage-based pricing even for regular models certain times of day if the slow queue is bad.

Anyway, if anyone is willing to share their experience with heavy max usage, I’d love to hear your thoughts!

It would be a game changer to have a little counter which shows how many tokens are being sent in each request (out of the max) and how much the request costed after it finished.

Guys, what’s going on? 2 hours and 500 requests have flown away. The agent couldn’t even launch a test and perform a mid-term one. What kind of pricing policy is this?! It’s easier to launch a local open model by buying the equipment once and for all.

Frustration with the New Request Pricing Structure — Power Users Penalized?
Hi all,

I’m writing as a longtime power user of Cursor AI who has come to rely on the platform for daily coding and productivity. I have invested a significant amount of money into Cursor Pro, specifically purchasing large blocks of requests under the previous yearly Pro plan.

However, with the recent shift from request blocks to a straight $0.04 per request pricing model, I feel incredibly frustrated and, frankly, penalized for being an early and heavy supporter.

Here’s the math that has me upset:
I paid $1,536 for 4,000 requests (that’s $0.384 per request).
Under the new structure, 4,000 requests would cost only $160 ($0.04 × 4,000).
That means anyone who prepaid at the old rate paid nearly ten times more than a new customer would for the exact same usage!
This isn’t just a small pricing change — it’s a massive gap that leaves early Pro supporters at a disadvantage.
I’ve optimized my workflow to maximize my request utility, but this new model makes it feel like all the investment in advance blocks was for nothing. If I’m misunderstanding something, please clarify. Otherwise, I believe Cursor AI should seriously consider refunding, crediting, or otherwise making things right for their most dedicated users who supported the platform under the old structure.

Has anyone else experienced this? Did you pay for a yearly Cursor Pro and now feel shortchanged?
Would love to hear how others are dealing with this change and whether the Cursor team plans to address it.

Hi, I’m confused by how you paid almost $1600 for 4000 requests.

In our old requests blocks ($20 for 500 requests), our current Pro plan (also $20 for 500 requests) and our standard usage-based pricing ($0.04/requests = $20/500), you should only ever pay $0.04 for each unit of “request”.

Some models and feature have >1 request cost, such as thinking models, super expensive models and so on, but they would be shown in your usage-pricing breakdown and on our models page.

The only exception to this is Max mode, which had the following pricing:

Old: $0.04 per request and $0.04 per tool call
New: Cost is directly correlated to request length (aka API pricing) - long request = higher cost
In this case, while a “request” could cost much more than $0.04, your costs to Cursor would match up with the costs you incur for us with the LLM provider.

Can you provide any further clarify on how you’ve reached this figure, and what that is made up from?

Well I am happy to show you my account, because my math and the numbers around it I went over multiple times and unless there is something wrong with my Cursor Pro packages I am not understanding the math is mathing for me.

image
image
366×376 11.7 KB
Now this only represents my Cursor Pro, which I have also paid monthly for the MAX usage prior to the change. I am currently around $2000~+ invested into Cursor usage for the last 8 months.

You can see by this that $192/ea Cursor Pro block of +500 = 4000 for a total of $1536

If I am wrong then please help me understand.

