Specify a new endpoint
An Endpoint represents a specific API endpoint or route. It's the core building block for defining, testing, and documenting an API in Apidog.

Apidog provides an intuitive UI for API designers to specify their APIs. Let's walk through an example:

Creating an Endpoint
This example demonstrates how to create an endpoint for querying user information by ID.

1
Create a New API: Open a new tab and click New API.
new api
2
Define the Request Path: In the API path field, enter /category/{id}. This sets up the base for the Request section.
Name: Get category by id

Path params:

Name: id

Type: integer

Example: 1

specify api
3
Define the Response Structure:
Scroll to the Responses section.

In the 'OK(200)' response, hover over the '+' icon and click to create a child node named "data".

schema-reference
4
Use a Schema Reference:
Hover over the 'data' node.

Click the type Object to change it to 'Schema Reference'.

Choose "Category" (or your relevant schema) from the list.

schema-reference
5
Add a Response Example:
Scroll to the Response Examples section.

Click Add Example.

image.png
6
Generate Example Data:
Name the example 'Success'.

Click Generate Automatically to populate the response data based on your defined structure.

Click OK to add the example.

image.png
7
Save Your Endpoint: Click Save to finalize your API specification.
image.png
Related
Importing Existing APIs
You can import an existing OAS file instead of creating a new endpoint from scratch.

For details, see Import data.

Using Schemas
For commonly used data structures, define them as Schemas. You can then reference these Schemas in your endpoint requests or responses.

Learn more about Schemas.

Next Step
Based on the already specified endpoint, you can now quickly Send an API request.

