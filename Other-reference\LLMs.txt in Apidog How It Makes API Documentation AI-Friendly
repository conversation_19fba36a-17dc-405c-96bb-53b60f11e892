# LLMs.txt in Apidog: How It Makes API Documentation AI-Friendly

In today's AI-driven development landscape, having documentation that LLMs can easily process is essential. Recognizing this need, Apidog now supports [LLMs.txt](http://apidog.com/blog/llms-txt/), transforming your shared/published API documentation into an AI-friendly format. This powerful feature enables AI tools to access clean, structured documentation content without the clutter of HTML and JavaScript that typically confuses AI systems.

Let's explore what this means for your development workflow and how you can leverage it immediately.

## What is LLMs.txt and Why Apidog Supports LLMs.txt

When AI assistants process online web content, they often struggle to filter out what matters. Most web pages today are bloated with styles, scripts, and interactive elements that are useful for humans, but distracting for machines. These redundant scripts waste tokens, reduce response speed, and increase costs in AI workflows.

[**LLMs.txt** is a new standard proposed in 2024](https://llmstxt.org/), designed to solve this. Similar to `sitemap.xml` for search engines, **llms.txt is a roadmap for LLMs**. It includes links to every Markdown (`.md`) version of your documentation pages, allowing AI to skip unnecessary content and focus on what really matters.

### Why Apidog’s Support for LLMs.txt Matters

Apidog goes a step further by automatically:

- Creating a clean `.md` version of every documentation page
- Generating a comprehensive `llms.txt` file with links to all Markdown files
- Including supporting files like `sitemap.xml` and `robots.txt` for complete web optimization

This means your documentation is instantly ready for AI consumption—with **no extra configuration required**.

## Key Features of Apidog’s LLMs.txt for AI-Friendly API Documentation

![Key Features of Apidog’s LLMs.txt](https://assets.apidog.com/blog-next/2025/04/apidog-llms-txt-features-1.png)

Apidog's support for LLMs.txt represents a fundamental improvement in how your API documentation interacts with AI systems:

### Seamless Markdown Access

Every shared/published documentation page now has a dedicated `.md` version:

- Just **append `.md` to the URL** for instant access.
- Alternatively, Use the intuitive **`Copy Page`** or **`View as Markdown`** buttons on the page.

### Site-Level LLMs.txt Generation

The API Documentation published by Apidog will now include a `/llms.txt` file baked.

- Accessible via `/llms.txt` at the root of your documentation site.
- Get a structured list of all available `.md` URLs
- Includes concise summaries to help LLMs prioritize key content.

### No Setup Required

- The LLMs.txt feature is **enabled by default** on published/shared docs.
- Works out of the box with no coding or settings.

### Aligned with SEO and AI Standards

| Feature          | LLMs.txt    | Sitemap.xml | Robots.txt  |
| ---------------- | ----------- | ----------- | ----------- |
| AI Readability   | ✅ Yes       | ❌ No        | ❌ No        |
| SEO Optimization | ✅ Yes       | ✅ Yes       | ✅ Yes       |
| Auto-Generated   | ✅ By Apidog | ✅ By Apidog | ✅ By Apidog |

This stack makes your documentation site fully optimized for both AI tools and traditional search engines.

---

## How to Use Apidog’s LLMs.txt Feature

Whether you're working with ChatGPT, Claude, Cursor, or any custom LLM interface, integrating your Apidog documentation into an AI workflow is now easier than ever.

### Method 1: Directly Share Markdown URLs

For AIs that support web browsing:

- **Add `.md`** to any Apidog doc page.  
  Example: `https://apidogsite.com/api-doc.md`
- Or use the `View as Markdown` button to get the documentation link.
- Then simply prompt the AI:

> “Read the API spec at https://apidogsite.com/api-doc.md and generate a TypeScript SDK.”

### Method 2: Copy and Paste Markdown into AI

For AI tools without web browsing ability:

- Use the **`Copy Page`** button on Apidog’s published doc page.
- Paste the content directly into your AI tool or chat.
- Write prompts to instruct the AI.  
  Prompt example:

> “Based on the following API spec, write the request and response handling in Python.”  
> *[Paste Markdown content here]*

💡

**PRO TIP:** Want to supercharge your AI integration? Pair LLMs.txt with the **[Apidog MCP Server](https://docs.apidog.com/apidog-mcp-server)**!  

While LLMs.txt makes your API documentation easier for AI tools to read, Apidog MCP Server goes a step further. It gives AI assistants direct access to your API specifications—so they can understand your API’s full structure, generate accurate code, and assist more intelligently.  

No need to copy and paste documentation manually. It’s a seamless way to build smarter, AI-powered development workflows.

[Sign Up for Free](https://app.apidog.com/)

Privacy protected

[Download Now](https://assets.apidog.com/download/Apidog-windows-latest.zip)[For Mac or Linux](https://apidog.com/download/)

Security guaranteed with no ads

## FAQs About Apidog’s Support for LLMs.txt

### Q1: How to disable the LLMs.txt feature?

If you want to disable this feature, you can enter the project, click `Share Docs` -> `Publish Docs Sites` -> `LLM-friendly Features` to perform the relevant operations.

### Q2: Will enabling LLMs.txt affect the security of the documentation?

**No, LLMs.txt does not expose any private or unpublished information.**  
It only includes content from API documentation that has already been publicly published. The LLMs.txt feature simply provides a Markdown version of the existing HTML pages — there’s no change to what is visible.

If your published documentation is protected (e.g., by password, IP allowlist, or email allowlist), those same authentication requirements still apply when someone tries to access the `.md` version or the LLMs.txt file.

### Q3: If my online documentation is protected with a password, IP allowlist, or email allowlist, can I still use the LLMs.txt feature?

**Yes, you can still use it.**

However, because the LLMs.txt file and Markdown (`.md`) versions of your docs follow the same access controls, authentication is still required. This means that AI assistants won’t be able to fetch those `.md` files directly via URL unless they have access.

To work around this, you (or users with access) can use the "**Copy page**" feature to easily copy the Markdown content and then paste it into the AI tool of your choice.

### Q4: Why don’t I see the "Copy page" and other buttons in the Apidog app?

The **`Copy page`** button and related features are only available in **shared or published online documentation**.

To access these features, you’ll need to **publish your documentation** first and then **view it in a web browser**—not within the Apidog desktop app.

### Q5: I’ve enabled the AI assistant’s "Search" feature — why can’t it read webpage content directly from URLs?

The "Search" feature is not the same as "Web Browsing".

- **Search** allows the AI to use search engines to look up topics based on your input, and then summarize the results it finds.
- **Web Browsing**, on the other hand, lets the AI directly access and read content from a specific URL.

### Q6: What should I do if the AI can’t access Markdown files through URLs?

If the AI fails to read the `.md` file directly from the URL, you can simply use the "**Copy page**" button on the online documentation. This lets you copy the full Markdown content manually and paste it into the AI assistant for further use.

### Q7: After enabling LLMs.txt, is there anything else I need to do?

No additional work is required. Once you enable the LLMs.txt feature, the system will **automatically generate the LLMs.txt file and a corresponding Markdown (.md) version for each documentation page**. You only need to continue updating and maintaining your original documentation as usual.

### Q8: How can I check if LLMs.txt is working correctly?

You can verify this by visiting the `/llms.txt` path at the root of your published documentation site. If the page displays a structured list of your documentation links, it means the feature is successfully enabled and working as expected.

### Q9: How does LLMs.txt handle complex or deeply nested schema references?

Apidog uses intelligent recursive descent parsing to handle this. It automatically expands multi-level nested references in your schemas, while keeping the documentation well-structured and easy to read for AI. This ensures that even complex schemas are clearly presented and accessible for both humans and AI tools.

## Conclusion: Transforming API Documentation for the AI Era

Apidog's implementation of **LLMs.txt** represents a significant advancement in making API documentation truly AI-ready. By automatically providing clean Markdown exports and comprehensive indexing, Apidog bridges the gap between human-readable documentation and machine-optimized content.

This isn't just a technical improvement—it's a transformation in how development teams can leverage AI throughout their workflow:

- **Accelerate development** by enabling AI to generate accurate code based on your API specs
- **Reduce misunderstandings** by ensuring AI tools correctly interpret your API structure
- **Lower token costs** by eliminating unnecessary HTML and JavaScript noise
- **Improve collaboration** by making documentation sharing with AI seamless

With Apidog's LLMs.txt support, your API documentation becomes a powerful asset for both human developers and AI assistants—working together to build better software faster.

Start using these features today: share Markdown links with AI tools, copy documentation content directly into AI conversations, and experience a new level of efficiency in your development process
