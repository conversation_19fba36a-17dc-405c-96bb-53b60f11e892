# How to Create an MCP Server with the Java SDK

Hey there, Java enthusiasts! Would you like to supercharge your AI projects with a custom **Java MCP Server**? The Model Context Protocol (MCP) lets AI models like <PERSON> or <PERSON><PERSON><PERSON> tap into your data with ease, and with the Java SDK, you can build a lightweight server to make it happen. In this tutorial, we’ll create a **Java MCP Server** to share presentation data with AI assistants. We’ll keep it fun, conversational, and beginner friendly, walking you through setup, testing, and extending your server. Let’s dive in!

## What Is a Java MCP Server?

A **[Java MCP Server](https://modelcontextprotocol.io/sdk/java/mcp-overview)** is a sleek Java application that uses the Model Context Protocol (MCP) to expose data to AI models. It’s like giving your AI a VIP pass to your app’s info—ours will share game data, but you can customize it for anything. The server uses the MCP Java SDK to create tools that AI assistants can call, like fetching a list of games or searching for one by title. It’s perfect for integrating your data with AI clients, and it’s super easy to set up with Spring Boot and <PERSON>ven.

![client server mcp connection](https://assets.apidog.com/blog-next/2025/07/java-mcp-client-architecture.jpg)

## Step-by-Step Guide to Create a Java MCP Server

### Prerequisites

Before we get started, ensure you have:

- **Java 24**: Download from [oracle.com](https://www.oracle.com/java/technologies/downloads/).
- **Maven 3.8+**: Install from [maven.apache.org](https://maven.apache.org/download.cgi).
- **IntelliJ IDEA**: Get it from [jetbrains.com](https://www.jetbrains.com/idea/) (or your preferred IDE).
- **Node.js**: For testing with MCP Inspector ([nodejs.org](https://nodejs.org/)).
- **Claude Desktop or Cursor**: Optional, for AI integration ([anthropic.com](https://www.anthropic.com/) or [cursor.sh](https://cursor.sh/)).

### Step 1: Create or Clone the Project

You can either clone an existing repo or create a project from scratch.

**Option 1: Clone the Repository (Recommended)**

1. In a new terminal, run:

```bash
git clone https://github.com/danvega/javaone-mcp.git
cd javaone-mcp
```

2. **Build the Project**:

- Use Maven to create an executable JAR:

```bash
mvn clean package
```

- Find the JAR in `target/javaone-mcp-0.0.2.jar`.

![clone github repo](https://assets.apidog.com/blog-next/2025/07/image-541.png)

**Option 2: Create from Scratch**

1. Open IntelliJ IDEA:

- Select **New Project**.
- Choose **Maven** as the build system.
- Set the project name (e.g., `java-mcp-server`) and click **Create**.

![create a new project](https://assets.apidog.com/blog-next/2025/07/image-540.png)

2. Add Dependencies:

- Open `pom.xml` and add:

```xml
<dependencies>
  <!-- MCP SDK -->
  <dependency>
    <groupId>io.modelcontextprotocol.sdk</groupId>
    <artifactId>mcp</artifactId>
    <version>0.9.0</version>
  </dependency>
  <!-- Logging -->
  <dependency>
    <groupId>org.slf4j</groupId>
    <artifactId>slf4j-api</artifactId>
    <version>2.0.16</version>
  </dependency>
  <dependency>
    <groupId>org.slf4j</groupId>
    <artifactId>slf4j-simple</artifactId>
    <version>2.0.16</version>
  </dependency>
</dependencies>
```

### Step 2: Understanding the Project Structure

The **Java MCP Server** we just cloned includes:

- **Application.java**: The main entry point that starts the MCP server.
- **Presentation.java**: A data model for game data (we’ll customize it for games).
- **PresentationTools.java**: Defines MCP tools for accessing game data.
- **Configuration**: Uses STDIO transport for AI communication.

![project structure](https://assets.apidog.com/blog-next/2025/07/image-542.png)

### Step 3: Define the Data Model

Create `Presentation.java` to represent game data:

```java
public record Presentation(String title, String url) { }
```

This record holds game details like title (e.g., “GTA 6”) and URL.

### Step 4: Implement MCP Tools

In `PresentationTools.java`, define tools using the MCP SDK:

```java
import io.modelcontextprotocol.sdk.McpSchema;
import java.util.ArrayList;
import java.util.List;

public class PresentationTools {
    private final List<Presentation> presentations = new ArrayList<>();

    public PresentationTools() {
        presentations.add(new Presentation("GTA 6", "https://example.com/gta6"));
        presentations.add(new Presentation("FC 26", "https://example.com/fc26"));
        presentations.add(new Presentation("Call of Duty: Black Ops 7", "https://example.com/codbo7"));
    }

    @McpSchema.Tool(name = "get_presentations", description = "Returns a list of all available games")
    public List<Presentation> getPresentations() {
        return presentations;
    }

    @McpSchema.Tool(name = "get_game", description = "Returns a specific game by title")
    public Presentation getGame(String title) {
        return presentations.stream()
            .filter(p -> p.title().equalsIgnoreCase(title))
            .findFirst()
            .orElse(null);
    }
}
```

These tools let AI models fetch all games or a specific game by title.

### Step 5: Configure and Start the MCP Server

In `Application.java`, set up the **Java MCP Server**:

```java
import io.modelcontextprotocol.sdk.McpServer;
import io.modelcontextprotocol.sdk.McpSchema;
import io.modelcontextprotocol.sdk.McpSyncServer;

public class Application {
    public static void main(String[] args) {
        PresentationTools tools = new PresentationTools();
        var syncToolSpecification = new McpServerFeatures.SyncToolSpecification(
            new McpSchema.Tool("get_presentations", "Returns a list of all available games", null),
            (exchange, arguments) -> tools.getPresentations()
        );

        McpSyncServer syncServer = McpServer.sync(new StdioTransportProvider())
            .serverInfo("javaone-mcp-server", "0.0.1")
            .capabilities(McpSchema.ServerCapabilities.builder()
                .tools(true)
                .logging()
                .build())
            .tools(syncToolSpecification)
            .build();

        syncServer.start();
    }
}
```

This configures the server to use STDIO transport and registers the `get_presentations` tool.

### Step 6: Run the Java MCP Server

1. **Build the Project**:

- Run:

```bash
mvn clean package
```

![run the server](https://assets.apidog.com/blog-next/2025/07/image-543.png)

2. **Start the Server**:

- Execute the JAR:

```bash
java -jar target/javaone-mcp-0.0.2.jar
```

- The server runs with STDIO transport, ready for AI integration.

### Step 7: Test with MCP Inspector

Use the MCP Inspector to debug your **Java MCP Server**:

**Install Node.js**: Download from [nodejs.org](https://nodejs.org/) if needed.

**Get the JAR Path**:

- Linux/macOS:

```bash
FULL_PATH=$(pwd)/target/javaone-mcp-0.0.2.jar
echo $FULL_PATH
```

- Windows PowerShell:

```powershell
$FULL_PATH="$(Get-Location)\target\javaone-mcp-0.0.2.jar"
echo $FULL_PATH
```

**Run MCP Inspector**:

- Use:

```bash
npx @modelcontextprotocol/inspector java -jar $FULL_PATH
```

- In the Inspector:
- Check the connection in the **Connection** pane.
- Go to the **Tools** tab, select `get_presentations`, and view the response (e.g., “GTA 6, FC 26, Call of Duty: Black Ops 7”).
- Monitor logs in the **Notifications** pane.

### Step 8: Integrate with Claude Desktop or Cursor

Connect your **Java MCP Server** to an AI client:

**Get the JAR Path**: Use the same command as above.

a. **Configure Claude Desktop**:

- Open `claude_desktop_config.json` (macOS: `~/Library/Application Support/Claude`, Windows: `%APPDATA%\Claude`).
- Add:

```json
{
  "mcpServers": {
    "javaone-mcp": {
      "command": "java",
      "args": ["-jar", "FULL_PATH"]
    }
  }
}
```

- Replace `FULL_PATH` with your JAR path.

b. **Configure Cursor**:

- Go to **Settings > Cursor Settings > Tools and Integrations > New MCP Server**.
- Add the same config and save.

**Restart the Client**: Close and reopen Claude or Cursor.

### Step 9: Test the Java MCP Server

Test with Claude Desktop or Cursor:

**Start the Server**:

- Run:

```bash
java -jar target/javaone-mcp-0.0.2.jar
```

**Issue a Command**:

- In Claude or Cursor, enter:

> Using the MCP server, get all available games.

- Expected response:

```
The available games are GTA 6, FC 26, and Call of Duty: Black Ops 7.
```

![available games on my mcp server](https://assets.apidog.com/blog-next/2025/07/Screenshot-2025-07-30-120037-1.png)

### Step 10: Extend the Java MCP Server

Make your server even cooler:

1. **Add More Presentations**:

- In `PresentationTools.java`, add to the constructor:

```java
presentations.add(new Presentation("Spring Boot 3.5 Deep Dive", "https://example.com/spring-boot", 2025));
```

2. **Create a Search Tool**:

- Add to `PresentationTools.java`:

```java
@McpSchema.Tool(name = "search_presentations", description = "Search presentations by title")
public List<Presentation> searchPresentations(String query) {
    return presentations.stream()
        .filter(p -> p.title().toLowerCase().contains(query.toLowerCase()))
        .collect(Collectors.toList());
}
```

- Register in `Application.java`:

```java
var searchToolSpec = new McpServerFeatures.SyncToolSpecification(
    new McpSchema.Tool("search_presentations", "Search presentations by title", null),
    (exchange, arguments) -> {
        String query = arguments.get("query").asText();
        return tools.searchPresentations(query);
    }
);
McpSyncServer syncServer = McpServer.sync(new StdioTransportProvider())
    .serverInfo("javaone-mcp-server", "0.0.1")
    .capabilities(McpSchema.ServerCapabilities.builder().tools(true).logging().build())
    .tools(syncToolSpecification, searchToolSpec)
    .build();
```

I customised my mcp server to work with game data. And here are my results:

![new tool added to java mcp server](https://assets.apidog.com/blog-next/2025/07/Screenshot-2025-08-01-020441.png)

**Try a Search**:

- I added a `search_games` tool (see below), and tried the following prompt:

> Search for a game titled "FC".

- Expected response:

```
You have a game titled FC 26.
```

![new tool results](https://assets.apidog.com/blog-next/2025/07/Screenshot-2025-08-01-014053.png)

3. **Advanced Features**:

- Add resource subscriptions for real-time updates.
- Implement asynchronous tools for complex tasks.
- Add authentication for secure access.

### Troubleshooting Tips

- **Server Not Starting?** Verify Java 24 and Maven are installed, and check `pom.xml` for correct dependencies.
- **MCP Inspector Issues?** Ensure Node.js is installed and the JAR path is correct.
- **Client Not Connecting?** Confirm the JAR path in the client config and restart the client.
- **No Data Returned?** Check `PresentationTools` for correct tool annotations and data initialization.

## Why Use the Java MCP Server?

The **Java MCP Server** is a fantastic way to connect your Java app to AI models, letting them access your data in real-time. Our game-themed server showed how easy it is to share data like “GTA 6” or “FC 26” with Claude or Cursor. The MCP Java SDK makes it simple to define tools, and STDIO transport keeps things lightweight. Whether you’re building for games, courses, or something else, this server is your ticket to AI integration.

## Conclusion

You’re now a **Java MCP Server** pro! You’ve built a server from scratch, tested it with game data, and learned how to extend it with new tools. The MCP Java SDK makes it a breeze to connect your app to AI assistants, and our test showed it delivers. Try adding more data or tools to make your server even more powerful.
