Notification Settings
Notification Overview
Apidog enables you to integrate with third-party applications to send notifications to designated recipients when certain events occur. When a specific event is triggered, notifications are sent in real-time to platforms like Slack. Supported notification channels currently include:

Slack

Webhook

Jenkins

Email

Only project admins can configure the notification settings. Currently, email notifications are only available for the following events:

Automated test completed/failed

Continuous integration completed/failed

Scheduled tasks completed/failed

Notification Targets
Notification targets are recipients who receive messages through a specific channel when certain events happen.

To set up notifications, firstly you need to create a notification target by specifying a name, selecting a channel, and configuring its settings. Each channel has a unique setup process. The sections below will walk you through configuring them step by step.

notification-settings.png
Slack
Apidog supports sending notification events to a specific Channel in Slack by integrating Slack apps - Incoming WebHooks in the specified Channel and configuring the Webhook URL of Incoming WebHooks, which can send event messages to the Slack Channel.

Configuration field description:

Configuration Field	Required	Description
Notification Name	No	Give a name to the third-party integrated notification to record its purpose.
Trigger Events	Yes	Supported events: API changes, schema changes, Document changes, Import data, Automation testing
Service URL	Yes	Webhook URL in 「Incoming WebHooks - Integration Settings」
Integrating Apidog Notification with Slack
Project admins can use third-party integration functions to associate Slack apps - Incoming WebHooks added in Slack - Channels with project notification events in Apidog to push related API changes, documentation changes, testing completions, etc. to the specified Channel in Slack.

1.
In the Slack channel, click the "Open channel details" option in the top right corner.

CleanShot 2024-12-03 at <EMAIL>
In the Integrations - Apps of the specific Channel, install and add Incoming WebHook.


2.
After installation is complete, click the button to the right of Incoming WebHooks - View.


3.
This will open the description page of Incoming WebHooks, click Configuration to set up.


4.
Click Add to Slack.


5.
Select the Channel to push Apidog notification event messages to, and click Add Incoming WebHooks integration.


6.
Get and copy the Webhook URL.


7.
Creating a new notification target

Click "Settings" > "Notifications" > "Notification Targets" > and create a new notification Target.

Fill in the notification target name.

Choose Slack as the notification channel.

Paste the Webhook URL obtained from the Incoming WebHooks settings in the Service URL.

After clickingSave, the set-up is complete. You now can move on to create a notification event.

Once the notification event is set up and triggered, you will receive the message in your designated Slack channel.

Webhook
Supports sending notification events to the HTTP Server. You can send event messages to the HTTP Server by specifying a URL address to receive POST requests.

Configuration field description:

Configuration Field	Required	Description
Notification Name	No	Give a name to the third-party integrated notification to record the purpose of the notification.
Trigger Event	Yes	Supported eventsAPI changesData model changesDocument changesImport dataAutomation testing
Server URL	Yes	URL address of the HTTP Server for receiving requests
Signature Auth	No	The sent content is encrypted by the HMAC SHA1 encryption algorithm, using a token as the KEY to display the value in hexadecimal (requires a configured token), and contains the prefix sha1=
Integrating Apidog Notification with Self-hosted HTTP Server
Project admins can use third-party integration to associate their self-hosted HTTP Server with project notification events in Apidog, to receive notifications for relevant API changes, document changes, and automation test completions in their HTTP Server.

To integrate Apidog notification with self-hosted HTTP server, click "Settings" > "Notifications" > "Notification Targets" > and create a new notification Target.

1.
Fill in the notification target name.

2.
Choose Webhook as the channel.

3.
Paste the URL of the self-hosted HTTP Server in the Service URL field.

4.
If the signature verification is enabled, copy and paste the key in the Signature Key field.

After clickingSave, the set-up is complete. You now can move on to create a notification event.

Jenkins
It supports sending notification events to Jenkins service. By configuring Jenkins Webhook URL, event messages can be sent to Jenkins.

Configuration field descriptions:

Configuration Field	Required	Description
Notification Name	No	Give a name to the third-party integration notification to record its purpose.
Trigger Events	Yes	API changesData model changesImport dataAutomation testingReal-time notifications will be triggered when any of the above events occur.
Service URL	Yes	The URL is configured in the Generic Webhook Trigger plugin.
Signature Auth	No	Sent to the Generic Webhook Trigger plugin via the Authorization Bearer header.
Integrating Apidog Notification with Jenkins Service
Project admins can use third-party integration functionality to associate the Webhook URL configured in the Jenkins Generic Webhook Trigger plugin with project notification events in Apidog, to trigger a build action in Jenkins automatically when events such as API changes, document changes, and automation test completions are triggered, and view messages in the build history.

1.
Webhook URL configured in Jenkins Generic Webhook Trigger plugin

Create a new view on the Jenkins Dashboard:

Click on the view in the previous step to enter Configure>Build Triggers, and select Generic Webhook Trigger. Webhook URL is "http://"+"your service address"+"/generic-webhook-trigger/invoke"

The custom token is supported:

2.
Create a new notification target

Click "Settings" > "Notifications" > "Notification Targets" > and create a new notification Target.

Fill in the notification target name.

Choose Jenkins as the channel

Paste the Webhook URL configured in the Generic Webhook Trigger plugin in the Service URL field

If a custom token is used, copy and paste the Token into the Signature Tokenfield.

After clickingSave, the set-up is complete. You now can move on to create a notification event.

Once the notification event is set up and triggered, Jenkins will automatically initiate a build action and you can view the message in the build history:

Email
Notifications can be sent via email to specified email addresses. Currently, email notifications are only available for the following events:

Automated test completed/failed

Continuous integration completed/failed

Scheduled tasks completed/failed

Configuration field description:

Field	Required	Details
Name	Yes	A descriptive name that highlights the purpose or characteristics of the notification target.
Notification Email Address	Yes	Enter the email address(es) to receive notifications. You can either select email addresses of project members or manually type in an address. Multiple email addresses are supported.
Integrating Apidog Notification with Email
1.
Fill in the notification target name.

2.
Choose Email as the channel

3.
Enter emails

After clickingSave, the set-up is complete. You now can move on to create a notification event.

Notification Events
You can create notification events to specify which events will trigger notifications to designated recipients (notification targets). To set up a notification event, you need to configure:

Notification Event Name

Trigger Event

Notification Targets

Trigger Event
You can choose form the following notification events to trigger the notification:

Trigger Event	Details
Endpoint Changed	Endpoint Created
Endpoint Updated
Endpoint Deleted
Schema Changed	Schema Created
Schema Updated
Schema Deleted
Document Changed	Document Created
Document Updated
Document Deleted
Data Import	Import (Manual lmport)
Import (Auto Sync)
Response Component Changed	Response Component Created
Response Component Updated
Response Component Deleted
Sprint Branch Changed	Sprint Branch Merged
Sprint Branch Created
Sprint Branch Archived
Sprint Branch Deleted
Sprint Branch Retrieved
APl Version Changed	API Version Created
APl Version Deleted
Security Scheme Changed	Security Scheme Created
Security Scheme Updated
Security Scheme Deleted
For notifications related to automated tests (e.g., automated test completed, continuous integration completed, scheduled task completed), you need to configure the settings individually for each test scenario. This approach is more flexible and better suits real-world use cases.

Notification Targets
Choose which notification targets will receive the notification when a trigger event happens. You can choose from the notification targets that have already been set up in the project and select multiple targets if needed.

However, email notifications are only supported for three specific events:

Automated test completed/failed

Continuous integration completed/failed

Scheduled tasks completed/failed

