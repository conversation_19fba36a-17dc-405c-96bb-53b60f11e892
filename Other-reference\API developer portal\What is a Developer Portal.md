# What is a Developer Portal?

A developer portal - often shortened to devportal - is the interface between a set of APIs, SDKs, or other interactive digital tools and their various stakeholders. The portal can play several roles to achieve the business goals of an organization.

A lot of API teams publish their “Swagger/Open API Spec” (or RAML, API Blueprint, I/O Docs, WSDL, etc) documentation and call it a developer portal.

However, **reference documentation is only one part of the [minimum viable developer portal](https://pronovix.com/blog/what-mvp-developer-portal)**.

Yes, your developer portal needs to contain API or SDK reference documentation (no matter what specification format you use) but a developer portal should also be

- a sort of **self-service support hub**,
- a **trust signal**,
- a **communication nexus for stakeholders** and
- a **key DevRel tool**

that helps an organization to provide the best possible developer experience for its APIs.

We’ve been working for a long time to capture what is the essence of a developer portal – and this infographic is our take. Does this match your view? We would welcome your feedback.

![Infographic: What Is a Developer Portal? A trust signal for your API. A self-service hub. A dashboard for your API products. A list of endpoints is not enough.](https://pronovix.com/sites/default/files/inline-images/whatisadeveloperportal_infographic_pronovix_blogpostcontent_2048px.png)
