In Apidog, a useful built-in field is the `Endpoint Status`. This field is designed to indicate the status of an endpoint, whether it is in development, testing, or already released. 

The Endpoint Status field is visible both in the Apidog app for internal team reference and in the API documentation generated by Apidog for external users.

## Utilize endpoint status

When specifying an endpoint in Apidog, you have the option to select the status of the endpoint. By default, a newly created endpoint is set to `Developing`.

<p style="text-align:center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341148/image-preview" style="width:340px" />
</p>

In the endpoint specification, each status name is prefixed with a colored dot to signify the different statuses of endpoints. This color-coded system allows users to quickly distinguish between endpoints and their respective statuses in the Endpoint directory tree.

<p style="text-align:center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341149/image-preview" style="width:240px" />
</p>

In the color scheme used, blue indicates `developing`, red represents `testing`, and gray signifies `deprecated`. Endpoints without a colored dot are considered `released`, indicating that they do not require special attention.

In the published documentation, you will also see these colored dots used to mark the status of endpoints.

<p style="text-align:center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341150/image-preview" style="width:640px" />
</p>

## Configure endpoint status

Every team may have a different development process, and the required endpoint statuses can vary accordingly. Apidog allows teams to customize and choose which endpoint statuses to use based on their specific requirements. 

You can configure and enable the desired endpoint statuses in the `Settings` > `General Settings` > `Feature Settings` > `Endpoint Feature Settings` section.

<p style="text-align:center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341151/image-preview" style="width:640px" />
</p>

## FAQ

**Q: Can I customize a new status?**

A: As different statuses correspond to specific functionalities, customizing a new status is not supported. You can choose from the listed statuses that best suit your team's needs.