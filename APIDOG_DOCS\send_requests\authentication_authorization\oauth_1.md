OAuth 1.0
OAuth 1.0 is an authorization mechanism based on the OAuth protocol, which allows third-party applications to access protected resources. For example, as a user of Product A, you can directly log in to the system of Platform B using your account on Product A, without exposing your username and password on Platform A.

OAuth 1.0 also uses a signature (Signature) to verify the integrity and authenticity of the request, as well as a timestamp (Timestamp) and a nonce (Nonce) to prevent replay attacks. Additionally, in OAuth 1.0, tokens and keys are only known to authorized applications and service providers.

Basic Setup
On the "Auth" page, select "OAuth 1.0" as the authorization method, and fill in the following information:

Add Location

Supports Request Body/Request URL and Request Header, you can choose the request body or request header.

Signature Method

The signature method, supporting HMAC-SHA1, HMAC-SHA256, HMAC-SHA512, RSA-SHA1, RSA-SHA256, RSA-SHA512, PLAINTEXT algorithms.

Consumer Key

A unique identifier assigned by the service provider to the application.

Consumer Secret

A secret key assigned by the platform to the application.

Access Token

The access token is a token issued by the service provider after the user authorizes the application, which is used to access the user's protected resources. It allows the application to perform limited operations on behalf of the user without requiring the user to provide their username and password.

Token Secret

The token secret is a secret string associated with the access token. Similar to the consumer secret key, it is used to create the signature during the OAuth 1.0 handshake to ensure the integrity and security of the request.



Advanced Settings
You can click the "Advanced" option to add more encryption settings. If left blank, they will be automatically generated.



Callback URL

The callback URL is the last step in the OAuth 1.0 authorization flow, which is the URL the user is redirected to by the service provider after authorizing the third-party application.

Verifier

The verifier is an intermediate step in the OAuth 1.0 authorization flow, which is a random string generated by the service provider to verify whether the user has authorized the third-party application to access their resources.

Timestamp

The timestamp is a parameter in the OAuth 1.0 request, which is used to prevent replay attacks. It is a Unix timestamp representing the time the request was initiated.

Nonce

The nonce is a parameter in the OAuth 1.0 request, which is used to prevent replay attacks. It is a random string used to uniquely identify a request.

Version

By default, no change is needed.

Realm

Used to identify the security domain or scope to which the protected resource belongs.

In addition to the above options, you can also choose whether to enable the Include body hash and Add empty parameters to signature options.

