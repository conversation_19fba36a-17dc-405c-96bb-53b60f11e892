Model Context Protocol (MCP) supports managing and passing relevant information to models with appropriate context, so they operate properly within a given scope. Using this technology, PayPal developed PayPal MCP Server to enable merchants to use natural language with their favorite MCP client to perform business tasks, such as creating or listing invoices. 

Examples in this content use Claude as the MCP client, but you can use any MCP client that you prefer, such as Cursor or Cline. 

Set up PayPal MCP Server
PayPal provides two ways for merchants to set up PayPal MCP Server: 

Installing and running PayPal MCP Server locally. This option enables developers to download, install, and run PayPal MCP Server locally. 
Using PayPal MCP Server remotely. This option is for users who prefer not to install PayPal MCP Server locally. With remote PayPal MCP Server support, users can continue their tasks across devices with a single login after authentication.
Run PayPal MCP Server locally
A preferred way to use PayPal MCP Server in some environments is to run it locally. To do that, you have to install Node.js locally first.

To install PayPal MCP Server in a local configuration: 

Visit the Node.js official website to download and install it. Be sure that you are running Node.js v18 or later. 
Update the configuration file in your favorite MCP client:   
Open the MCP client. 
In the configuration settings for the client, locate the external tools or connectors configuration section, and add the PayPal connector configuration that follows this procedure. In <PERSON>, for example, you add this to ~/Claude/claude_desktop_config.json
In the new entry, replace YOUR_PAYPAL_ACCESS_TOKEN with your actual PayPal access token. Alternatively, you can set the PAYPAL_ACCESS_TOKEN as an environment variable. You also can pass it as an argument using --access-token in args.

Set the PAYPAL_ENVIRONMENT to SANDBOX for testing or PRODUCTION for your production environment.

Test your integration:
Quit and restart the MCP client to apply your changes.
Ask the MCP client to perform one of the supported tasks. For example, ask the MCP client to list your PayPal invoices for the last month.
{
   "mcpServers": {
     "paypal": {
       "command": "npx",
       "args": [
         "-y",
         "@paypal/mcp",
         "--tools=all"
       ],
       "env": {
         "PAYPAL_ACCESS_TOKEN": "YOUR_PAYPAL_ACCESS_TOKEN",
         "PAYPAL_ENVIRONMENT": "SANDBOX"
       }
     }
   }
}
If your test effort doesn't produce the results you expect, try the ideas here.  

Connect to PayPal MCP Server remotely
Another way of connecting with PayPal MCP Server is to do so remotely. Use your preferred MCP client for this procedure. 

Open the configuration file for your MCP client in a text editor, and replace the configuration with the new configuration that follows this procedure. 
Save the file, and restart your MCP client. The MCP client will send you to the PayPal login page. 
Provide your consent for the client to work with PayPal MCP Server: 
Log into PayPal when the login page appears.
Authorize the client to work with PayPal MCP Server. 
Quit and reopen your MCP client.
To test your integration, ask the MCP client to preform one of the supported tasks. For example, ask it to create an invoice for landscaping services for Green Lawns for $200 with a date of last Friday.
{
  "mcpServers": {
    "paypal-mcp-server": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "https://mcp.paypal.com/sse"
      ]
    }
  }
}
If you have trouble connecting after completing this procedure, clear the files that the integration adds to ~/.mcp-auth:

rm -rf ~/.mcp-auth
Also be aware that emerging technology, like MCP and MCP clients, can come with performance issues or other challenges initially. For example, Windows users with Cursor might encounter a known issue when connecting to PayPal MCP Server remotely. 

PayPal MCP Server tools overview
The tools in PayPal MCP Server currently include tools for performing two critical invoice jobs: 

Creating invoices
Listing invoices
Plans for this feature include adding more tools over time. 

Create Invoice
Using your favorite MCP client with PayPal MCP Server to create invoices offers several advantages over creating invoices in a more traditional way. For example, a merchant can use natural language with an MCP client tool to process multiple invoice requests by using a drive-system connector or a file-system connector. With this connection, users can ask the MCP client to perform a PayPal-related task, the MCP clients can access their records for the data, and then it can use that data to send PayPal invoices to multiple customers in bulk.

For example, a user asks the MCP client to create an invoice using PayPal. 

image

The user supplies the necessary information, as the MCP client indicates. Then the MCP client accesses the necessary data, creates the invoice using PayPal MCP Server, and sends it, as shown in the following illustration. 

image

The following example shows a sample of the invoice the customer receives. 

image

List Invoices
This tool lists the merchant or service provider's PayPal invoices. Additionally, the merchant or service provider can gain valuable data, such as identifying unpaid customer invoices or tracking invoice-related trends for a specific time period.

image