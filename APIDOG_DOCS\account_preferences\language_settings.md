Apidog offers nuanced language settings across three different aspects to accommodate global team dynamics. This guide elaborates on each aspect and its application.

## Software Language

The **Software Language** determines the language displayed in the Apidog software interface. You can set this via: `Settings > General > Software Language`. This setting is personal and impacts only your local environment without affecting other team members or the Online Documentation.

![](https://assets.apidog.com/uploads/help/2023/07/12/ae4a9dfff29415d6f171e577ffde3a83.png)

:::tip[]
Switching the software language to English may still display some elements in English by default. This introduces the concept of **Project Language** which affects other functionality.
:::

## Project Language

The **Project Language** influences auto-generated content within the project, such as default names for responses, examples, API test cases, markdown documents, and dataset names in test data.

Admin-only adjustable, this setting is found under `Settings > Basic Settings > Project Language`. It operates prominently at the project level; once set, all team members utilize the same project language for consistency, including in Online Documentation.

![](https://assets.apidog.com/uploads/help/2023/07/12/0ef4d59863c1334f9fd108e4fef1ef0a.png)

:::tip[]
Switching the project language does not auto-translate manually entered data in API documents; manual intervention is required for translation.
:::

### Specifying Project Language in New Projects

When initiating a new project, if the software and project languages are previously determined, you can set the project language accordingly.

![](https://assets.apidog.com/uploads/help/2023/07/12/d6fbbce81e3cbb11d9538f86f04b741d.png)

Choosing to **Include Sample Data** will generate the data entirely in English as per the set software language, exemplified below.

![](https://assets.apidog.com/uploads/help/2023/07/12/7e5f7e9c9efe70b3d5eafa584000a2c9.png)

## Documentation Language

The **Documentation Language** applies to the 'Online Documentation' of the tool and can be thought of as the software language for Online Documents. This setting is available through: `Share > Share Docs > New Share > Language`.

![](https://assets.apidog.com/uploads/help/2023/07/12/4b69c70d0c2e64eea4442ec3c467c3f0.png)

Here’s how Online Documentation appears with different language settings:

![](https://assets.apidog.com/uploads/help/2023/07/12/d225696423d2381fe9465a2dd1285190.png)