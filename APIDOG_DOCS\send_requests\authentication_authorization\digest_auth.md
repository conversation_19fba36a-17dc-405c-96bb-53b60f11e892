Digest Auth
Digest Authentication is an authentication mechanism for HTTP access control. It is similar to Basic Auth, but Digest Auth is more secure than the latter in terms of authentication method.

Basic setup
In Basic Auth, the client sends the username and password in plaintext to the server, and these credentials can be transmitted over the network and may be intercepted by a middleman. To solve this problem, Digest Authentication uses encryption algorithms to protect the transmission of user credentials.

Advanced settings
You can click the "Advanced" option to add more encryption settings. If left blank, they will be generated automatically.



Realm

The domain set in the response header by the target server, used to identify the resource currently being requested.

Nonce

A unique string specified in the response header by the target server.

Algorithm

The encryption algorithm. Supports MD5, SHA-256, SHA-256-sess, SHA-512-256, SHA-512-256-sess, etc.

qop

Quality of Protection is used to specify the quality level of digest authentication and the digest algorithm.

In the HTTP header, the value of qop can be one of the following:

auth: Indicates the use of authentication quality assurance.

auth-int: Indicates the use of authentication quality assurance and integrity protection.

Client Nonce

The Client Nonce is a random number generated by the client to enhance the security of digest authentication. It is included in the digest authentication request sent by the client, and a new random number is generated for each request.

Opaque

Opaque is a random string returned by the server to the client, used to enhance the security of authentication.

