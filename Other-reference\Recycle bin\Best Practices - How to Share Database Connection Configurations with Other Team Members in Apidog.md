# Best Practices | How to Share Database Connection Configurations with Other Team Members in Apidog

Apidog is a collaborative tool for API documentation + debugging + mock + testing. One of its highly praised features is the support for database operations when sending/receiving requests. This feature greatly facilitates users who need to prepare request data when calling APIs or insert data into databases when receiving API responses, making it beloved by users.

However, during the extensive daily use of this feature by a large number of users, some optimization points have been identified. The most frequently mentioned one is:

Can the database connection configuration be set up by the team administrator and then used collaboratively by others? It's really troublesome for everyone to rewrite the connection information.

When designing this feature initially, we considered whether to allow collaborative use of database configurations. However, allowing users to save sensitive information such as database usernames and passwords on cloud servers requires careful consideration. Therefore, **data security** was the core reason why we didn't implement the collaborative database connection configuration feature at that time.



**Cloud Storage of Database Connection Configurations**

Today, Apidog has become the primary API collaboration management tool for millions of developers, and Apidog's own foundational capabilities have also evolved to be more powerful. Therefore, we have reconsidered this optimization requirement for collaborative use of database connection configurations, striving to balance data security with efficient usage, hoping to satisfy users.

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHRrwUuhB2ek6GeMibpmVd9glwEFjYQRW5EiapWKwBtSM4bG9icn4KYickgw/640?wx_fmt=png&from=appmsg&randomid=50djwdh6&tp=webp&wxfrom=10005&wx_lazy=1)

When your Apidog version is updated to **2.6.50** or above, go to "Project Settings -> Database Connection" and click to create a new database connection. You will find that all connection fields guide you to use variable format to fill in the field values.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWySCdRvnSTHp4hOWOD2sBl9AIA4xqvArG1cibYOLxVAWictrmFkkoOibRIw/640?wx_fmt=png&randomid=hktyz3si&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHibEdw9kqjoE8AfibpXFqffXRamPhoL4o4PDWsaTb9n4IOxnltNOrXzzA/640?wx_fmt=png&from=appmsg&randomid=nrfz0ocm&tp=webp&wxfrom=10005&wx_lazy=1)

In environment management, set up variables for database connections that need to be used in different environments within the environment variables, so that these variables can be applied in database connection configurations.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyG6kiay3GHa0riczjbVKLrDWgXyyPwIduD0LuQ4P6UrEjn7v4SMRS2iaLg/640?wx_fmt=png&randomid=zpk868xc&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHkm6icBUaAzYXTFN8TyIwvX9NL6aTGS8wXFrHvqUF0xRW82WCVdffDkA/640?wx_fmt=png&from=appmsg&randomid=9hia0bsr&tp=webp&wxfrom=10005&wx_lazy=1)

Back to the database connection configuration page, you can manually fill in variables using variable format, or directly reference these variables through the "Dynamic Value" feature. We recommend using environment variables to fill in here, so that different configurations corresponding to different environments can be automatically switched according to different environment contexts. Except for the port number, all other fields should preferably use variable format.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyL2xv7zVsvr0zIFPXSOjOnY3qBiaWFF03wp5Uva9Y2n6q093GyJDAOTw/640?wx_fmt=png&randomid=5a7lyia8&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHo5KkDQzWkK5MVjVLr6lNeASPNAPibGx91kKlSG5FmvOWzseGyVmj7qA/640?wx_fmt=png&from=appmsg&randomid=pi57fyrp&tp=webp&wxfrom=10005&wx_lazy=1)

Save the database connection configuration that uses variables, and it can be used in database operations in API management, automated testing, and other places.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyp0x1wKia1EAdr29lY9cfnhndEAav2fZcrT6348EW1uvictBSSf0zNDRA/640?wx_fmt=png&randomid=6jn5lf50&tp=webp&wxfrom=10005&wx_lazy=1)

When using, the actual usage mechanism for database connection configurations saved locally and saved in the cloud is as follows:

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlyaV3YyV7vSlmoGicyomnJ4E9D4qiadupibkjtvFnuxFtklMeq6AohL3yI9KlKykxnmSXsodhLYLIaQ/640?wx_fmt=png&randomid=zcwgifkt&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHUwEgu04p2sXka3m1k20QfHcCavygwjKy3SsbIbcRVgQEHEb3pVkylQ/640?wx_fmt=png&from=appmsg&randomid=1x29x53k&tp=webp&wxfrom=10005&wx_lazy=1)

For other project members who need to use this database connection configuration, they now only need to go to environment management, find the corresponding variables and fill in local values, without having to configure in project management like before.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyUxznQTZBB6R1UdzYqCRqiaaE0wMpcG255Tz1H1Abtiaph2QiaFiaxUncfA/640?wx_fmt=png&randomid=l4f2lncf&tp=webp&wxfrom=10005&wx_lazy=1)

The above are the specific operation steps for using cloud database connections. Because we recommend using local values, the actual configuration is still saved locally, so there's no need to worry about data security risks. It's just using variable format to make it more convenient for everyone to use. Of course, Apidog still supports directly filling in actual values in database connection configurations to maintain compatibility with previous data and for those who still prefer to use local data. However, there will be strong reminders to inform users that they can convert to using variables to save to the cloud and improve user experience.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyLrbN3AulleN3R2vdTLc4GooUYvDzViaLKP6jUYlv6cC6ZHRJg2A8m9Q/640?wx_fmt=png&randomid=4uuq4s9z&tp=webp&wxfrom=10005&wx_lazy=1)

**Precautions for Using Cloud Database Connections**  

In database connection configurations, when variables are used, the actual content saved in the cloud is the variable name. When executing database connections, the complete connection configuration will be assembled according to variable usage rules to initiate the connection.

✅ **Recommended Behavior**

❌ **Not Recommended Behavior**

■  Use "Local Value" or "Vault Variables" for variable values

■  Port numbers can be filled in directly without using variables, convenient to use & risk-free

■  Use "Remote Value" for variable values

■  Mixing plain text with variables, causing everyone to still need to configure separately in project settings

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHmralwjNCeaB58haZdW1mG216PYSy9icwvWicVqvtjqRVNMmF4EkqZTowudNl726Fk9O8mWiaTicGYscA/640?wx_fmt=png&from=appmsg&randomid=yqe1kywm&tp=webp&wxfrom=10005&wx_lazy=1)

**Using Vault Variables**

**to Save Database Connection Configurations**

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHmWenDk4xMMmmpfWNsBfhT5wicrXnsMewicVN7YOKg0Qo1blYc599Ozfm6H1qQTbGibgApRou2WNR04Q/640?wx_fmt=png&randomid=vth7f4ag&tp=webp&wxfrom=10005&wx_lazy=1)

For database connection configurations, we recommend using Vault variables in the configuration. Because Vault variables are obtained from external professional key vaults and are encrypted and stored in your local client, this can achieve the best effect of both collaborative efficiency of cloud storage and data security.

[Apidog Business Flagship Edition can use the Vault Secrets (key vault) feature.](https://apidog.com/pricing)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHRrwUuhB2ek6GeMibpmVd9glwEFjYQRW5EiapWKwBtSM4bG9icn4KYickgw/640?wx_fmt=png&from=appmsg&randomid=3vikqvbw&tp=webp&wxfrom=10005&wx_lazy=1)

Set up Vault variables and store the plain text of database connections in Vault variables. For database connection configurations in different environments, you need to create different Vault Keys in your provider. For specific methods, refer to the [help documentation](https://docs.apidog.com/5831220m0).

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyEs5ibF5vSxO9vJNMiapXfwXUv9TnKvq7oqicZCUmvgBK8VazEUicAEHkiaA/640?wx_fmt=png&randomid=luto007m&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHibEdw9kqjoE8AfibpXFqffXRamPhoL4o4PDWsaTb9n4IOxnltNOrXzzA/640?wx_fmt=png&from=appmsg&randomid=ie6zqzsr&tp=webp&wxfrom=10005&wx_lazy=1)

In different environments, create environment variables with the same name, for example: dbHost. Then set references to the corresponding Vault variables for that environment in the **remote value**, and keep the local value following the remote value.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyku25LHmOJicDFqBHO9S3rCtNFg69Y23zY8kRfNcoDVEKDw5O9zkvGSQ/640?wx_fmt=png&randomid=5j6ut7qd&tp=webp&wxfrom=10005&wx_lazy=1)

The purpose of doing this is:

1. Use environment variables to wrap Vault variables, so that when setting up database connection configurations later, you only need to select environment variables. When actually using database connection configurations, it will automatically select the connection configuration for this environment according to the environment context;

2. Save in remote values so that project members don't need to manually set it up again, improving collaborative efficiency.

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHkm6icBUaAzYXTFN8TyIwvX9NL6aTGS8wXFrHvqUF0xRW82WCVdffDkA/640?wx_fmt=png&from=appmsg&randomid=i6shcst5&tp=webp&wxfrom=10005&wx_lazy=1)

In database connections, fill in the database connection variables set up in environment management. You can use the "Dynamic Value" feature to quickly reference variables.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyJdukFN7FdoEHUQZlPkaWYsBLQ57NhLQ2ccHICF1x27wWLbeWFRSZGg/640?wx_fmt=png&randomid=nshwz8i1&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHo5KkDQzWkK5MVjVLr6lNeASPNAPibGx91kKlSG5FmvOWzseGyVmj7qA/640?wx_fmt=png&from=appmsg&randomid=e5pxxlcy&tp=webp&wxfrom=10005&wx_lazy=1)

Click test connection, and it will prompt you to select the environment to test. Note that the corresponding variable actual values must be configured in the test connection environment. Click confirm and find that the connection is successful. If there are problems, you can handle them according to specific error messages.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyicwXoTnVNS9zZ8xONx9PX90IaibYBYoZZImM2vEvEeIC5Ef1C9sO9kGA/640?wx_fmt=png&randomid=tt4b172m&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHUwEgu04p2sXka3m1k20QfHcCavygwjKy3SsbIbcRVgQEHEb3pVkylQ/640?wx_fmt=png&from=appmsg&randomid=o546fmu8&tp=webp&wxfrom=10005&wx_lazy=1)

In the pre/post operations of an API request, add a database operation and select the database connection configuration saved in the cloud above, then send the request. For example, I need to find the id of a pet named "Nancy" from the database table, then request the API to query details.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyEI3ND421szKZudL4tccr75GAErQCLRFX2fj4Y0veVYwD4WMm9CAraw/640?wx_fmt=png&randomid=gr95lth7&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHkETgJmmIJADpIER2KJnDBHqLFibkWJe0DSWrzKXy4MGdrOKrpZvRmxtibS0XBw9Pcx094C4ibl4ebqQ/640?wx_fmt=png&from=appmsg&randomid=kj3c7okc&tp=webp&wxfrom=10005&wx_lazy=1)

I found that Apidog successfully executed the database operation, retrieved data from the database and saved the petID value to the petId variable as requested, then sent out the request.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWysWYB21BM2AeD841noaVyhJib9k1nTURYcNAD9mZhkiaF1fzAdxGHYBKQ/640?wx_fmt=png&randomid=u46ul8h4&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Y2mibLibqKzHmCZv7HRa0TvgtBl7rhInPLNlkmX7LrHT2Ve9L3HoxMGic8sSBIBIMIScnicXJcAcxMLESFD5jm5OZQ/640?wx_fmt=png&from=appmsg&randomid=anr41bm4&tp=webp&wxfrom=10005&wx_lazy=1)

If you have configured the database connection configuration according to the above steps, other project members can directly specify to use this configuration in the database operations of API requests to operate the database, without needing to configure it themselves.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHn6ohktWbPNN70b80LBiceWyUxznQTZBB6R1UdzYqCRqiaaE0wMpcG255Tz1H1Abtiaph2QiaFiaxUncfA/640?wx_fmt=png&randomid=xgq9h3jf&tp=webp&wxfrom=10005&wx_lazy=1)

The above is the practice of using Vault variables to save database connection configurations and their actual application. The key points are:

- Set up a Vault variable for each environment's database connection configuration, except for the port. For example, test and production environments have Vault variables: testDBHost, prodDBHost respectively;

- Use environment variables to add variables with the same name in each environment, and reference the corresponding environment's Vault variable values in the remote values of these environment variables. For example, configure the variables and specific variable remote values for test and production environments according to the following table (local values can follow remote values):

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHlyaV3YyV7vSlmoGicyomnJ4KuZCKhSmLo21w5IS4mhibGFxSDPQxapQI7ziaQS949oadgSw5QbjDZgw/640?wx_fmt=png&randomid=haqlg4b8&tp=webp&wxfrom=10005&wx_lazy=1)
