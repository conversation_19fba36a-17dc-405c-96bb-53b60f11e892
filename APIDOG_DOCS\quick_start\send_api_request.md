Send an API request
Based on the created endpoint spec, we can now easily initiate a request.

1
Switch to the Run tab.
2
Select "Local mock" as the environment in the upper right corner.
3
Click Send to send the request.
4
You can view the result of this request in the Response area.
image.png
5
Switch to Actual request to view the actual request sent out.

6
Clicking "Save as Case" to the right of the Send button allows you to save this request as an endpoint case, making it convenient for future sending and referencing.

When you specify an endpoint, a corresponding API endpoint will be automatically generated in the local mock environment. You can send requests to this endpoint and receive responses. Learn more about Mock API data.

If you want to send an actual usable API request, you need to put the service URL you want to access to the "Base URL" in the environment management in the upper right corner.


Learn more about Environments.

If you don't want to spec the API before sending the API request, you can also create a request directly.

