# Mastering API Testing with Supertest, Express.js, and Jest

## Introduction

[Supertest](https://github.com/ladjs/supertest) is a highly efficient and flexible testing library designed for testing HTTP assertions.

Working hand in hand with frameworks like [Express.js](https://expressjs.com/), Supertest makes it easy to write assertions for your APIs, ensuring they respond as expected. Coupled with Je<PERSON>, a delightful JavaScript Testing Framework with a focus on simplicity, you can ensure that your APIs are robust and reliable. In this tutorial, we will dive into setting up a simple Express.js application and write tests using Supertest and Jest.

In this blog post, we will set up a basic Express app and write tests using Supertest and [Jest](https://jestjs.io/docs/getting-started).

At first, the tests will be straight forward on validating the response status. Then, we will dive into asserting the response body and its values.

All code from today can be found remotely on [this repo](https://github.com/okeeffed/demo-express-jest-supertest).

## [](https://www.dennisokeeffe.com/blog/2023-10-27-testing-express-apps-with-jest-and-supertest#setting-up-the-repository)Setting Up The Repository

Before we dive into writing our application and tests, we need to set up our repository. Here are the steps to do that:

1# Create a new directory for your project

2mkdir demo-express-jest-supertest

3cd demo-express-jest-supertest

4

5# Initialize a new Node.js project

6pnpm init

7

8# Install necessary dependencies

9pnpm i express

10pnpm i -D jest supertest

copy

Once our repository is set up, we can start writing our application and tests.

## [](https://www.dennisokeeffe.com/blog/2023-10-27-testing-express-apps-with-jest-and-supertest#crafting-the-demo-express-app)Crafting The Demo Express App

Let's build a simple Express.js application to demonstrate how we can test it using Supertest and Jest.

Firstly, create a new file `app.js` at the root of the project directory using `touch app.js` and add the following:

1// app.js

2

3// 1. Require express

4const express = require("express");

5// 2. Create the new express app instance for our API

6const app = express();

7

8// 3. Define the route for the GET /greet endpoint

9app.get("/greet", (req, res) => {

10  const name = req.query.name || "World";

11  res.json({ message: `Hello, ${name}!` });

12});

13

14// 4. Export the app for testing later

15module.exports = app;

copy

In the above code, we add the following:

1. We require the `express` module.
2. We create a new instance of the Express app.
3. We define a route for the `GET /greet` endpoint that returns a JSON response with a `message` property.
4. We export the app instance for testing later.

This will set up our baseline Express app, but it does not start the server. If we want to do that, we need to create a new file `server.js` at the root of the project directory using `touch server.js` and add the following:

1// 1. Import our app from the app.js file

2const app = require("./app");

3

4// 2. Start the server on port 3000

5app.listen(3000, () => {

6  console.log("Server is running on port 3000");

7});

copy

At this point, we could run our app using `node server.js` on the terminal and test it using a tool like `curl` or Postman.

To confirm, we can run `curl http://localhost:3000/greet` on the terminal and get the following response:

1curl http://localhost:3000/greet

2# {"message":"Hello, World!"}

copy

Running `curl http://localhost:3000/greet?name=John` on the terminal will return the following response:

1curl http://localhost:3000/greet?name=John

2# {"message":"Hello, John!"}

copy

At this point, we are ready to write our first tests. You can stop running the server in the first terminal using `Ctrl + C` and move onto the next section.

## [](https://www.dennisokeeffe.com/blog/2023-10-27-testing-express-apps-with-jest-and-supertest#writing-tests-with-supertest-and-jest)Writing Tests with Supertest and Jest

Now that we have our Express app ready, it's time to write some tests to assert the response and value of the body.

First, create a new file `app.test.js` at the root of the project directory using `touch app.test.js` and add the following:

1// app.test.js

2const request = require("supertest");

3const app = require("./app");

4

5describe("GET /greet", () => {

6  it("should greet the world when no name is provided", async () => {

7    const res = await request(app)

8      .get("/greet")

9      .expect("Content-Type", /json/)

10      .expect(200);

11

12    expect(res.body.message).toBe("Hello, World!");

13  });

14

15  it("should greet the user when a name is provided", async () => {

16    const res = await request(app)

17      .get("/greet?name=John")

18      .expect("Content-Type", /json/)

19      .expect(200);

20

21    expect(res.body.message).toBe("Hello, John!");

22  });

23});

copy

In the code above, we are using Supertest to send HTTP requests to our app and Jest to write and run the test assertions.

The `expect` method is used to check the response status and headers, while Jest's `expect` function is used to assert the value of the response body.

## [](https://www.dennisokeeffe.com/blog/2023-10-27-testing-express-apps-with-jest-and-supertest#running-the-tests)Running The Tests

To run the tests, let's jump into the `package.json` file that was initialized with `pnpm init` and update the `scripts > test` property to the following:

1{

2  "scripts": {

3    "test": "jest"

4  }

5}

copy

You can now run `pnpm` test to run the tests. You should see the following output:

1$ pnpm test

2

3> demo-express-jest-supertest@1.0.0 test /Users/<USER>/code/projects/demo-express-jest-supertest

4> jest

5

6Determining test su

7 PASS  ./app.test.js

8  GET /greet

9    ✓ should greet the world when no name is provided (45 ms)

10    ✓ should greet the user when a name is provided (14 ms)

11

12Test Suites: 1 passed, 1 total

13Tests:       2 passed, 2 total

14Snapshots:   0 total

copy

Our tests are passing, happy days!

## [](https://www.dennisokeeffe.com/blog/2023-10-27-testing-express-apps-with-jest-and-supertest#conclusion)Conclusion

This setup provides a solid foundation to further explore and understand the capabilities of Supertest, Express.js, and Jest in the realm of API testing. As you delve deeper, you'll discover the power and flexibility this combination offers to ensure the reliability and correctness of your APIs.

We demonstrated how to set up a basic Express.js application and write tests using Supertest and Jest. We started with simple tests to assert the response status and then moved onto asserting the response body and its values.

Supertest can be used for far more things than I demonstrated in today's post. It is also great for asserting headers, cookies, and more. I encourage you to check out the [Supertest documentation](https://github.com/ladjs/supertest).
