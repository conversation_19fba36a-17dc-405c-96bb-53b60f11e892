# Working with the model providers to launch a new higher tier

Hello! Over the next week, we’re excited to roll out an option to purchase Ultra, a $200 / mo plan with 20x more usage than Pro on all API models.

This change was highly requested by power users seeking more predictability than usage-based pricing would offer, and we still recommend the Pro plan for the vast majority of users.

Ultra is made possible by deep partnerships with OpenAI, Anthropic, Google, and xAI! Their support and the size of our userbase have let us negotiate long term agreements for this volume of compute.

We're also happy to report we're rolling out changes this week to make our Pro plan more generous. By default, the Pro plan will follow an unlimited-with-rate-limits model, and all limits on tool calls will be lifted. Existing users can opt to stay with the “500 request limit” method if they prefer (see [cursor.com/dashboard](http://cursor.com/dashboard) > Settings > Advanced).

Please let us know if you have any questions or feedback!

(P.S. These changes will probably not immediately work well with unofficial, third-party extensions for tracking Cursor usage.)



1. You really need more plans like $50 and $100 plans in addition to the $20 plan. $200 for `Ultra` is just sky high.

2. You need to be 100% transparent about what the rate limits are, and any model restrictions or exclusions for both the `Pro` and `Ultra` plans.

3. It sounds like the new `Pro` plan is opt in, and the old plan will stay for now. You need to explain how to change plans. If a user switches from the old plan to the new plan is there any going back? Will new users be able to pick the old plan or is their only choice the new plan?

4. Explain how `MAX` modes work for into the new `Pro` plan and the `Ultra` plan.

As explained, I am not optimistic, given `Anysphere`'s past history and reputation. This seems like another ratcheting away from the old `Pro` plan. It seems as non-transparent as ever. It seems like things continue to get slowly worse, and even less predictable.



couldn't have said it better, might be worth making a post with these concerns instead



Came here to ask these questions and make similar observations. I need transparency to justify an increase in expense.

I'm not a power user ~40/month with plan + overage. I'm also of the opinion that there's no way things are this cheap. I need a way to justify the higher price point, especially because my employer is paying for it. If they had a $50 plan I'd jump on it today. If they had a $100 plan I could probably be nudged in that direction. $200 has me shopping around and trying other options.

I like Cursor, and I'll pay what I need to pay to get the performance I want, up to a point.



Some quick replies! (Sorry for repetition, also replied this on your main post)

1. Open to this! We want to keep the pricing simple and the Pro plan should work for the vast majority of people, but if there's sufficient demand for something in between we'd do it.

2. Understood. More docs coming. All models are available on both plans.

3. Yes, you can move back and forth! [cursor.com/dashboard](http://cursor.com/dashboard) > Settings > Advanced.

4. After you use a certain amount of MAX on pro, you'll be nudged to turn it off. That limit is much higher for Ultra.



I was ready to click the buy btn @ Ultra (power user using Claude Max + ChatGPT Pro), but then realised I have no idea what the limits actually are. How do I know when I need Ultra? When Pro just starts "slowing" down? How do I know it is slowing down? When does it do that? How do I know how far to my limits I am? I probably am in the top percentile users in terms of load, but then again - I really don't know!



You could just start with Pro and see if that's fine for you



Yea but. How do I know if it's not? How can I see how close to limits I am? When do I know I've actually hit the limits? Does it vary based on global pool peak usage or is it per individual account?



^THIS EXACTLY!

What are the limits?



Update: Just canceled everything instead. Terrible transparency on your pricing and communication and the competition is catching up.



Okay this is genuinely pissing me off now. I was automatically added to the new plan (rate-limits), and my 350 premium requests were gone in 20 claude-4 prompts.

The "Opt out" setting was not available in Advanced settings, there was only "Delete Account".

Now, after seeing that you can move back and forth, I saw the "Opt Out" button, and, thinking I'd maybe lose like 50 premium requests, clicked it and now it says I have 660/500 requests and I have to wait in slow mode like an idiot.

The worst part, I CAN'T EVEN SWITCH BACK. There's no "Opt In" button at all.

Please reply to this, I'm genuinely triggered and I need to know this is an issue that will be addressed.



We would really need to know exactly how much max we can use before it get nerfed that we need to turn it off. It’s insane to keep this in a black box.



By default, the Pro plan will now follow an unlimited-with-rate-limits model

Can you explain/elaborate on the rate limits within the new pro plan please.

We followed what the model providers do for their plans (track compute usage, rate limit if hit some very high upper bound), and then worked with them on compute agreements that would make that same pricing work.

Thanks. I'm trying to determine if I'm better off with the current pro model or the new one. Are they comparable in terms of rate limits so in the new pro tier, below X requests its fast to respond then after X+Y you begin to rate limit as a gradient? What are those numbers?

You won't get an answer. They hide this number for business advantage.

So it’s better to stay with the current pro plan. At least we know the remaining requests count

Business advantage? That’s just a scam to hide the actual numbers. Your rate could be 1 message per month and you’d never know it. I hope laws are passed to protect users from this kind of nonsense.

No need. They can only hide the number so long as a competitor is playing the same game. Once a competitor reveals their numbers, cursor will follow

This doesn't really say much. What is the actual limit? I mean there are days when I'll burn through 60-70 requests in a span of few hours and there are days when I barely do anything other than autocomplete. Most of the time I stay within 500 limit range though. What this new system will mean for me?

That does not say much.

If those rate limits are similar to what the OG providers are applying to paying users, then it is very advantageous to switch to the "unlimited rate limited" plan since those rate limits are rarely hit.

For exemple for Gemini 2.5 pro that I use heavily alongside Cursor. It happens that I make around 40 requests an hour and I am still far from reaching the rate limit.

If the limits are similar within Cursor. Then that is a real deal.

Also I suppose that those limits will be reached faster through MAX modes.



From the blog post on the Cursor page

"As part of this rollout, we're also happy to report we're making our Pro plan more generous. By default, the Pro plan will now follow an unlimited-with-rate-limits model."

What does "unlimited-with-rate-limits" even mean? Is it "unlimited, but limited"? It sounds so bad, there is no explanation at all. Fast and slow requests were clear, this one is confusing at best.



Well unlimited means you can make as many requests as you like each month.... rate limited means it will limit the rate at which it fulfills those requests... just slows you down basically, like an all you can eat buffet, but you have to take a 5 min break before going back up for more



So in essence for Pro, instead of 500 fast requests then unlimited slow requests, it's now just unlimited slow requests from the get-go? 🤔 I feel like the vague 'rate limiting system' is just a way to sugarcoat downgrading users away from the original system.



Supposedly they are all fast, but the rate limits are undefined. If they are like `Anthropic` and `OpenAI` rate-limits they will be so low as to make the plan worthless. Yet they claim they are high. We will see.



Can you explain how many actual requests can we use before hit the rate limit? And what is the time unit for this rate limit in minutes/hours/days? Or will we rate limited based on server capabilities? Update: Cursor team [responded](https://forum.cursor.com/t/questions-about-the-two-new-pro-systems-ultra-update/104711/4).



with the "new" pro plan, is there going to be a way of seeing your usage? at least as it is now, there's numeric values mapping to x/500 requests p/m as opposed to "unlimited" with an ambiguous rate limit.

then how does usage-based billing apply? is this a monthly allocation of requests under the hood or is it more like claude in that your usage resets every 24 hours? so then i could go over my limits in 20 hours, switch to usage-based billing for the duration of my rate limited usage, then switch back to my unlimited usage?



your description is too ambiguous meaning lol.  
per my usage on Pro Plan activated: Credits burning in normal mode, not MAX very high, it's likely MAX mode.

The Unlimited you mentioned is the SLOW requests after burned all 500 fast requests? if that so what is the differences??

Okay if you launched Ultra that's fine to bring more Fast requests. But don't say Generous OK? you are not generous at all



Has the rate limit already taken effect, and how is it expected to be enforced?

I encountered this today—after multiple rounds of tool and MCP calls, it kept showing "generating" until it eventually failed completely. I'm not sure if there's a correlation. Today, this issue has occurred more than ten times, something that has never happened before.



With the latest update, it burns through Sonnet-4 requests like I’m using Opus Max. What’s going on? The request count is sketchy now and I have no clue what to do. You guys seriously have a communication problem. What does “unlimited with rate limits” even mean? It just sounds like fast and slow requests, but no one knows what it actually is.

No documentation, no clear explanation. It’s a sudden change, and we have no idea how to adapt to it.



Essentially, the change to Cursor AI's Pro plan is about predictability.

- Old Way ("Slow Requests"): After using your 500 fast requests, your subsequent requests would become unpredictably slow, as they were placed in a lower priority queue. You never knew how long you'd have to wait.

- New Way ("Unlimited with Rate Limit"): You now get unlimited requests. However, to prevent abuse, there's a rate limit. This means if you make too many requests very quickly, you'll be temporarily throttled in a predictable way.

In short, you've traded an unpredictable, and often frustrating, slowdown for a clear and consistent usage rule.

Haha, only if the rate limit is transparent. Also only if the rate limits are actually high enough to be useful. `OpenAI` and `Anthropic` rate limits at `$20` are so low as to be useless. This smells the same.



Hmmm, I just do my job with the cursor and still have like 100 requests left. BUT THEN SUDDENLY I CANNOT USE CLAUDE 4 SONNET, it shows a message that implies I'm using the slow pool.

Then I just go to my dashboard and the fast request disappears. So how should I know if I was in a slow or fast pool? There is no indicator for the request that can be made.



I am so confused now.

Usage Based is costing way more per request now. They are also acting much slower now for some reason even when paying. Let's say that the 500 requests are now converted to $20 of usage based credits, if the requests cost 5x more then those 500 requests are worth much less?

I was completely fine with 20/month for pro and some budget for usage based. But now the "Unlimited" 500 fast requests and usage based budget are being eaten 5x faster. It would be super nice to get some more info on the specifics of how this works for Pro users/usage cost for requests/"Unlimited" rates and limits.

I also agree there should be a separate tier, something like $50 a month. $200 is insanely high for my usage. I feel like that actually would make more people switch from pro to a middle tier, meaning you make more money.




