# KOL Briefings for Apidog Promotion Campaign

This document contains tailored briefings for Key Opinion Leaders (KOLs) to promote Apidog as the best API development platform. Each briefing is customized to the KOL's specific role, expertise, and audience interests.

## Frontend Developer KOLs

### Briefing for <PERSON><PERSON> (iOS Developer)

**Target Audience:** iOS developers working with Swift

**Key Message:** Apidog streamlines iOS development by providing reliable API mocking and testing capabilities before backend implementation is complete.

**Value Proposition:**
- **Mock Server Integration:** Debug your iOS apps with Apidog's Mock Server while the backend team is still developing the actual endpoints
- **API Documentation Access:** Access comprehensive API documentation published by backend teams directly within your development workflow
- **AI-Powered Development with MCP Server:** Generate Swift models, network calls, and UI components directly from API specifications using Cursor IDE with Apidog MCP Server

**Suggested Content Angles:**
1. "How I Cut iOS Development Time by 30% Using Apidog's Mock Server"
2. "From API Spec to Swift Code: Using Apidog MCP Server with Cursor to Auto-Generate Models"
3. "Building iOS Apps Before the Backend is Ready: A Guide to Apidog Mock Server"

**Technical Demonstration Ideas:**
- Show how to connect Cursor IDE to Apidog MCP Server and generate Swift models from API specs
- Demonstrate setting up a mock server for a new iOS app feature
- Create a tutorial on implementing API calls in Swift using documentation from Apidog

---

### Briefing for Lucas Montano (Android Developer)

**Target Audience:** Android developers working with Kotlin/Java

**Key Message:** Apidog provides Android developers with the tools to accelerate development through API mocking, documentation, and AI-assisted code generation.

**Value Proposition:**
- **Seamless API Testing:** Test your Android app against Apidog's Mock Server while backend development is in progress
- **Comprehensive Documentation:** Access detailed API documentation with request/response examples directly relevant to Android development
- **AI-Powered Development:** Use Apidog MCP Server with Cursor to generate Kotlin data classes, Retrofit interfaces, and more directly from API specifications

**Suggested Content Angles:**
1. "Accelerating Android Development with Apidog's Mock Server and MCP Integration"
2. "From OpenAPI to Kotlin: Generating Android Code with Apidog MCP Server"
3. "Building Better Android Apps with AI-Assisted API Integration"

**Technical Demonstration Ideas:**
- Create a video showing how to set up Apidog MCP Server with Cursor for Android development
- Demonstrate generating Retrofit interfaces and data classes from API specifications
- Show a real-world example of using Apidog's Mock Server during Android app development

## Backend Developer KOLs

### Briefing for Adriano Binhara (C#/.NET Developer)

**Target Audience:** .NET developers working with C# and ASP.NET

**Key Message:** Apidog empowers .NET developers to design, document, and implement APIs more efficiently with visual tools and AI-assisted code generation.

**Value Proposition:**
- **Visual API Design:** Create and modify API specifications visually with Apidog's intuitive interface
- **Automatic Documentation:** Generate and publish comprehensive API documentation that frontend teams can immediately use
- **AI-Powered Implementation:** Use Apidog MCP Server with Cursor to generate C# models, controllers, and service implementations directly from your API specifications

**Suggested Content Angles:**
1. "Designing RESTful APIs Visually with Apidog: A .NET Developer's Guide"
2. "From API Spec to C# Code: Using Apidog MCP Server to Generate ASP.NET Controllers"
3. "Streamlining API Documentation for .NET Projects with Apidog"

**Technical Demonstration Ideas:**
- Show the process of designing an API in Apidog and generating C# code with MCP Server
- Demonstrate how to publish API documentation and connect it to Apidog MCP Server
- Create a tutorial on implementing a complete ASP.NET Web API using Apidog's tools

---

### Briefing for Deyvid Nascimento (Ruby Developer)

**Target Audience:** Ruby and Ruby on Rails developers

**Key Message:** Apidog enhances Ruby development workflows by providing visual API design tools and AI-assisted code generation for Rails applications.

**Value Proposition:**
- **Streamlined API Design:** Design APIs visually and generate OpenAPI specifications compatible with Ruby on Rails
- **Comprehensive Documentation:** Create and publish API documentation that frontend teams can use immediately
- **AI-Powered Development:** Use Apidog MCP Server with Cursor to generate Ruby models, controllers, and serializers directly from your API specifications

**Suggested Content Angles:**
1. "Designing APIs for Ruby on Rails Applications with Apidog"
2. "From API Spec to Ruby Code: Using Apidog MCP Server with Rails"
3. "Accelerating Ruby API Development with Apidog's Visual Tools"

**Technical Demonstration Ideas:**
- Demonstrate designing an API in Apidog and generating Ruby code with MCP Server
- Show how to publish API documentation and integrate it with frontend development
- Create a tutorial on implementing a complete Rails API using Apidog's tools

---

### Briefing for Stephanie Cardoso (Golang/Ruby Developer)

**Target Audience:** Golang and Ruby developers

**Key Message:** Apidog provides powerful tools for designing, documenting, and implementing APIs in Go and Ruby with AI-assisted code generation.

**Value Proposition:**
- **Efficient API Design:** Create and modify API specifications visually with Apidog's intuitive interface
- **Automatic Documentation:** Generate and publish comprehensive API documentation that frontend teams can use immediately
- **AI-Powered Implementation:** Use Apidog MCP Server with Cursor to generate Go structs, handlers, and services directly from your API specifications

**Suggested Content Angles:**
1. "Designing RESTful APIs for Go Applications with Apidog"
2. "From API Spec to Go Code: Using Apidog MCP Server to Generate Structs and Handlers"
3. "Streamlining API Documentation for Go and Ruby Projects with Apidog"

**Technical Demonstration Ideas:**
- Show the process of designing an API in Apidog and generating Go code with MCP Server
- Demonstrate how to publish API documentation and connect it to Apidog MCP Server
- Create a tutorial on implementing a complete Go API using Apidog's tools

## Full-Stack Developer KOL

### Briefing for Full-Stack Developer KOL

**Target Audience:** Full-stack developers working across frontend and backend technologies

**Key Message:** Apidog provides an end-to-end solution for API development, from design and documentation to implementation and testing, with AI-assisted code generation for both frontend and backend.

**Value Proposition:**
- **Complete API Lifecycle Management:** Design, document, test, and implement APIs all within one platform
- **Seamless Team Collaboration:** Enable frontend and backend teams to work simultaneously with shared API specifications and mock servers
- **AI-Powered Development:** Use Apidog MCP Server with Cursor to generate code for both frontend and backend components directly from API specifications
- **Real-time AI Response Testing:** Debug and visualize streaming AI model responses with Apidog's SSE debugging capabilities

**Suggested Content Angles:**
1. "End-to-End API Development with Apidog: A Full-Stack Developer's Guide"
2. "Bridging Frontend and Backend Development with Apidog's Tools"
3. "Accelerating Full-Stack Development with AI-Assisted API Integration"
4. "Testing AI Model Streaming Responses with Apidog's SSE Debugging Features"
5. "Visualizing Real-time AI Responses: A Guide to Apidog's SSE Timeline View"

**Technical Demonstration Ideas:**
- Create a comprehensive tutorial showing the entire API lifecycle with Apidog
- Demonstrate generating both frontend and backend code from the same API specification
- Show how to set up a complete development environment with Apidog's tools for a full-stack project
- Create a tutorial on testing OpenAI, Gemini, Claude, or Ollama API endpoints using Apidog's SSE debugging capabilities
- Demonstrate how to visualize and debug AI model thought processes using the Timeline view
- Show how to customize JSONPath extraction rules for non-standard AI model responses

---

### SSE Debugging Feature Highlight

**Key Benefits for Full-Stack Developers:**
- **Real-time Visualization:** See AI model responses as they stream in, providing immediate feedback during development
- **Built-in Support for Popular AI Models:** Automatic recognition and merging of responses from OpenAI, Gemini, Claude, and Ollama APIs
- **Thought Process Visualization:** For models like DeepSeek R1, view the AI's reasoning process in the timeline for better debugging
- **Customizable for Any AI Model:** Configure JSONPath extraction rules or use post-processor scripts for non-standard AI response formats

**Implementation Scenarios:**
1. **AI Integration Testing:** Test your application's integration with AI models by visualizing the exact responses received
2. **Response Format Debugging:** Identify and fix issues with AI model response formats using the timeline view
3. **Custom AI Model Development:** Use JSONPath extraction and post-processor scripts to work with proprietary or custom AI models
4. **Cross-Model Comparison:** Test the same prompt against multiple AI models to compare response quality and performance

**Demo Script Example:**
```
// Example request to OpenAI API
POST https://api.openai.com/v1/chat/completions
Content-Type: application/json
Authorization: Bearer YOUR_API_KEY

{
  "model": "gpt-3.5-turbo",
  "messages": [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "Explain API testing best practices"}
  ],
  "stream": true
}
```

---

## Implementation Guidelines

### Setting Up Apidog MCP Server

For all KOLs, include these basic setup instructions:

1. **Prerequisites:**
   - Node.js (version 18 or higher, preferably the latest LTS version)
   - An IDE that supports MCP, such as Cursor or VS Code with Cline plugin

2. **Configuration Options:**
   - For accessing API specifications within Apidog projects (requires API access token)
   - For accessing published API documentation (no token required)
   - For accessing OpenAPI/Swagger files directly

3. **Example Commands:**
   - For Windows users:
     ```json
     {
       "mcpServers": {
         "API specification": {
           "command": "cmd",
           "args": [
             "/c",
             "npx",
             "-y",
             "apidog-mcp-server@latest",
             "--project=<project-id>"
           ],
           "env": {
             "APIDOG_ACCESS_TOKEN": "<access-token>"
           }
         }
       }
     }
     ```

### Demo Prompts for AI-Assisted Development

Suggest these example prompts for KOLs to demonstrate in their content:

1. "Please fetch API specification via MCP and generate models for the 'User' schema in [language]"
2. "Based on the API specification, create a service class that implements the '/products' endpoint in [language]"
3. "Generate unit tests for the API client based on the specification from MCP"
4. "Create documentation comments for this class based on the API specification from MCP"

### Key Benefits to Emphasize

- **Time Savings:** Reduce development time by automating code generation and documentation
- **Consistency:** Ensure frontend and backend implementations match the API specification
- **Collaboration:** Improve team workflow with shared, accessible API specifications
- **Quality:** Reduce errors and bugs through standardized API implementation
- **Flexibility:** Support for multiple programming languages and frameworks
