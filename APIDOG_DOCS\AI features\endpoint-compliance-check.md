> To use the AI-powered endpoint compliance check, ensure that [AI features are enabled](apidog://link/pages/1225685) and your Apidog version is `2.7.22` or later.


You can use AI to perform an endpoint compliance check on your current API documentation based on the [APl design guidelines](apidog://link/pages/1343750). The AI will generate a report. Use this report to improve your API documentation so it aligns better with the design standards — making it more professional, consistent, and easy to read.

You can access the endpoint compliance check feature from the upper-right corner of any API documentation page. Simply click it to begin the AI-driven endpoint compliance check.

<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/359659/image-preview)
</Background>

After clicking the `Endpoint compliance check` button, the AI will analyze the API documentation based on the API design guidelines. It will then provide a score for each evaluation criteria, along with detailed explanations and suggestions for improvement.

<Background>
![Check Endpoint Design Guidelines](https://assets.apidog.com/uploads/help/2025/07/11/tuffe-go.gif)
</Background>