# What is docs as code? All the benefits and how to get started

## [​​What is documentation as code?](https://www.gitbook.com/blog/what-is-docs-as-code#what-is-documentation-as-code)

Documentation as code is the process of creating and maintaining documentation using the same tools that you use to code.

That could mean different things depending on the tools you use, but it will probably involve elements such as version control, Markdown formatting, automated reviews and tests. By following these existing workflows, the development and product teams can work more closely together, and technical writers can be involved in the documentation process earlier.

It also means that your technical documentation is easier to keep up-to-date, because it’s created in sync with the development process. It’s also likely to be more accurate, as the developers themselves will typically write the first draft themselves.

Plus, with the option to include documentation within those automated reviews and tests, you can catch any undocumented code before it’s merged, and check for formatting and style errors. It all adds up to documentation that’s clearer and more consistent.

## [Why is a docs as code workflow useful?](https://www.gitbook.com/blog/what-is-docs-as-code#why-is-a-docs-as-code-workflow-useful)

There are plenty of benefits to a docs as code workflow:

### [Keep docs and code in sync](https://www.gitbook.com/blog/what-is-docs-as-code#keep-docs-and-code-in-sync)

By working on documentation alongside your code, you can be sure that nobody merges changes into production without documentation to accompany them.

Outdated public-facing documentation can be confusing for customers and increase requests to your support team. While inaccurate internal documentation can be confusing for new team members who want to reduce time-to-code, and increase technical debt.

### [Avoid context switching](https://www.gitbook.com/blog/what-is-docs-as-code#avoid-context-switching)

One of the biggest pain points for teams — but especially for devs — is context switching. Moving from your coding tool to a separate tool to write documentation can be distracting and disrupt your flow.

But documentation as code tools puts code and documentation in the same tool. Which means you can stay in VS Code, NetBeans or whatever platform you use, and still write docs alongside your code without constantly switching.

Plus, when your documentation is available right next to your code, you can see the existing documentation in context. Which means it’s simpler to make smaller, targeted changes without needing help from a technical writer. It also acts a reminder to update your docs whenever you make a change to your codebase.

### [Increase collaboration between teams](https://www.gitbook.com/blog/what-is-docs-as-code#increase-collaboration-between-teams)

Without a documentation as code workflow, it’s easy for developers to forget about documentation, or just leave it to a technical writer to create and maintain. With a docs as code workflow, developers typically write the first draft of any documentation, which a technical writer will then pick up and complete.

This doesn't just ensure the docs are more accurate — it also makes the technical writer’s job easier, and saves them going back to the developer to ask basic questions. This is especially useful in larger projects where a lot of code changes, as it makes the whole process smoother and more efficient.

### [Built-in version control and checks](https://www.gitbook.com/blog/what-is-docs-as-code#built-in-version-control-and-checks)

Most teams use pull requests to manage code changes. They promote collaboration between teams, help maintain the quality of your code, and help everyone keep track of who changed and what, and when. And using this same workflow for documentation has all the same benefits.

It means people can review documentation before it’s published. Plus, pull requests often use automated checks to make sure the code is good — and you can use similar checks on your documentation. These checks can catch formatting errors, broken links, and missing images, adding an extra layer to your workflow without any extra effort.

### [TL;DR:](https://www.gitbook.com/blog/what-is-docs-as-code#tl-dr)

With a docs-as-code workflow you:

- write your docs in your IDE

- write the first draft of your docs then collaborate with others to finalize them

- use the same version control setup for your code and your docs

- make use of automated checks to maintain the quality of your docs

- follow the same review process as you do for your code.

## [An example docs as code workflow](https://www.gitbook.com/blog/what-is-docs-as-code#an-example-docs-as-code-workflow)

To give you an idea of what a documentation as code workflow looks like, here’s an example of how it could work for your team. You can adapt it to fit with your own processes, but the general flow should work with most CI/CD strategies.

### [1. Write a draft using Markdown](https://www.gitbook.com/blog/what-is-docs-as-code#1-write-a-draft-using-markdown)

Developers will typically work in an integrated development environment (IDE), so it makes sense that this is also the place to write documentation. You can save your documentation in a dedicated repo, or store it alongside your code — either way, you can let your version control system (VCS) handle the versioning and organization of your files.

At this stage, the goal is to simply make note of important information, without being distracted by advanced formatting, or things like images or graphics. Writing in plain text will do the job, but you can also use Markdown to help with some basic organization, such as adding headers, links, tables and bullet lists.

### [2. Share with a technical writer](https://www.gitbook.com/blog/what-is-docs-as-code#2-share-with-a-technical-writer)

Once you’ve written the first draft of your documentation and finishing your code work, you can submit everything for a review. If you have a technical writer on your team, this is where they can step in and make changes to your work to fit with your wider style and formatting rules.

The advantage of working in a Git-like environment like GitHub or GitLab is that it’s easy for the technical writer to understand what you’ve changed. And if they make edits to your docs, it’s easy for you to check what they’ve done using the [diff view](https://www.gitbook.com/blog/git-basics-what-is-a-diff-and-what-makes-diff-view-so-powerful).

### [3. Review and run tests](https://www.gitbook.com/blog/what-is-docs-as-code#3-review-and-run-tests)

It’s likely that you’re already using a VCS in GitHub or GitLab to review your pull requests and encourage collaboration between teams. And when you work with docs as code, you can do the same for your documentation — request a review from other team members, who can leave feedback and make suggestions before it goes live.

You can also set up automatic tests that will run whenever you updated your pull request. Depending on your needs, these could check for things like formatting and style errors, broken links and images that are no longer included.

### [4. Publish and automate](https://www.gitbook.com/blog/what-is-docs-as-code#4-publish-and-automate)

Once you’re happy with your documentation (and your code), you can publish it using a static site generator, or a dedicated documentation platform that links to your repo.

Static site generators can convert plain text into HTML, and allow for custom CSS style sheets and JavaScript additions. Alternatively, you can use a documentation platform like [GitBook](https://www.gitbook.com/).

## [Documentation as code with GitBook](https://www.gitbook.com/blog/what-is-docs-as-code#documentation-as-code-with-gitbook)

Docs as code is an efficient workflow that helps you keep your documentation updated alongside your code. But not everyone on your team will be familiar with GitHub or GitLab — some may want to edit documentation in a more familiar editing setup, with on-screen controls and a dedicated CMS. Plus, if you have multiple repos that all need documentation, having a central place to view and search through them all can be a big help.

With GitBook, you can sync individual spaces to different repositories, so that any changes you make in you repository appear in your docs — and vice versa. It offers a WYSIWYG editor and a CMS alongside these Git integrations, meaning less technical users can tweak and review documentation using more familiar tools, while still following the same VCS workflow.

Plus, with change requests, version history and built-in customization options, you can take your standard Markdown files and transform them into engaging, beautiful documentation.
