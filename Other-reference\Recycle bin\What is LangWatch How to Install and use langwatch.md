> **Pro Tip:**
> Want to streamline your API and AI workflow? [Apidog](https://apidog.com/) is the all-in-one platform for designing, testing, and documenting APIs—trusted by modern teams. [Try Apidog for free today!](https://app.apidog.com/user/login)

# LangWatch: The Ultimate Guide to Monitoring and Optimizing Your LLM Workflows

Evaluating and improving large language model (LLM) pipelines can be a headache—especially when every project has its own data, custom models, and unique requirements. Enter **LangWatch**: a modern platform that makes it easy to monitor, evaluate, and fine-tune your LLM-powered apps. This guide will show you what LangWatch is, why it’s a game-changer, and how to get started with installation, integration, and optimization.

---

## What Is LangWatch?

LangWatch is a flexible, developer-friendly platform for tracking, evaluating, and optimizing LLM workflows. Unlike traditional ML metrics (F1, BLEU, ROUGE), LangWatch is built for the non-deterministic, context-rich world of generative AI. It lets you:
- **Experiment and iterate** on LLM pipelines
- **Monitor real-time performance**
- **Evaluate results** with custom datasets and evaluators
- **Support any stack**—bring your own data, models, and pipelines

Whether you’re building chatbots, translation tools, or custom AI apps, LangWatch helps you deliver reliable, high-quality results.

![](https://assets.apidog.com/blog-next/2025/07/Screenshot-2025-07-18-192407.png)

---

## Quickstart: Installing and Using LangWatch

### Prerequisites
- Python 3.8+
- [LangWatch account](https://app.langwatch.ai/)
- OpenAI API key (for chatbot demo)
- Code editor (VS Code, PyCharm, etc.)
- Git & Docker (optional, for local setup)

### 1. Sign Up and Get Your API Key
- Register at [app.langwatch.ai](https://app.langwatch.ai/)
- Find your `LANGWATCH_API_KEY` in Project Settings

![](https://assets.apidog.com/blog-next/2025/07/Screenshot-2025-07-18-193051.png)

### 2. Set Up a Python Project

```bash
mkdir langwatch-demo
cd langwatch-demo
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install langwatch chainlit openai
```

Create `app.py` and add a simple Chainlit+OpenAI chatbot (see original for full code). Set your OpenAI API key as an environment variable:

```bash
export OPENAI_API_KEY="your-openai-api-key"  # On Windows: set OPENAI_API_KEY=your-openai-api-key
```

Run the chatbot:

```bash
chainlit run app.py
```

Open [http://localhost:8000](http://localhost:8000/) to chat with your bot.

![](https://assets.apidog.com/blog-next/2025/07/Screenshot-2025-07-18-193707.png)

---

### 3. Integrate LangWatch for Tracking

Add LangWatch to your code and decorate your main handler with `@langwatch.trace()`:

```python
import langwatch
...
@cl.on_message
@langwatch.trace()
async def main(message: cl.Message):
    ...
```

Restart the app, ask a question, and check your LangWatch dashboard ([app.langwatch.ai](https://app.langwatch.ai/)) under **Messages** to see tracked interactions.

![](https://assets.apidog.com/blog-next/2025/07/image-336.png)

---

### 4. Evaluate Your Chatbot with Datasets & Evaluators

- Create a dataset in LangWatch (e.g., “What’s the French word for today?” → “Aujourd’hui”)
- Add an **LLM Answer Match** evaluator in the dashboard
- Run the evaluator and check if your bot’s answers match expectations

![](https://assets.apidog.com/blog-next/2025/07/image-337.png)

Click **Evaluate Workflow** to test your pipeline against the dataset. Review the results and iterate as needed.

![](https://assets.apidog.com/blog-next/2025/07/Screenshot-2025-07-18-195429.png)

---

### 5. Optimize Your Workflow

- Use the **Optimize** feature in LangWatch to fine-tune prompts or settings
- Review improvements and repeat as needed

![](https://assets.apidog.com/blog-next/2025/07/Screenshot-2025-07-18-195717.png)
![](https://assets.apidog.com/blog-next/2025/07/image-339.png)

---

### 6. (Optional) Run LangWatch Locally

For sensitive data or local testing:

```bash
git clone https://github.com/langwatch/langwatch.git
cd langwatch
cp langwatch/.env.example langwatch/.env
docker compose up -d --wait --build
```

Open [http://localhost:5560](http://localhost:5560/) to access the dashboard. (Note: Local Docker is for testing, not production.)

---

## Why Use LangWatch?

LangWatch takes the guesswork out of LLM evaluation. It’s your single source of truth for monitoring, evaluating, and optimizing generative AI workflows—no matter how custom your stack. With Python, Chainlit, and OpenAI integration, you can start tracking and improving your LLM apps in minutes.

---

## Conclusion

LangWatch makes it easy to monitor, evaluate, and optimize your LLM-powered apps. From setup to evaluation and optimization, you’re in control of your AI pipeline’s quality. Ready to level up your LLM projects? [Sign up for LangWatch](https://app.langwatch.ai/) and start experimenting today.
