In Apidog, the default mocking method is Smart mock, which automatically generates mock data based on the response spec. Another commonly used mocking method is to directly use the response example as the mock response.

To implement this alternative mocking method:
1. Go to Project Settings - Feature Settings - Mock Settings
2. Change the "Default mock method" to "Response example first"
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/343626/image-preview" style="width: 640px" />
</p>

**Smart mock first**:
- It's the default value of "Default mock method".
- In this setting, the mock method priority is: Advanced mock expectation > Smart mock

**Response example first**:
- The Mock method priority becomes: Advanced mock expectation > Response example > Smart mock
- For endpoints without a specified response example, Smart mock will still be used as a fallback.

No matter which priority sequence is utilized, if mock expectations are set, they will take the first priority.
