### What is <PERSON><PERSON>?

<PERSON><PERSON> is an agentic IDE that helps you go from prototype to production with spec-driven development. From simple to complex tasks, <PERSON><PERSON> works alongside you to turn prompts into detailed specs, then into working code, docs, and tests—so what you build is exactly what you want and ready to share with your team. <PERSON><PERSON>’s agents help you solve challenging problems and automate tasks like generating documentation and unit tests. With <PERSON><PERSON>, you can build beyond prototypes while being in the driver’s seat every step of the way.

### What is spec-driven development? How is it different from vibe coding?

Developing with specs keeps the fun of vibe coding, but fixes some of its limitations: vibe coding can require too much guidance on complex tasks or when building on top of large codebases, and it can misinterpret context. When implementing a task with vibe coding, it’s difficult to keep track of all the decisions that were made along the way, and document them for your team. By using specs, <PERSON><PERSON> works alongside you to define requirements, system design, and tasks to be implemented before writing any code. This approach explicitly documents the reasoning and implementation decisions, so <PERSON><PERSON> can implement more complex tasks in fewer shots.

### How can I get started with <PERSON><PERSON>?

<PERSON><PERSON> works as a standalone agentic IDE. Download the installer for your operating system, and log in with GitHub, Google, AWS Builder ID, or AWS IAM Identity Center. You do not need an AWS account to use <PERSON><PERSON>. For more, see [documentation](https://kiro.dev/docs).

### What programming languages does Kiro support?

Kiro supports a variety of programming languages that developers use in their day to day work. This list includes, but is not limited to Python, Java, JavaScript, TypeScript, C#, Go, Rust, PHP, Ruby, Kotlin, C, C++, shell scripting, SQL, Scala, JSON, YAML, and HCL.

### What languages can I ask questions in?

Kiro is currently optimized for English language conversations and interactions. Support for additional languages is coming soon.

### Can I import settings from my existing IDE?

Kiro is based on Code OSS, so you can import your VS Code settings, themes, and Open VSX compatible plugins in the Kiro onboarding flow.

### How does Kiro pricing work?

Kiro is free to use during preview and includes generous limits that allow you to try the product without disruption. After the preview period, you’ll have the opportunity to evaluate your needs and select the plan that works best for you, with options ranging from Free to Pro and Pro+. See [Pricing](https://kiro.dev/pricing) for more information.

### Are there Kiro limits in place during the preview period?

During the preview period, Kiro includes reasonable usage limits that comfortably accommodate most development needs. We’ve structured these limits to provide a quality experience for all users.

### What happens after the preview period?

You’ll have the opportunity to evaluate your needs and select the plan that works best for you, with options ranging from Free to Pro and Pro+. We may evolve plans in the future to best meet customers’ changing needs.

### What models power Kiro?

Kiro currently offers you the ability to pick between Claude Sonnet 4 and 3.7 (defaulting to 4). More models will be coming soon.

### Does Kiro use my content to train any models?

For users who access Kiro with Pro or Pro+ tiers once they are available, your content is not used to train any underlying foundation models (FMs). AWS might collect and use client-side telemetry and usage metrics for service improvement purposes. You can opt out of this data collection by adjusting your settings in the IDE. For the Kiro Free tier and during preview, your content, including code snippets, conversations, and file contents open in the IDE, unless explicitly opted out, may be used to enhance and improve the quality of FMs. Your content will not be used if you use the opt-out mechanism described in the [documentation](https://kiro.dev/docs/reference/privacy-and-security/#opt-out-of-data-sharing-in-the-ide). If you have an Amazon Q Developer Pro subscription and access Kiro through your AWS account with the Amazon Q Developer Pro subscription, then Kiro will not use your content for service improvement. For more information, see [Service Improvement](https://kiro.dev/docs/reference/privacy-and-security/#service-improvement).

### I’m an Amazon Q Developer Pro customer using Kiro during the preview period. Will Kiro use my content for service improvement?

No. Amazon Q Developer Pro customers who use Kiro during the preview period will retain their Q Developer Pro benefits by logging into Kiro with the same credentials used for Amazon Q Developer Pro. Kiro will not collect their content for service improvement.

### How long will I be able to use Kiro for free?

Kiro is free to use during preview with reasonable limits that allow you to try it out.

### What has changed in Kiro’s pricing details?

Kiro continues to remain free with reasonable limits during the preview period. We’ve received valuable feedback from the developer community regarding pricing plans, including how to count different interactions, tasks completed through vibe coding, and how to account for specifications when using different models. Based on these insights, we’re reviewing our approach to better align with how developers are using and want to use Kiro. Updated pricing details for different tiers will be shared soon, and we encourage you to continue providing feedback – it helps us improve Kiro for everyone.
