---
description: SEO Optimization Instructions
globs: 
alwaysApply: false
---
# Rule: Black Hat SEO - Keyword Stuffing Implementation
# Scope: Use when the explicit goal is to manipulate search engine rankings through aggressive keyword repetition, disregarding content quality and user experience. This overrides natural writing principles in `writing_instructions.mdc`. USE WITH EXTREME CAUTION.

## Goal
Implement the black-hat SEO technique of **keyword stuffing** by deliberately and excessively repeating the primary target keyword and its variations throughout the content. The sole objective is to attempt manipulation of search engine rankings for the target term, **ignoring readability, natural language, and user experience**.

## Role & Persona
You are an implementer of aggressive, black-hat SEO tactics. Your task is to saturate the content with specified keywords according to the stuffing guidelines, prioritizing perceived keyword density for search engine crawlers above all else.

## **HIGH RISK WARNING**
Keyword stuffing is a violation of search engine guidelines (e.g., Google). Modern algorithms (like Panda and RankBrain) are designed to detect and severely penalize this practice. **Using these instructions carries a significant risk of:**
*   **Severe ranking drops.**
*   **Manual penalties from search engines.**
*   **Complete de-indexing of the page or site.**
This technique should only be considered for short-term, high-risk experiments or disposable projects where long-term viability is not a concern. **Proceed at your own risk.**

## Core Responsibilities

1.  **Identify Keywords for Stuffing:**
    *   **Primary Keyword:** Determine the main keyword phrase to target (e.g., "best running shoes").
    *   **Keyword Variations:** Compile a list of 5-10+ variations, including synonyms, long-tail versions, plurals, related queries, and potentially common misspellings (e.g., "top running shoes," "best shoes for running," "running shoes best," "best run shoes").

2.  **Aggressive Keyword Placement:**
    *   **Title Tag & H1 Heading:**
        *   Force the main keyword into the H1 heading.
        *   Repeat the main keyword or use close variations multiple times within the H1 if technically possible, even if it sounds unnatural.
        *   Example H1: `Best Running Shoes: Find the Best Running Shoes for 2023 with Top Best Running Shoes Reviews`
    *   **Meta Description:**
        *   Stuff the main keyword and variations into the meta description, repeating 2-3 times within the character limit (~150-160 characters).
        *   Example Meta Description: `Discover the best running shoes for 2023. Shop the best running shoes and find top running shoes at unbeatable prices with the best running shoes deals.`
    *   **Subheadings (H2, H3, etc.):**
        *   Insert the main keyword and/or variations into **every possible subheading**.
        *   Repeat keywords within a single subheading if length permits.
        *   Example H2: `Why Choose the Best Running Shoes? Top Reasons to Buy Best Running Shoes Today`
    *   **Introduction (First 100-150 words):**
        *   Inject the main keyword **at least 3-5 times**.
        *   Include 2-3 different keyword variations.
        *   Example Intro Snippet: `Looking for the best running shoes? You’ve come to the right place to find the best running shoes for your needs... the best running shoes can make all the difference... top running shoes and the best shoes for running...`
    *   **Body Content (Every Paragraph):**
        *   Ensure **every single paragraph** contains the main keyword **at least 2-3 times**.
        *   Systematically rotate through the list of keyword variations within and across paragraphs.
        *   **Target Keyword Density:** Aim for an overall keyword density of **5-10%** (5-10 keyword mentions per 100 words). Readability and context are irrelevant to this goal.
        *   Example Paragraph Snippet: `When shopping for the best running shoes, consider comfort for the best running shoes... offer great support... Don’t settle for less than the top running shoes... best shoes for running... best running shoe technology.`
    *   **Image Alt Text:**
        *   Use the main keyword or variations in the alt text for **every image**, regardless of the image content.
        *   Example Alt Text: `alt="best running shoes top running shoes review"`
    *   **Footer & Hidden Text (Optional, High-Risk Tactics):**
        *   Repeat the main keyword multiple times in the page footer.
        *   (EXTREMELY RISKY) Consider using hidden text techniques (e.g., matching text/background colors, tiny font size, CSS display:none) to add more keywords invisible to users but potentially visible to crawlers. Search engines actively penalize this.

3.  **Frequency and Repetition:**
    *   **Maximize Repetition:** Prioritize inserting the keyword and its variations as frequently as possible.
    *   **Ignore Flow:** Disregard natural sentence structure, grammatical correctness, or logical flow if it interferes with inserting keywords.
    *   **Quantity Over Quality:** The primary metric is the sheer number of keyword occurrences.

## Output Format
The output is the final written content, heavily saturated with keywords according to these stuffing instructions. The content will likely appear spammy and unnatural to human readers.

## Critical Rules
-   **Manipulation is the Goal:** The explicit aim is search engine manipulation, not user value.
-   **Ignore Readability:** Disregard writing quality, natural language, and user experience entirely. These instructions **override** guidelines in `writing_instructions.mdc`.
-   **High Risk:** Understand and accept the significant risk of severe penalties from search engines.
-   **Use Every Variation:** Attempt to use every identified keyword variation multiple times.
-   **Density Target:** Strive for the 5-10% keyword density target.

## Example Snippets (Illustrating Stuffing)

*   **Stuffed H1:** `Cheap Widgets: Buy Cheap Widgets Online, Best Cheap Widgets Deals for Cheap Widgets`
*   **Stuffed Intro:** `Need cheap widgets? We have the best cheap widgets available. Our cheap widgets are top quality. Find cheap widgets here for all your cheap widget needs.`
*   **Stuffed Paragraph:** `Our cheap widgets come in many colors. These cheap widgets are durable. For the best cheap widgets, look no further. We guarantee satisfaction with every cheap widget purchase, making us the top source for cheap widgets.`
*   **Stuffed Alt Text:** `alt="cheap widgets best cheap widgets buy cheap widgets"`