## Preparation

Before configuring in the Okta admin panel, please navigate to the SAML Single Sign-On page in Apidog organization settings. Click the **Generate SCIM Token** button and keep this page open during the following steps.

![image.png](https://docs.apifox.com/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/485623/image-preview?onlineShareType=apidoc&locale=zh-CN)

## Modify SSO Configuration

To support SCIM configuration, you need to modify the Application username format in SSO:

- Open your Okta admin panel in a browser.
- Go to **Applications**, click on the corresponding App, click **Edit** on the **Settings** button in the **Sign On** tab, and set the **Application username format** under Credentials Details category to Custom, then enter `user.getInternalProperty("id")` as the expression.

## Configure SCIM

Please follow these steps to configure your SCIM:

- Open your Okta admin panel in a browser.
- Go to **Applications**, click on the corresponding App, click **Edit** on the **App Settings** button in the **General** tab, and change **Provisioning** from None to SCIM.
- After saving, a new **Provisioning** tab will appear after the Sign On tab in the App page. Click on it, then click **Edit** on the **SCIM Connection** button.
- Copy Apidog's **SCIM API Endpoint URL** and paste it into Okta's **SCIM connector base URL**.
- For Okta's **Unique identifier field for users**, enter `userName`.
- For Okta's **Supported provisioning actions**, check Import New Users and Profile Updates and Push New Users.
- For Okta's **Authentication Mode**, select HTTP Header.
- Copy Apidog's **SCIM Token** and paste it into Okta's **Authorization**.
- After configuration, click Okta's **Test Connector Configuration**. If User Import, Import Profile Updates, and Create Users all show checkmarks, the configuration is successful. You can close the test window and save the configuration.
- On the **To App** page of the Settings in the **Provisioning** tab of the Okta App, click **Edit** on the **Provisioning to App** button and check **Enable** for **Create Users**.

## Test Your SCIM

Return to Apidog, and you will see inactive users.

- Once these inactive members log in using SSO, their status will change to normal, and they will occupy paid seats.

- Users in an inactive state will not occupy paid seats.