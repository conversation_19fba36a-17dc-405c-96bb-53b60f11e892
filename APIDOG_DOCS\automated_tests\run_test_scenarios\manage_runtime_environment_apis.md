Manage the runtime environment of APIs from other projects
Associating the running environment with other projects
When you select another project and import its APIs as test steps for the first time, you need to ensure these steps can execute properly and avoid issues with service URLs (Base URL). The popup will guide you through an "environment association" setup, which links the environment of the other project with the current project's environment.

Environment Association

Managing the running environment for APIs from other projects
You can view and manage the environment associations between the current project and other projects whose APIs have been imported as test scenario steps in the environment association function.

When importing APIs from other projects into test steps, click "Environment association" to manage these associations.

Manage Environment Association

If the current test scenario has already imported APIs from other projects, a prompt will appear in the running environment of the functional testing, allowing you to manage it.

Functional Testing Prompt

Inside the environment association function, you can see all imported projects along with their environment associations with the current project and the specific services (Base URLs).

Imported Projects

Permission control for steps imported from other projects
Click the "Members" tab on the homepage to view or manage team members' permissions. If you do not have read or higher permissions for the target project, you cannot view or edit the APIs (test cases) imported from that project.

Manage Permissions

Viewing/editing step details of imported APIs
To view or edit APIs imported from other projects into test steps, the operator needs to have "Read-Only" or higher permissions for the target project.

View/Edit Imported APIs

If you do not have the necessary permissions, you cannot view or edit the API.

Permission Denied

Running test scenarios containing APIs from other projects
To run test scenarios containing APIs from other projects, the operator needs to have "Read-Only" or higher permissions for all imported projects. If you do not have access permissions for a particular project, you cannot run the test scenario.

Run Test Scenarios

Manually synchronizing environment association data from other projects
To ensure data consistency, if the APIs or running environments in other projects have been updated, you can click "Check the running methods of these steps" → "Update" button to refresh the cross-project data in the current test scenario.

Synchronize Data

This retouched version provides clearer headings, improved readability, and structured information to help users manage the runtime environment of APIs from other projects effectively.

