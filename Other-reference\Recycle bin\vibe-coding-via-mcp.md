Vibe Coding (via MCP)

AI Features

Show "Vibe Coding (via MCP)"

If enabled, a "Vibe Coding (via MCP)" button will be displayed in the documentation, guiding end users how to use the current API documentation in MCP-enabled IDEs, such as Cursor, Cline, etc., to assist the Agentic AI in writing code.

🚀 Configuration Guide
Prerequisites
Node.js (version 18 or higher, preferably the latest LTS version).
An IDE that supports MCP, such as:
Cursor
VS Code + Cline plugin
Configure the IDE
Copy the following JSON configuration code to add to the MCP configuration file in your IDE:
{
  "mcpServers": {
    "Project - API Specification": {
      "command": "npx",
      "args": [
        "-y",
        "apidog-mcp-server@latest",
        "--site-id=545587"
      ]
    }
  }
}
If you're on Windows and the configuration file above isn't working, try using the configuration file below instead:
{
  "mcpServers": {
    "Project - API Specification": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "apidog-mcp-server@latest",
        "--site-id=545587"
      ]
    }
  }
}
🎯 How Apidog MCP Server Works?
Once the Apidog MCP Server is set up, it automatically reads and caches all API documentation data on your local machine. The AI can then retrieve and utilize this data seamlessly.
Simply instruct the AI on what you’d like to achieve with the API documentation. Here are some examples:
1.
Generate Code: "Use MCP to fetch the API documentation and generate Java records for the 'Product' schema and related schemas".
2.
Update DTOs: "Based on the API documentation, add the new fields to the 'Product' DTO".
3.
Add Comments: "Add comments for each field in the 'Product' class based on the API documentation".
4.
Create MVC Code: "Generate all the MVC code related to the endpoint '/users' according to the API documentation".