Authentication and Authorization in Apidog
APIs utilize authentication and authorization to ensure that client requests access data securely. Authentication involves verifying the identity of the request sender, while authorization confirms that the sender has permission to visit the endpoint.

If you're building an API, you can choose from a variety of auth models. If you're integrating with a third-party API, the required authorization will be specified by the API provider.

Authentication in Apidog
Some APIs require establishing a client's identity with a digital certificate. You can add your certificate authority (CA) or client certificates to Apidog so you can access APIs that require authentication. Learn more about Add and manage CA and client certificates in Apidog.

Authorization in Apidog
You can pass auth details along with any request you send in Apidog. Auth data can be included in the header, body, or as parameters of a request.

If you enter your auth details in the Authorization tab of a request, Apidog will automatically populate the relevant parts of the request for your chosen auth type.

You can use the Authorization tab of a request or folder to select an auth type and complete relevant details. To learn more, go to Add API authorization details to requests in Apidog.

For more details on each authorization type you can use, go to Authorization types supported by Apidog.

