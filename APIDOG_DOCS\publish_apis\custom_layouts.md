Page layout settings allow users to customize the `Docs Sites`, including adding navigation functions, a document bottom banner, login and registration buttons, and other convenient entry points.

## 1. Top Navigation 

Provides custom navigation menu editing and supports three types of navigation:

**Specified API Group:** Supports selecting all APIs

**A Specific API Group Custom Link:** Supports a custom text

 **Links Multi-level Menu:** Supports defining a two-level menu

![layouts-settings-01.png](https://api.apidog.com/api/v1/projects/544525/resources/347913/image-preview)

### Button Component 

The top navigation right-side function currently supports adding `button`,`icon `,`link` components.

![layouts-settings-02.png](https://api.apidog.com/api/v1/projects/544525/resources/347915/image-preview)

## 2. Left Side Catalog Style Module

 To use the `Left Side Catalog Style Module`, the `Top Navigation Module` must be turned off.

![layouts-settings-03.png](https://api.apidog.com/api/v1/projects/544525/resources/347917/image-preview)

Fully expanded effects:   

![fully expanded effects](https://assets.apidog.com/uploads/help/2023/09/04/25c2b5a011b97f06c1a7f41ad3ad110b.png)

Collapsed primary directory effect: 

![collapsed primary directory effect](https://assets.apidog.com/uploads/help/2023/09/04/893131b5af27b1190b4d1e02d9516fd2.png)

## 3. Top Bulletin
You can add text  to enhance user experience and provide useful information like updates, tips, status, and others by adding item to the `Top Bulletin`.

![layouts-settings-04.png](https://api.apidog.com/api/v1/projects/544525/resources/347919/image-preview)

## 4. Content Footer

Currently,only `Footer Image` component is supported, which is used for providing  visual information at the bottom of the document to help with business promotion,contextual information etc.

![content footer.png](https://api.apidog.com/api/v1/projects/544525/resources/347923/image-preview)