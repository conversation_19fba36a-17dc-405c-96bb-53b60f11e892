You may have spent a lot of time perfecting your API documentation, but when you search for related keywords in search engines, your docs rarely appear in the results. The issue isn't about poor content—it's a lack of essential SEO optimization for your doc site.
Apidog's published online doc sites come with a range of built-in SEO optimizations by default, including sitemap generation, robots file management, semantic URLs, and customizable metadata tags for each page. These are all critical for search engines to crawl and index your content effectively.
Beyond these defaults, you can fine-tune SEO settings to meet your specific needs. The features are divided into two levels: page-level settings and site-level settings. Page-level settings let you optimize individual endpoint or Markdown docs, while site-level settings provide unified management for your entire doc site.
Let's dive into the details. If your Apidog version is outdated, we recommend updating to the latest version first (click the download button below). 

Configuring Page-Level SEO Settings within Apidog
Page-level SEO settings allow you to optimize how each endpoint or Markdown documentation page appears in search engines. It’s simple to configure and can significantly improve visibility and click-through rates.
How to Configure Page-Level SEO Settings
Step 1: Open the Page You Want to Optimize
Navigate to the desired endpoint or Markdown documentation page in your project.
Step 2: Click the SEO Settings Icon
Look for the SEO Settings icon at the top right of the page. Click it to open the settings panel.

Step 3: Set a Custom URL Slug
By default, URLs of the documentation pages are system-generated with a string of numbers (e.g., https://your-domain.com/5702007m0), which aren't readable or SEO-friendly. 
You can enter a meaningful URL slug "find-pet-by-id" for the endpoint "Find pet by ID", and the URL becomes: https://your-domain.com/find-pet-by-id. This makes the URL clearer and easier to remember.
Step 4: Edit the Meta Title and Meta Description
Meta Title: This is the blue headline shown in search results. A clear, keyword-rich title improves rankings and encourages clicks.
Meta Description: This text may appear below your title in search results. While search engines sometimes auto-generate it, a well-written description helps users quickly understand the content of the page.

Step 5: Add Relevant Keywords (Optional)
Though search engines rely less on meta keywords today, adding them can still signal the topic of the page. For example, for the endpoint "Find pet by ID", here are some keywords that you may want to add: pet info, per store endpoint, pet data, REST API
Step 6: Insert Custom Metadata (Advanced Option)
For advanced SEO control, you can define custom meta tags using JSON.Example:

These will be rendered as standard <meta> tags in the HTML and can control indexing or how the page appears when shared on social media.

Setting Up Site-Level SEO Settings within Apidog
Beyond single-page SEO settings, Apidog offers comprehensive, site-level SEO configuration to help you manage metadata, indexing rules, and URL redirects across your entire doc site.
You can access these settings while publishing your documentation under:
Share Docs → Publish Docs Sites → SEO Settings

Step 1. Configure Global Metadata
Use Global Metadata to define meta tags that apply across all documentation pages. This ensures consistent branding and improves SEO without duplicating work on every page. You can set up the global metadata like this:

You can use built-in variables like:
{{PAGE_TITLE}}
{{PAGE_URL}}
{{SITE_NAME}}
{{SITE_ICON}}
{{DESCRIPTION}}
{{KEYWORDS}}

⚠️ Priority Rules: Page-level SEO settings override Global metadata, which overrides System defaults.
Step 2. Manage robots.txt
The robots.txt file gives instructions to search engines about which pages to crawl. Apidog automatically generates a basic robots.txt that allows all crawlers to access all pages and points to the sitemap file.
If you want to prevent certain pages from being indexed, just add {"name": "robots", "content": "noindex"} in the global Metadata or page-level seo settings.

Step 3. Enable or Disable sitemap.xml
The sitemap.xml feature is enabled by default because it's a huge SEO boost. This file(also known as "the site map") is like a directory of your site, listing all page URLs. Search engine crawlers use it to systematically and efficiently index your content. Once enabled, it's accessible at https://{your-domain.com}/sitemap.xml.
If you need to disable this feature, the system will stop generating the sitemap and automatically update robots.txt to remove the sitemap reference.

Step 4: Set up Docs Redirect Rules to Avoid 404 Errors
If you change the URL of a published document (for example, for SEO optimization), use Docs Redirect Rules to preserve traffic and search engine rankings.  By setting up redirect rules, users visiting the old URL are automatically sent to the new one.
How to Set Up Redirects:
1. Go to the Docs Redirect Rules section in the SEO Settings panel. 
2. Add a rule specifying the old URL and the new URL.
This ensures both users and search engines are automatically sent to the correct page without hitting a 404 error.

Two URL Naming Strategies
For URL settings, the system uses different strategies depending on whether you set a custom URL. If you do, the system uses your value directly—e.g., set to api-overview, the final address is https://{your-domain.com}/api-overview.
If you don't set a custom URL, there are two auto-generation rules:
Rule 1: Title + ID. e.g., https://{your-domain.com}/SEO-Settings-5702007m0. This combines semantic info with uniqueness, helping both search engines and users understand the page.
Rule 2: ID only. e.g., https://{your-domain.com}/5702007m0. This is simpler but less descriptive.

From the perspective of SEO, using keywords in your URLs is generally the better choice—search engines do consider URL keywords when ranking pages. That said, if your documentation site already has strong domain authority, using keyword-rich or "semantic" URLs becomes less important.
For most websites, including keywords in the URL is a best practice. However, if you prefer cleaner URLs or your documentation structure is complex, using simplified or system-generated URLs is also a valid option.
Practical Tips for Configuring API Documentation SEO
To make your API documentation more visible in search results, follow these step-by-step SEO best practices:
1. Start with Global SEO Settings
Begin by setting up global metadata—such as a default meta title, description, and keywords. This creates a consistent SEO foundation across your entire documentation site, ensuring every page has the basics covered.
2. Prioritize Key Pages for Page-Level SEO
Next, identify your most important pages—like frequently used endpoints, onboarding guides, or quickstart tutorials. These are the pages you want to drive the most traffic to.
For each one, customize the:
Meta Title: Make it clear and keyword-rich.
Meta Description: Summarize the page content concisely.
Keywords: Add relevant terms users might search for.
3. Choose the Right URL Structure
When setting URL rules:
Use title-included URLs (e.g., /get-user-data) if your team values SEO and wants to improve discoverability.
Use ID-only URLs (e.g., /123456abc) if you prefer simplicity or have a complex documentation structure.
4. Understand SEO is a Long-Term Game
Even with perfect on-page settings, SEO takes time. Search rankings depend on:
Your site’s domain authority
Backlinks from other trusted sources
User engagement and behavior
Crawl frequency by search engines
Don’t expect immediate results—be patient.
5. Continuously Improve Based on Data
Regularly monitor SEO performance:
See which pages get traffic and which don’t.
Gather feedback from users.
Update weak pages with clearer content and better metadata.
⚠️ Reminder: No amount of SEO can compensate for poor content. Focus on delivering high-quality, helpful documentation. Good SEO only works when paired with valuable content.
Conclusion
Apidog provides a comprehensive SEO toolkit to help your API documentation rank better in search engines. You can fine-tune individual pages or manage SEO settings across your entire site—all in one place.
Key features include:
Custom URLs for cleaner, keyword-rich links
Meta titles and descriptions for better search visibility
Sitemap generation to help search engines crawl your site
Redirect rules to manage content updates without losing SEO value
Whether you're making small page-level edits or configuring global settings, Apidog has everything you need to optimize your docs for search.
For more details, check out the SEO Settings section in Apidog's help docs.
If you want to learn more about search engine optimization, see:
Google Search Central - SEO Starter Guide