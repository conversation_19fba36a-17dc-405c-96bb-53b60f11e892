Email Subject: Apidog's February Updates: Enhanced SSE for Streaming LLM Responses(Like DeepSeek) & More🚀

Hello Apidog Users,
We’re excited to share the latest updates and optimizations we have rolled out in February! From smarter debugging tools for LLM APIs to enhanced team collaboration features, these updates are designed to make your API development workflow smoother and more efficient. Let’s dive in!

🔥 Enhanced SSE Debugging for AI Endpoints: When debugging Server-Sent Events (SSE) streaming responses for endpoints related to AI with Large Language Models (LLMs), Apidog can automatically combine message content and display the response in natural language. Plus, it supports showing the thought process of reasoning models, such as Deepseek R1, giving you deeper insights into how AI models work. Learn more. 
[Image]
![debugging SSE](https://assets.apidog.com/uploads/help/2025/02/14/908a74aeb50fc684f894e5e8dac54d47.gif)

🖧 Synchronization of Database Connection Credentials: User names, passwords, and other database connection details, if filled using variables, can now be synchronized among team members. This ensures everyone on your team has access to the latest credentials, reducing errors and streamlining collaboration.

📝 "No-Content" Response Type: You can now configure the content type of endpoint responses as No-Content, making it clear that the endpoint does not return a response body. This is especially useful for endpoints where the absence of a body is expected.
[Image]

📄  Enhanced Privacy in Public Project Documentation: When accessing public projects outside your team, the endpoint documentation no longer displays modifier or creator information, ensuring greater privacy protection.

📥 Smarter OpenAPI/Swagger Imports: When importing data in OpenAPI/Swagger format, if the body is in common file or image formats, it will now be imported as a Binary type, ensuring better compatibility and accuracy.

🍪 Cookie Handling Update: We’ve resolved an issue where only the first cookie was being sent when multiple cookies with the same name were included. Now, all cookies will be properly sent with API requests, ensuring accurate session management.

📋 Improved Test Scenario Display: We’ve enhanced the display style of test scenarios included in scheduled tasks after they’ve been modified or deleted, making it easier to track changes and manage your tests.

✉️ Secure Team Invitations: When inviting new users via email, only the invited email account can join the team. Forwarding the invitation email will not work, adding an extra layer of security to your team management.

🎮 Empty Mounted Data Directory by Default: When deploying a Runner, the mounted data directory is now empty by default. You’ll need to manually fill it in, avoiding issues where users lack access to the default directory.

📊 Smaller Test Report Files: We’ve reduced the file size of test reports, significantly improving upload speeds and making it faster to share and analyze results.

🛠️ What’s Next?
We’re constantly working to make Apidog even better! Stay tuned for more updates in March!

💬 Join the Conversation!
Have feedback or ideas? Join our Discord, Slack, or Microsoft Teams communities to connect with fellow developers, share tips, and stay updated on the latest Apidog news.

P.S. Explore all the new updates in the Apidog Changelog and let us know what you think! 🚀

Happy API Building!
Regards,
The Apidog Team