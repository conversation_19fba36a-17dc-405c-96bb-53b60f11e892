OAuth 2.0
Apidog can directly generate tokens according to the OAuth 2.0 specification and attach them to requests, no need to generate them in other tools and then paste them, which is very convenient.



Basic Settings
To generate a Token according to the OAuth 2.0 specification, you first need to select the authorization mode. The required configuration content and the process of obtaining the Token are different under different authorization modes. The following five authorization modes are supported:

Authorization Code

Authorization Code (With PKCE)

Implicit

Password Credentials

Client Credentials

Authorization Code
Required fields:

Auth URL

The URL of the login page, generally obtained from the backend of the third-party OAuth 2.0 service.

Access Token URL

The URL to obtain the Token via the Code, generally obtained from the backend of the third-party OAuth 2.0 service.

Callback URL

The callback URL after successful login, also called Redirect URL, which is usually your own business domain. It needs to be pre-recorded in the backend of the third-party OAuth 2.0 service.

Client ID

Also known as App ID, generally obtained from the backend of the third-party OAuth 2.0 service.

Client Secret

Also known as App Secret, generally obtained from the backend of the third-party OAuth 2.0 service.

After filling in the required fields, click the "Get Token" button, and the login page will pop up. After completing the login process on the login page, the login page will automatically close, and the Token will be automatically obtained.



After successfully obtaining the Token, the Token content and its validity period will be displayed on the interface. With the Token, you can click the "Run" button, and the generated Token will be automatically attached to the Authorization Header, with the Bearer prefix added before being sent.

Select Access Token or ID Token
If the OAuth 2.0 service returns both Access Token and ID Token, Apidog will use the Access Token by default. If you want to switch to ID Token, you can do so by selecting ID Token in the "Token Type Used" option.

Refresh Token
If the OAuth 2.0 service returns a Refresh Token, a "Refresh Token" button will appear. If the token expires, you can click the "Refresh Token" button to obtain a new token directly without the login window popping up.

Obtain Token Again
If the OAuth 2.0 service does not return a Refresh Token, and the token expires, you can click the "Obtain Token Again" button, and a login window will pop up for you to go through the login process again.

Switch Login Account
Generally, the login page of the OAuth 2.0 service will remember the user's login status. When obtaining a token again, it will use the account from the last login by default. If you want to change the account, you can click the "Clear Cookies" button, and then click "Obtain Token".

Advanced Settings
You can click the "Advanced" option to add more encryption settings. If left blank, they will be generated automatically.



Scope

Scope is the authorization scope of OAuth 2.0, used to limit the range of resources to be accessed.

State

State is an additional parameter for the OAuth 2.0 authorization request, which can be used to prevent Cross-Site Request Forgery (CSRF) attacks.

Credentials

Provides two methods: Send as Basic Auth header and Send client credentials in body.

Refresh Token URL

If you want the Refresh Token URL to be different from the Access Token URL, you can configure it in this option.

Http Authorization Prefix

Generally, it is Bearer, but you can adjust it according to your actual needs.

