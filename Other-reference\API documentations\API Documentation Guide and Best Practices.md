# API Documentation Guide and Best Practices

Can an API even be used without good documentation? While technically possible, it’s through API documentation best practices that developers first experience an API and get to know its functionality.

Whether your API is meant for internal use, exposed to partners, or fully public, developers will need both complete and accurate REST and OpenAPI documentation examples to best complete their integrations.

In this API documentation guide, we’ll cover the basics of how to document APIs and different types of documentation. We will also cover the basics of the commonly asked question ‘what is API documentation?’ in detail with examples.

This guide will also include exploring what you will need in your API documentation template and how this translates into creating your first API documentation sample. We’ll also cover and look into API description documents examples.

## What is API documentation?

API docs, or API description documents, are the collection of references, tutorials, and examples that help developers use your API.

Your API’s documentation is the primary resource for explaining what is possible with your API and how to get started. It also serves as a place for developers to return with questions about syntax or functionality. The best API docs have these answers hence why it is so important that you document your API.

Typically, documentation is hosted in a special section of your website, or its own API-focused portal. The content should be as widely accessible as it can be for your audience. If only developers within your own company use your API, its documentation is likely also internal. However, it should be easily discoverable. You shouldn’t have to know who to ask.

For APIs used outside your organization, make your documentation public. Even if you whitelist certain partners to the API, developers like to see what’s possible before discussing partnerships.

Once you’ve determined where these API docs will reside, you need to ensure they cover the needs of developers who will use them.

## What types of API documentation are there?

Your API documentation will have several types of content. Some are meant to show what’s possible to a developer considering an integration. Others will get those developers started quickly. And yet, good & simple API documentation should remain useful when that developer is deep into their work.

Your documentation must completely describe the API’s functionality, be accurate, educational, and inspire usage. It’s a big job that can roughly be broken down into three types:

![](https://cdn.prod.website-files.com/6320e912264435aca2ab0351/644b0b43a514ffd156fa6975_search-box.svg)

Reference and functionality

![](https://cdn.prod.website-files.com/6320e912264435aca2ab0351/644b0b43a514ffc320fa695e_guide-tutorial.svg)

Guides and tutorials

![](https://cdn.prod.website-files.com/6320e912264435aca2ab0351/644b0b429881d7c80fcecde4_document-idea.svg)

Examples and use cases

We’ll discuss these in detail, but you can think of them as moving on a continuum of facts to context. A reference describes the endpoints of an API, it lays out all the pieces. Guides take some of those pieces and start to put them together, explaining why you’d use those parts. Finally, examples offer up a very specific solution, solving a common problem.

As you’ll see, the best API documentation nails all three of these types of content.

## How to create good REST API documentation

Now that we know what types of documentation to look for, let’s look at some examples of great REST API documentation. For many years, two names continue to come up when discussing API docs: Stripe and Twilio.

Your API’s audience may not be as wide as either of these companies. You may only have internal developers or a few select partners. It’s still worth learning from the best, even if you won’t implement everything they have done. Stripe and Twilio have based their entire companies on developers successfully integrating, so they’ve placed a lot of attention on their API document.

[Stripe's API reference](https://stripe.com/docs/api/) has nearly become a standard for its completeness and browsability.

Each operation on an endpoint is described in human-friendly terms, along with the arguments developers pass to Stripe. Finally, a copy-paste request is shown, with an example response provided below.

![](https://cdn.prod.website-files.com/6320e912264435aca2ab0351/644ae20b7ea56560ab87af68_stripe-docs-1.jpg)

[Twilio's guides](https://www.twilio.com/docs/quickstart) are notable for both their programming language coverage and how they walk you through step by step.

![](https://cdn.prod.website-files.com/6320e912264435aca2ab0351/644ae270e415203e2fbcae23_twilio-docs-1.jpg)

Twilio keeps the code visible while you read the description of what’s happening and how to customize it for your needs. You can click through a step at a time or browse the code samples, which are described the way a developer would use them.

[Heroku's examples](https://devcenter.heroku.com/start) are not an API, but worth mentioning for how well they cover the cloud platform’s supported programming languages.

![](https://cdn.prod.website-files.com/6320e912264435aca2ab0351/644ae29fffb2668fe67002d8_image3.png)

Each example is also accompanied by a guide, but what’s notable is Heroku walks you through cloning a git repository. By helping developers start with a complete app in their chosen language, Heroku quickly points would-be customers to a quick success.

One of the most important jobs of documentation is to help someone completely unfamiliar with your API. At the same time, you want it to remain useful for the developer who has already used your API. Of the three types of documentation, the reference most needs to remain relevant throughout a developer’s interaction with your API.

## Should I build my own API documentation?

As you get serious about your developer experience, you might consider rolling out your own API documentation internally, using an open source documentation framework, or subscribing to a documentation provider. Each can be a viable choice, as long as you understand the costs and trade-offs of each alternative.

Stripe, Twilio, and Heroku are all companies who market directly to developers. They have entire teams to build and maintain their documentation and other developer resources. When you write API documentation in-house, be ready to take the bulk of at least one engineer or technical writer’s time for the initial build. Also remember that, like any software, the documentation will require maintenance. You’ll frequently require additional time from engineers to fix or update your docs.

Starting from scratch isn’t a good option for most companies. Instead, you might look to open source frameworks as a foundation for your documentation. Most likely, you’ll still require some engineering time to customize the style and functionality. Just like in building your own, this method also requires maintenance. The good news is some of this will be handled by the open source community. However, you’ll still need to bring changes from the project into your documentation, or risk your installation becoming outdated.

Finally, you can use a hosted option like Stoplight, which provides beautiful, customizable documentation. Going down this route typically requires the least amount of engineer time; keeping your team focused on your core business. Since many of these services are delivered in the cloud, updates are automatic and maintenance is minimal.

All of these options require investment at varying levels. Regardless of which you choose for your API documentation, your developer experience is worth it.

## Generating useful API references

When a developer thinks about API documentation, it’s most likely they envision a complete reference. This is only one type of documentation, but it’s an important one. Here you’ll find a list of API endpoints, what request and response fields are available, and how to authenticate with the API. These are all very important parts of integrating with your API.

Developers may use a reference when determining what is possible with an API, creating SDKs or testing against an API, or simply reminding themselves of an endpoint or field’s name. In all of these cases, it’s important to have an accurate, up-to-date, and complete reference. To that end, the more you can automate your API reference, the more likely it will reflect your latest API updates.

### Start with your API specification document

The best place to start when generating an API reference is a machine-readable description of your API. There are a handful of options here, including OpenAPI, Swagger, and RAML. That said, the industry has rallied around The OpenAPI Initiative, created by a consortium and operated by the Linux Foundation.

The OpenAPI Specification comes in two flavors: version 2, which is based on the original Swagger spec, and the newer version 3. The latest version is the way forward, but you’ll still find tools built on version 2, and may need to convert between them.

OpenAPI allows you to describe your API endpoints, request data, response fields, authentication, headers, and more. While the format is human-readable, the biggest reason to use an OpenAPI document is for automation. There’s a lot you can do, including [mock and test your API](https://stoplight.io/mock-api-guide/basics/), but one popular use of OpenAPI is to generate documentation.

You may generate OpenAPI documents from code, but you’ll be missing out on using it earlier in your API design. There are major advantages to [design-first APIs](https://stoplight.io/api-design-guide), including early collaboration across departments, mock servers to try out your APIs, and automated API testing from design through production.

### Show and tell API functionality

Every API reference should tell a developer what’s possible. That is, listing the endpoints and their input fields describe the functionality. A developer expects this kind of black-and-white, citation of facts. The best and most useful API references also show what’s possible.

At its simplest, a good API documentation can show example responses. The data that is returned from an API is at least as important as how you make the calls, but you’d be surprised how many API references leave responses out.

If you have started with an OpenAPI definition, the responses objects (and other related components in version 3) is where you’ll include this important data. When a developer can see what to expect, they’re better able to predict how they might integrate with an API without making live calls.

That said, showing your API docs can go beyond static content. Interactive documentation is becoming more common and therefore expected. Developers can preview API requests, change values, and see mock or live responses. You can even construct curl command line examples or generate source code in popular languages.

## Writing good quality API documentation guides

Your API reference helps new developers see what’s possible. Later, they’ll return to remind themselves of syntax or specific functionality. In between, you’ll need documentation that helps them achieve common tasks with your API. These guides are less about describing functionality and more about defining use cases.

Writing good quality API documentation guides and following best practices helps developers understand the “why” and “how” in addition to the “what” of a reference. You should keep these two areas in mind when writing API documentation to ensure they are fully useful and the best API docs possible.

Many developers will look for a tutorial before diving deep into your list of endpoints. The most important page of your documentation is the Getting Started guide. You can think of this as a Hello World that also takes them into an area of your API that shows them the benefits of integration. Often, your getting started guide will include your API’s most common use case.

Be careful not to attempt to cover too much in this initial guide, but enough that a developer can take the next steps on their own. To that end, you’ll want to include brief authentication instructions within this guide.

If you use a complex authentication scheme like OAuth, consider providing personal tokens for developers, or supply a tool that easily goes through the OAuth flow. Every additional step is a rough edge that will keep developers from success. Your guides should help provide a smooth experience.

At the end of your getting started guide, what will a developer do next? Your answer to that question will help you figure out the next guides you need to write. Choose additional and advanced use cases, based around parts of your API.

Other potential guides might include how to use your API with popular frameworks, other APIs, or walking through entire applications. In fact, you can write API documentation and guides for every example app., which is the final type of documentation you’ll want to provide for developers.

## Inspiring API usage with examples

A close relative of the guide is the example app, which includes all the code you need to produce a full integration with your API. Great API documentation will have at least one and often many examples, frequently with the source hosted in a public repository like GitHub.

The quickest way to add an example app to your documentation is to package all the code from your getting started guide. While the guide may take you through one section of code at a time, the example app should have everything you need in one place. It may only show basic usage of your API, but it offers a place to start with simple instructions that may look something like this:

1. Download this code
2. Replace your API key
3. Run the code

While you may have a couple more steps, the ideal example app has the lowest possible barrier to getting something working.

You can inspire developers even more with example apps that support advanced use cases. You can provide a complete working app that someone might legitimately tweak and use, not just follow for learning.

As with guides, understanding the problems that your developers are solving will help you determine what examples to share. You can also take inspiration from existing developers and share the use cases that are already popular.

## Technical documentation best practices

We’ve already covered some great ways to approach various types of documentation. Now that you’re ready to take your API documentation to the next level, let’s look at some best practices for comprehensive, maintainable API docs.

**Make it someone’s job.** It doesn’t have to be their entire job, but it might be. The important thing is that you have someone watching out for how developers experience your documentation.

**Involve multiple teams.** Gather diverse perspectives to understand what’s needed in your documentation. You’ll find great insights from engineering, marketing, product, support, and more.

**Look for type and topic coverage.** Consider whether references, guides, and examples are as complete as they should be. Are there areas of your API that aren’t covered well in one or more types of documentation? Use this to determine where to focus future efforts.

**Include documentation in existing processes.** As your API evolves, your documentation should keep up with the times. Automate where possible and make sure you consider whether new references, guides, or examples should be included with new feature launches.

**Acknowledge your documentation is a work in progress.** It doesn’t need to be immediately perfect. Look for ways to incrementally improve one section or type at a time. Over time, alongside these other best practices, you’ll have great API documentation.
