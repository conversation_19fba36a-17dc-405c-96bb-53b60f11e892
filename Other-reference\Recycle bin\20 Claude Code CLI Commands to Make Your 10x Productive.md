---
meta-title: "20 Claude Code CLI Commands That Will Make You a Terminal Wizard (2025 Edition)"
meta-description: "Unlock the full power of Claude Code CLI! Discover 20 essential commands, pro workflows, and productivity hacks for AI-powered development."
excerpt: "Ready to 10x your dev workflow? Master these 20 Claude Code CLI commands and turn your terminal into an AI-powered productivity machine."
---

# 20 Claude Code CLI Commands That Will Make You a Terminal Wizard (2025 Edition)

> **Pro Tip:** Want to supercharge your API workflow while you're at it? Try **[Apidog](https://apidog.com/)**—the all-in-one platform for designing, testing, and documenting APIs. It's like having a senior dev, a QA, and a project manager in your terminal (minus the cryptic error messages). [Sign up for free!](https://app.apidog.com/)

**Let's face it:** The days of coding solo, hunched over a keyboard, are fading fast. In 2025, your best teammate might just be an AI—and if you're living in the terminal, Claude Code CLI is your new power tool. Forget clunky GUIs: the command line is where real devs get things done, and <PERSON> is here to make you faster, smarter, and (dare we say) a little bit lazier—in the best way possible.

This isn't just a list of commands. It's your cheat sheet to a new era of agentic, AI-powered development. Whether you're a CLI ninja or just getting started, these 20 commands and workflows will help you 10x your productivity, automate the boring stuff, and keep your focus on what matters: building cool things.

---

## Part 1: Getting Started—Setup, Sessions, and Context Magic

### 1. Installation: Summon Your AI Sidekick

```bash
npm install -g @anthropic-ai/claude-code
```
*Install Claude Code CLI globally. Now you can call on Claude from anywhere—no browser tab required.*

### 2. Configuration: Make Claude Ring a Bell (Literally)

```bash
claude config set --global preferredNotifChannel terminal_bell
```
*Get a terminal bell when Claude finishes a long task. Multitask like a pro and let Claude ping you when it's done thinking.*

### 3. `claude`: Start a Fresh Chat

```bash
claude
```
*Kick off a new session. Clean context, clean mind. Perfect for new tasks or when you want to leave yesterday's bugs behind.*

### 4. `claude --continue` or `claude -c`: Pick Up Where You Left Off

```bash
claude --continue
```
*Resume your last session, context and all. No need to re-explain yourself—Claude remembers everything (well, almost).*

### 5. `claude --resume` or `claude -r`: Juggle Multiple Projects

```bash
claude --resume
```
*See a list of past sessions and jump back into any project. It's like having a separate AI for every repo you touch.*

---

## Part 2: Core Workflow—Slash Commands for Everyday Devs

### 6. `/init`: Give Claude a Brain for Your Project

```bash
/init
```
*Creates a `CLAUDE.md` in your project root. Store architecture, dependencies, and conventions here—Claude will use it as its project memory. (Pro tip: Ask Claude to help you write it!)*

### 7. `/clear`: Wipe the Slate Clean

```bash
/clear
```
*Reset the current session's context. Great for switching tasks without starting over.*

### 8. `/compact`: Summarize and Save Tokens

```bash
/compact
```
*Claude summarizes the convo so you can keep chatting without hitting the context limit. It's like having an AI that takes meeting notes for you.*

### 9. `/review`: Instant Code Review, No Waiting

```bash
/review
```
*Ask Claude to review a PR, file, or code block. Get feedback, bug spotting, and style checks in seconds. Merge with confidence!*

### 10. `/help`: Your Built-In Cheat Sheet

```bash
/help
```
*Lists all slash commands and what they do. Never get stuck or lost in the docs again.*

### 11. `/model`: Pick Your Claude (Opus or Sonnet)

```bash
/model
```
*Switch between Claude models for your session. Need deep thinking? Go Opus. Need speed? Sonnet's your friend.*

---

## Part 3: Project Mastery—Understand Any Codebase, Fast

### 12. `> summarize this project`

```bash
> summarize this project
```
*Get a high-level overview of any repo. Perfect for onboarding or open source spelunking.*

### 13. `> explain the folder structure`

```bash
> explain the folder structure
```
*Claude breaks down the project's directory tree so you know where everything lives.*

### 14. `> find the files that handle user authentication`

```bash
> find the files that handle user authentication
```
*Let Claude hunt down the code for any feature. It's like `grep` with a PhD in software architecture.*

### 15. `> explain the main architecture patterns used here`

```bash
> explain the main architecture patterns used here
```
*Claude identifies the big-picture design patterns so you can code in harmony with the project's style.*

---

## Part 4: Advanced Arsenal—Power User Moves

### 16. Custom Slash Commands: Build Your Own Dev Tools

*Create markdown files in `.claude/commands` and invent your own slash commands. Example:*

```markdown
# .claude/commands/test.md
Run all the unit tests and report the results.
```
*Now `/project:test` runs your custom command. Automate anything—testing, deployment, boilerplate, you name it.*

### 17. `claude mcp add`: Plug In Superpowers

```bash
claude mcp add playwright npx @playwright/mcp@latest
```
*Add the Playwright MCP and let Claude control a browser. Ask it to log in, take screenshots, or run end-to-end tests. The future is now.*

### 18. `permission.allow` / `permission.deny`: Lock Down Your AI

*Edit `.claude/settings.json` to whitelist or blacklist commands. Keep Claude on a leash—or let it off, if you dare (with `--dangerously-skip-permissions`).*

### 19. `npx ccusage@latest`: Track Your Token Spend

```bash
npx ccusage@latest
```
*See how many tokens you're burning and estimate costs. Optimize your prompts and keep your AI budget in check.*

### 20. `> ultrathink ...`: Unleash Deep AI Brainstorming

```bash
> ultrathink how to design a scalable real-time chat application
```
*Ask Claude to "ultrathink" and get a deep, structured, multi-step answer. It's like a whiteboard session with a senior architect—minus the dry-erase fumes.*

---

## Part 5: Agentic Workflows—Claude as Your Dev Teammate

### Test-Driven Development (TDD) with Claude

1. `> write a failing test for the new feature`
2. Run the test (confirm it fails)
3. `> write the code to make the test pass`
4. Run the test again (confirm it passes)
5. `> refactor the code for clarity and efficiency`

*Claude can generate both tests and implementation, making TDD a breeze. No more excuses for skipping tests!*

### Using Multiple Claude Instances

- **Instance 1:** Write the code
- **Instance 2:** Review the code
- **Instance 3:** Refactor based on feedback

*Parallelize your workflow and get multiple "AI perspectives" on your code. It's like having a whole team of devs in your terminal.*

---

## Conclusion: The Future of Coding Is a Conversation

Claude Code CLI isn't just a tool—it's your new coding partner. Master these commands, and you'll spend less time on grunt work and more time building, learning, and shipping. The future is agentic, collaborative, and a lot more fun. So fire up your terminal, start a chat, and let the productivity (and the jokes) begin!
