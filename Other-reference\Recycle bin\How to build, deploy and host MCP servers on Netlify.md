# How to build, deploy and host MCP servers on Netlify

Are you ready to supercharge your AI workflows with the **MCP Server on Netlify**? Imagine giving your AI agents—like <PERSON>, <PERSON>urs<PERSON>, or VS Code Copilot—a direct line to your platform’s tools and data, all hosted on Netlify’s slick serverless infrastructure. In this fun, conversational guide, we’ll walk through what the **MCP Server on Netlify** is, why it’s awesome, and how to build, deploy, and host it step-by-step. Plus, we’ll test it with a cool example to make sure it’s working. Let’s dive in and make your AI smarter!

## What Is an MCP Server and Why Netlify?

The **Model Context Protocol (MCP)** is like a universal adapter for AI agents. It’s an open, lightweight standard that lets AI clients (for example, <PERSON>op or Cursor) talk to servers that expose tools, data, and prompts. This means your AI can interact with your app’s features in real time, making it context-aware and way more useful.

Hosting your **MCP Server on Netlify** is a no-brainer. Netlify’s serverless platform is scalable, low-maintenance, and perfect for deploying MCP servers. It handles the heavy lifting—traffic spikes, updates, and security—so you can focus on building cool AI integrations. Whether you’re adding AI to a blog, e-commerce site, or custom app, the **MCP Server on Netlify** makes it seamless.

![netlify official website](https://assets.apidog.com/blog-next/2025/07/Screenshot-2025-07-14-183851.png)

## Why You’ll Love Hosting MCP Servers on Netlify

Here’s why the **MCP Server on Netlify** is a game-changer:

- **Scalable Power**: Netlify’s serverless functions handle traffic surges without breaking a sweat.
- **Easy Deployment**: Push to GitHub, and Netlify auto-deploys your server. No DevOps degree required!
- **AI-Friendly**: Connects AI agents to your platform’s data and tools with minimal setup.
- **Low Overhead**: Serverless means you only pay for what you use, keeping costs down.

Ready to bring your AI to life? Let’s set up your **MCP Server on Netlify**!

![deploy to netlify](https://assets.apidog.com/blog-next/2025/07/image-273.png)

## Building, Deploying, and Hosting MCP Servers on Netlify

### Prerequisites

Before we start, make sure you have:

- **Node.js 20+**: For local development and running the Netlify CLI ([nodejs.org/en/download](https://nodejs.org/en/download)).
- **Netlify Account**: Sign up at [netlify.com](https://www.netlify.com/).
- **Netlify CLI**: We’ll install this to test and deploy.
- **Git**: For version control and deployment.
- **MCP-Compatible Client**: Claude Desktop, Cursor, Windsurf, or VS Code Copilot for testing.
- **GitHub Account**: Optional, for Git-based deployment.

### Step 1: Set Up Your Netlify Project

**Set Up Project Structure**:

- Create a new project folder (e.g., `mcp-netlify`).
- Inside, create a `/netlify/functions` directory for your MCP server code.

Example structure:

```bash
/mcp-netlify
  /netlify
    /functions
      mcp.js
```

**Create a Netlify Site**:

- Log in to [app.netlify.com](https://app.netlify.com/).
- Click **New site from Git** or create a new site manually.
- If using Git, connect your GitHub/GitLab/Bitbucket repository later.

### Step 2: Implement the MCP Server Function

Create the `mcp.js` file in `/netlify/functions` to define your **MCP Server on Netlify**. This uses the MCP SDK to expose tools, resources, and prompts to AI clients.

Here’s an example for `mcp.js`:

```javascript
import { StreamableHTTPServerTransport } from "@modelcontextprotocol/sdk/server/streamableHttp.js";
import { toFetchResponse, toReqRes } from "fetch-to-node";
import { z } from "zod";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import {
  CallToolResult,
  ReadResourceResult,
  JSONRPCError
} from "@modelcontextprotocol/sdk/types.js";

// Netlify serverless function handler which handles all inbound requests
export default async (req: Request) => {

  try {

    // for stateless MCP, we'll only use the POST requests that are sent
    // with event information for the init phase and resource/tool requests
    if (req.method === "POST") {

      // Convert the Request object into a Node.js Request object
      const { req: nodeReq, res: nodeRes } = toReqRes(req);
      const server = getServer();

      const transport = new StreamableHTTPServerTransport({
        sessionIdGenerator: undefined,
      });

      await server.connect(transport);

      const body = await req.json();
      await transport.handleRequest(nodeReq, nodeRes, body);

      nodeRes.on("close", () => {
        console.log("Request closed");
        transport.close();
        server.close();
      });

      return toFetchResponse(nodeRes);

    }

    return new Response("Method not allowed", { status: 405 });

  } catch (error) {

    console.error("MCP error:", error);
    return new Response(
      JSON.stringify({
        jsonrpc: "2.0",
        error: {
          code: -32603,
          message: "Internal server error",
        },
        id: '',
      } satisfies JSONRPCError),
      {
        status: 500,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
};

function getServer(): McpServer {

  // initialize our MCP Server instance that we will
  // attach all of our functionality and data.
  const server = new McpServer(
    {
      name: "acme-devtool-server",
      version: "1.0.0",
    },
    { capabilities: { logging: {} } }
  );


  server.tool(
    "run-analysis-report",
    "Checks the data available in Acme Devtool and returns all of the important data regarding the latest numbers.",
    {
      days: z.number().describe("Number of days to analyze").default(7),
    },
    async (
      { days },
    ): Promise<CallToolResult> => {

      const random = Math.random() * 100;

      return {
        content: [
          {
            type: "text",
            text: JSON.stringify({
              lastNDays: days,
              data: Array.from({ length: days }, (_, i) => `Day ${i + 1} had ${random * days} growth.`),
            }),
          },
        ],
      };
    }
  );

  // providing a resource for agents that might need to take a given report
  // and parse the information in it
  server.resource(
    "interpreting-reports",
    "acme://interpreting-reports",
    { mimeType: "text/plain" },
    async (): Promise<ReadResourceResult> => {
      return {
        contents: [
          {
            uri: "acme://interpreting-reports",
            text: `Reports from Acme will include an array of text that informs the growth of over that number of days. It's unstructured text but is consistent so parsing the information can be based on looking at a single line to understand where the data is.`,
          },
        ],
      };
    }
  );

  return server;
};

// Ensure this function responds to the <domain>/mcp path
// This can be any path you want but you'll need to ensure the
// mcp server config you use/share matches this path.
export const config = {
  path: "/mcp"
};
```

This creates a serverless MCP endpoint at `/.netlify/functions/mcp`. Customize `tools`, `resources`, and `prompts` based on your app’s needs (e.g., API calls, database queries).

### Step 3: Install Dependencies

In your project folder, initialize a Node.js project and install the MCP SDK:

```bash
npm init -y
npm install @modelcontextprotocol/sdk
```

### Step 4: Local Development and Testing

1. **Install Netlify CLI**:

```bash
npm install -g netlify-cli
```

2. **Run Locally**:

```bash
netlify dev
```

This starts a local server at `http://localhost:8888`. Your **MCP Server on Netlify** will be available at:

```awk
http://localhost:8888/.netlify/functions/mcp
```

3. **Test with MCP Inspector**:

Use the MCP Inspector to verify your server:

```bash
npx @modelcontextprotocol/inspector npx mcp-remote@next http://localhost:8888/mcp
```

Open [http://localhost:6274](http://localhost:6274/) in your browser to explore your server’s tools and resources interactively.

![test with mcp inspector](https://assets.apidog.com/blog-next/2025/07/mcp-inspector.png)

### Step 5: Deploy Your MCP Server to Netlify

You have two deployment options:

**Option 1: Git-Based Deployment**

1. Push your project to a GitHub, GitLab, or Bitbucket repository.
2. In Netlify, link the repository to your site under **Site configuration > Build & deploy**.
3. Netlify auto-deploys on every push to your main branch.

![install netlify to github repo](https://assets.apidog.com/blog-next/2025/07/Screenshot-2025-07-14-184322.png)

**Option 2: CLI Deployment**

Deploy directly with the Netlify CLI:

```bash
netlify deploy
```

For production:

```bash
netlify deploy --prod
```

After deployment, your **MCP Server on Netlify** will be live at:

```awk
https://your-site-name.netlify.app/.netlify/functions/mcp
```

Note the URL for the next step.

### Step 6: Connect MCP Clients to Your Netlify MCP Server

Configure your AI client (Claude Desktop, Cursor, Windsurf, or VS Code Copilot) to connect to your deployed **MCP Server on Netlify**. Use one of these configs:

**For Local Testing**

```json
{
  "mcpServers": {
    "my-netlify-mcp": {
      "command": "npx",
      "args": [
        "mcp-remote@next",
        "http://localhost:8888/mcp"
      ]
    }
  }
}
```

**For Deployed Server**

```json
{
  "mcpServers": {
    "my-netlify-mcp": {
      "command": "npx",
      "args": [
        "mcp-remote@next",
        "https://your-site-name.netlify.app/mcp"
      ]
    }
  }
}
```

Replace `your-site-name.netlify.app` with your actual Netlify site URL.

### Client Configuration

**Claude Desktop**:

1. Go to **Settings > Developer > Edit Config**.
2. Open `claude_desktop_config.json`, paste the config, and save.
3. Restart Claude Desktop.

![claude edit mcp configuration](https://assets.apidog.com/blog-next/2025/07/claude_developer_edit_config-3.png)

**Cursor**:

1. Go to **Settings > Tools and Integrations > Add a Custom MCP Server**.
2. Paste the config and save.
3. Switch to **Agent Mode** in the chat panel.

![edit cursor mcp configuration](https://assets.apidog.com/blog-next/2025/07/image-274.png)

**VS Code Copilot**:

1. Open **Settings** (`Ctrl+,` or `Cmd+,`).
2. Search for “MCP” and enable it under **Features > Chat**.
3. Click **Edit in settings.json**, paste the config, and save.

![edit vs code mcp configuration](https://assets.apidog.com/blog-next/2025/07/image-275.png)

### Step 7: Test Your MCP Server

Let’s test your **MCP Server on Netlify** with a sample prompt in your MCP client:

> Using the MCP Server on Netlify, can you run a report for the last 3 days?

If you used the sample `mcp.js` above, the AI should respond with something like:

![testing the remote mcp server](https://assets.apidog.com/blog-next/2025/07/claude-using-mcp.png)

This confirms your server is working. Customize your `mcp.js` to add real tools (e.g., API integrations, database queries) for more complex tasks.

### Step 8: Inspect and Debug

- **Run MCP Inspector** on your deployed server:

```bash
npx @modelcontextprotocol/inspector npx mcp-remote@next https://your-site-name.netlify.app/mcp
```

- Check Netlify’s **Function logs** in the Netlify dashboard for errors.
- Use Netlify’s analytics to monitor usage and optimize performance.
- Add authentication (e.g., API keys) via Netlify’s environment variables for security.

### Best Practices for MCP Servers on Netlify

- **Keep It Stateless**: Design functions to be ephemeral, as serverless invocations don’t persist state.
- **Secure Secrets**: Store API keys or tokens in Netlify’s **Environment variables** (Site configuration > Environment variables).
- **Hybrid Setup**: If your client doesn’t support streaming, run a local MCP proxy to bridge to your Netlify server.
- **Version Control**: Use Git for collaboration and easy rollbacks.
- **Optimize Performance**: Minimize function execution time to reduce latency and costs.

### Troubleshooting Tips

- **Server Not Responding?** Check Netlify CLI logs (`netlify dev`) or Function logs for errors.
- **Client Connection Issues?** Verify the MCP config URL and restart the client.
- **Inspector Not Working?** Ensure the MCP Inspector package is updated (`npx @modelcontextprotocol/inspector@latest`).
- **Rate Limits?** Monitor Netlify’s function usage and adjust for high-traffic scenarios.

## Why Host MCP Servers on Netlify?

The **MCP Server on Netlify** is a perfect match for AI-driven workflows. Netlify’s serverless platform scales effortlessly, so your MCP server can handle thousands of AI requests without crashing. Plus, its Git-based deployment and CLI make updates a breeze. Whether you’re building a chatbot, a content management tool, or an e-commerce integration, hosting your **MCP Server on Netlify** ensures your AI agents have live, dynamic access to your platform’s features.

For example, you could create an MCP tool to fetch user data from your app’s API or trigger automated workflows based on AI prompts. The serverless setup means you don’t need to manage servers—just code, deploy, and let Netlify do the rest.

## Conclusion

And there you go! You’ve just learned how to build, deploy, and host an **MCP Server on Netlify**, turning your AI agents into powerhouses of productivity. From setting up a simple serverless function to connecting it with Claude, Cursor, or VS Code, this guide makes it easy to integrate AI with your platform. Our test prompt—“process the query: Hello, Netlify!”—shows how straightforward it is to get started.

Ready to take it further? Add custom tools to your `mcp.js`, like API calls or database queries, and watch your AI do amazing things.
