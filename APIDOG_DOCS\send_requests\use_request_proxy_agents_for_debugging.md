Use request proxy agents for debugging
When sending or debugging endpoint requests in Apidog, you can use a request proxy agent to initiate requests. This helps avoid issues like being unable to access intranet endpoints due to network restrictions.

Personal settings for request proxy agent
In Apidog, you'll find the request proxy agent settings at the bottom-right corner after entering a project. Through this setting, you can choose different methods to proxy the endpoint requests initiated from Apidog.

use request proxy for sending endpoint request.png
When using the Apidog client:

Follow Software Settings: Requests will be sent via software proxy. (Note that the proxy will only work if it is properly configured in the "Proxy configurations for sending requests".) IfNot Using Proxyis selected, the request will be sent directly from the client to the endpoint.

software proxy settings.png
Use Self-hosted Request Proxy Agent: Requests made in Apidog will be routed through the specified self-hosted request proxy agent.

using-self-hosted-request-proxy-agent-sending-requests.png
When using Apidog web:

Auto-select Proxy: Apidog will automatically choose between the browser extension agent or the cloud agent based on the current browser setup. The browser extension agent takes priority.

Browser Extension Agent: This option uses a browser extension as the proxy for endpoint requests. You must first install the extension.

Cloud Agent: This option uses Apidog's cloud-based request proxy agent to send requests. Note that this agent cannot access endpoints in internal network.

Self-hosted Request Proxy Agent: Requests made in Apidog will be routed through the self-hosted request proxy agent you’ve specified.

These personal request proxy agent settings are stored in the cloud and are specific to each project. making it easy to apply them quickly in future debugging.

Set different request proxy agents for services in different environments
Within the project, you can configure self-hosted request proxy agents for different environments and services (Base URLs) on the environment management page. Once a service is assigned a request proxy agent, all endpoint requests to that service will automatically route through the selected agent. This simplifies proxy setup across services with varying network environments, saving time and effort for your team members.

use request proxy for debugging endpoint request.png
NOTE
1.
Service Proxy Settings Take Priority:If a request proxy agent is configured for a service, it will override any personal proxy settings. For example, if a service is configured to use Proxy A, but a team member has set their personal proxy settings toNot Using Proxy, the service will still use Proxy A.

2.
Service Proxy Settings Apply Only Within Apidog App: The request proxy agent settings in the service apply only to endpoint requests made within the project. For endpoint debugging initiated from shared documentations or public doc sites, the CORS proxy configured in those documentations will be used to send the requests.

You can also view or configure the request proxy agent for all services using the proxy selector located at the bottom right of the project.

view or configure the request proxy agent for all services on project page.png
The request proxy agent settings for services are stored in the cloud and are shared among all project members.

