# Addressing your API use cases: Choosing between Apigee, API Gateway, and Cloud Endpoints

API adoption is exploding everywhere — in fact over 80% of web traffic is now API traffic ([source](https://www.akamai.com/newsroom/press-release/state-of-the-internet-security-retail-attacks-and-api-traffic)). This is because APIs enable easier and more standardized delivery of services and data — and this tends to lead to business growth. Many organizations still regard APIs as implementation detail, but this massive growth of APIs can be difficult to manage without a well-defined API strategy.

This is where API management comes in — making it easier to build APIs, secure every API call, managing your API traffic and productizing critical data assets. At Google Cloud we have three solutions for your API use cases: Apigee API Management, API Gateway, and Cloud Endpoints, and each has a unique sweet spot. So how do you choose which Google Cloud solution is appropriate for your use case? Let’s start with some definitions:

**APl Gateway:** Fully managed service tailormade to package serverless functions as
RESTfUI APIs

**Cloud Endpoints:** Customer managed service with extensible service proxy to run tactical use cases

**Apigee APl Management:** Comprehensive solution to build, manage, and secure APls-for any use case, environment, or scale

### Apigee

[Apigee](https://cloud.google.com/apigee) is Google Cloud’s API management product that enables organizations to build, manage, and secure APIs — for any use case, environment, or scale. Apigee lets you operate in any environment of your choice (on-premises, in Google Cloud, or a hybrid environment) with enhanced scale, security and automation. Apigee includes advanced enterprise capabilities including security features like bot and misconfigured API detection, tools to package and monetize digital assets as APIs, flexible runtime support, developer portals, governance policies, and more. Apigee offers two pricing models: [Pay-as-you-go](https://cloud.google.com/apigee/pricing) pricing to manage your own costs without any upfront commitment, and multiple [subscription](https://cloud.google.com/apigee/pricing) tiers to maintain predictable costs

**When is it optimal?**

While Apigee is designed for nearly any use case, it's especially valuable for customers who have very high API volumes, a need for high reliability and enterprise-grade security, or who rely on APIs exposed to third parties or partners as part of their business. 

Many organizations manage a high and/or growing volume of APIs – often deployed across multi-cloud environments and distributed architectures. This is particularly common in large organizations relying on APIs to modernize their legacy applications without slowing development. In these use cases, Apigee’s flexible deployment options, consistent governance, and robust tools for managing the full lifecycle of APIs are essential to managing the APIs consistently at scale.

Maintaining high API uptime, performance and security in spite of unexpected traffic spikes or growing malicious attacks is also essential for many organizations. Often these are use cases where customer-facing applications rely on APIs to orchestrate large amounts of business-critical or sensitive transactions. Apigee’s comprehensive controls to monitor, secure, and analyze API traffic enable organizations to maintain customer trust and consistent performance.

Finally, many organizations leverage public APIs as a channel to create new revenue streams or collaborate with external partners. Often these use cases focus on maximizing the value of digital assets by packaging them into API products and monetizing their adoption. In these use cases, Apigee’s multiple developer portal offerings, API productization and monetization capabilities streamline this process significantly.

**Why do customers love it?**

- API products - Ability to productize APIs easily (e.g., developer portals) leads to new revenue streams and opportunities

- Security & governance - Automated analytics, monitoring and governance help establish consistent standards without slowing down the pace of innovation

- Battle tested - Apigee is Google Cloud’s primary API Management platform. Google (Apigee) was recognized as a Leader in the 2021 Gartner® [Magic Quadrant](https://pages.apigee.com/gartner-magic-quadrant-2021-register.html?utm_source=blog)TM [for Full Life Cycle API Management](https://pages.apigee.com/gartner-magic-quadrant-2021-register.html?utm_source=blog) six times in a row1 

- Flexibility - Apigee can manage home grown or third-party APIs, REST or GraphQL, on premises, in Google Cloud or a hybrid deployment

### API Gateway

[API Gateway](https://cloud.google.com/api-gateway) is a fully managed service that enables developers to create, secure, and monitor APIs for services built on Google Cloud. Designed for serverless workloads, API Gateway makes it easy to manage APIs for [Cloud Functions](https://cloud.google.com/functions), [Cloud Run](https://cloud.google.com/run), and [App Engine](https://cloud.google.com/appengine). API Gateway includes security features like authentication and key validation as well as monitoring, logging, and tracing. With consumption-based pricing, it’s also easy to manage costs with API Gateway.

**When is it optimal?**

API Gateway is optimal for cloud-native use cases where the target backends are generally limited to services deployed on Google Cloud, and where the developer audiences consuming the APIs don’t require a developer portal. Often these are for very specific API use cases to package serverless applications: Cloud Functions, Cloud Run, and App Engine. This is often especially useful for digital-native organizations, small businesses, or for larger organizations that are building new applications or testing a proof of concept. If you’re getting started with API-first driven development and building serverless backends, then using API Gateway is a great way to get started.

**Why do customers love it?**

- Easy to take an OAS v2 spec and simply import it for generation

- Seamless API gateways for serverless apps at Google Cloud

- Very inexpensive to get started

### Cloud Endpoints

[Cloud Endpoints](https://cloud.google.com/endpoints) is a gateway that enables developers to configure, deploy and manage an ESPv2 proxy. Although Cloud Endpoints includes basic functionality like user authentication, basic cloud logging and monitoring, and API keys to validate API calls, it requires customers to manage every detail of the API operations. You can manage Cloud Endpoints costs with consumption-based pricing.

**When is it optimal?**

Cloud Endpoints is most useful when you specifically want to host the gateway on your own runtime with private networking, but would like the same kind of control plane features (analytics and authentication enforcement) that you enjoy with API Gateway.

The most common use case for Cloud Endpoints today is with gRPC services, where a developer wants to locally host the gateway with their project. This can be useful if you’d like to run and test the Cloud Endpoint on your own local machine during development.

### So what should you choose?

First of all, if you’re already using one of these products and it meets your needs, keep doing what you’re doing. If not, try using the decision tree below to consider key factors in the decision process.

![https://storage.googleapis.com/gweb-cloudblog-publish/images/2_api_uses.png.max-900x900.jpg](https://storage.googleapis.com/gweb-cloudblog-publish/images/2_api_uses.png.max-900x900.jpg)

For most customers running complex or high-scale applications, we suggest considering Apigee first because it’s a full-featured product designed for API consumption across teams, partners, organizations, and even ecosystems. You will never outgrow Apigee because it’s both the most feature-rich and scalable, and also the most cost-effective API management option at high scale. Apigee is where we are investing most heavily in developing new features and functionality — your API product dreams are most likely to come true with Apigee. 

However, if you’re working on a project that leverages serverless products like Cloud Run and you’re not sure if you need to manage third-party APIs, optimize for high scale or establish consistent governance yet, API Gateway is a good choice because it’s a lightweight product designed for exposing APIs at the project level. You can get started slowly and If your organization or project scales up or expands to require new features, you can consider upgrading to Apigee. 

Finally, if you’re running local tests, operating in your own private network, require greater control over your gateway configurations and runtime operations at the cost of operational complexity, then Cloud Endpoints may be a good choice because it’s designed specifically for these use cases.
