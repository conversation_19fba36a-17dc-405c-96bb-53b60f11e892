
# Apidog Newsletter Writing Style Guide

## 1. Structure & Formatting

- **Subject Line:**  
  - Clear, concise, and highlights the most exciting update(s) or theme of the month.  
  - Use emojis to add energy and catch attention (e.g., 🚀, 🌱, 🔥).

- **Greeting:**  
  - Friendly and inclusive (“Hello Apidog Users,” “Dear Apidog Users,” etc.).
  - Express appreciation for the community and set a positive, welcoming tone.

- **Introduction:**  
  - Briefly summarize the month’s focus or theme.
  - Mention the team’s ongoing commitment to improvement and innovation.
  - Use a conversational, upbeat tone.

- **Feature Highlights:**  
  - Use emoji bullets for each feature or update (e.g., 🌱, 🔒, 📋).
  - For each feature:
    - **Bold the feature name** for emphasis.
    - Provide a concise, benefit-driven description.
    - If relevant, include a “Learn more” link or a related image/GIF.
    - Use short paragraphs and clear, direct language.
  - Group related features together for clarity.

- **Images & Media:**  
  - Include relevant screenshots, GIFs, or images after the feature description.
  - Place image URLs on a new line for clarity.

- **Looking Ahead / What’s Next:**  
  - Preview upcoming features or improvements.
  - Express excitement for future updates and encourage readers to stay tuned.

- **Community Engagement:**  
  - Invite feedback, suggestions, and participation in Discord, Slack, or other community channels.
  - Encourage users to read the full changelog for more details.

- **Closing:**  
  - End with a positive, encouraging message (“Happy API Building!”).
  - Sign off as “The Apidog Team.”

---

## 2. Tone & Voice

- **Friendly and Approachable:**  
  - Write as if you’re talking to a fellow developer or teammate.
  - Use “we” and “you” to foster a sense of community.

- **Energetic and Positive:**  
  - Celebrate new features and improvements.
  - Use exclamation marks and emojis to convey excitement.

- **Clear and Concise:**  
  - Avoid jargon and long-winded explanations.
  - Focus on what’s new, why it matters, and how it helps the user.

- **Benefit-Oriented:**  
  - Highlight how each update makes the user’s workflow easier, faster, or more secure.

- **Actionable:**  
  - Encourage users to try new features, join the community, or read more.

---

## 3. Content Best Practices

- **Consistency:**  
  - Use similar formatting and structure each month for familiarity.
  - Keep feature descriptions brief and to the point.

- **Visuals:**  
  - Use images and GIFs to illustrate new features and improvements.
  - Place visuals close to the relevant text.

- **Links:**  
  - Include “Learn more” or “Explore more” links for deeper dives.
  - Link to changelogs, documentation, or community channels as appropriate.

- **Accessibility:**  
  - Use clear language and avoid overly technical terms unless necessary.
  - Make sure all images have descriptive alt text if possible.

---

## 4. Example Feature Highlight

```
🌱 **Schema Reference Insights:** On the Schema page, you can now see which endpoints, schemas, and Markdown documents reference the current schema — streamlining the management of dependencies and documentation.  
[Image]  
https://assets.apidog.com/uploads/help/2025/04/30/304c312ba6612b40f3d40c9d4422856d.gif
```

---

## 5. Example Closing

```
🌟 Looking Ahead  
We’re committed to continuous improvement and innovation. Your feedback and ideas are what drive us forward—so keep them coming! If you have suggestions or want to connect with other API developers, join our vibrant Discord or Slack communities.

P.S. Explore the full details of all these updates in the Apidog Changelog! 🚀

Happy API Building!  
Regards,  
The Apidog Team
```

---

**Follow this guide to ensure every Apidog newsletter is clear, engaging, and community-focused—making users excited for what’s next!**
