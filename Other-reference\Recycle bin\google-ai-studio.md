Work with the latest AI models and turn your ideas into apps with free AI tools

Google AI Studio is the fast path for developers, students, and researchers who want to try Gemini models and start building with the Gemini Developer API. We also offer free tools for common AI use cases, including translation, image and video analysis, speech-to-text, and more.

Which AI tools are free?
Google Cloud offers free usage of many AI products up to monthly limits, including Translation, Speech-to-Text, Natural Language, and Video Intelligence. There is no charge to use these products up to their specified free usage limit. The free usage limit does not expire, but is subject to change.

Additionally, Google AI Studio makes it easy to start building with Gemini, including free tiers across our family of multimodal generative AI models. With NotebookLM, you can create a personalized AI assistant that surfaces insights and provides Audio Overviews on data you upload, including text, video, and audio. NotebookLM is free to use while it is in the early testing phase.



Is there a free AI website?
Visit the AI product directory for a full list of AI products, including the monthly limits of Google Cloud free AI tools.

Is there a free generative AI tool?
Text, chat, and code generation using Vertex AI, a unified platform for building and leveraging generative AI, starts as low as $0.0001 per 1,000 characters. Plus, new Google Cloud customers get $300 free credits to use towards Vertex AI. 

Additionally, new Vertex AI Agent Builder customers enjoy a one time $1,000 credit per Google Cloud billing account.

What is a free AI writing tool?
Depending on your use case, Gemini Code Assist offers assistance for writing and developing code. Gemini for Workspace integrates with Docs to help you write and develop web pages, business proposals, and other content via a conversational interface. Both are free to use while in preview. 

Learn more about setting up Gemini Code Assist.

Is Google Gemini free to use?
Google Gemini is free to use for users 18 and over with a personal Google Account or a Google Workspace account for which your admin enabled access to Gemini.

Compare the 10+ free AI tools

Product	Description	Free offer details	Pricing
Google AI Studio

Get an API key and start integrating Gemini models into your apps

The Gemini API “free tier” is offered through the API service with lower rate limits for testing purposes. Google AI Studio usage is completely free in all available countries.

Model pricing details

NotebookLM

Create a personalized AI assistant that surfaces insights and provides Audio Overviews on data you upload

NotebookLM is still in the early testing phase, so we are not charging for access at this time.

NotebookLM pricing details

Translation Basic

Translate and localize text in real time with support for 100+ language pairs.

Compare to AWS Polly

First 500,000 characters free per month. 

No expiration

Translation Basic pricing details

Translation Advanced

Translation support for batch text and formatted documents, custom models, and romanized text.

Compare to AWS Polly

First 500,000 characters free per month 

No expiration

Translation Advanced pricing details

Cloud Vision

Detect faces, properties, landmarks, logos, text, and more in images.

Compare to AWS Rekognition

First 1,000 units free per month

No expiration

Cloud Vision pricing details

Speech-to-Text

Accurately convert speech into text using domain-specific models to improve quality.

Compare to AWS Transcribe

First 60 minutes free per month

No expiration

Speech-to-Text pricing details

Text-to-Speech

Convert text into natural-sounding speech with human intonation.

Compare to AWS Polly

First 4 million characters (Standard)

First 1 million characters (WaveNet)

No expiration

Text-to-Speech pricing details

Natural Language API

Identify and analyze entities and sentiments in unstructured text.

Compare to AWS Comprehend

First 5,000 units free per month

No expiration

Natural Language API pricing details

Video Intelligence

Detect shots, faces, celebrities, explicit content, logos, text, and more in video.

Compare to AWS Rekognition

First 1,000 minutes free per month

No expiration

Video Intelligence pricing details

Dialogflow

Build lifelike, state-of-the-art virtual agents with conversational AI

Compare to AWS Lex

New Dialogflow customers get a $600 credit

Expires after 12 months

Dialogflow pricing details

Compute Engine

Create and run virtual machines on Google’s infrastructure

Compare to AWS EC2

1 non-preemptible e2-micro VM instance in US regions free per month

No expiration

Compute Engine pricing details

Cloud Storage

Store unstructured data for training deep learning and machine learning models cost-effectively

Compare to AWS S3

5 GiB of US regional storage free per month

No expiration

Cloud Storage pricing details

To get started, create or sign in to your Google Cloud account. New customers get $300 free credits on signup.

How It Works

All customers can explore and assess Google Cloud with free usage of over 20 products, including 10 free AI tools, up to monthly limits. When you stay within the free usage limits, these resources are not charged against your free credits or to your Cloud Billing account's payment method.

Google Cloud free program
Learn more about Google Cloud free usage
Common Uses

Build and prototype with generative AI models
build
How-tos
Experiment, test, and discover the power of Gemini with Google AI Studio
Start using Gemini with Google AI Studio, a web-based tool that lets you prototype, run prompts right in your browser. If you're a developer, student, and researcher, try the Gemini Developer API, which is suitable for experimentation, prototyping, and AI deployments. 

Gemini API guides and examples

Gemini API on Google AI Studio quickstart

Explore prompt ideas for the Gemini API in Google AI Studio

*Google AI Studio usage is free of charge in all available regions. See Billing FAQs for details.

AI-powered language translation
build
How-tos
book
Additional resources
Detect and translate simple text in real time
With Translation API Basic, you can dynamically translate over 100+ language pairs using Google's pre-trained Neural Machine Translation (NMT) model. The first 500,000 characters are free per month, making it a good fit for applications that handle primarily casual user-generated content, such as chat, social media, or comments.

Translation API Basic setup guide

Translate text using Translation API Basic

Detect the language of a text string

AI image detection and analysis
build
How-tos
book
Additional resources
Detect faces, landmarks, logos, and insights from images
Cloud Vision AI uses Google's pre-trained machine learning model to easily integrate vision detection features within applications. The first 1,000 units (feature requests) are free per month, including detecting and labeling faces, objects, landmarks, logos, and more insights in images.

Detect faces in images documentation

Detect labels in images

Optical character recognition with Google Cloud AI

Speech-to-text transcription
build
How-tos
Transcribe short audio from local files
Speech-to-Text API uses synchronous speech recognition to transcribe audio files up to 60 seconds long. Audio content can be uploaded through local files or Google Cloud Storage buckets. The first 60 minutes of processed audio is free per month.

How to transcribe short audio files (60 secs and under)

How to transcribe long audio files (over 60 secs) 

How to transcribe audio from streaming inputs

Text-to-speech generation
build
How-tos
Create natural-sounding, synthetic speech as playable audio
Text-to-Speech API converts arbitrary strings, words, and sentences into the sound of a person speaking the same things and supports over 380 voices, spanning 50+ languages and variants. Common uses of the API include adding synthetic voice to apps, enhancing customer service voicebots, and improved accessibility user experiences. The first 4 million of processed text in Standard voice is free per month.

Cloud Text-to-Speech basics documentation

Supported voices and languages

How to create audio files with Text-to-Speech API

Analyze unstructured text
build
How-tos
Recognize entities and sentiment in unstructured text
Natural Language API uses natural language understanding (NLU) to identify and extract meaning from applications with features including sentiment analysis, entity analysis, entity sentiment analysis, content classification, and syntax analysis. The first 5,000 units (1 unit = 1,000 Unicode characters) of analyzed text is free per month for all features except content classification (30,000 units free per month).

Set up Natural Language API

Natural Language API code samples

Perform sentiment analysis by using client libraries

Label and analyze video with AI
build
How-tos
Detect objects and actions in stored and streaming video
Video Intelligence API uses pre-trained machine learning models that automatically identify and label video metadata, including shot types, faces, people, celebrities, explicit content, logos, and text. The first 1,000 minutes of analyzed video is free per month.

Video label detection tutorial

Video Intelligence API code samples

Detect shot changes in video

AI research assistants
build
How-tos
Create a personalized AI research assistant
NotebookLM uses Gemini 1.5’s multimodal understanding capabilities to summarize uploaded source material (including PDFs, websites, YouTube videos, audio files, etc.) and  surface interesting insights between topics. Plus, with Audio Overview, you can turn your sources into engaging “Deep Dive” discussions—similar to a podcast.

