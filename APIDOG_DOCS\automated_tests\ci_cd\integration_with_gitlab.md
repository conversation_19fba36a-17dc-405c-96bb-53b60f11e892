Integration with Gitlab
Initialize Gitlab pipeline
Access Gitlab and log in, click "Projects" in the left navigation bar, then click the "New project" button.

image.png
After entering the project, in the "Build" section, click "Pipelines editor" to create a new pipeline.

Get Gitlab pipeline code
In an Apidog Test scenario, switch to CI/CD tab. Specify the runtime environment, choose whether to enable test data, set iteration count, etc.

image.png

Select Gitlab CI pipeline, then click "Copy Code" in the upper right corner.

Paste into Gitlab's Pipeline editor, then click "Commit changes" to trigger the pipeline. If your pipeline includes other built-in tasks, please modify as necessary to ensure the pipeline runs correctly.

image.png

Run the pipeline
After the pipeline starts running, you will see the run logs in the terminal. At this point, you have integrated Apidog automated testing steps into your Gitlab CI pipeline.

image.png

