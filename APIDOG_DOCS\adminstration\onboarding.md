Basic Concepts
Apidog offers powerful management tools to help you efficiently organize, manage, and collaborate on API design, development, and documentation. By organizing your work at three levels — "Team", "Project", and "Organization" — you can manage and oversee API tasks more effectively and with greater precision across various scopes.

Teams
Teams are the core unit of collaboration and organization. Each team can contain multiple projects and members, and the data between teams is independent and invisible to each other, ensuring data isolation.

The functions of a team mainly include member management, permission control, and collaboration. You can invite team members to join your team and assign them different roles and permissions to keep your project and data secure. Team members can share resources, co-edit, and exchange ideas, improving the team's collaboration efficiency and the quality of project workflow.

You can read the following documents to get a quick overview of what Teams does and how to use it:

Managing teams

Managing team members


Projects
Project is the basic unit for organizing and managing APIs and related documentations. Each project represents an independent workspace where you can create, edit, and test APIs, design and manage API documentation, and collaborate with your team members on projects.

By creating multiple projects, you can categorize APIs and documents according to different needs and capabilities of the team. This way, you can better organize and manage various APIs, improving the efficiency and maintainability of your work. Each project has its own independent settings and permission controls to ensure the security of the project and the privacy of the data.


You can read the following documents to get a quick overview of what Projects does and how to use it:

Managing Projects
Managing Project Members

Organizations
An "Organization" is the top level of management and collaboration, typically used to represent a company or large team. It allows you to manage multiple teams from one central location, with the ability for unified permissions and resource allocation. This makes it easier to oversee all API projects across teams while ensuring data security and seamless collaboration.

With the organization setup, admins can assign specific permissions to different teams and manage all projects centrally. Each organization can have multiple teams, and the data and projects of each team are kept separate to protect information security and privacy.

apidog-organization-settings.png
Differences between Team, Project, and Organization
In Apidog, "Team," "Project," and "Organization" represent different levels of management and collaboration, each serving a distinct role:

Organization: This is the highest level of management, representing a company or large team. It is responsible for centrally managing multiple teams and projects, with the authority to manage permissions across teams.

Team: A team is a smaller unit within the organization, typically representing a department or group. It is responsible for managing specific API projects. Team members collaborate on these projects, while the admin can oversee and manage project permissions at the team level.

Project: A work unit within a team, focused on managing specific API resources and documentations. Each project acts as an independent workspace, making it easier to manage and collaborate on specific tasks.

In summary:

The Organization manages multiple teams.

The Team handles specific API projects.

The Project is where API design and development happen.

Onboarding Guide
To get started using Apidog within your organization, you can fallow through the following tasks to set your Apidog team up for success. It's recommended that you first collaborate with your Team Admins and your organization's IT team to set up, secure, and manage Apidog in your organization. Then you can set up your Apidog team by configuring relevant settings, inviting people to your team and assigning them roles, and creating resources related to your projects.

You can download and install the Apidog desktop app for Windows, Mac, and Linux. You can also access Apidog on the web with the Apidog Agent.

Collaborate with your IT team
Contact your IT team to establish the procedure for adding a new piece of software, which varies from organization to organization. The following topics are common:

Your IT team may need to add an exception to device policy allowing for Apidog to be installed on employee workstations. Provide a Apidog download link to the IT team to help establish this exception.

If your organization's network connection is behind a proxy, you may need to configure Apidog appropriately. Retrieve proxy connection details from your IT team and set them up within Apidog.

If you are already logged into Apidog, you can set up a proxy by navigating to the ⚙Settings - Proxy option located in the top right corner of the Apidog interface. Alternatively, if you are not logged into Apidog yet, you can configure the proxy settings in the bottom left corner of the login screen in the Apidog client application.

If your organization operates behind a firewall, your IT team may need to configure allowlists for Apidog's domains. This ensures Apidog data is synced with the cloud and all functionality works as expected.

Apidog's domains include:

*.apidog.com

*.apidog.io

Set up Apidog team
First, you need to decide which member in your organization will be the Team Admin. The Team Admin can register an Apidog account, create a new team, and automatically become the Team Owner of this new team.

The team owner have the following permissions:

Modify team information

Create new project

Invite and remove members

Set team permissions and project permissions for other members

Manage subscriptions and payments

Transfer and dissolve the team

A team can consist of multiple members and manage various projects. Administrators have the authority to determine which members have which permissions within specific projects.

In the Apidog free version, a team can have a maximum of 4 members. If your team exceeds 4 members, you will need to upgrade to the paid plan of Apidog.

Manage team members
The team owner can invite a few other members to be team admins, who will then be responsible for inviting and managing additional collaborative members within the team.

Team admins can invite other members to join the team via email or invitation links. When inviting members to join, they can set the default permissions for the members regarding projects within the team. These permissions typically include Admin, Editor, Read-only, and Forbidden.

It is important to note that in Apidog, Team permissions and Project permissions are distinct authorization levels. Team permissions are utilized for managing the team as a whole, encompassing Team settings, member management, and overall team governance. Team permissions include roles such as Team owner, Team Admin, Team member, and Guest.

On the other hand, Project permissions are specifically tailored for managing individual projects within the team. These permissions govern aspects related to project settings, access control, and collaboration within the project scope. Project permissions consist of roles such as Admin, Editor, Read-only or Forbidden, which dictate the level of control and access team members have within a particular project.

Create projects
In Apidog, each project corresponds to an OpenAPI Specification. Besides the API spec, an Apidog project also includes Test Scenarios, API Documentation, a series of independent requests related to this spec, and various configurations specific to the project.

In alignment with Postman, while a Postman Workspace corresponds to a Team in Apidog, Postman Collection corresponds to a Project in Apidog.

Apidog includes two types of project: HTTP projects and gRPC projects. These two project types have different organizational structures and cannot be mixed.

HTTP Projects: In HTTP projects, users can debug common HTTP protocol APIs such as REST API, SOAP/WebService, GraphQL, WebSocket, and Server-Sent Events (SSE). HTTP projects provide a versatile environment for testing and interacting with a variety of APIs adhering to HTTP standards. Users can explore and debug diverse API types within the HTTP project setting.

gRPC Projects: On the other hand, gRPC projects are specifically designed for debugging gRPC APIs.

Only the team owner and team admins have the privilege to create new projects in Apidog. When creating a project in Apidog, admins can choose the project type and define the default permissions for other team members within this team.

Data Migration
Apidog supports importing data in more than a dozen different formats, enabling the effortless import of various [common API formats] into Apidog.

When importing from Postman, due to variances in basic data structures, Postman requests are recognized as Endpoint cases in Apidog, while the base URL is stored in the Apidog Environment Service module. This will not impact the normal functioning of Postman requests within Apidog. Postman scripts can be directly executed within Apidog without any compatibility issues. For more information on migrating from Postman, refer to apidog migrating blog.

If you need to migrate data from a project within Apidog to another Apidog project, utilize the "Export" option in the Settings menu to export the data in the Apidog format. Subsequently, import this file into the target project. The Apidog format encapsulates comprehensive project information. Avoid using the OpenAPI format for data migration between Apidog projects, as it may result in the loss of data other than the API Spec. This method ensures a complete and accurate transfer of project data between Apidog projects.

Data between Apidog Global, Apidog Europe, and Apidog On-premises platforms is not interoperable and should also be migrated using the Export to Apidog Format method.

Initial project configuration
It is recommended to configure the following settings at the inception of a project to streamline team collaboration in the future.

1.
Project Language: Apidog distinguishes between "Project Language" and "Interface Language". Project Language refers to the language used for the content created within the project, while the Interface Language is the language displayed on the user interface. When creating a project, it is essential to select the Project Language. Certain initial content in the project will be initialized in this language.

2.
Default Response Template: If your company follows a standardized response structure, you can set it as the "Default Response Template". This ensures that new Endpoints created will have this template as the default response, maintaining consistency across the project.

Default Response Template can be set in APIs - Components - Default Response Template

3.
Response Components: Typically, error responses for various Endpoints within a project are similar (e.g., 400, 404). You can configure these common error responses as "Response Components" and enable them by default. This setting ensures that new Endpoints created will have these pre-configured responses, facilitating efficiency and uniformity in response handling.

4.
Endpoint Status: Apidog provides a range of built-in Endpoint statuses that you can customize based on your company's specific requirements. These statuses allow you to categorize and track the progress or status of each Endpoint within the project. You can enable and configure which Endpoint statuses align with your workflow and project management practices.

Endpoint status can be set in Settings - General Settings - Feature Settings - Endpoint Feature Settings - Endpoint Status

Integrations
Apidog supports integration with various common tools, greatly facilitating API building processes.

1.
CI/CD Integration: Apidog can be executed from the command line, allowing integration with a variety of CI/CD tools such as Jenkins, Github, Gitlab, and more. This integration enables seamless automation of API testing and deployment processes within the CI/CD pipeline.

2.
Database Integration: Apidog offers support for executing SQL queries in pre and post operations, as well as the capability to generate Endpoint Schema from database table structures. Various databases including MySQL, SQL Server, Oracle, MongoDB, and others are compatible for integration, enhancing flexibility in API development and data management.

3.
Notification Integration: Apidog facilitates the forwarding of activities such as Endpoint creation, modification, or deletion to third-party applications such as Slack or any tool supporting Webhooks. This notification integration ensures real-time updates and collaboration on API development activities across teams and platforms.

In Apidog, you can further explore additional external tool integrations to enhance your workflow and extend the capabilities of the platform. By leveraging the integration features in Apidog, you can easily connect with a wide range of external tools and services to streamline API development and collaboration.

Getting Familiar with Apidog Features
Apidog offers comprehensive documentation to help your team understand the functionalities of the platform. If one is new to using Apidog, it is recommended to read through the "Apidog first steps" guide to familiar with the basic features of Apidog.

