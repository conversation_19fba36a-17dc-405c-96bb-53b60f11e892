# How to Connect to Any LLM to Any MCP Server, Locally and Open Source

Want to hook up your favorite large language model (LLM) to a **Model Context Protocol (MCP)** server without cloud dependencies or subscription fees? You’re in the right place! With the **Director** CLI tool, you can connect any LLM to any MCP server locally, using open-source tools. In this guide, we’ll walk you through setting up **Director**, configuring an MCP proxy, and linking it to clients like Claude, Cursor, or VS Code. By the end, you’ll be querying Hacker News or posting to Slack with ease—all from your own machine. Let’s get started!

## What Is Director and Why Use It?

**Director** is an open-source CLI tool launched in July 2025 that simplifies connecting LLMs to MCP servers. MCP, introduced by Anthropic in November 2024, is a standardized protocol for linking LLMs to external data sources and tools, like a “USB-C for AI.” Whether you’re pulling top stories from Hacker News or posting summaries to Slack, **Director** makes it easy to set up local MCP proxies and manage multiple servers and clients. It’s free, privacy-focused, and perfect for developers who want control over their AI workflows. Ready to dive in?

![director cli tool](https://assets.apidog.com/blog-next/2025/08/Screenshot-2025-08-08-122820.png)

## Step 1: Install the Director CLI Tool

Let’s get **Director** running on your machine. You can install it locally via npm for a quick setup, no browser required, or use the original curl method for flexibility.

### Option 1: Install via npm

1. **Open a Terminal**:
- Fire up your favorite terminal (macOS, Linux, or Windows WSL2). Ensure **Node.js 20+** is installed ([nodejs.org](https://nodejs.org/)).

2. **Install Director**:

- Run:

```bash
npm install -g @director.run/cli
```

This installs the **Director** CLI globally.

3. **Start the Gateway**:

- Launch **Director**’s gateway:

```bash
director serve
```

This starts a local server at `http://localhost:8080`.

4. **Open the Studio**:

- Open the configuration interface in your browser:

```bash
director studio
```

This launches the **Director** UI for proxy setup.

### Option 2: Install via Curl

1. **Install Director**:
- Run:

```bash
curl -LsSf https://director.run/install.sh | sh
```

This fetches the latest **Director** binary and sets it up in your PATH.

2. **Start the Quickstart**:

- Launch the configuration interface:

```bash
director quickstart
```

This opens a browser window at `http://localhost:8080`.

**Pro Tip**: If the browser doesn’t open, check the terminal for the URL or navigate to `http://localhost:8080`. Ensure port 8080 is free (`lsof -i :8080`).

## Step 2: Configure an MCP Proxy Server

You can set up an MCP proxy server using **Director**’s browser UI or CLI. The proxy acts as a middleman, routing LLM requests to MCP servers like Hacker News or Slack. Let’s explore both methods.

### Option 1: Configure via Browser UI

1. **Create a Proxy**:
- In the **Director** browser interface (`http://localhost:8080`), click **Create Proxy Server**.
- Name it, e.g., `my-proxy`. This is your hub for connecting MCP servers and clients.

![name the proxy server](https://assets.apidog.com/blog-next/2025/08/videoframe_10201.png)

2. **Add an MCP Server**:

- Search for the **Hacker News MCP server** in the search bar.
- Click **Add to Proxy**. No extra config needed for Hacker News—it’s ready to go!

![add an mcp server](https://assets.apidog.com/blog-next/2025/08/videoframe_17116.png)

3. **Save and Deploy**:

- Save your settings. **Director** spins up the proxy locally.

![save and connect to an mcp client](https://assets.apidog.com/blog-next/2025/08/videoframe_21488.png)

### Option 2: Configure via CLI

1. **Start the Gateway**:
- Ensure **Director** is running:

```bash
director serve
```

2. **Create a Proxy**:

- Run:

```bash
director create my-first-proxy
```

This sets up a proxy named `my-first-proxy`.

3. **Add an MCP Server**:

- Add the Hacker News server (or another, like `fetch`):

```bash
director add fetch --target=my-first-proxy
```

The `fetch` server is a simple MCP server for testing, similar to Hacker News.

4. **Connect Clients**:

- Link the proxy to your MCP clients:

```bash
director connect my-first-proxy --target=claude
director connect my-first-proxy --target=cursor
```

- Print manual connection details if needed:

```bash
director connect my-first-proxy
```

**Why Hacker News or Fetch?** These are lightweight MCP servers that expose tools for fetching data (e.g., top stories), making them great for testing.

## Step 3: Connect Your MCP Client

Now, let’s hook up your LLM client (Claude Desktop, Cursor, or VS Code) to the proxy. We’ll use **Claude Desktop** as an example, but the process is similar for others.

1. **Launch Claude Desktop**:
- Open **Claude Desktop** and go to the **Tools** section.
- Search for **Director** and select your proxy (`my-first-proxy` or `my-proxy`).
- You’ll see tools from the Hacker News or fetch server, like `list_top_stories`, under `director_my-first-proxy`.

![director proxy serrver](https://assets.apidog.com/blog-next/2025/08/videoframe_29558.png)

2. **Test It Out**:

- In Claude Desktop, try:

> Give me a list of the top stories on Hacker News using my MCP server.

- Claude calls the tools via **Director**, and you’ll see results in the chat. Check your terminal for **Director** logs showing the tool calls.

![get the result from the hackernews mcp server](https://assets.apidog.com/blog-next/2025/08/videoframe_50174.png)

**What’s Happening?** **Director** routes Claude’s request to the MCP server, fetches data, and sends it back to Claude—all locally and open-source.

## Step 4: Add More MCP Servers (Like Slack)

Want to expand your setup? Let’s add a **Slack MCP server** to your proxy, which requires extra configuration but is a breeze with **Director**.

1. **Access Director**:
- Use the browser UI (`http://localhost:8080`) or CLI.
- **Browser**: Select your proxy (`my-first-proxy`) and click **Add MCP Server**.
- **CLI**: Run:

```bash
director add slack --target=my-first-proxy
```

![search for the slack mcp server](https://assets.apidog.com/blog-next/2025/08/videoframe_65416.png)

2. **Configure Slack**:

- Slack needs authentication. **Director** prompts for:
- **API Token**: Create a Slack bot app at [api.slack.com/apps](https://api.slack.com/apps) with `chat:write` and `channels:read` scopes.
- **Channel ID**: Specify the target channel (e.g., `#general`).
- In the browser UI, enter these in the config fields. For CLI, pass them as flags:

```bash
director add slack --target=my-first-proxy --api-token=your-slack-token --channel-id=C123456
```

- Save or hit **Add to Proxy**.

![configure the slack mcp server](https://assets.apidog.com/blog-next/2025/08/videoframe_68743.png)

3. **Verify in Claude Desktop**:

- Refresh Claude Desktop’s **Tools** section under `director_my-first-proxy`.
- New Slack tools (e.g., `post_message`) appear alongside Hacker News tools.
- Test with:

> Give me a list of the top stories on Hacker News, summarize them, and post them to Slack.

- Claude fetches stories, summarizes them, and posts to Slack. Check **Director**’s terminal logs or `director logs my-first-proxy` for confirmation.

![test the slack mcp server](https://assets.apidog.com/blog-next/2025/08/videoframe_119337.png)

**Why Slack Needs More Config?** Slack’s MCP server interacts with external APIs, requiring secure tokens. **Director** handles this seamlessly via its UI or CLI.

## Step 5: Scale with Multiple Servers and Clients

**Director** shines when managing multiple MCP servers and clients. Here’s how to scale:

- **Add More Servers**:
- Use the browser UI or CLI to add servers like **GitHub MCP** (for repo management) or **MongoDB MCP** (for database queries). For CLI:

```bash
director add github --target=my-first-proxy --api-token=your-github-token
```

- **Director**’s UI guides you through any required configs (e.g., API keys or OAuth tokens).
- **Connect Multiple Clients**:
- Link additional clients like **Cursor** or **VS Code**:

```bash
director connect my-first-proxy --target=cursor
director connect my-first-proxy --target=vscode
```

- For **Cursor**, add to `~/.cursor/mcp.json`:

```json
{
  "mcpServers": {
    "director_my-first-proxy": {
      "command": "director",
      "args": ["proxy", "my-first-proxy"]
    }
  }
}
```

- For **VS Code**, install the **Director MCP Extension** from the marketplace and select `my-first-proxy`.
- **Manage Proxies**:
- Create separate proxies for different projects (e.g., `work-proxy`, `personal-proxy`):

```bash
director create work-proxy
```

- List proxies with:

```bash
director list-proxies
```

**Pro Tip**: For detailed CLI usage, check the [CLI Reference](https://director.run/docs/cli-reference).

## Troubleshooting Common Issues

- **Director Not Starting?** Ensure port 8080 is free (`lsof -i :8080`) and Node.js is installed for npm setups. Retry `director serve`.
- **MCP Server Not Found?** Verify the server exists in **Director**’s search index or check for typos. Some servers need internet for initial setup.
- **Authentication Errors?** Confirm API keys/tokens in **Director**’s config. For Slack, ensure the bot has `chat:write` permissions.
- **Client Not Seeing Tools?** Restart your client or toggle the proxy in **Director** (`director restart my-first-proxy`).
- **Slow Responses?** Local MCP servers rely on your hardware. Use 16GB+ RAM and a decent CPU for complex servers like GitHub.

## Why Use Director for MCP Connections?

**Director** is a developer’s dream for local AI workflows. Here’s why it rocks:

- **Open-Source Power**: MIT-licensed with 1.8k+ GitHub stars, **Director** is transparent and community-driven.
- **Privacy First**: Runs locally, keeping your data secure.
- **Flexible Setup**: Supports multiple MCP servers (Hacker News, Slack, GitHub) and clients (Claude, Cursor, VS Code) via UI or CLI.
- **Ease of Use**: Simplifies complex configs (like Slack’s OAuth) with an intuitive interface.

Users highlight **Director**’s versatility, noting it “makes MCP setup feel like magic” for Claude Code. Whether you’re a solo dev or building enterprise AI, **Director** saves time and hassle.

## Conclusion

You’re now equipped to connect any LLM to any MCP server using **Director**! From pulling Hacker News stories to posting Slack summaries, you’ve got a local, open-source AI powerhouse at your fingertips. Experiment with more MCP servers, tweak your proxy, or share your setup in the comments. Let’s keep the AI revolution local and open—happy coding!
