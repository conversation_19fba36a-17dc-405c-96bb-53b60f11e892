# What is a CORS Proxy? Use Cases and Examples

[CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) (Cross-Origin Resource Sharing) is a mechanism for a server to determine which origins have access to a resource. It was created to allow an exception to the strict security measure called [SOP](https://developer.mozilla.org/en-US/docs/Web/Security/Same-origin_policy) (Same Origin Policy).

This security measure helps prevent websites from making cross-origin requests, but it can make fetching data from remote APIs challenging, especially if you don’t control the API or resource. However, this sort of restriction can be overridden by using what’s known as a CORS proxy.

## What Is a CORS Proxy?

A CORS proxy is a service that helps you get around [CORS errors](https://corsfix.com/blog/common-cors-mistakes). It works by performing the request on your behalf on a server. If you’ve ever developed a web application, you’ve likely encountered a CORS error at some point.![CORS Error](https://assets.corsfix.com/rxg1odk.png)

*CORS Error*

When you send a request to a CORS proxy with your target URL, the proxy fetches the data from the target URL and sends it back to you.![CORS Proxy flow](https://assets.corsfix.com/zzswj8o.png)

*CORS Proxy flow*

As a CORS proxy performs the request on a server, it can bypass the CORS restrictions, since CORS is only enforced in the browser. Your app can then access the resource without any issues, as the CORS proxy returns the data along with the necessary [CORS headers](https://corsfix.com/cors-headers).

Terminal window

```
Access-Control-Allow-Origin: your-origin.com
```

*CORS header*

## When to Use It?

You’ll want to use a CORS proxy if:

- The API or resource you’re trying to fetch doesn’t provide CORS headers.
- You don’t have control over the API or resource you’re trying to fetch.
- You’re working on a web application without a backend.

> Ideally, you should use a CORS proxy for fetching public unauthenticated data. For credentialed requests (e.g., API keys), you should use a CORS Proxy that supports secrets like Corsfix.

## How to Use It?

Whenever you’re stuck with a CORS error while fetching external resources you don’t control, you can add the proxy URL before your target URL. Here’s how to do it using Corsfix:

```
fetch("https://proxy.corsfix.com/?https://example.com/api")  .then((response) => response.json())  .then((data) => console.log(data))  .catch((err) => console.error(err));
```

*CORS Proxy usage example*

Your request will be sent to the Corsfix server, which will then fetch the data from your target URL and return it to you.

## Conclusion

CORS proxy is a service to fetch data from an API without CORS errors. It is a simple and effective way to get around the CORS restrictions and access the data you need.

If you need a CORS proxy, you can use [Corsfix](https://corsfix.com/), we have a [free tier](https://corsfix.com/docs/free-tier) that you can use to get started immediately. When you are ready to go to production, you can choose any of our [plan](https://corsfix.com/pricing) based on your needs.
