Apidog provides a powerful mock feature that allows you to simulate API responses with fine-grained control. This document introduces the two main ways to customize your mock data:

1. **Customize Specific Fields**: Control certain fields in the mock response, with smart mock for other fields.
2. **Full response customization**: Define the entire mock response with expectations (supports fixed, conditional, and dynamic responses).

## Customize Specific Fields

Sometimes, you may want to define specific values for certain fields in the response, while letting <PERSON><PERSON><PERSON> auto-generate (smart mock) the remaining fields. Apidog provides flexible ways to handle this:

### How to Set Custom Field Values

#### 1. Directly Input Values

Specify a fixed value directly in the endpoint spec’s mock field, and Apidog will always return this value for that field. All unspecified fields will use <PERSON>pidog’s smart mock generation.

**Example:**
    <Frame>
    ![image.png](https://api.apidog.com/api/v1/projects/544525/resources/353916/image-preview)
    </Frame>

#### 2. Use Faker.js Dynamic Values

You can use Apidog’s dynamic values (based on Faker.js) to generate realistic random data. Use the syntax:

```
{{$category.method}}
```
Examples:
- A random full name: `{{$person.fullName}}`, results like `<PERSON>`
- An email address: `{{$internet.email}}`, results like `<EMAIL>`
- A product name: `{{$commerce.productName}}`, results like `Elegant Plastic Bike`

You can directly select items in the dropdown.
  <Frame>
  ![image.png](https://api.apidog.com/api/v1/projects/544525/resources/353921/image-preview)
  </Frame>

#### 3. Use Faker Methods With Parameters

You can pass parameters to dynamic value methods for more specialized data, following Apidog’s enhanced Faker.js syntax.  
For example:

- **Generate an integer between 0 and 10,000:**
  ```
  {{$number.int(min=0,max=10000)}}
  ```
- **Generate a phone number in a readable style:**
  ```
  {{$phone.number(style='human')}}
  ```
- **Generate an integer that is a multiple of 3:**
  ```
  {{$number.int(multipleOf=3)}}
  ```
- **Pick a random element from an array:**
  ```
  {{$helpers.arrayElement(['red','blue','green'])}}
  ```
- **Generate a date within a range (custom formats supported):**
  ```
  {{$date.between(from='2024-01-01',to='2024-12-31',format='yyyy-MM-dd')}}
  ```

Explore the complete list of modules, methods, and their parameters in the [Dynamic Values Modules documentation](https://docs.apidog.com/dynamic-values-modules-1938252f0).

#### 4. Concatenate Multiple Dynamic Expressions (Generate a Full Address Example)

You can freely combine static text and multiple dynamic expressions to generate complex field values.

For example, to generate a realistic full address in one string, you can write:
```
{{$location.streetAddress}}, {{$location.city}}, {{$location.state}}, {{$location.zipCode}}, {{$location.country}}
```
This will output a result similar to:
```
"8507 Hudson Alley, Rochester, Wisconsin, 96512, United States"
```
Each part is dynamically generated, creating a unique and realistic address every time the mock API is called.

:::tip[Mock locales]
Apidog supports customizable mock locales, allowing you to generate dynamic test data in different languages and formats that match your target region or audience. To learn more about configuring mock locales, visit [Mock language (Locales)](apidog://link/pages/965986).
:::

## Customize the Entire Mock Response (Mock Expectations)

If you need to return a specific or highly customized mock response, use "mock expectations." This gives you full control over what the mock service returns.

### Setting a Mock Expectation

#### Background
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/352097/image-preview)

#### Returning Fixed Data

You can set an unconditional expectation to always return the same data.

**Steps**  
1. Click "New expectation".
2. Add an expectation name. Leave the conditions blank.
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/343601/image-preview" style="width: 640px" />
    </p>
3. Fill in the Response data you want to return, then save.
4. Copy and use the provided mock URL to access this endpoint.

#### Returning Conditional Data

You can return different mock data based on request parameters. Apidog's mock expectations support parameter-based conditions.

- Add multiple expectations with different conditions.
- The mock engine matches incoming requests to these conditions (from top to bottom), returning the first matching expectation.
- If no conditions match, Apidog falls back to the Mock method priority set in Project Settings > Feature Settings > Mock Settings.

**Supported Condition Types**

- Query parameters
- Path parameters
- Header parameters
- Cookie parameters
- Body parameters (JSON only)

For body parameters, specify a JSON path to the property. Conditions are combined as an intersection (all must match).

> **Note**
> - Body parameters support only JSON, not XML.
> - Parameter conditions cannot use `{{variables}}`.
> - If you use body parameters for expectation matching, the request body format must match the API spec (e.g., form-data, JSON, etc.).
> - You can also add IP address conditions to restrict responses to specific IPs.

<p style="text-align: center">
<img src="https://api.apidog.com/api/v1/projects/544525/resources/343619/image-preview" style="width: 640px" />
</p>

#### Returning Dynamic Mock Data

Mock expectations support dynamic, randomized data using [Faker.js](https://fakerjs.dev/) and [Nunjucks](https://mozilla.github.io/nunjucks/) syntax.

##### Example

```json
{
    "data": [
        {% for i in range(0, 20) %}
        {% if i>1 %},{% endif %}
        {
            "id": {{i}},
            "firstname": "{{$person.firstName}}",
            "lastname": "{{$person.lastName}}"
        }
        {% endfor %}
    ],
    "success": true
}
```
The above generates:
- An array of 20 user objects (id = 0 to 19)
- Each with randomly generated "firstname" and "lastname"
- A constant `"success": true`

- `{{$...}}` syntax invokes Faker.js to generate random values.  
- Nunjucks is used for loops (`{% for ... %}`) and logic.
- `{{i}}` refers to a Nunjucks loop variable—not an Apidog variable.

> **Notes**
> - Apidog uses `{{$...}}` for Faker.js expressions (not the native JS call syntax).
>   - **Faker.js (native):** `faker.person.firstName()`
>   - **Apidog:** `{{$person.firstName}}`
> - Apidog project/environment variables (like `{{variable}}`) are not available in mock expectations.
> - Full Faker.js and Nunjucks documentation: [Faker.js](https://fakerjs.dev/), [Nunjucks](https://mozilla.github.io/nunjucks/)

---

### More Features

- **Custom Response Headers**  
  You can add custom headers to a mock expectation, useful for simulating authentication, pagination, or any other API contract.
  <p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/343623/image-preview" style="width: 640px" />
  </p>

- **Advanced Response Properties**  
  In the "More" tab inside mock expectations, you can set:
   - **HTTP status code:** Simulate error or special cases (default is 200).
   - **Response delay:** Simulate slow API responses by adding a delay (in milliseconds).
   <p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/343624/image-preview" style="width: 640px" />
  </p>

- **Enable/Disable Expectations**  
  Toggle each expectation on/off separately for local and cloud mock environments from the expectation list.

## Summary

With Apidog custom mock, you can:
- Mix specific mock values with auto-generated data for flexibility.
- Define full fixed, conditional, or dynamic responses for comprehensive test scenarios.
- Leverage Faker.js and Nunjucks to create powerful, realistic, and variable mock data.
- Simulate headers, delays, status codes, and more!

Explore these features to create robust, high-fidelity API simulations that mirror your production APIs!