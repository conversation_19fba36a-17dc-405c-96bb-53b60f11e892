Import cURL
Utilizing Traffic Capture Tools
Make use of traffic capture tools such as Chrome, Charles, or Fiddler to capture and document web traffic. Once the data is captured, it can be exported in cURL format for analysis or reuse.

Chrome
1.
Open the Developer Tools by pressing Ctrl+Shift+I (Windows/Linux) or Cmd+Option+I (Mac).

2.
Navigate to the 'Network' tab.

3.
Start capturing traffic.

4.
Locate the desired API request, right-click on it, select 'Copy', and then choose 'Copy as cURL'.

5.
Refer to the screenshot below for guidance.



Charles
1.
Start capturing traffic.

2.
Find the API of interest.

3.
Right-click on the request and select 'Copy cURL Request'.

4.
See screenshot below for more details.



Fiddler
1.
Begin traffic capture.

2.
Navigate to the top-left menu.

3.
Go to File, select Export Sessions, and then Selected Sessions.

4.
Choose cURL script and proceed to save the file as a .bat file.

5.
Open this .bat file using a text editor to copy its contents.

Import Captured Data
1.
Hover over or click the + button adjacent to the search box on the left side. From the drop-down menu, select "Import cURL". Alternatively, you can use the shortcut key Ctrl(⌘) + I.


2.
In the dialog that appears, paste the cURL format data previously copied from your selected tool.


3.
Click the 'OK' button. You will see the captured packet data loaded into the quick debugging API interface.


4.
This quick debugging interface allows you to directly test and debug the API. Once confirmed, click the 'save' button to save the API configuration permanently.


This streamlined process ensures efficient handling and reutilization of captured web traffic data.

