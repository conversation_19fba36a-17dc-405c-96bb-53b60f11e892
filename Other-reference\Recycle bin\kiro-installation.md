# Installation

## [Copied!](https://kiro.dev/docs/getting-started/installation/#download-kiro)Download Kiro

Getting started is simple:

1. Go to [kiro.dev](https://kiro.dev/) and download the installer
2. Open the downloaded file and follow the installation instructions for your operating system (Windows, macOS, or Linux).
3. Open Kiro IDE and start coding!

## [Copied!](https://kiro.dev/docs/getting-started/installation/#first-run)First run

1. When you open Kiro for the first time you will be asked to login with a provider of your choice that include social and AWS login options. Learn more about the [auth methods](https://kiro.dev/docs/reference/auth-methods).

2. Once logged in, you can choose to [import your VS Code](https://kiro.dev/docs/guides/migrating-from-vscode) settings and extensions. If you're using another editor, you can skip this step. Select your preferred theme from the available options, then allow <PERSON><PERSON> to set up shell integration, enabling the agent to execute commands on your behalf.

3. Finally, you'll arrive at the welcome page. Open a project to get started and proceed with [your first project](https://kiro.dev/docs/getting-started/first-project).

## [Copied!](https://kiro.dev/docs/getting-started/installation/#language-support)Language support

Kiro supports most popular programming languages. We have guides that help set up your environment and best practices for the following languages:

- [TypeScript and JavaScript](https://kiro.dev/docs/guides/languages-and-frameworks/typescript-javascript-guide)
- [Java](https://kiro.dev/docs/guides/languages-and-frameworks/java-guide)
- [Python](https://kiro.dev/docs/guides/languages-and-frameworks/python-guide)
