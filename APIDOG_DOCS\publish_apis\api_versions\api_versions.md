Today, open APIs have become a crucial means for technical teams to provide services and data to external parties. However, as business evolves and underlying technologies continue to advance, the endpoints offered by teams are inevitably subject to multiple version Springs. This change is unavoidable, as teams need to optimize existing functionalities, introduce new features, and address bugs according to market demands and technological progress.

Managing multiple API versions is crucial for catering to different user groups, ensuring both compatibility and innovation. This approach allows teams to deliver a stable and evolving service while maintaining version control. Effective management minimizes disruptions for existing users, ensuring business continuity and reliability.

## Use Case

Teams may need to offer multiple API versions concurrently—such as the latest, stable, and long-term maintenance versions—to ensure minimal disruption for users on older versions.

## Hightlights of API Version Feature

- **Full Version Creation**

  A new API version can be created based on an existing API version, containing copies of all endpoints from the original version. After creation, each endpoint within the version can be modified as needed. Alternatively, you can start from a blank API version and manually add endpoints.

- **Comprehensive Sharing**

  Select one or more API versions from your project to publish. All endpoints within the selected versions will be visible to users. When publishing, the API version's display name and slug settings can be configured to enhance reader experience.

- **One-Click Switching**

  If multiple API versions have been published, users can switch between them via a version selector located next to the project name on the public page. Clicking on a version will display all related endpoints and content for that specific version.

:::tip [Difference Between "API Version" and "Sprint Branch"]

- **API Version**: Designed for external release, especially when major changes to the endpoints cause significant incompatibilities between versions. It contains all endpoints, not just the modified or newly added ones.

- **Sprint Branch**: Used internally by development teams, aligning with the "sprint" concept in agile development. Each sprint typically creates a branch that includes only the new or modified endpoints, excluding those unchanged during that sprint. 

:::