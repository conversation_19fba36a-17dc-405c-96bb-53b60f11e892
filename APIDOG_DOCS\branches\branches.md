Branches in Apidog
Maintaining and managing the consistency of endpoint projects can be a challenging task during rapid sprint development.

The sprint branch feature in Apidog is designed to offer a flexible mechanism for managing endpoint sprints. It allows team members to iteratively upgrade existing endpoints and swiftly develop new ones, all without disrupting other team members or affecting released endpoints.

Use Cases
Agile API Development

The sprint branch feature allows developers to design and debug endpoints in isolated branches, ensuring the main branch remains stable. This separation ensures that testing and continuous integration processes are not disrupted, maintaining the consistency of the original endpoints until new versions are merged.

Agile API Testing

In the sprint branch, updated endpoints are automatically flagged within testing scenarios. Testers can quickly duplicate, adjust, and execute automated tests for the endpoints to be tested within the sprint branch, ensuring that the changed endpoints pass the new testing tasks.

Key Features of Sprint Branches
Version Control

Each sprint branch can be viewed as an independent version of the endpoint specification, including schemas, response components, and other data. Data between branches does not affect each other. You can create corresponding branches for different needs, separating production versions from development versions, ensuring the accuracy and consistency of the main branch.

Content Protection

Sprint branches can be set asprotected. Once a sprint branch is set as protected, regular content maintainers won’t be able to edit it directly. Instead, they’ll need to create or update content in a separate sprint branch and submit a MR(Merge Request). This request must be reviewed and approved by an administrator before any modifications are allowed, ensuring the stability and reliability of the sprint branches.

Parallel Collaboration

Different team roles can work concurrently on separate branches, enhancing work efficiency and collaboration flexibility without causing interference.

Quick Merging

Once the functional development in a new branch is complete, developers can seamlessly merge the sprint branch back into the main branch, integrating new features without introducing unnecessary risks during updates.

Automatic Matching

The sprint branch feature automatically identifies updated endpoints in the new branch and marks them in relevant testing scenarios. Testers can swiftly create test scenarios for the new or modified endpoints within the sprint branch, ensuring that all changes meet functional expectations.

How to access the sprint branch?
To access the sprint branch within your project, switch to the sprint branch by clicking the sprint branch switch located next to the APIs. This allows you to view and work on the content specific to the sprint branch.

Introduction to Apidog Sprint Branch

Next, you can continue reading Create a new sprint branch.

