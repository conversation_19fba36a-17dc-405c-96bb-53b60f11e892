Apidog is an API collaboration platform that enhances productivity for back-end, front-end, and testing teams. For front-end developers, Apidog serves as a tool for API invocation. Specifically, it encompasses three primary functions: 

1. [Sending API requests](apidog://link/pages/626721), 
2. [Reading API documentations](apidog://link/pages/631148),
3. [Mocking APIs](apidog://link/pages/617869).

If you're a frontend developer, this video is a great resource to dive into the core features of Apidog and see how they can enhance your workflow:

<Video src="https://www.youtube.com/watch?v=qIG4GtXsRNA"></Video>

## Sending API requests

Apidog offers an intuitive user interface that facilitates rapid API invocation. Similar to established tools like Postman, developers can create a `New Request` by specifying essential components such as headers, request bodies, and parameters. Upon configuration, the API call can be executed by clicking the `Send` button, enabling immediate testing and validation of endpoints.
<Background>
![img](https://assets.apidog.com/blog-next/2024/12/send-api-quick-request.png)
</Background>

### Support for Multiple Protocols

Beyond conventional HTTP protocols, Apidog supports a diverse range of communication protocols including:

- [WebSocket](apidog://link/pages/629877)
- [GraphQL](apidog://link/pages/629866)
- [SOAP/WebService](apidog://link/pages/629910)
- [SSE](apidog://link/pages/629889)

## Reading API Documentations

For front-end developers, the ideal scenario is having a complete and well-documented API provided by the back-end team, so you don’t need to construct API requests yourself. 

If your team collaborates in Apidog, it will generates very clear and visually appealing API documentation, which is comfortable to read. 

<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/344101/image-preview)
</Background>

In this documentation, clicking the "Try it out" button on the top right opens an execution interface, akin to embedding a client in your web browser. You can modify parameters and test requests with ease, eliminating the need for Postman altogether.

<Background>
<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/344103/image-preview" style="width: 440px" />
</p>
</Background>

Moreover, the documentation supports a "generate request code" feature, offering code snippets in over 30 different languages ready to be copied straight into your codebase. The response module also has a "generate code" button, which automatically creates data structure code for directly utilizing the returned API data. It’s a superb API documentation tool.

:::tip[]
Learn more about [View API documentations](apidog://link/pages/631148).
:::

## Mocking APIs

There are two steps to mock the API.

1. Get the endpoint spec.
2. Get the mock URL.

### Step 1: Get the endpoint spec

There are three scenarios:

1. The backend has already specified this endpoint in Apidog, so you can directly use it.
2. Your backend is not using Apidog but has provided an API spec, which you can [import into Apidog](apidog://link/pages/633884).
3. You don't have an API spec but only a response JSON. In this case, you can directly [import the JSON file](https://dev.to/apidog/how-to-mock-apis-in-40s-and-build-frontend-apps-10x-faster-4d3i).

### Step 2: Get the mock URL

you'll find a mock module within the endpoint details. This module provides you with a mock URL. Simply click on the mock URL to copy it.
<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/348769/image-preview)
</Background>
Next, paste this URL into your browser. 
<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/348770/image-preview)
</Background>
Congratulations! You now have a working mock API! 

It's not just a static response; every time you refresh the page, you'll notice that the content changes, simulating the behavior of a real API. This dynamic response feature is invaluable for testing and developing frontend applications in a realistic environment without waiting for the backend to be ready.
<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/348771/image-preview)
</Background>

### How it works

Apidog operates as a robust platform for API development and mocking by intelligently interpreting your input JSON data. Here's a closer look at its functionality:

- **JSON Parsing and Data Schema Generation**:

When you paste your JSON into Apidog, it parses the structure to create a corresponding data schema, establishing an API specification. This schema defines the expected format and type of data for each property in the JSON.

- **Mock Engine**:

Apidog includes an integrated mock engine, effectively functioning as a lightweight mock server that runs locally on your PC. This engine uses your API specification to dynamically generate mock data.

For dynamic responses, Apidog utilizes the names of your JSON properties to apply appropriate mock functions from [faker.js](https://github.com/Marak/Faker.js/). These mock functions are responsible for producing realistic, random data. Each browser refresh triggers the generation of new mock data, giving the illusion of interacting with a real API.

:::tip[]
Learn more about [Mock data automatically](apidog://link/pages/618190).
:::

### Static Mock

If you prefer consistent outputs on every request, you have the option to switch the mock data from dynamic to static effortlessly:

- During the JSON parsing process, Apidog saves the data as a response example.
- To make use of this static option, go to **Settings**, navigate to **Feature Settings**, and access **Mock Settings**.
- Change the default mock type to "Response example first." By doing this, the mock API will consistently return the saved response example, making your test data stable and predictable.

<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/348772/image-preview)
</Background>

:::tip[]
Learn more about [Mock priority sequence](apidog://link/pages/618208).
:::

### Custom Mock

If the automatically generated mock output for a specific property doesn't meet your requirements, Apidog allows you to customize this property's mock behavior easily.

- Navigate to the **Response Schema** section for the relevant endpoint.
- Locate the property whose mock needs adjustment. In the mock field of this property, choose a suitable function that aligns with your desired output.
  
  <Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/348773/image-preview)
</Background>

:::tip[]
Learn more about [Mock expectations](apidog://link/pages/618204).
:::

### Cloud Mock

The mock API we've discussed so far is hosted locally. However, if you need to share it with others or give access outside your local environment, Apidog provides a cloud mock feature.

To enable this feature, Go to **Settings**, navigate to **Feature Settings**, and access **Mock Settings**. Toggle on the **Cloud Mock** option.

<Background>

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/348774/image-preview)
</Background>
Once enabled, your API will be accessible from the cloud. Simply copy the cloud mock URL provided and open it in a browser to access it. This feature makes it easy to share your mock API with team members, stakeholders, or testers who need to access it remotely.

For example, try visiting this cloud mock address to see it in action:
`https://mock.apidog.com/m1/644296-613644-default/user/1`
<Background>
![image.png](https://api.apidog.com/api/v1/projects/544525/resources/348775/image-preview)
</Background>

:::tip[]
Learn more about [Cloud mock](apidog://link/pages/621066).
:::

These features enable frontend developers to work more independently and efficiently, reducing dependencies on backend development timelines. By providing robust tools for both API interaction and data mocking, Apidog streamlines the frontend development process, allowing for faster iterations and more thorough testing of frontend applications against various API scenarios.
