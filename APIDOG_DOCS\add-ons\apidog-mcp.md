wThe Apidog MCP Server allows you to use your API documentation from Apidog projects as a data source for AI-powered IDEs like Cursor. This means Agentic AI can directly access and work with your API documentation, speeding up development and making your workflow more efficient.
With Apidog MCP Server, developers can leverage the AI assistant to:
- Generate or modify code based on API documentation.
- Search through API documentation content.
- And much more! Let your imagination and your team's creativity guide you. 😜
🎯 How Apidog MCP Server Works?
Once the Apidog MCP Server is set up, it automatically reads and caches all API documentation data from your Apidog project on your local machine. The AI can then retrieve and utilize this data seamlessly.

Simply instruct the AI on what you’d like to achieve with the API documentation. Here are some examples:

1. Generate Code: "Use MCP to fetch the API documentation and generate Java records for the Product schema and related schemas".
2. Update DTOs: "Based on the API documentation, add the new fields to the Product DTO".
3. Add Comments: "Add comments for each field in the Product class based on the API documentation".
4. Create MVC Code: "Generate all the MVC code related to the endpoint /users according to the API documentation".
🚀 Installation Guide
Prerequisites
- Node.js (version 18 or higher, preferably the latest LTS version).
- An IDE that supports MCP, such as:
  - Cursor
  - VSCode + Cline plugin
Installation Steps
Step 1. Generate an Access Token in Apidog
- Open Apidog, hover over your profile picture at the top-right corner, and click Account Settings > API Access Token.
- Create a new API access token (refer to the help documentation for details).
- Copy the access token and replace <access-token> in the configuration file below.
Step 2. Get the Apidog Project ID
- Open the desired project in Apidog.
- Click project Settings at the left sidebar, and copy the Project ID from the Basic Settings page.
- Copy the project ID and replace <project-id> in the configuration file below.
Step 3. Configure the IDE
- Copy the following JSON configuration code to add to the MCP configuration file in your IDE:
{
  "mcpServers": {
    "API specification": {
      "command": "npx",
      "args": [
        "-y",
        "apidog-mcp-server@latest",
        "--project-id=<project-id>"
      ],
      "env": {
        "APIDOG_ACCESS_TOKEN": "<access-token>"
      }
    }
  }
}
If you're on Windows and the configuration file above isn't working, try using the configuration file below instead:
{
  "mcpServers": {
    "API specifiacation": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "apidog-mcp-server@latest",
        "--project-id=<project-id>"
      ],
      "env": {
        "APIDOG_ACCESS_TOKEN": "<access-token>"
      }
    }
  }
}
- Add the copied JSON configuration code to the MCP file in your IDE:
  - For Cursor: Add to the global ~/.cursor/mcp.json or the project-specific .cursor/mcp.json.
  - For Windsurf: Add to ~/.codeium/windsurf/mcp_config.json.
  - For Cline: Open the Cline panel > MCP Server > Configure MCP Server.

---
Important Notes
- Replace <access-token> and <project-id> with your personal Apidog API access token and project ID.
- Name the MCP Server something like "API Documentation" or "xxx API Documentation" to help the AI recognize its purpose. Avoid generic names like "Apidog" or "Apidog MCP", as it might confuse AI.
- If you need to work with API documentation from several projects, simply add multiple MCP Server configurations to the configuration file. Each project should have its own unique <project-id>. For clarity, name each MCP Server following the format "xxx API Documentation".
- If your team syncs the MCP configuration file to a code repository, it is recommended to remove the line "APIDOG_ACCESS_TOKEN": "<access-token>" and instead, configure the APIDOG_ACCESS_TOKEN as an environment variable on each member's machine to prevent token leakage.
- For users of the on-premise deployment, add the following parameter to the MCP configuration file in your IDE: "--apidog-api-base-url=<API address of the on-premise server, starting with http:// or https://>". Additionally, ensure that your network can access www.npm.com properly.
- In addition to the Apidog project, Apidog MCP Server also has the ability to directly read Swagger or OpenAPI Specification (OAS) files. To use this feature:
  - Remove the --project-id=<project-id> parameter.
  - Add the--oas=<oas-url-or-path>parameter, such as npx apidog-mcp-server --oas https://petstore.swagger.io/v2/swagger.jsonor npx apidog-mcp-server --oas ～/data/petstore/swagger.json.


---
❓ Help and Support

The Apidog MCP Server is currently in beta. We’d love to hear your feedback and suggestions! Join our Discord or Slack community for support and updates.


🚀 Supercharge Your Vibe Coding with Apidog MCP Server!

Apidog MCP Server lets you feed your API documentation from your Apidog projects directly into Agentic AI, making your vibe coding experience smoother, smarter, and faster than ever! Whether you’re using Cursor, Cline, this tool levels up your AI-powered development workflow.

🔥 Why you’ll love it:

✨ Instant API Access for AI – The AI assistant can pull real-time API documentation from Apidog, making code generation and modification effortless.

⚡ Faster Development, Less Context Switching – No more digging through docs manually! AI fetches the details while I stay in the flow.

💡 Perfect for Vibe Coding – I just tell the AI what I need, and it handles the boring stuff—letting me stay creative and focused.

📂 Seamless Data Integration – Connect effortlessly to Apidog projects, public API docs published by Apidog, and any OpenAPI (OAS) files using Apidog MCP, making AI-powered development smoother and smarter! 🚀

🛠 Super Easy Setup – Took me just a few minutes to get started, and the boost in productivity was immediate.

🚀 Make AI work for you and experience true coding flow: 🔗 [Insert Link]


Benefits of Apidog MCP Server:
- Integrated API Documentation Access: AI assistants can directly access API documentation from Apidog, enabling efficient code generation and modification based on accurate specifications.
- Enhanced Development Efficiency: Eliminate manual documentation searches as AI retrieves relevant API details automatically, maintaining your development workflow continuity.
- Streamlined AI-Assisted Development: Simply provide instructions to the AI regarding your requirements, allowing it to handle implementation details while you focus on higher-level design and creative aspects.
- Comprehensive Data Source Integration: Seamlessly connect to Apidog projects, published Apidog documentation, and standard OpenAPI (OAS) files through Apidog MCP, creating a more intelligent and efficient AI-powered development environment.
- Simplified Configuration Process: Implementation requires minimal setup time, delivering immediate productivity improvements across your development workflow.