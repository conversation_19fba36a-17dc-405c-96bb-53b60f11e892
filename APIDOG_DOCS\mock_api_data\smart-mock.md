Apidog supports direct data mocking based on API spec without any additional configuration. This is called Smart mock.

Smart mock data comes from three sources:
a) Mock expressions corresponding to property names.
b) Mock fields in the response spec properties.
c) JSON Schema in the response spec.

## Automatic mocking by name
Smart mock's core algorithm automatically matches mock data based on the property's type and name. Apidog provides a series of built-in matching rules. If the type and name match a rule, data will be mocked according to that rule.

You can see these built-in rules in Settings - General settings - Feature settings - Mock settings. Built-in rules use Wildcard or RegEx methods to match name strings.

![image.png](https://api.apidog.com/api/v1/projects/544525/resources/343590/image-preview)

If the built-in rules are insufficient, you can create Custom matching rules. Click New to create a new matching rule. Properties meeting the Condition details will generate data according to the set mock expression. 

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/343593/image-preview" style="width: 640px" />
</p>

If the property name doesn't match any rule, a default mock value will be generated based on the property type.

## Mocking according to the mock field
If there's a value in the mock field of a property in the response spec, this value will override the value from mocking by name.
In this mock field, you can directly fill in a fixed value or write a Faker statement.

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/343594/image-preview" style="width: 640px" />
</p>

## Mocking according to JSON Schema
Generated mock data will also be constrained by JSON Schema.

For examples: 
- If a string field named 'name' has auto-mocked data "Richard", but JSON Schema limits string length to 3-5 characters, the final mock data for name will be "Richa".
- If a string field named 'status' has enumerated values in JSON Schema ("sold", "pending", "available"), the final mock data for status will be one of these three values.
- If a integer field has set minimum and maximum values, the final mocked data will be between the boundaries.
- If an array field has set minimum and maximum element counts, the final mocked data will comply with these limits.

In summary, all property settings will be reflected in the final mock data, ensuring it always conforms to the JSON schema.


:::tip[Mock locales]
Apidog supports customizable mock locales, allowing you to generate dynamic test data in different languages and formats that match your target region or audience. To learn more about configuring mock locales, visit [Mock language (Locales)](apidog://link/pages/965986).
:::