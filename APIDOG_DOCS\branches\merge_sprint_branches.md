Merge sprint branches
Once endpoint definitions are developed and deployed within a sprint branch, you have the option to merge some or all of the endpoint changes from the sprint branch into the main branch.

Merging sprint branches
When you are in a sprint branch, you can see the "Merge" button in the branch switch next to APIs. You can also see the Merge to main button at the right bottom of the APIs.

Apidog Merging Sprint Branches Entry Point
In automated testing, individual test scenarios require separate merging and do not merge automatically with resources in APIs. While in a sprint branch, you can find the 'Merge to Main' dropdown option by hovering over the ... button next to a test scenario in automated testing.

Apidog Merging Test Scenario Entry Point
Merging sprint branches into unprotected main branch
If the main branch is not set asprotected, users with project merge permissions can review the changes and directly merge them into the main branch.

Overview of pending merges
In APIs, clicking the "Merge" button opens the merge overview popup.

Apidog Merge Overview
In this popup, you will see key elements:

Filter: Use the filter to view only the resources in the current sprint branch that have been modified compared to the main branch, or view all resources in the current sprint branch. By default, modified resources are shown to help users focus on changes.

Merging Preview: This section displays the final effect on the main branch directory after merging the current sprint branch. Use this directory to confirm the placement and content of resources post-merge. Resources in the directory are marked with color-coded dots (gray for unchanged, orange for modified, green for new) to indicate their status. Use the checkboxes on the right to select or deselect resources for merging into the main branch.

Post-Merge Endpoint Status: Choose how to adjust the status of the merged resources in the main branch. Options include Follow the Current Branch, Follow Main, or Specified Status.

Detailed review of pending merges
In the merge overview popup, you can perform additional operations on the folder content to review whether the resources should be merged into the main branch. Clicking a resource expands the popup to show detailed content, aiding in the merge decision.

Different resources have different merge logic and details:

New Resources:

New Resources refers to resources created in the current sprint branch, not present in the main branch.

If merged, the resource will be created in the main branch at the specified location.

Click to view the complete content of this resource, as shown in the image below.

Apidog New Resource Details
Modified Resources:

Modified Resources refers to resources that is forked from the main branch into the current sprint branch and associated with the main branch resource.

If merged, the main branch resource will be overwritten.

Click to see the changes of this resource compared with the main branch resource, as well as the complete content, as shown in the image below.

Apidog Modified Resource Details
Unchanged Resources:

Unchanged Resources refers to resources that is forked from the main branch without any modifications in the current sprint branch.

Unchanged resources cannot be selected for merging (and don't need to be).

Clicking shows no detailed content for this resource, as shown in the image below.

Apidog Unchanged Resource Details
Clicking the merge button at the bottom right will merge all selected resources from the folder into the main branch according to the sprint branch settings. Note that if a parent resource is not selected, none of its child resources can be merged individually.

After merging, a toast message confirms the operation's success, closes the merge popup, and opens a summary popup to review the changes made to the main branch.

Viewing merge details / Reverting merges in the main branch
In the main branch, accessing the Change History of a resource displays the content modified through merges, allowing users to view detailed modification. Additionally, users can compare the content with that of other versions, facilitating easy tracking and rollback of changes.

Merging sprint branches into protected main branch
When merging changes from a sprint branch into a protected main branch, a user with only the editing permissions must create a merge request. The project administrator will then review and approve the request before the changes are applied.

Creating a merge request
When you clickMergein the project, it works just like merging into an unprotected main branch, allowing you to view all the changes in the current branch. From here, you can select the specific resources you want to merge into the main branch. Once selected, click theCreate Merge Requestbutton at the bottom-right corner to submit the request for review.

Reviewing a merge request
After a merge request is created, reviewers will see a clear notification in Project Overviewindicating a new merge request has been submitted. To review it, go toProject Overview>Merge Requests, where you can view a list of requests along with detailed information.

Clicking on a pending merge request allows the reviewer to evaluate the contents of the merge request. They can compare the content before and after the merge in detail and decide whether to approve the request. If they decide to move forward with the merge, they can click theMergebutton at the bottom right, which will merge all the changes from the current request into the main branch.

The merging process follows the same logic as merging into an unprotected main branch.

Once the merge is completed, the changes will be merged into the main branch, and there will be an overview table indicating the modifications made to the main branch.

The status of the merge request will also be updated toMerged. Clicking on it will show the details of the merged request.

Modifying a submitted merge request
Occasionally, after submitting a merge request, reviewers may request changes or adjustments might be needed before approval. In such cases, you can directly modify the content in the sprint branch. These changes will automatically sync with the existing merge request, eliminating the need to create a new one. The modifications will be indicated on the merge request review page.

Rejecting a merge request
If the reviewer decides that the merge request is not suitable for the sprint branch, they can clickRejecton the merge request review page. Once rejected, the merge request will be marked asClosed. To make changes and attempt merging again, a new merge request must be created.

Clicking on a closed merge request will show all the details.

Merging automated test scenarios
Automated test scenarios and endpoints are currently merged independently and separately. Test scenarios need to be merged individually, without an overall merge page.

Important Notes:
1.
Please merge the resources in APIs first before merging test scenarios. Failing to do so may cause related test scenarios in the main branch to run abnormally after merging.

2.
Currently, only project administrators have the ability to merge test scenarios into the protected main branch. However, we plan to implement a new feature that will allow users to submit merge requests for test scenarios, similar to the existing feature for endpoint merge requests. This will make it easier to merge test scenarios into the protected main branch in the future.

When you are in a sprint branch, hover your mouse over the ... button of a test scenario in automated testing to see the "Merge to Main" option.

Clicking this opens the test scenario merge popup, displaying the following information:

Basic Information: Shows the sprint branch test scenario to be merged and the associated main branch test scenario (if any).

Last Run Result: Displays the last manual run result of the test scenario in the sprint branch. Hovering over the icon shows a summary of the results. Testers can use this information to decide whether to proceed with the merge. If the result shows Untested or Failed, it is recommended to ensure the test scenario passes the test completely before merging.

Merge Action: Options include Overwrite and Add. If the test scenario was forked from the main branch, it will be merged by overwriting the main branch resource; if the test scenario is new, it will be added to the main branch.

