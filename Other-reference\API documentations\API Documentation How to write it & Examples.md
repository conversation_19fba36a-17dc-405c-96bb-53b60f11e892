# API Documentation: How to write it & Examples



When you buy a new product it comes with a manual to instruct you in how to use it. You wouldn’t take home and unbox your new games console without expecting there to be a manual for setup, use, and maintenance. When customers don’t know how to use products, they are less likely to be retained by the company or buy other products in the future.

An API (application programming interface) is no different. When you have developers learning how to use an API, they need a set of instructions to be successful. Rather than being faced with an abundance of tickets submitted to your support team, documentation offers an interface between your company and end users.

API providers are obligated to supply API documentation that is relevant, specific and fresh, in keeping with the latest developments in your product. It doesn’t matter how good your API is if developers don’t understand how to put it to use.

## What is API Documentation?

API documentation is a set of instructions that tells developers and other interested parties how to use your API and its services for a specific end. It usually contains code examples, tutorials, and details about the functions, classes, and return types. It essentially provides developers with all the information they need to build integrations with the API and make API calls with the software.

API calls are a type of request that is made by the third-party developer to the platform’s API. The API calls are described in the documentation and tells the developer exactly what they can ask the API to do and how.

API documentation clearly explains its endpoints, interprets why you’d want to use them, and gives very specific examples of how you’d want to use them.

APIs are important because it means developers don’t have to keep building the same software solutions over and over from scratch. APIs mean developers can take advantage of other platforms that have already been created and integrate their functionality into their own programs. Many big platforms have APIs, including Twitter and GitHub.

## Types of API

### For the Team

You might have an API that is internal to your company and therefore intended to be used only by members of your team. The purpose of this type of API would be to streamline the transfer of data between teams and systems, so your company’s internal developers are the ones who would be in charge of using this API.

### For the Partners

Partner APIs are shared outside the organization but only with those who have a business relationship with the company who is providing the API. Only authorized clients have access to the API and as a consequence security measures tend to be more stringent with this type of API.

### For the End-users

APIs for end-users or [open APIs](https://document360.com/blog/open-api/) can be used by any developer without any restrictions. These types of APIs don’t have particularly strict authentication and authorization measures because the providers want the API to be adopted by as many developers as possible. Sometimes this type of API will be available for a subscription fee which is tiered depending on the number of API calls being made.

## Who Writes API Documentation?

Naturally, as developers are the ones who build the APIs they are often tasked with writing the documentation. Unfortunately, developer-driven documentation can often be overly technical because developers are so close to the subject matter. Documentation written by developers may also fall by the wayside as developers are actually focused on building and maintaining the API.

For this reason, many companies employ professional technical writers to create their API documentation. Technical writers have the technical ability to understand the API and the creative skills to be able to write engaging content for end users who are developers.

The API developers supply the technical writer with the information they need to be able to document the API accurately. If any parts are missing from the documentation the developers can help the technical writer fill them in, with the end result that you have a document that is clear and accessible to its target audience.

*Also, check out our article on [How to create an enchanting API developer experience with the documentation](https://document360.com/blog/api-developer-experience/)*

## Benefits of API Documentation

For providers who want to offer an API, developing documentation can have many important benefits for your organization.

### Enhances the API’s Developer Experience

First and foremost, API documentation improves the developer experience. It doesn’t matter how good your API is if potential developers don’t understand how to use it. Good API documentation helps developers understand the different endpoints it has to offer and examples of particular use cases. When you improve the developer experience you increase the number of potential users you are able to attract to your product.

Reduces Time Spent Onboarding Internal Developers or External Partners  
Good API documentation means your support and success teams need to spend less time onboarding new internal developers or external partners. New users of your API have all the information they need to get started with your platform and set themselves up for success. When the processes are documented it removes the need for particular team members to intervene.

### Efficient product upkeep and faster updates

When you document your API effectively it means you can manage the upkeep of your product and update it more quickly. With API documentation you know exactly what your product is meant to do and how it is supposed to help end users. Documentation gives you a more intimate view of the API and allows you to roll out faster updates that will be adopted by users.

### Aids Both Internal and External Users in Comprehending the API and its Capabilities

One of the main benefits of [API documentation](https://apidocs.document360.com/docs) is that it helps both internal and external users to understand the API, what it can be used for, and how you can deploy the API for your own ends. If you don’t explain the potential capabilities of the API then new users won’t know how to use it and you’ll experience slow product adoption. Potential users of an API use the documentation as a way to make the decision whether or not to use your product.

### The Go-to Source for Team Members to Refer to API Goals

Internal team members in your organization can refer to the API documentation when they want to familiarize themselves with the goals of your API. Even those who weren’t directly involved in building the API or writing the documentation will understand the intended purpose of the API and be able to support the work of the API development team.

### Allows to identify bugs quickly and issues

When you document the API this allows you to quickly identify bugs and issues as a result of testing the API to document all its features. If your API doesn’t work as designed this feedback can be passed on to the API development team who can then take steps to fix any problems. The result is a more professional and effective API that works as expected.

An intuitive knowledge base software to easily add your content and integrate it with any application. Give Document360 a try!

[GET STARTED](https://document360.com/signup/)

## The Structure of API Documentation – Design and Function

### An Outline

The outline of your API documentation is also known as the overview. It contains a summary of the API and its purpose, and may inform potential users about the benefits of using this API over others.

### Tutorials

Tutorials form the main part of the API and their purpose is to teach users the concept of the API and how to use it effectively. It usually contains step-by-step guides for how to integrate the API and what proper functioning looks like.

### Authentication

Authentication is how the provider keeps the API’s data safe for developers and end users, and so it might have multiple authentication schemes. The API documentation explains each authentication method so users understand how to access the API.

### Endpoint definition

API endpoint definitions are the point at which the API connects with the software program. The point at which the API interacts with another system is considered the endpoint, and can include an URL of the server or service.

### Status and error codes

Status and error codes are used by APIs to inform developers when the API is not performing as expected, alongside a description of the status or error. They can contain instructions for how to proceed and resolve any errors that they come across.

### Examples

When users understand how the API works, it’s good to give them examples that show successful examples of calls, responses, error handling and other operations that they might encounter when using the API.

### Glossary

Instead of explaining every technical term throughout your documentation, you can link to a glossary which provides definitions for terms, schemas and more.

## How to Write Your First API Documentation

### Recognize the Audience

Before you start creating any type of API documentation you should make sure you understand the intended audience for your product. You must know exactly what types of users you want to focus on, the particular benefits they will get from the API, and the way they will use your documentation in the field.

It’s important to remember that your potential audience for your API documentation can be typically split into two groups. The first one is the developers who will be interacting with the API and actively using it, who will require more documentation along the lines of tutorials and code examples. The second audience is composed of technical leaders and product managers who will be assessing the API and how it aligns with business goals.

### Create a User Journey Map

When users are interacting with your API documentation, they may be in one of many stages of the user journey. Users that are first evaluating your API will need documentation to tell them exactly what your API can do and the problems it solves, as well as definitions of functions and endpoints, and how your API is different from other providers out there.

Creating a user journey map allows you to cater for users who are at different stages and provide a better developer experience. Developers will be supported every step of the way instead of wondering what your API can do for them.

### Start with Guidelines for Common Scenarios

There are some of the most common functions that your API will be used for so you can create guidelines for these scenarios. You must make sure to address typical use cases for your API so new developers can understand how to properly utilize the API. Each use case should have a separate section and include a sample message in each one.

Providing guidelines for common scenarios will help your developers get up and running without having to struggle too much on their own. It also shows developers what your API is capable of and may persuade them to choose your API over others.

### Add Samples of Code

Adding code samples to your API documentation helps developers to get started with quickly trying out your API and to understand its full potential. Each endpoint should come with its own code samples so developers can tailor the code to meet their exact specification and try out the most important functions of the endpoints.  
Code samples show potential developers how your API works and makes it easy for them to get started because they can simply copy and paste the code. You can include code samples in all the different programming languages your API is available in.

### Call Out Error Messages and Status Codes

Error messages and status codes should be included in your documentation because they tell your developers when they have or haven’t made a successful API call. Each message or code should include a brief description of why it is being displayed so users can understand what is happening when they interact with the system.

Descriptions that come alongside error messages should be constructed to show users how to resolve problems themselves. They should be detailed and specific so users can understand why the system is returning an error.

### Maintain Your Documentation

After publishing your documentation for the first time, you need to make sure you regularly revisit it to keep your content up-to-date. There is nothing more off putting to potential users of your API than documentation that is incomplete or inaccurate.  
Without maintaining effective documentation over time, developers won’t be able to use your API and you’ll experience a drop-off in adoption. Every time you make an update or release a new feature, this should be reflected in the documentation and be considered an essential part of shipping your API.

**💡 Don’t Miss:** [The Developer’s Guide to Writing Documentation That Gets Read](https://document360.com/blog/developer-documentation-best-practices/)

## API Documentation Best Practices

### 1. Adopt clear language

When writing API documentation, you’ll be unaware of the level of expertise that users of your documentation will have. That’s why it’s important to use clear, plain language that everyone can understand.

### 2. Include reference documentation

Reference documentation for APIs is a comprehensive list of objects and methods exposed by the API, along with a description of how to use each one. This teaches developers about everything that is available and how it operates.

### 3. Implement examples

As often as possible you should use examples for how your API works, that can be found inside any reference area of your documentation. It can be anything that illustrates the concept of the API and helps developers get started with their own API calls.

### 4. Put someone in charge of the docs

You need someone on your team whose job it is to oversee the developer experience of your API documentation. It could be their entire job if they are a technical writer or a part-time responsibility if they are also a developer.

### 5. Cover different types and topics

You need to make sure that your API documentation is comprehensive and that it covers references, guides, and examples. If certain areas are missing then you’ll use this information to decide where to focus future efforts.

### 6. Incorporate documentation into processes

Your documentation and your API should develop in tandem. With the evolution of the API, comes the development of your documentation especially alongside new feature releases. Automate as much as you can and save time with your documentation.

### 7. Provide quickstart guides

Quickstart guides are the best way to onboard new developers with your API and get them started with using your API. They contain instructions on how to use your API as well as code samples that make accessing your API much easier.

Also, check out our blog on [API documentation checklist](https://document360.com/blog/api-documentation-checklist/)

## Best Examples of API Documentation

Here are a few examples of real API documentation out there that you can use to inspire your own efforts.

### GitHub API

The GitHub API is a [REST API](https://document360.com/blog/what-is-rest-api/) that developers can use to connect with the GitHub platform, which is a collaborative development tool for software projects. GitHub offers thorough quickstart documentation to help developers get to grips with the API and detailed sections for each aspect of using the API.

### Twilio API docs

Twilio’s API is another REST API that developers can use to connect with the Twilio platform, a customer engagement platform that empowers businesses to communicate at scale. The documentation contains everything you need to integrate with Twilio, including authenticating with both HTTP and SDKs.

### Dropbox API docs

Dropbox’s API enables developers to create integrations with Dropbox’s document-sharing platform. It offers pre-built components that help users to embed Dropbox components, along with an API reference to enable developers to build custom applications and integrations. It also offers several official SDKs for popular programming languages.

## Wrapping Up

Simply building an API is not enough to ensure product adoption – you need to provide comprehensive API documentation to show your potential and current users how to use your tool. If no one understands what your API is meant to do, no one will be motivated to use it and you’ll be missing out on a lot of potential profits. Even if your API is non-profit, you’ll still want to maximize the number of users that you are exposing your API to.

When creating your API documentation, think carefully about your potential users, and the types of content that will help them get the most out of your tool. You must cater to all the most common use cases and anticipate the hurdles that your users are most likely to encounter when trying to implement your API.

Offering an API is a wonderful way to extend the functionality of your product and reach large pools of new users. Documentation is the bridge between your API and the final users who will be employing your API to achieve their goals.
