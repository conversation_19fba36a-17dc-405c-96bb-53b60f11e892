# How to Automatically Trigger Apidog Automated Tests After Code Submission?

In team collaborative development, having to manually run interface tests after every code submission can be a hassle. Wouldn’t it be great if tests could run automatically with each code commit?

Actually, this idea is completely achievable. Most teams use CI/CD platforms to manage code builds and deployments, and these platforms can listen for Git commit events. When you push code, the platform automatically executes your preset tasks—such as compiling, packaging, deploying, and so on.

Since the platform can execute various tasks, running test tasks is no problem. Apidog provides a CLI command line tool that can start automated tests with a simple command. By configuring this command in your CI/CD pipeline, you can achieve automatic testing after code submission.

The entire configuration process isn’t complicated. The key is to understand the trigger principle and then choose the appropriate integration method based on your existing platform.

**Principle of Automatically Triggering Tests**

The core of the whole process is "event listening + command execution."

When you push code to a Git repository, the CI/CD platform listens for this Git event and, according to your preset configuration (such as pipeline scripts or config files), automatically executes the Apidog test command.

This principle can be illustrated as follows:

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHkpodSl9Dic9eWrl5IxoPJ0kP0GbxF9bmBuichHdQogag6STkqr4b5daggRXcbo4jM3oErP48X82Jlg/640?wx_fmt=png&from=appmsg&randomid=1ojvg0bo&tp=webp&wxfrom=10005&wx_lazy=1)

There are mainly two ways for CI/CD platforms to listen for Git events.

The first is the platform’s built-in event mechanism. For example, GitHub Actions can directly specify in the config file: on: [push, pull_request]. When you push code or create a PR, the platform automatically listens for these Git events and starts testing.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHkpodSl9Dic9eWrl5IxoPJ0kYoqibiaaD8GKIxO6LJgUotsZNoSoRia9J6iantXDW1U1bgeD3PGqpy8mug/640?wx_fmt=png&from=appmsg&randomid=koiuurkg&tp=webp&wxfrom=10005&wx_lazy=1)

The second is via Webhook, suitable for scenarios like Jenkins that require cross-platform linkage. You need to manually configure a trigger URL.

Regardless of the method, the final step is the same: execute the apidog run command to start automated testing.

**Integration Solutions for Mainstream Platforms**

If you use code hosting platforms like GitHub or GitLab, triggering tests is especially simple. These platforms have built-in CI/CD services (like GitHub Actions, GitLab CI) that can directly listen for Git events and execute tasks. You can refer to these docs to get started quickly:

- [Integrate with Github Actions](https://docs.apidog.com/integration-with-github-actions)
- [Integrate with Gitlab](https://docs.apidog.com/integration-with-gitlab)

However, many teams have more complex setups. For example, code is hosted on GitHub or GitLab, but the CI/CD pipeline runs on Jenkins. In this case, GitHub/GitLab and Jenkins are two independent systems, and the former cannot directly call the latter’s functions.

For such cross-platform scenarios, Webhook is generally used. Webhook is essentially a callback mechanism—when a specific event (like a Git push) occurs on GitHub, it actively sends a request to a specified Webhook URL to notify an external system. Jenkins just needs to provide a URL to receive Webhook requests, and upon receiving the notification, it can automatically start the test task.

Let’s look at a specific configuration: code is hosted on GitHub, but the test pipeline runs on Jenkins.

**GitHub + Jenkins: Linking to Run Apidog Automated Tests**

If your team’s code repository is on GitHub and build tasks run on Jenkins, refer to this section.

**Step 1: Configure Jenkins and Obtain Webhook URL**

First, prepare the test task in Jenkins. Follow the [Integrate with Jenkins](https://docs.apidog.com/integration-with-jenkins) documentation to create a project, configure the build command, and ensure the CLI command can run properly.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHkpodSl9Dic9eWrl5IxoPJ0kaKwB5gAgHy1zqa7tREjhHJwREOJowIucxW2Qm2FiaOqGaJcAiclNxvsQ/640?wx_fmt=png&from=appmsg&randomid=m8iy0ics&tp=webp&wxfrom=10005&wx_lazy=1)

Next, get the Webhook address from Jenkins. This address is the entry point for external systems to call Jenkins; GitHub will use it to trigger test tasks.

The simplest way is to install the "Generic Webhook Trigger" plugin. Search for and install it in Jenkins’ plugin management, then restart Jenkins.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHkpodSl9Dic9eWrl5IxoPJ0kicVj8J0aLBnpAZlYnViaLibQ9M9CC2dTaRUxv56az3Npk2co9Gpz40MkQ/640?wx_fmt=png&from=appmsg&randomid=21u6743y&tp=webp&wxfrom=10005&wx_lazy=1)

Then go to your project’s configuration page and enable this plugin. The Webhook address will be:

`http://<your Jenkins server address>/generic-webhook-trigger/invoke`

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHkpodSl9Dic9eWrl5IxoPJ0kdyCYVK0oN8sLzicZvM37tFHic30Ae6DUfkTmic2zu2Nyb3pEXyqicnSqZQ/640?wx_fmt=png&from=appmsg&randomid=zb4dio0s&tp=webp&wxfrom=10005&wx_lazy=1)

For security, it’s recommended to set a custom Token, so the address becomes:

`http://<your Jenkins server address>/generic-webhook-trigger/invoke?token=<xxxxxx>`

Once you have this URL, you can configure the Webhook in GitHub.

**Step 2: Configure GitHub Webhook**

Go to your "GitHub repository → Settings → Webhooks", add a new Webhook, enter the address from the previous step, set Content type to application/json, select push or other events you need, and save.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHkpodSl9Dic9eWrl5IxoPJ0kOJQX5ZicKOniadQvphKO73vAEKwiagONFLmUp3XGEsvL26jzsYDGhibwBA/640?wx_fmt=png&from=appmsg&randomid=k4x2g9sy&tp=webp&wxfrom=10005&wx_lazy=1)

After configuration, every code push will automatically trigger Jenkins to execute the test task. Try pushing code and check the build log and test results in Jenkins.

**Step 3: End-to-End Process Verification**

Push code to GitHub. Since we configured the Webhook, GitHub will send a notification to Jenkins via the Webhook URL. Jenkins will then automatically start the build task. You can view the test execution log in the Jenkins project’s "Console" and see the final test report.

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHkpodSl9Dic9eWrl5IxoPJ0kdABiawZmswCpt2vnjYAiaQiaiaC4OHGkPico4ibAS0Ev716y5Y0WRqpRhjrg/640?wx_fmt=png&from=appmsg&randomid=ptadkt3x&tp=webp&wxfrom=10005&wx_lazy=1)

![Image](https://mmecoa.qpic.cn/sz_mmecoa_png/Y2mibLibqKzHkpodSl9Dic9eWrl5IxoPJ0kia9QjWk0WJs9M1ve8PnMcLsD99Ibr6G00Y5aWKNunPjgJbQtnDwFNCg/640?wx_fmt=png&from=appmsg&randomid=xga4m7vq&tp=webp&wxfrom=10005&wx_lazy=1)

**Webhook Configuration for Other Platforms**

Besides GitHub, other code hosting platforms also support Webhooks, such as:

- [GitHub Webhooks](https://docs.github.com/en/webhooks)
- [GitLab Webhooks](https://docs.gitlab.com/user/project/integrations/webhooks/)
- [Bitbucket Cloud](https://support.atlassian.com/bitbucket-cloud/docs/manage-webhooks/)
- [Bitbucket Server](https://confluence.atlassian.com/bitbucketserver/manage-webhooks-938025878.html)

The configuration methods are similar. The key is to understand the trigger mechanism: a Git commit generates an event, which is then used to notify the CI/CD platform via event listening or Webhook, ultimately triggering the automatic execution of the test command.

For more CI/CD platform integration methods, refer to the [CI/CD Integration](https://docs.apidog.com/cicd) section of the Apidog official documentation.
