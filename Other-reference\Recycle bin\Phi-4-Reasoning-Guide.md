# Unlocking the Power of Phi-4 Reasoning: A Beginner's Guide

> **PRO TIP:** Looking to experiment with Phi-4 Reasoning models in your projects? Try [Apidog](https://apidog.com) – the all-in-one API development platform that makes testing and integrating AI models seamless. With its intuitive interface for API testing, documentation, and mock servers, Apidog simplifies working with advanced models like Phi-4. Perfect for developers who want to harness AI reasoning capabilities without the complexity!

## What Are Small Language Models and Why Should You Care?

While giant AI models grab headlines, something exciting is happening with smaller, more efficient models. Microsoft's Phi series is leading this revolution with two impressive new additions: **Phi-4-reasoning** and **Phi-4-reasoning-plus**.

These compact models can solve complex problems with human-like reasoning, despite being much smaller than their massive counterparts. Let's explore what makes them special and how you can start using them today.

## Understanding Phi-4 Reasoning Models

### The Basics: What Sets These Models Apart

Phi-4 reasoning models are built on Microsoft's 14-billion parameter Phi-4 foundation but with special training to enhance their problem-solving abilities. Think of them as specialized versions designed specifically for tasks requiring step-by-step thinking.

There are two main versions:

* **Phi-4-reasoning**: The standard version, trained through supervised learning on carefully selected examples of step-by-step reasoning
* **Phi-4-reasoning-plus**: An enhanced version that uses reinforcement learning to produce more detailed reasoning chains

Both models can handle up to 32,000 tokens of text at once (that's roughly 25,000 words!), making them suitable for complex problems and lengthy explanations.

### How They're Trained

**Phi-4-reasoning** is created through a process called supervised fine-tuning (SFT). Microsoft trained it on:
* High-quality public data
* Synthetic examples
* Chain-of-thought reasoning patterns (breaking problems into steps)
* Safety-aligned content

**Phi-4-reasoning-plus** takes this a step further with reinforcement learning, which teaches the model to use more computing power to generate more thorough explanations. This results in answers that are about 1.5 times longer and typically more accurate, though slightly slower.

## Performance: How Do They Compare?

These models excel at challenging reasoning tasks across multiple domains:

* **Math**: From high school to olympiad-level problems
* **Science**: Graduate-level scientific questions
* **Coding**: Competitive programming challenges
* **Logic and Planning**: Spatial reasoning and complex planning tasks

![Phi-4-Reasoning & Phi-4-Reasoning-Plus Benchmarks](https://assets.apidog.com/blog-next/2025/05/image-3.png)

![Phi-4-Reasoning & Phi-4-Reasoning-Plus Benchmarks](https://assets.apidog.com/blog-next/2025/05/image-4.png)

![Phi-4-Reasoning-Mini Benchmarks](https://assets.apidog.com/blog-next/2025/05/image-5.png)

### Key Performance Highlights

* **Phi-4-reasoning-plus** consistently outperforms the standard version across most benchmarks
* Both models outperform much larger models (5x their size) on many reasoning tasks
* They approach or sometimes exceed the performance of industry giants on specific benchmarks
* While specialized for reasoning, they also show strong general capabilities in instruction following, coding, and safety

This impressive performance proves Microsoft's point: with high-quality training data focused on reasoning, smaller models can achieve remarkable results without needing massive size.

## Running Phi-4 Reasoning on Your Computer

One of the biggest advantages of these smaller models is that you can run them on your own computer. Here's how to get started using Ollama, a popular tool for running AI models locally.

### Step 1: Install Ollama

Visit [ollama.com](https://ollama.com/) and download the installer for your operating system (Windows, macOS, or Linux). Run the installer and follow the prompts.

### Step 2: Download the Model

Open your command prompt or terminal and run one of these commands:

```
# For the standard version
ollama pull phi4-reasoning

# For the enhanced plus version
ollama pull phi4-reasoning:plus
```

This will download the model files to your computer. The download size is around 11GB, so it might take some time depending on your internet connection.

### Step 3: Start Using the Model

Once downloaded, start the model with:

```
# For the standard version
ollama run phi4-reasoning

# For the enhanced plus version
ollama run phi4-reasoning:plus
```

You'll see a prompt where you can type your questions.

### Step 4: Get the Best Results

For the best performance, especially on complex problems, use this approach:

1. **Ask clear, specific questions**
2. **Be patient** - the model will think through problems step by step
3. **Look for the thinking process** - the model will show its work in `<think>...</think>` tags
4. **Find the final answer** in the `<solution>...</solution>` section

**Hardware Note**: These models work best with at least 16GB of RAM. The quantized versions Ollama uses require about 11GB of memory.

## Using Phi-4 Reasoning Through a Free API

If your computer doesn't have enough resources or you want to integrate these models into applications, you can use OpenRouter's free API tier.

### Step 1: Get an API Key

1. Go to [openrouter.ai](https://openrouter.ai/)
2. Create an account or log in
3. Navigate to your settings and create a new API key
4. Copy and save this key securely

### Step 2: Set Up Your Environment

If you're using Python, install the OpenAI library:

```
pip install openai
```

### Step 3: Test with Apidog (Optional)

Apidog is a user-friendly tool for testing APIs. Here's how to use it with Phi-4-reasoning:

![Apidog Interface for Testing Phi-4](https://assets.apidog.com/blog-next/2025/04/main-interface-20.png)

1. Download and install Apidog from their website
2. Create a new project and add a new request
3. Set the method to POST and the URL to `https://openrouter.ai/api/v1/chat/completions`
4. Add an Authorization header with `Bearer YOUR_API_KEY` (replace with your actual key)

![Configuring Phi-4 API Request in Apidog](https://assets.apidog.com/blog-next/2025/04/image-750.png)

5. In the request body (JSON format), use:

```json
{
  "model": "microsoft/phi-4-reasoning:free",
  "messages": [
    {"role": "user", "content": "Your question here"}
  ]
}
```

![Sending Phi-4 API Request in Apidog](https://assets.apidog.com/blog-next/2025/04/image-751.png)

6. Click Send to see the model's response

## Practical Applications

Phi-4 reasoning models excel at tasks requiring step-by-step thinking:

* **Math problem solving**: From algebra to calculus
* **Scientific analysis**: Breaking down complex scientific concepts
* **Coding challenges**: Solving programming problems with clear explanations
* **Logical reasoning**: Working through puzzles and logical problems
* **Educational support**: Explaining concepts with detailed reasoning

## Conclusion

Phi-4-reasoning and Phi-4-reasoning-plus represent an exciting development in AI. They prove that smaller, more efficient models can achieve impressive reasoning capabilities when properly trained.

Whether you run them locally on your computer or access them through an API, these models offer powerful reasoning tools that were previously only available in much larger systems.

As small language models continue to evolve, the Phi-4 reasoning family shows that focused training on specific capabilities can be more important than sheer size. This democratizes access to advanced AI reasoning tools and opens new possibilities for developers and users alike.