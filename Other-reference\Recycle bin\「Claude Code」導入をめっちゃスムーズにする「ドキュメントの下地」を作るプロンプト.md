# 「Claude Code」導入をめっちゃスムーズにする「ドキュメントの下地」を作るプロンプト

「Cursor擦り倒すシリーズ」ということで何本か記事を書いてきましたが、ハタと気づきました。

「最近は Claude Code のほうをよく使っています。出しているアウトプットの総量は断然 Cursor より多いです。」

ということで、一旦 Cursor 擦り倒すは終了して、今度は「Claude Code」擦り倒してさらなる生産性向上に努めたい所存です。

ちなみに、Cursor 擦り倒すシリーズの 1 つ目の記事はこちらです。

# はじめに

「Claude Code はいい感じにコードベースの意図をくみ取って、バシバシ開発してくれるらしい」と耳にして、**Claude Code** を導入した私は最初の一歩でつまずきました。ツール自体の学習コストよりも、*運用に載せるまでの摩擦* はそれなりに大きかったです。

そこで私は、一枚の **“初期設定プロンプト”** を用意することにしました。結果から言えば、このプロンプトが **導入ハードルを劇的に下げ、開発者の“やってみるか”を後押し** する決定打となりました。今日は、その舞台裏を整理しておきたいです。

## 背景──「AI は魔法」ではなかった

私が所属するいえらぶ GROUP では昨年から *Cursor* や *Copilot* を試験的に使い始め、コード生成やレビューを AI に肩代わりさせる動きが加速しています。しかし、**「生成までは速いが、知識が散逸する」** という課題は残ったままです。

特に *Claude Code* は「長文プロンプト × ファイル操作」を得意とする一方で、

- 👉 **どのファイルをいつ更新すべきか**
- 👉 **更新可否を誰が判断するか**
- 👉 **履歴をどう残すか**

といった “運用ポリシー” をユーザーが設計しない限り、メリットを発揮できません。つまり、「AI さえ入れればドキュメントは勝手に整う」という発想は甘かったです。

## 導入を阻む三つの摩擦

1. **ファイル構成が把握できないです**
   
   > 「そもそも docs/ に何があったっけ」と新入りメンバーほど躊躇します。

2. **更新ルールが曖昧です**
   
   > 「CI の結果を貼るのは README なのか、それとも /rules/troubleshooting.md なのか」と判断コストが高いです。

3. **承認フローが重いです**
   
   > Pull Request → レビュアー → ドキュメント班… “書くよりレビューに時間がかかる” 悪循環です。

**摩擦を「仕方ない」と受け流さず、具体的な仕組みに落とし込む**。それこそが私たちエンジニアの仕事です。

## 実際のプロンプト

```
Claude Code 初期設定プロンプト
以下の手順で、このプロジェクトにインタラクティブなドキュメント更新システムを導入してください。
1. 既存ドキュメントの探索
まず、プロジェクト内の既存ドキュメントを探索してください：
.cursor/rules/ ディレクトリ内のすべての.mdファイル
docs/ ディレクトリ（存在する場合）
ルートディレクトリの*.mdファイル（README.md、CONTRIBUTING.mdなど）
その他プロジェクト固有のドキュメントディレクトリ
見つかったドキュメントをリストアップし、それぞれの役割を簡潔に説明してください。
2. CLAUDE.mdへの追記
CLAUDE.mdファイルに以下の内容を追記してください。既にCLAUDE.mdが存在する場合は、既存の内容を保持したまま、以下のセクションを追加してください。
## 📚 ドキュメント自動更新システム

このプロジェクトでは、開発中に得られた知識を体系的に管理し、既存ドキュメントに反映させるシステムを採用しています。

### 参照すべきドキュメント

作業開始時に必ず以下のドキュメントを確認してください：

[ここに探索結果に基づいてドキュメントのリストを生成]
例：
- `.cursor/rules/coding-standards.md` - コーディング規約
- `.cursor/rules/architecture.md` - アーキテクチャ設計
- `docs/troubleshooting.md` - トラブルシューティングガイド

### 更新ルール

#### 提案タイミング
以下の状況で、ドキュメント更新を提案してください：

1. **エラーや問題を解決した時**
2. **効率的な実装パターンを発見した時**
3. **新しいAPI/ライブラリの使用方法を確立した時**
4. **既存ドキュメントの情報が古い/不正確だと判明した時**
5. **頻繁に参照される情報を発見した時**
6. **コードレビューの修正を終わらせた時**

#### 提案フォーマット
💡 ドキュメント更新の提案： [状況の説明]
【更新内容】 [具体的な追加/修正内容]
【更新候補】
[ファイルパス1] - [理由]
[ファイルパス2] - [理由]
新規ファイル作成 - [理由]
どこに追加しますか？（番号を選択 or skip）

#### 承認プロセス
1. ユーザーが更新先を選択
2. 実際の更新内容をプレビュー表示
3. ユーザーが最終承認（yes/edit/no）
4. 承認後、ファイルを更新

### 既存ドキュメントとの連携

- 既存の記載形式やスタイルを踏襲すること
- 関連する既存内容がある場合は参照を明記すること
- 日付（YYYY-MM-DD形式）を含めて更新履歴を残すこと

### 重要な制約

1. **ユーザーの承認なしにファイルを更新しない**
2. **既存の内容を削除・変更せず、追加のみ行う**
3. **機密情報（APIキー、パスワード等）は記録しない**
4. **プロジェクトの慣習やスタイルガイドに従う**

### ドキュメントの分割管理

CLAUDE.mdが肥大化することを防ぐため、以下の基準で適切にファイルを分割してください：

- **100行を超えた場合**: 関連する内容を別ファイルに分離することを提案
- **推奨される分割方法**:
  - `.cursor/rules/update-system.md` - 更新システムのルール
  - `.cursor/rules/project-specific.md` - プロジェクト固有の設定
  - `.cursor/rules/references.md` - 参照すべきドキュメントのリスト
- **CLAUDE.mdには概要とリンクのみ残す**: 詳細は個別ファイルへ
3. 推奨ドキュメント構造の提案
既存のドキュメント構造を分析した上で、不足している可能性のあるドキュメントを提案してください：
📁 ドキュメント構造の提案：
現在のプロジェクトに以下のドキュメントを追加することを推奨します：

[探索結果に基づいて、不足しているドキュメントを提案]
例：
1. `.cursor/rules/patterns.md` - 実装パターンとベストプラクティス
   → 効率的なコードパターンを蓄積

2. `.cursor/rules/troubleshooting.md` - トラブルシューティングガイド
   → エラーと解決策を体系化

3. `.cursor/rules/dependencies.md` - 依存関係とAPI使用例
   → 外部ライブラリの使用方法を記録

4. `.cursor/rules/remote-integration.md` - リモートリポジトリ連携
   → Git操作のベストプラクティス、ブランチ戦略、PR/MRテンプレート、CI/CD設定等を記録

これらのファイルを作成しますか？（作成する番号を選択: "1,2" or "all" or "skip"）
選択されたファイルに対して、初期テンプレートを作成してください。
4. 動作確認
設定完了後、以下のメッセージを表示してください：
✅ ドキュメント自動更新システムの設定が完了しました！

【設定内容】
- CLAUDE.mdに運用ルールを追記
- [作成したドキュメントのリスト]

【今後の動作】
1. 作業中に新しい発見があった際、更新提案を行います
2. あなたの承認を得てから、ドキュメントを更新します
3. 既存のドキュメント形式を踏襲し、知識を体系的に蓄積します

動作テストをしますか？（テスト用のエラーを発生させて、提案フローを確認できます）
5. 初期設定の記録
最後に、.cursor/rules/（または適切な場所）にsetup-log.mdを作成し、実行した初期設定を記録してください：
# ドキュメント自動更新システム 設定ログ

## 設定日時
[YYYY-MM-DD HH:MM]

## 実行内容
1. 既存ドキュメントの探索
   - [見つかったファイルのリスト]

2. CLAUDE.md への追記
   - ドキュメント参照リスト
   - 更新ルール
   - 承認プロセス

3. 新規作成したドキュメント
   - [作成したファイルのリスト]

## 備考
[特記事項があれば記載]

以上の手順を実行し、各ステップでユーザーの確認を取りながら進めてください。
```

### 実行すると、段階に沿ってドキュメント化を提案してくれます。

こんな感じ

[![image.png](https://qiita-user-contents.imgix.net/https%3A%2F%2Fqiita-image-store.s3.ap-northeast-1.amazonaws.com%2F0%2F215128%2F3555c4fe-2227-4908-b163-d32c6b4f3165.png?ixlib=rb-4.0.0&auto=format&gif-q=60&q=75&s=4b485776f62c7d7abd214b46fb33f0a4)](https://qiita-user-contents.imgix.net/https%3A%2F%2Fqiita-image-store.s3.ap-northeast-1.amazonaws.com%2F0%2F215128%2F3555c4fe-2227-4908-b163-d32c6b4f3165.png?ixlib=rb-4.0.0&auto=format&gif-q=60&q=75&s=4b485776f62c7d7abd214b46fb33f0a4)

---

## プロンプト設計の方針──“具体と自動の両立”

### 1. “探索 → 追記 → 提案” の 3 ステップを**強制**します

「まずは既存を読みましょう」と、プロンプトの最初に **ディレクトリ走査** を義務づけました。読まずに書く行為を物理的にブロックする狙いです。

### 2. **選択式** の承認フローを採用します

更新候補を番号付きリストで提示し、ユーザーは「1,3」や「skip」と返答するだけです。*チャット UI* だからこそ、**“書くより選ぶ”** を優先しました。

### 3. **削除禁止 / 追加のみ** のガードレールを設けます

> 「やらかしは人に起こる。ならば仕組みで潰す」と考えました。  
> 削除や改変を初期設定で封じ、「追加のみ可」と明示しました。これは *『失敗の科学』* に倣った *フォールトトレランス思考* の応用です。

---

## 実装したプロンプトの骨子です

```
1. 既存ドキュメント探索
   - .cursor/rules/*.md, docs/, *.md を列挙します
2. CLAUDE.md への追記テンプレート生成
   - 📚 ドキュメント自動更新システム
   - 更新ルール / 承認プロセス / 分割ガイドラインを含みます
3. 不足ドキュメント提案
   - patterns.md, troubleshooting.md, dependencies.md などを提示します
4. ユーザー確認 → yes/edit/no の選択を求めます
5. setup-log.md へログを出力します
```

箇条書きはあえて **動詞始まり** を徹底しました。*「検知」「追記」「提案」*──曖昧な「ちゃんと」は排し、**作業イメージを即座に喚起** するためです。

---

## 効果──“試してみた”が 3 分で終わる世界です

*Before*：

> 「README はどこでしょう。CLAUDE.md…？ まあ後でいいか」と触れずに 2 週間放置します。

*After*：

> `claude init` 相当の一回呼び出しで 3 分後に「✅ 設定完了」と表示されます。  
> → そのまま *AI* が Pull Request を生成し、10 分後に merge されます。

**“AI 導入のラストワンマイル”** をプロンプトが肩代わりした結果、トライアル参加者は 1 週間で 4 名から 18 名に増加しました。ドキュメント更新数は *2.7 倍*、レビュー待ち行列は *30% 短縮* しました。

---

## 学び──プロンプトは「設計図」ではなく「運用装置」です

- **プロンプト＝仕様書** と捉えると、今度は “読み飛ばし” が起きます。
- **プロンプト＝対話型 UI** と捉えると、ユーザーは *手足を動かしながら* 学習できます。
- そして何より、「更新を提案し、ユーザーが最終責任を負う」フローが、**心理的安全性** を担保します。

AIを使いこなすエンジニアは、 *圧倒的な技術力* だけを指しません。**仕組みで仲間を巻き込み、再現性を生むエンジニア** をこそ、これから引っ張っていく強いAI活用力があると定義したいと思います。

今回のプロンプトは第一歩に過ぎませんが、その一歩があるかないかで、組織の学習速度は倍速で変わると私は確信しています。

---

## おわりに

ドキュメントは書けば終わりではありません。**書く行為を「習慣」に昇華させる仕組み** があってこそ、ナレッジは資産になります。AI を導入する真の意義も、そこにあると私は考えます。

今日紹介したプロンプトはまだ粗削りです。それでも、「書こうか、迷うな」と指が止まりかけた同僚が、**“とりあえず init”** と呟いてキーボードを叩く——その瞬間を増やせただけで、開発組織は一歩前に進んだはずです。
