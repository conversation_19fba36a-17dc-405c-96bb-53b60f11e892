LLMs.txt - conheça o novo padrão para ajudar IAs a consumirem e navegarem suas aplicações 🤩


Eu achei isso doidera! 

Existe uma proposta em andamento que basicamente é criar um arquivo padrão com links e descrições do que sua página faz, para ao invés de agentes de inteligência artificial terem que ler e processar arquivos HTML, baixar assets e mais, ir direto no texto e evitar gastar seu dinheiro em CDNs 🥳

Eh processar páginas HTML é algo complexo, pois envolve componentes dinâmicos, modais e mais e ter um arquivo em formato markdown (anos luz mais simples que uma página HTML) facilita demais a vida dos agentes de IA 💡

LLMs.txt já é uma realidade tão grande que muita gente anda apontando que o maior tráfego de seus sites tem sido de automações de IA e no analytics o primeiro arquivo que buscam é esse para cortar caminho 💥

Agora o que você talvez não saiba, é que se você tem uma API você pode também ensinar AIs a consumirem ela, sem necessariamente criar um servidor de Model Context Protocol (MCP), você pode criar o arquivo LLMs.txt assim como para páginas Web

E pra isso, a melhor ferramenta do momento é a API Dog! ✅

Eu já fiz video sobre eles no meu canal e já até publiquei aqui sobre, é uma alternativa fenomenal ao Postman e que agora está no foco de fazer documentações de API serem AI-friendly, incrível né?

Faz o seguinte, aqui nos comentários eu deixei o acesso ao APIDog (gratuito) e também o video completo que fiz mostrando como usar a ferramenta 👇🏻

Ela é incrivelmente fáci lde trabalhar e depois que conheci, nunca mais usei Postman, Insomnia ou similares para documentar ou requisitar Web APIs

E me conta ai, o que você acha desse movimento do LLMs.txt? 🤩
