Request settings
A<PERSON>og allows you to configure a variety of settings for your API requests. These settings enable you to customize <PERSON><PERSON><PERSON>'s behavior when sending a request.

Configuring custom request settings
To configure custom settings, select the Settings tab of your request, then toggle the setting on or off. Each setting provides a description of its effect when sending the request.

For example, you can turn on SSL certificate validation or turn off URL encoding for a request.

Encoding your request URLs
<PERSON><PERSON>og parses and encodes the URL of your request to maximize the chances of a successful API call. <PERSON><PERSON><PERSON> encodes the characters in the URL and maps them to a representation that your API is most likely to accept.

Apidog supports three methods:

WHATWG

RFC 3986

No encoding

WHATWG
This is the encoding method used by modern web browsers. It's more lenient and preserves more characters in their original form. For example, it doesn't encode tildes (~) or spaces (which are converted to '+' signs instead of %20).

RFC 3986
This is a stricter standard defined by the Internet Engineering Task Force (IETF). It encodes more characters, including those that WHATWG leaves unencoded. For instance, it will encode tildes and convert spaces to %20 instead of '+'.

No encoding
This option sends the URL as-is, without any encoding. This can be useful if you've already encoded the URL manually or if you're testing how a server handles unencoded URLs. However, it may cause issues with special characters or spaces in the URL.

