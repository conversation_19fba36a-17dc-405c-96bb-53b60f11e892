The `endpoint change history` logs modifications made by team members to various fields within an API, as well as adjustments resulting from `import overrides`.

It features a `comparison` tool to highlight differences between versions before and after modifications, allowing for reversion to any prior version.

To access the `endpoint change history`, click on the icon located in the upper right corner of the `API` section to expand the history view.

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341159/image-preview" style="width: 640px" />
</p>

You can view the revision history of the endpoint, including contributors, and timestamps.

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341160/image-preview" style="width: 240px" />
</p>

Selecting a record will display the changes made to each field before and after the modification.

<p style="text-align: center">
    <img src="https://api.apidog.com/api/v1/projects/544525/resources/341161/image-preview" style="width: 640px" />
</p>

To revert to a previous version, simply click `Revert`. This action creates a new version based on the chosen historical version, while preserving the original modification history.
