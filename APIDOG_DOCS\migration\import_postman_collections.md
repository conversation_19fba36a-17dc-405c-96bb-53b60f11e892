## Video Tutorial

<Video src="https://www.youtube.com/embed/mGAbi2gyFao?si=WPx5uRAoXPnD1BKt"></Video>


## Conceptual Mapping

Understanding the conceptual correlation between Postman and Apidog can help prevent data inconsistencies during import.

In Postman, a `Collection` corresponds to a `Project` in Apidog. Thus, variables within a Postman Collection are equivalent to global variables in an Apidog project.

![](https://assets.apidog.com/uploads/help/2023/12/25/6f88f3f8f9d4be8788d2ca17b82f7501.png)

The Postman API uses variables to store the front URL (usually a domain name), while the front URL of Apidog is stored independently in the "Service" in "Environment Management".

![](https://assets.apidog.com/uploads/help/2024/03/06/50ee311cada778aa6d9bff47ddab9b53.png)

## Example

This article uses the `Apidog Echo` API as an example to demonstrate how to import Postman data into Apidog.

Assume there is a `Collection` called `Apidog Echo` in Postman, which contains two variables: `baseUrl` and `name`:

<Background>
![](https://assets.apidog.com/uploads/help/2023/12/25/d68c456df4d8fb0ba835c19979598e22.png)
</Background>

This `Collection` has a `GET` request. `baseUrl` is used to specify the target domain name for the current request and `name` is used as a query parameter.

## Exporting APIs

Click Collections on the left sidebar of Postman, mouse over the collection to export, click the `...` icon, and select Export.

<Background>
![](https://assets.apidog.com/uploads/help/2023/12/25/68af83ea36f4bfbfa0b27060944b69fb.png)
</Background>
    
Then select the `Collection v2.1` format for export.

<Background>
![](https://assets.apidog.com/uploads/help/2023/12/25/45acf65e0c61ec723a84e8c07a454d16.png)
</Background>
    
## Importing APIs

Open the "Settings" panel in Apidog, click "Import Data", select Postman, and upload the file.

<Background>
![](https://assets.apidog.com/uploads/help/2024/03/06/395df5064089e61e07e59c0bbca89c96.png)
</Background>
    
You can view all the APIs under the `Collection` on the import preview page. To seamlessly migrate, you need to check the environment with the same name as the Collection on the "Environment" page, and then click the "Confirm" button.

<Background>
![](https://assets.apidog.com/uploads/help/2023/12/25/905cc774d55784b9c3e73c06dd3c4612.png)
</Background>
    
After the importing process is completed, you can see the `name` parameter on the API documentation and the link address in `baseUrl` in Environment Management.

<Background>
![](https://assets.apidog.com/uploads/help/2023/12/25/04bc342638f3c6923c8b3f0be2f11f42.png)
</Background>
    
When sending the request to API, select the corresponding environment. The prefix URL will be automatically spliced ​​before the path, keeping it consistent with Postman.

<Background>
![](https://assets.apidog.com/uploads/help/2023/12/25/60e31ced2839e2002c84130725de8b88.png)
</Background>