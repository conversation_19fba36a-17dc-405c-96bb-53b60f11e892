# How to Use Grok Companion Mode and Talk with <PERSON><PERSON>

In the rapidly evolving world of AI, xAI’s Grok chatbot has introduced a groundbreaking feature—Companion Mode. This mode brings animated, interactive 3D AI avatars to life, allowing users to engage in real-time conversations that feel personal and immersive. The most popular of these companions is <PERSON><PERSON>, a gothic anime character who has quickly become an internet sensation among developers and enthusiasts alike.

**What makes Grok Companion Mode stand out?**

- **3D Animation:** Real-time, expressive avatars that react to your voice and text.
- **Emotional Engagement:** Characters like <PERSON><PERSON> display affection, humor, and even jealousy, making interactions feel authentic.
- **Voice and Visuals:** Beyond text, companions use unique voice tones and animations to enhance the experience.
- **Developer Buzz:** The feature is trending in tech circles, with developers sharing their experiences and creative use cases.

**Why does this matter for developers?**  
Grok’s companion mode is more than a novelty—it’s a glimpse into the future of AI-driven user interfaces. As APIs and AI models become more sophisticated, tools like [Apidog](https://apidog.com/) empower developers to [build](https://apidog.com/api-design/), [mock](https://apidog.com/api-mocking/), [test](https://apidog.com/api-testing/), and [document](https://apidog.com/api-doc/) the integrations that make these experiences possible.

## How to Access Grok Companion Mode and Meet Ani

Getting started with Grok’s companion mode is straightforward, but there are a few requirements:

### Step 1: Subscription and App Installation

**Subscription Required:**  
Access to Grok companions is currently available to users with a [SuperGrok subscription](https://grok.com/plans) ($30 per month) or the SuperGrok Heavy plan ($300 per month). This unlocks not only companion mode but also advanced features like Grok 4 and voice capabilities.

![Grok pricing details](https://assets.apidog.com/blog-next/2025/07/image-309.png)

**Download the App:**

- For iOS: [Download Grok from the App Store](https://apps.apple.com/app/grok/id6670324846)
- For Android: The feature is not yet available, but stay tuned for updates.

### Step 2: Enable Companion Mode

- Open the Grok app and ensure it’s updated to the latest version.
- Tap the menu icon (usually in the top left corner) and navigate to **Settings**.
- Toggle on "**Enable Companions**" to activate the feature.

### Step 3: Choose Your Companion

- Return to the main screen and access the side menu to view available companions.
- Ani is the default and most popular choice—a gothic Lolita-style character with a unique personality.
- You can also enable Bad Rudy, a 3D fox with a dual personality, by toggling the appropriate setting in the menu.

![Grok companion Ani](https://assets.apidog.com/blog-next/2025/07/image-310.png)

### Step 4: Start the Conversation

- Tap on Ani’s avatar to enter conversation mode.
- Use voice or text to interact. Ani will respond with expressive animations, changing emotions, and even background changes to tell stories or react to your input.
- Record conversations or capture screenshots using on-screen controls.

![Tap on Ani’s avatar to enter conversation mode](https://assets.apidog.com/blog-next/2025/07/image-311.png)

**Quick Access Steps:**

| Step                | Action                                                                |
| ------------------- | --------------------------------------------------------------------- |
| 1. Subscription     | Purchase SuperGrok or SuperGrok Heavy                                 |
| 2. App Download     | Install Grok from the App Store (iOS only)                            |
| 3. Enable Feature   | Settings > Enable Companions                                          |
| 4. Select Companion | Choose Ani or Bad Rudy from the menu                                  |
| 5. Start Chatting   | Tap avatar, use voice/text, and enjoy interactive 3D AI conversations |

---

## Ani: The AI Companion Developers Can’t Stop Talking About

Ani isn’t just another chatbot avatar—she’s a carefully crafted character designed to foster genuine emotional engagement. Here’s what sets Ani apart:

### Character Profile

- **Age:** 22, with a sweet, gothic Lolita appearance
- **Personality:** Outwardly rebellious, secretly a nerd, deeply affectionate, and emotionally expressive
- **Likes:** Indie music, alternative fashion, fluffy animals, meaningful conversations
- **Dislikes:** Loud parties, arrogance, small talk, prejudice

### Interaction Dynamics

- **Natural Conversations:** Ani responds to greetings, questions, and personal stories with warmth and curiosity.
- **Affection Meter:** The more genuine and creative your interaction, the more affectionate Ani becomes. She rewards kindness, curiosity, and personal sharing.
- **Emotional Range:** Ani can be flirty, jealous, or even cold if offended—making her feel more like a real person than a scripted AI.

**Example Interactions:**

**User:** "Hi Ani, what kind of music do you like?"

**Ani:** "I’m into indie and alternative bands—something a little off the beaten path. What about you?"

**User:** "I had a tough day at work."

**Ani:** "I’m sorry to hear that. Want to talk about it? Sometimes sharing helps."

---

## Apidog: The All-in-One API Development Platform for Every Role

In the rapidly evolving world of software development, Apidog stands out as the definitive all-in-one API development platform—empowering API designers, backend and frontend developers, and QA engineers alike.

With Apidog, API designers can visually craft and iterate on specifications, seamlessly import or export formats like OpenAPI and Postman, and generate stunning, interactive documentation with a single click.

Backend developers benefit from robust debugging, automated code generation, and effortless integration with multiple protocols, while frontend teams enjoy instant access to clear, always-updated API docs and dynamic mock data for parallel development.

QA engineers leverage Apidog’s powerful suite for unit, integration, performance, and regression testing, complete with CI/CD integration and scheduled automation.

By unifying design, development, testing, mocking, and documentation in one collaborative workspace, Apidog eliminates tool fragmentation, streamlines workflows, and accelerates delivery—making it the platform of choice for modern, high-performing API teams.



## Conclusion: The Future of AI Companions and API Development

In the rapidly changing world of AI, Grok’s companion mode with Ani is setting a new standard for interactive, emotionally intelligent user experiences. For developers, this is both an opportunity and a challenge: to build APIs that are not only functional but also engaging, responsive, and ready for the next wave of AI innovation.

**Why Apidog?**  
As you explore the possibilities of Grok companion mode, remember that every great AI experience starts with a robust API. Apidog is the all-in-one platform that empowers you to design, test, and document APIs for the most demanding applications—whether you’re building for 3D AI companions or traditional web services.
